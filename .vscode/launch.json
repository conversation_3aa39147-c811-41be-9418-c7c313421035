{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "chrome",
            "request": "launch",
            "name": "Debug Web App",
            "url": "http://localhost:5174",
            "webRoot": "${workspaceFolder}/apps/web",
            "sourceMaps": true,
            "sourceMapPathOverrides": {
                "/*": "${webRoot}/*"
            }
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Debug Admin App",
            "url": "http://localhost:3001",
            "webRoot": "${workspaceFolder}/apps/admin",
            "sourceMaps": true,
            "sourceMapPathOverrides": {
                "/*": "${webRoot}/*"
            }
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Server",
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "app-dev"],
            "cwd": "${workspaceFolder}/apps/server",
            "skipFiles": ["<node_internals>/**"],
            "outFiles": ["${workspaceFolder}/apps/server/dist/**/*.js"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen"
        },
        {
            "name": "Debug All",
            "type": "node",
            "request": "launch",
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "dev"],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen"
        }
    ]
}