# Hospice OS Coding Conventions

This file documents conventions observed in the repository so that agent-based tooling can understand how code is organized and how tasks should be performed.

## Repository Layout
- **Monorepo** using NPM workspaces. Top-level `package.json` lists `apps/*` and `packages/*` as workspaces.
- `apps/` contains application projects:
  - `server` – Express backend written in TypeScript
  - `web` – React frontend
  - `admin` – React admin interface
- `packages/` contains shared libraries:
  - `apptypes` – generated TypeScript types
  - `ui-components` – shared React components
  - `cdk` – AWS CDK infrastructure
- Utility scripts live in `scripts/`.

## Development
- Node.js and TypeScript are used across the repo.
- Run all applications in development with `npm run dev` (uses `concurrently`).
- Individual apps have their own `dev` or `start` scripts (e.g. `apps/server` uses `ts-node-dev`).
- Docker Compose provides a PostgreSQL 16.6 database (`docker-compose.yml`).
- The server Docker image is defined in `DOCKERFILE.server`.

## Building and Deployment
- Each workspace has a `build` script that runs `tsc` (server and packages) or `tsc` + `vite` (web/admin).
- The root `generate-types` script (`scripts/generate-types.ts`) reads JSON schemas from the database and creates TypeScript definitions in `packages/apptypes`.
- AWS infrastructure code lives under `packages/cdk`; see its README for deployment commands.

## Testing
- **Vitest** is the test framework. Workspace configuration is in `vitest.workspace.js`.
- Tests typically reside in `__tests__` folders or use `*.test.ts`/`*.test.tsx` naming.
- Coverage reports are produced via Vitest's V8 provider.

## Formatting & Linting
- **Prettier** is used with the default configuration (`.prettierrc` is empty). Run `npm run format` to format the repo. `.prettierignore` excludes `node_modules`, build outputs and coverage.
- **ESLint** is configured in each package using `@eslint/js`, `typescript-eslint` and (for React apps) `react-hooks` and `react-refresh` plugins.
- The `.vscode/settings.json` file enforces Prettier on save.

## TypeScript
- Root `tsconfig.json` sets common options (module `NodeNext`, target `ES6`). Each package extends or overrides these settings.
- Frontend apps use `moduleResolution: 'bundler'` and have strict type checking enabled. The admin app currently has `strict: false` in `tsconfig.json`.

## Environment & Configuration
- Environment variables are loaded via `.env` files for local development.
- In production/staging, configuration values are retrieved from AWS Systems Manager Parameter Store (`apps/server/src/config.ts`).
- The `APP_STAGE` variable selects the parameter store path (dev/staging/prod).

## Miscellaneous
- Turborepo (`turbo.json`) defines cached tasks for build, type checking and tests.
- `.gitignore` excludes `node_modules`, Turbo cache, and compiled assets.
- Debug configurations for VSCode are provided in `.vscode/launch.json`.

## Suggestions for Consistency
- Consider enabling `strict: true` across all TypeScript projects for consistency.
- The repository relies on Prettier defaults; if specific formatting rules are desired, populate `.prettierrc` accordingly.
- Ensure test file naming is consistent (`*.test.ts`/`*.test.tsx`) across packages.
- Document any required environment variables in each app's README to ease setup.
