# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
```bash
# Start all applications concurrently
npm run dev

# Start individual applications
npm run dev:server   # Backend server only
npm run dev:web      # Web frontend only
npm run dev:admin    # Admin interface only
npm run dev:worker   # Temporal worker

# Database setup (requires Docker)
docker-compose up -d              # Start PostgreSQL
cd apps/server && npm run db:init     # Initialize schema
cd apps/server && npm run db:seed     # Seed initial data
```

### Building
```bash
# Build UI components (required before other builds)
npm run build:ui-components

# Build everything
npm run build

# Build Temporal workflows
cd apps/server && npm run build:workflow
```

### Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run single test file
npx vitest run path/to/test.ts
```

### Code Quality
```bash
# Format code with Prettier
npm run format

# Run ESLint
npm run lint

# Check package version mismatches
npm run sync-list

# Fix package version mismatches
npm run sync-fix
```

### Type Generation
```bash
# Generate TypeScript types from database schemas
npm run generate-types
```

## High-Level Architecture

### Monorepo Structure
This is a TypeScript monorepo using NPM workspaces with three main applications:

1. **Server** (`apps/server/`): Express.js backend with PostgreSQL
   - API endpoints in `src/api/`
   - Database layer in `src/db/`
   - Business logic in `src/services/`
   - Temporal workflows in `src/temporal/`

2. **Web** (`apps/web/`): React 19 frontend for hospice care management
   - Pages in `src/pages/`
   - Features in `src/features/`
   - Redux store in `src/store/`
   - Mantine UI components

3. **Admin** (`apps/admin/`): React 19 admin interface
   - Similar structure to web app
   - Schema and UI schema management

### Key Architectural Patterns

**Resource-Based Architecture**: Everything is a "resource" with a JSON schema defining its structure and validation rules. Resources have:
- Unique IDs (UUIDs)
- Resource types (e.g., "Patient", "Practitioner")
- JSON Schema for validation
- Optional UI Schema for display customization

**Schema-Driven UI**: The UI dynamically renders forms and displays based on JSON schemas and UI schemas. This allows for flexible data structures without changing code.

**Event System**: Resource events enable reactive behaviors when data changes. Events are processed through a pub/sub system.

**Temporal Workflows**: Complex business processes (e.g., patient requests, PDF generation) are implemented as Temporal workflows for reliability and observability.

**Multi-Tenancy**: Data is isolated by agency using row-level security in PostgreSQL.

### Data References Pattern
Cross-resource references use a consistent pattern:
```json
{
  "doctor": {
    "id": "550e8400-e29b-41d4-a716-************",
    "resourceType": "Practitioner"
  }
}
```

### Environment Configuration
- Local development: `.env` files
- Production: AWS Systems Manager Parameter Store
- Stage selection: `APP_STAGE` environment variable (dev/staging/prod)

### Testing Strategy
- Vitest for all testing
- Test files: `*.test.ts` or `*.test.tsx`
- Tests in `__tests__` directories
- Database tests use transactions for isolation

### Important Notes
- Always run `npm run build:ui-components` before building other packages
- Frontend apps use `strict: true` in TypeScript, server uses `strict: false`
- Prettier uses default configuration (no custom rules)
- The admin app temporarily has `strict: false` for TypeScript