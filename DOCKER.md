# Docker Setup for Local Development (TODO: Add DOCKERFILE.server)

This document explains how to use Docker Compose for local development with PostgreSQL 16.6.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker Desktop)

## Getting Started

1. Start the PostgreSQL database:

```bash
docker-compose up -d
```

This will start PostgreSQL 16.6 in a container, accessible on port 5432.

2. Initialize the database schema:

```bash
cd apps/server
npm run db:init
```

3. Seed the database with initial data (optional):

```bash
cd apps/server
npm run db:seed
```

4. Start the server in development mode:

```bash
cd apps/server
npm run app-dev
```

## Database Connection Details

- **Host**: localhost
- **Port**: 5432
- **Database**: hospice_os
- **Username**: postgres
- **Password**: postgres

These connection details are already configured in the server's `.env` file and `db/config.ts`.

## Resource Optimization

The PostgreSQL container is configured with optimized settings to reduce disk space usage:

- Reduced WAL (Write-Ahead Logging) segment size
- Limited memory usage to 512MB
- Limited CPU usage to 0.5 cores
- Optimized PostgreSQL configuration parameters for development use

These settings make the container more suitable for development environments with limited resources.

## Additional Services

The Docker Compose file includes a commented-out pgAdmin service. If you want to use pgAdmin for database management, uncomment the pgAdmin section in the `docker-compose.yml` file and run:

```bash
docker-compose up -d
```

pgAdmin will be available at http://localhost:5050 with the following credentials:
- Email: <EMAIL>
- Password: admin

## Stopping the Services

To stop all running containers:

```bash
docker-compose down
```

To stop and remove all containers, networks, and volumes:

```bash
docker-compose down -v
```

## Data Persistence

The PostgreSQL data is stored in a named volume (`hospice-os-postgres-data`), which persists between container restarts. If you need to reset the database completely, you can remove the volume:

```bash
docker volume rm hospice-os-postgres-data
```

## Troubleshooting

### No Space Left on Device

If you encounter a "No space left on device" error, you may need to:

1. Free up disk space on your host machine
2. Prune unused Docker resources:
   ```bash
   docker system prune -a
   ```
3. Remove unused volumes:
   ```bash
   docker volume prune
   ```
