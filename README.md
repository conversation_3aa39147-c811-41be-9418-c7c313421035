# Hospice OS

This repository contains the Hospice OS application, a comprehensive solution for hospice care management.

## Docker Setup for Local Development

This section explains how to use Docker Compose for local development with PostgreSQL 16.6.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker Desktop)

## Getting Started

1. Start the PostgreSQL database:

```bash
docker-compose up -d
```

This will start PostgreSQL 16.6 in a container, accessible on port 5432.

2. Initialize the database schema:

```bash
cd apps/server
npm run db:init
```

3. Seed the database with initial data (optional):

```bash
cd apps/server
npm run db:seed
```

4. Start the server in development mode:

```bash
cd apps/server
npm run app-dev
```

## Database Connection Details

- **Host**: localhost
- **Port**: 5432
- **Database**: hospice_os
- **Username**: postgres
- **Password**: postgres

These connection details are already configured in the server's `.env` file and `db/config.ts`.

## Additional Services

The Docker Compose file includes a commented-out pgAdmin service. If you want to use pgAdmin for database management, uncomment the pgAdmin section in the `docker-compose.yml` file and run:

```bash
docker-compose up -d
```

pgAdmin will be available at http://localhost:5050 with the following credentials:
- Email: <EMAIL>
- Password: admin

## Stopping the Services

To stop all running containers:

```bash
docker-compose down
```

To stop and remove all containers, networks, and volumes:

```bash
docker-compose down -v
```

## Data Persistence

The PostgreSQL data is stored in a named volume (`hospice-os-postgres-data`), which persists between container restarts. If you need to reset the database completely, you can remove the volume:

```bash
docker volume rm hospice-os-postgres-data
```

## Schema and UI Schema System

The Hospice OS application uses a flexible schema and UI schema system to define and display data. This section explains how to create and use schemas and UI schemas.

### JSON Schema

JSON Schema is used to define the structure and validation rules for data objects in the system.

#### Schema Creation

1. Create a new JSON Schema file in the appropriate location (typically in the server's schema directory).
2. Define the schema using JSON Schema Draft-07 format.
3. Register the schema in the system using the schema management API.

#### Schema Structure

A typical schema includes:

- `$id`: A unique identifier for the schema
- `type`: Usually "object" for data models
- `required`: An array of required property names
- `properties`: Object containing property definitions
- `definitions`: Reusable schema components

Example:

```json
{
  "$id": "https://hospice-os.org/schemas/PatientBase",
  "type": "object",
  "$schema": "http://json-schema.org/draft-07/schema#",
  "required": ["resourceType", "id", "firstName", "lastName"],
  "properties": {
    "id": {
      "type": "string"
    },
    "firstName": {
      "type": "string"
    },
    "lastName": {
      "type": "string"
    },
    "resourceType": {
      "type": "string",
      "const": "Patient"
    }
  },
  "definitions": {
    "Address": {
      "type": "object",
      "properties": {
        "street": { "type": "string" },
        "city": { "type": "string" },
        "state": { "type": "string" },
        "zip": { "type": "string" }
      }
    }
  }
}
```

### UI Schema

UI Schema is used to customize how data is displayed and edited in the user interface.

#### UI Schema Creation

1. Create a UI Schema object that corresponds to a JSON Schema.
2. Define display options, widgets, and ordering for properties.
3. Register the UI Schema in the system using the UI schema management API.

#### UI Schema Structure

A UI Schema can include:

- `ui:order`: Array defining the order of properties
- Property-specific configurations with widgets and options
- Conditional display rules

Example:

```json
{
  "ui:order": ["firstName", "lastName", "dateOfBirth", "allergies", "medications"],
  "medications": {
    "ui:widget": "table",
    "ui:options": {
      "columns": ["name", "dosage", "frequency"]
    }
  },
  "allergies": {
    "ui:options": {
      "collapsible": false
    }
  },
  "address": {
    "ui:widget": "textarea"
  }
}
```

### Property Ordering Rules

The system orders properties in three groups:

1. **Simple properties** (strings, numbers, booleans)
2. **Arrays with primitive items** (e.g., allergies, tags)
3. **Complex properties** (objects and arrays with object-based items)

This ordering can be overridden using the `ui:order` property in the UI schema.

### Display Customization

The UI Schema supports several customization options:

- **Widgets**: Specify how a property is rendered (`ui:widget`)
- **Collapsible Sections**: Control whether sections can be collapsed (`collapsible`)
- **Header Levels**: Set the heading level for sections (`headerLevel`)
- **Line Thresholds**: Auto-collapse sections exceeding a line count (`lineThreshold`)

### Schema Composition

Schema composition refers to how schemas can be built by referencing other schemas or reusing schema components. This is different from how data objects reference each other at runtime.

#### Local Definitions

The simplest form of schema composition is using local definitions within the same schema file:

```json
{
  "$id": "https://hospice-os.org/schemas/Patient",
  "type": "object",
  "properties": {
    "address": {
      "$ref": "#/definitions/Address"
    },
    "emergencyContact": {
      "$ref": "#/definitions/Contact"
    }
  },
  "definitions": {
    "Address": {
      "type": "object",
      "properties": {
        "street": { "type": "string" },
        "city": { "type": "string" },
        "state": { "type": "string" },
        "zip": { "type": "string" }
      }
    },
    "Contact": {
      "type": "object",
      "properties": {
        "name": { "type": "string" },
        "relationship": { "type": "string" },
        "phone": { "type": "string" }
      }
    }
  }
}
```

Benefits of local definitions:
- All schema components are in one file
- No external dependencies
- Simpler to manage for smaller schemas

#### External Schema References

For more complex systems, you can reference other schemas stored in the system:

```json
{
  "$id": "https://hospice-os.org/schemas/PatientVisit",
  "type": "object",
  "properties": {
    "patient": {
      "$ref": "https://hospice-os.org/schemas/Patient"
    },
    "practitioner": {
      "$ref": "https://hospice-os.org/schemas/Practitioner"
    },
    "medications": {
      "type": "array",
      "items": {
        "$ref": "https://hospice-os.org/schemas/Medication"
      }
    }
  }
}
```

You can also use UUID format for external schema references:

```json
{
  "properties": {
    "medication": {
      "$ref": "b30b1e44-c7e2-2b5c-90cd-49dfda514e50"
    }
  }
}
```

Benefits of external schema references:
- Promotes schema reuse across the system
- Enables modular schema design
- Supports versioning of individual schema components
- Reduces duplication and maintenance overhead

### Data Relationships

Data relationships refer to how data objects reference each other at runtime, which is different from schema composition.

#### Resource References (Foreign Key-like Functionality)

The system implements a foreign key-like functionality using a combination of `id` and `resourceType` properties. This pattern is used to reference resources across the system:

```json
{
  "properties": {
    "doctor": {
      "type": "object",
      "properties": {
        "id": {
          "type": "string",
          "format": "uuid"
        },
        "resourceType": {
          "type": "string",
          "const": "Practitioner"
        }
      }
    }
  }
}
```

This defines the schema for a reference, but the actual data at runtime would look like:

```json
{
  "doctor": {
    "id": "550e8400-e29b-41d4-a716-************",
    "resourceType": "Practitioner"
  }
}
```

When the UI encounters this reference in the data:
1. It can fetch the Practitioner resource with the given ID
2. Display relevant information from that resource
3. Provide navigation to the full Practitioner record

#### One-to-Many Relationships

One-to-many relationships are implemented using arrays of resource references:

```json
{
  "properties": {
    "medications": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "resourceType": {
            "type": "string",
            "const": "Medication"
          }
        }
      }
    }
  }
}
```

The actual data at runtime would look like:

```json
{
  "medications": [
    {
      "id": "med-123",
      "resourceType": "Medication"
    },
    {
      "id": "med-456",
      "resourceType": "Medication"
    }
  ]
}
```

#### Resolving References

When rendering data with these references, the system can:

1. **Fetch Referenced Resources**: Load the referenced resources using their IDs and types
2. **Display Relevant Information**: Show key information from the referenced resources
3. **Enable Navigation**: Allow users to navigate to the full details of referenced resources

### Best Practices

1. **Keep Schemas Focused**: Each schema should represent a single concept.
2. **Use References**: Use `$ref` to reference definitions instead of duplicating them.
3. **Provide Descriptions**: Include descriptions for properties to improve usability.
4. **Test Validation**: Ensure schemas correctly validate both valid and invalid data.
5. **Consistent Naming**: Use consistent naming conventions for schemas and properties.
6. **Resource References**: Use the `{ id, resourceType }` pattern for cross-resource references.
7. **Versioning**: Consider versioning schemas to manage changes over time.

For more detailed information about the markdown rendering system, see the [Markdown Rendering documentation](apps/web/MARKDOWN_RENDERING.md).
