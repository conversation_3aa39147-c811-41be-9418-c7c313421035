# HospiceOS Admin Application

This is the admin application for HospiceOS. It provides an interface for managing agencies, users, schemas, and resources.

## Features

- **Agency Management**: Create, view, update, and delete agencies
- **User Management**: Create, view, update, and delete users across all agencies
- **Schema Management**: Create, view, update, and validate schemas
- **Resource Review**: Review and approve/reject resources before they are processed by the AI agent

## Authentication

The admin application uses Stytch for B2B authentication. It is configured to use a fixed agency slug "super_admin" for all admin users.

### Stytch Authentication Flow

1. The admin application uses Stytch's B2B authentication to authenticate users against the "super_admin" agency.
2. When a user logs in, the Stytch session JWT is automatically stored as a cookie.
3. The session JWT cookie is included in all API requests to the server.
4. The server validates the session JWT with <PERSON>yt<PERSON> and verifies that the user belongs to the "super_admin" agency.
5. If the validation is successful, the server processes the request. Otherwise, it returns a 401 or 403 error.

### Server-Side Authentication

The server uses the Stytch Node.js SDK to validate session JWTs. The authentication flow is as follows:

1. The client sends a request with the Stytch session JWT in a cookie.
2. The server extracts the JWT from the cookie using cookie-parser.
3. The server validates the JWT with Stytch using the `stytchClient.sessions.authenticateJwt()` method.
4. The server verifies that the user belongs to the "super_admin" agency.
5. If the validation is successful, the server processes the request. Otherwise, it returns a 401 or 403 error.

### Configuration

To configure Stytch authentication, you need to set the following environment variables:

#### Client-Side (.env in apps/admin)

```
VITE_STYTCH_PROJECT_ENV=test
VITE_STYTCH_PUBLIC_TOKEN="your-stytch-public-token"
VITE_API_URL="http://localhost:3000/api/admin"
```

#### Server-Side (.env in apps/server)

```
STYTCH_PROJECT_ID=project-test-00000000-0000-0000-0000-000000000000
STYTCH_SECRET=secret-test-00000000000000000000000000000000
STYTCH_ENV=test
```

## Getting Started

### Prerequisites

- Node.js 16+
- npm or yarn

### Installation

1. Install dependencies:

```bash
npm install
```

2. Configure environment variables:

Create a `.env` file in the `apps/admin` directory with the following content:

```
VITE_STYTCH_PROJECT_ENV=test
VITE_STYTCH_PUBLIC_TOKEN="your-stytch-public-token"
VITE_API_URL="http://localhost:3000/api/admin"
```

### Development

To start the development server:

```bash
npm run dev
```

This will start the admin application on port 3001.

### Building for Production

To build the application for production:

```bash
npm run build
```

## Project Structure

```
apps/admin/
├── public/             # Static assets
├── src/
│   ├── features/       # Feature-based components
│   │   └── auth/       # Authentication components
│   ├── pages/          # Page components
│   ├── store/          # Redux store and API
│   ├── App.tsx         # Main application component
│   ├── Router.tsx      # Application routing
│   └── main.tsx        # Application entry point
├── .env                # Environment variables
├── index.html          # HTML template
├── package.json        # Dependencies and scripts
├── tsconfig.json       # TypeScript configuration
└── vite.config.ts      # Vite configuration
```

## API Integration

The admin application communicates with the server's admin API endpoints. These endpoints are protected by admin authentication middleware that validates the Stytch session token.

## Authentication Flow

1. User navigates to the admin application
2. User is prompted to log in with Stytch
3. Stytch authenticates the user against the "super_admin" agency
4. Upon successful authentication, the user is redirected to the admin dashboard
5. The Stytch session token is stored as a cookie and included in all API requests

## Resource Review Workflow

1. Resources are created by users in the main application
2. Resources with status "pending_review" appear in the admin application
3. Admins can review, approve, or reject resources
4. Approved resources are processed by the AI agent
5. Rejected resources are returned to the creator for revision
