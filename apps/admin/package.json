{"name": "@hospice-os/admin", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc --skipLibCheck && vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite --port 3001", "test": "vitest run"}, "dependencies": {"@hospice-os/apptypes": "0.0.1", "@hospice-os/ui-components": "0.0.1", "@mantine/core": "^7.17.3", "@mantine/hooks": "^7.17.3", "@reduxjs/toolkit": "^2.6.1", "@stytch/react": "^19.4.3", "@stytch/vanilla-js": "^5.22.2", "@tabler/icons-react": "^3.31.0", "@types/cookie-parser": "^1.4.8", "axios": "^1.8.4", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "json-edit-react": "^1.26.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.1", "remark-gfm": "^4.0.1", "stytch": "^12.17.0", "uuidv7": "^1.0.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}