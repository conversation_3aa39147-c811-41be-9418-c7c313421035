import { 
  AppShell, 
  <PERSON>,
  Anchor,
  But<PERSON>,
  Menu,
  Group,
  Text,
  Image
} from '@mantine/core';
import { useStytchMemberSession, useStytchB2BClient } from '@stytch/react/b2b';
import { Outlet, Link } from 'react-router-dom';
import './App.css';
import tallioIcon from './assets/tallio-icon.svg';

function App() {
  const { session } = useStytchMemberSession();
  const stytchClient = useStytchB2BClient();

  const handleLogout = () => {
    stytchClient.session.revoke();
  };

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{ width: 250, breakpoint: 'sm', collapsed: { mobile: !session, desktop: !session } }}
      padding="md"
    >
      <AppShell.Header style={{ 
        borderBottom: '1px solid #e9ecef',
        boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%',
          padding: '0 24px'
        }}>
          {/* Logo and Title */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px'
          }}>
            <Image src={tallioIcon} height={32} width={32} /> 
            <Title order={3} style={{ fontWeight: 600, margin: 0 }}>HospiceOS Admin</Title>
          </div>
          
          {/* Account */}
          <div>
            {session ? (
              <Menu position="bottom-end" shadow="md">
                <Menu.Target>
                  <Button variant="subtle" radius="md" style={{ fontWeight: 500 }}>Account</Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item onClick={handleLogout} color="red">Logout</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            ) : (
              <Button 
                variant="filled" 
                component={Link} 
                to="/login"
                radius="md"
                style={{ fontWeight: 500 }}
              >
                Login
              </Button>
            )}
          </div>
        </div>
      </AppShell.Header>

      {session && (
        <AppShell.Navbar p="md">
          <Group mb="md">
            <Text fw={500} size="sm">ADMIN PANEL</Text>
          </Group>
          
          <Anchor component={Link} to="/" display="block" mb="sm">
            Dashboard
          </Anchor>
          <Anchor component={Link} to="/agencies" display="block" mb="sm">
            Agencies
          </Anchor>
          <Anchor component={Link} to="/users" display="block" mb="sm">
            Users
          </Anchor>
          <Anchor component={Link} to="/schemas" display="block" mb="sm">
            Schemas
          </Anchor>
          <Anchor component={Link} to="/resources" display="block" mb="sm">
            Resources
          </Anchor>
          <Anchor component={Link} to="/requests" display="block" mb="sm">
            Requests
          </Anchor>
        </AppShell.Navbar>
      )}

      <AppShell.Main>
        <Outlet />
      </AppShell.Main>
    </AppShell>
  );
}

export default App;
