import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import App from './App';
import { HomePage } from './pages/Home.page';
import { AgenciesPage } from './pages/Agencies.page';
import { AgencyDetailsPage } from './pages/AgencyDetails.page';
import { UsersPage } from './pages/Users.page';
import { UserDetailsPage } from './pages/UserDetails.page';
import { SchemasPage } from './pages/Schemas.page';
import { SchemaDetailsPage } from './pages/SchemaDetails.page';
import { ResourceDetailsPage } from './pages/ResourceDetails.page';
import { ResourceCreatePage } from './pages/ResourceCreate.page';
import { AuthPage } from './pages/Auth.page';
import { ProtectedRoute } from './features/auth';
import { ResourcesPage } from './pages/Resources.page';
import { RequestsPage } from './pages/Requests.page';
// Define routes
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <ProtectedRoute><HomePage /></ProtectedRoute>,
      },
      {
        path: 'agencies',
        element: <ProtectedRoute><AgenciesPage /></ProtectedRoute>,
      },
      {
        path: 'agencies/:id',
        element: <ProtectedRoute><AgencyDetailsPage /></ProtectedRoute>,
      },
      {
        path: 'users',
        element: <ProtectedRoute><UsersPage /></ProtectedRoute>,
      },
      {
        path: 'users/:id',
        element: <ProtectedRoute><UserDetailsPage /></ProtectedRoute>,
      },
      {
        path: 'schemas',
        element: <ProtectedRoute><SchemasPage /></ProtectedRoute>,
      },
      {
        path: 'schemas/:id',
        element: <ProtectedRoute><SchemaDetailsPage /></ProtectedRoute>,
      },
      {
        path: 'resources',
        element: <ProtectedRoute><ResourcesPage /></ProtectedRoute>,
      },
      {
        path: 'resources/create',
        element: <ProtectedRoute><ResourceCreatePage /></ProtectedRoute>,
      },
      {
        path: 'resources/:id',
        element: <ProtectedRoute><ResourceDetailsPage /></ProtectedRoute>,
      },
      {
        path: 'auth',
        element: <AuthPage />,
      },
      {
        path: 'authenticate',
        element: <AuthPage />,
      },
      {
        path: 'requests',
        element: <ProtectedRoute><RequestsPage /></ProtectedRoute>,
      },
    ],
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
