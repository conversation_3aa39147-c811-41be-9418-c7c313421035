import React, { useState, useCallback, createElement } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Box, Collapse, Group, ActionIcon } from '@mantine/core';
import { IconChevronRight, IconChevronDown } from '@tabler/icons-react';

interface CollapsibleSectionProps {
  heading: string;
  level: number;
  children: React.ReactNode;
}

function CollapsibleSection({ heading, level, children }: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(level === 1); // Only top-level headers start expanded
  
  const toggleOpen = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);
  
  return (
    <div className={`collapsible-section level-${level}`}>
      <Group onClick={toggleOpen} className="collapsible-header" style={{ cursor: 'pointer' }}>
        <ActionIcon size="sm" variant="transparent">
          {isOpen ? <IconChevronDown size={16} /> : <IconChevronRight size={16} />}
        </ActionIcon>
        {createElement(`h${level}`, { style: { margin: 0 } }, heading)}
      </Group>
      <Collapse in={isOpen}>
        <Box pl={20} className="collapsible-content">
          {children}
        </Box>
      </Collapse>
    </div>
  );
}

interface CollapsibleMarkdownProps {
  markdown: string;
}

export function CollapsibleMarkdown({ markdown }: CollapsibleMarkdownProps) {
  // Parse the markdown to extract sections with their headings and content
  const sections = parseMarkdownSections(markdown);
  
  // Render the sections recursively
  return (
    <div className="markdown-content">
      {renderSections(sections)}
    </div>
  );
}

interface MarkdownSection {
  heading: string;
  level: number;
  content: string;
  children: MarkdownSection[];
}

function parseMarkdownSections(markdown: string): MarkdownSection[] {
  const lines = markdown.split('\n');
  const rootSections: MarkdownSection[] = [];
  
  let currentSection: MarkdownSection | null = null;
  let currentContent = '';
  let preambleContent = '';
  let foundFirstHeading = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Check if the line is a heading
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
    
    if (headingMatch) {
      foundFirstHeading = true;
      
      // If we were building a section, finalize it
      if (currentSection) {
        currentSection.content = currentContent.trim();
        currentContent = '';
      }
      
      const level = headingMatch[1].length;
      const heading = headingMatch[2];
      
      // Create a new section
      const newSection: MarkdownSection = {
        heading,
        level,
        content: '',
        children: []
      };
      
      // Add the section to the appropriate parent
      if (level === 1 || rootSections.length === 0) {
        rootSections.push(newSection);
      } else {
        // Find the appropriate parent section
        let parent = findParentSection(rootSections, level);
        if (parent) {
          parent.children.push(newSection);
        } else {
          // If no parent found, add to root
          rootSections.push(newSection);
        }
      }
      
      currentSection = newSection;
    } else if (currentSection) {
      // Add the line to the current section's content
      currentContent += line + '\n';
    } else if (!foundFirstHeading) {
      // Content before the first heading
      preambleContent += line + '\n';
    }
  }
  
  // Finalize the last section
  if (currentSection) {
    currentSection.content = currentContent.trim();
  }
  
  // If there's content before the first heading, create a special section for it
  if (preambleContent.trim()) {
    rootSections.unshift({
      heading: 'Introduction',
      level: 1,
      content: preambleContent.trim(),
      children: []
    });
  }
  
  // If no sections were found, create a default one with all content
  if (rootSections.length === 0 && (preambleContent.trim() || currentContent.trim())) {
    rootSections.push({
      heading: 'Content',
      level: 1,
      content: (preambleContent + currentContent).trim(),
      children: []
    });
  }
  
  return rootSections;
}

function findParentSection(sections: MarkdownSection[], level: number): MarkdownSection | null {
  // Start from the last section and work backwards
  for (let i = sections.length - 1; i >= 0; i--) {
    const section = sections[i];
    
    // If this section's level is less than the target level, it's a potential parent
    if (section.level < level) {
      return section;
    }
    
    // Check this section's children
    const childParent = findParentSection(section.children, level);
    if (childParent) {
      return childParent;
    }
  }
  
  return null;
}

function renderSections(sections: MarkdownSection[]): React.ReactNode {
  return sections.map((section, index) => (
    <CollapsibleSection 
      key={`${section.level}-${index}`} 
      heading={section.heading} 
      level={section.level}
    >
      <ReactMarkdown remarkPlugins={[remarkGfm]}>
        {section.content}
      </ReactMarkdown>
      {renderSections(section.children)}
    </CollapsibleSection>
  ));
}
