import { useState, useEffect, FC } from 'react';
import { 
  Text, 
  Button, 
  Group, 
  Card, 
  TextInput, 
  Textarea, 
  Modal, 
  JsonInput,
  Menu,
  Stack,
  Select,
  ActionIcon,
  Tooltip,
  Tabs
} from '@mantine/core';
import { IconTrash, IconEdit, IconCirclePlus, IconCode, IconTree, IconExternalLink, IconPlus } from '@tabler/icons-react';
import { SchemaPropertyTree } from './SchemaPropertyTree';
import { PropertyEditor } from './PropertyEditor';

interface JSONSchema {
  $schema?: string;
  $id?: string;
  title?: string;
  description?: string;
  type?: string | string[];
  properties?: Record<string, JSONSchema>;
  required?: string[];
  definitions?: Record<string, JSONSchema>;
  $ref?: string;
  allOf?: JSONSchema[];
  anyOf?: JSONSchema[];
  oneOf?: JSONSchema[];
  [key: string]: unknown;
}

interface SchemaTypeSelectorProps {
  schema: JSONSchema;
  onChange: (schema: JSONSchema) => void;
}

const SchemaTypeSelector: FC<SchemaTypeSelectorProps> = ({ schema, onChange }) => {
  return (
    <Select
      label="Schema Type"
      placeholder="Select schema type"
      value={schema.type as string || ''}
      data={[
        { value: 'object', label: 'Object' },
        { value: 'array', label: 'Array' },
        { value: 'string', label: 'String' },
        { value: 'number', label: 'Number' },
        { value: 'integer', label: 'Integer' },
        { value: 'boolean', label: 'Boolean' },
        { value: 'null', label: 'Null' }
      ]}
      onChange={(value) => {
        const updatedSchema = { ...schema };
        if (value) {
          updatedSchema.type = value;
        } else {
          delete updatedSchema.type;
        }
        onChange(updatedSchema);
      }}
      mb="md"
    />
  );
};

interface DefinitionsManagerProps {
  schema: JSONSchema;
  onChange: (updatedSchema: JSONSchema) => void;
  initialDefinitionName?: string; // Optional name of definition to edit when component mounts
  createNewDefinition?: boolean; // Flag to automatically open the create definition modal
}

export function DefinitionsManager({ 
  schema, 
  onChange, 
  initialDefinitionName,
  createNewDefinition
}: DefinitionsManagerProps) {
  const [definitions, setDefinitions] = useState<Record<string, JSONSchema>>({});
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedDefinition, setSelectedDefinition] = useState<string | null>(initialDefinitionName || null);
  const [definitionName, setDefinitionName] = useState('');
  const [definitionContent, setDefinitionContent] = useState('{}');
  
  // State for managing deep object editing
  const [isNestedEditModalOpen, setIsNestedEditModalOpen] = useState(false);
  const [nestedObjectPath, setNestedObjectPath] = useState<string | null>(null);
  const [nestedObjectSchema, setNestedObjectSchema] = useState<JSONSchema>({});
  const [nestedObjectTitle, setNestedObjectTitle] = useState('');
  
  // Handle initialDefinitionName changes
  useEffect(() => {
    if (initialDefinitionName && schema.definitions && schema.definitions[initialDefinitionName]) {
      setSelectedDefinition(initialDefinitionName);
      setDefinitionName(initialDefinitionName);
      setDefinitionContent(JSON.stringify(schema.definitions[initialDefinitionName] || {}, null, 2));
      setIsEditModalOpen(true);
    }
  }, [initialDefinitionName, schema.definitions]);

  // Handle createNewDefinition flag
  useEffect(() => {
    if (createNewDefinition) {
      setDefinitionName('');
      setDefinitionContent('{}');
      setIsCreateModalOpen(true);
    }
  }, [createNewDefinition]);

  // Initialize definitions from schema
  useEffect(() => {
    if (schema && schema.definitions) {
      setDefinitions(schema.definitions);
    } else {
      setDefinitions({});
    }
  }, [schema]);

  // Handle edit definition
  const handleEditDefinition = () => {
    if (!selectedDefinition || !definitionName.trim()) return;

    try {
      const content = JSON.parse(definitionContent);
      console.log('content', content);
      
      // Create a completely new schema object instead of modifying the existing one
      // This avoids issues with read-only properties
      const newDefinitions = { ...(schema.definitions || {}) };
      
      // If name changed, remove old definition
      if (selectedDefinition !== definitionName) {
        delete newDefinitions[selectedDefinition];
      }
      
      // Update the definition with the new content
      newDefinitions[definitionName] = JSON.parse(JSON.stringify(content));
      
      // Create a completely new schema object
      const updatedSchema: JSONSchema = {
        ...JSON.parse(JSON.stringify(schema)),
        definitions: newDefinitions
      };
      
      // Delete and recreate the definitions property to avoid modifying any read-only objects
      delete updatedSchema.definitions;
      updatedSchema.definitions = newDefinitions;
      
      // Update schema
      onChange(updatedSchema);
      
      // Reset form
      setSelectedDefinition(null);
      setDefinitionName('');
      setDefinitionContent('{}');
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error editing definition:', error);
      alert('Invalid JSON content');
    }
  };

  // Handle create definition
  const handleCreateDefinition = () => {
    if (!definitionName.trim()) return;

    try {
      const content = JSON.parse(definitionContent);
      console.log('content', content);
      
      // Create a completely new schema object instead of modifying the existing one
      // This avoids issues with read-only properties
      const newDefinitions = { ...(schema.definitions || {}) };
      
      // Add the new definition
      newDefinitions[definitionName] = JSON.parse(JSON.stringify(content));
      
      // Create a completely new schema object
      const updatedSchema: JSONSchema = {
        ...JSON.parse(JSON.stringify(schema)),
        definitions: newDefinitions
      };
      
      // Delete and recreate the definitions property to avoid modifying any read-only objects
      delete updatedSchema.definitions;
      updatedSchema.definitions = newDefinitions;
      
      // Update schema
      onChange(updatedSchema);
      
      // Reset form
      setDefinitionName('');
      setDefinitionContent('{}');
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error('Error creating definition:', error);
      alert('Invalid JSON content');
    }
  };

  // Handle delete definition
  const handleDeleteDefinition = () => {
    if (!selectedDefinition) return;

    // Clone schema
    const updatedSchema = { ...schema };
    
    // Ensure definitions exists
    if (!updatedSchema.definitions) {
      return;
    }
    
    // Delete definition
    delete updatedSchema.definitions[selectedDefinition];
    
    // If definitions is empty, remove it
    if (Object.keys(updatedSchema.definitions).length === 0) {
      delete updatedSchema.definitions;
    }
    
    // Update schema
    onChange(updatedSchema);
    
    // Reset form
    setSelectedDefinition(null);
    setIsDeleteModalOpen(false);
  };

  // Open edit modal for a definition
  const openEditModal = (name: string) => {
    setSelectedDefinition(name);
    setDefinitionName(name);
    setDefinitionContent(JSON.stringify(definitions[name] || {}, null, 2));
    setIsEditModalOpen(true);
  };

  // Open delete modal for a definition
  const openDeleteModal = (name: string) => {
    setSelectedDefinition(name);
    setIsDeleteModalOpen(true);
  };

  // State for managing the editing mode
  const [editMode, setEditMode] = useState<'visual' | 'json'>('visual');
  
  // State for managing the selected property path in the visual editor
  const [selectedPropertyPath, setSelectedPropertyPath] = useState<string | null>(null);
  
  // State for the current definition being edited as a schema object
  const [currentDefinitionSchema, setCurrentDefinitionSchema] = useState<JSONSchema>({});
  
  // Parse definition content to schema object
  useEffect(() => {
    try {
      const schema = JSON.parse(definitionContent) as JSONSchema;
      setCurrentDefinitionSchema(schema);
    } catch (e) {
      // If parsing fails, initialize with an empty object
      setCurrentDefinitionSchema({});
    }
  }, [definitionContent]);
  
  // Update JSON content when the schema object changes
  const updateJsonFromSchema = (updatedSchema: JSONSchema) => {
    const jsonString = JSON.stringify(updatedSchema, null, 2);
    setDefinitionContent(jsonString);
    setCurrentDefinitionSchema(updatedSchema);
  };
  
  // Handle property updates from the visual editor
  const handleUpdateProperty = (path: string, updates: any) => {
    // Create a deep clone of the current schema
    const updatedSchema = JSON.parse(JSON.stringify(currentDefinitionSchema)) as JSONSchema;
    
    // Get the property to update
    const pathParts = path.split('.');
    let current = updatedSchema as Record<string, any>;
    
    // Navigate to the parent of the property to update
    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]] as Record<string, any>;
      if (!current) return; // Path not found
    }
    
    // Update the property
    const lastPart = pathParts[pathParts.length - 1];
    if (updates.updates) {
      current[lastPart] = updates.updates;
    }
    
    // Update the JSON content
    updateJsonFromSchema(updatedSchema);
  };
  
  // Handle adding a property from the visual editor
  const handleAddProperty = (path: string, property: { name: string; property: JSONSchema; required?: boolean }) => {
    // Create a deep clone of the current schema
    const updatedSchema = JSON.parse(JSON.stringify(currentDefinitionSchema)) as JSONSchema;
    
    if (path === 'root') {
      // Add to root level
      if (!updatedSchema.properties) {
        updatedSchema.properties = {};
      }
      updatedSchema.properties[property.name] = property.property;
      
      // Handle required properties
      if (property.required) {
        if (!updatedSchema.required) {
          updatedSchema.required = [];
        }
        updatedSchema.required.push(property.name);
      }
    } else {
      // Add to nested path
      const pathParts = path.split('.');
      let current = updatedSchema as Record<string, any>;
      
      // Navigate to the path
      for (const part of pathParts) {
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part] as Record<string, any>;
      }
      
      // Add the property
      if (!current.properties) {
        current.properties = {};
      }
      current.properties[property.name] = property.property;
      
      // Handle required properties
      if (property.required) {
        if (!current.required) {
          current.required = [];
        }
        current.required.push(property.name);
      }
    }
    
    // Update the JSON content
    updateJsonFromSchema(updatedSchema);
  };
  
  // Helper function to get a nested object from a path
  const getNestedObject = (obj: Record<string, any>, path: string): any => {
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (!current[part]) {
        return {};
      }
      current = current[part];
    }
    
    return current;
  };
  
  // Helper function to set a nested object at a path
  const setNestedObject = (obj: Record<string, any>, path: string, value: any): Record<string, any> => {
    const parts = path.split('.');
    const result = { ...obj };
    let current = result;
    
    // Navigate to the parent object
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!current[part] || typeof current[part] !== 'object') {
        current[part] = {};
      }
      current = current[part];
    }
    
    // Set the value at the last part
    current[parts[parts.length - 1]] = value;
    
    return result;
  };
  
  // Function to open the nested object editor
  const openNestedObjectEditor = (path: string, title: string) => {
    // Get the object at the path
    const nestedObject = getNestedObject(currentDefinitionSchema, path);
    
    setNestedObjectPath(path);
    setNestedObjectSchema(nestedObject);
    setNestedObjectTitle(title);
    setIsNestedEditModalOpen(true);
  };

  // Function to get reference options from schema definitions
  const getDefinitionOptions = (): { value: string; label: string }[] => {
    if (!schema.definitions) return [];
    
    return Object.keys(schema.definitions).map(defName => ({
      value: `#/definitions/${defName}`,
      label: defName
    }));
  };
  
  // Function to save changes from the nested object editor
  const saveNestedObjectChanges = () => {
    if (!nestedObjectPath) return;
    
    // Update the schema with the edited nested object
    const updatedSchema = setNestedObject(
      JSON.parse(JSON.stringify(currentDefinitionSchema)),
      nestedObjectPath,
      nestedObjectSchema
    );
    
    // Update the current definition schema
    updateJsonFromSchema(updatedSchema);
    
    // Close the modal
    setIsNestedEditModalOpen(false);
    setNestedObjectPath(null);
    setNestedObjectSchema({});
    setNestedObjectTitle('');
  };
  
  // Enhanced SchemaPropertyTree renderer with support for deep object editing
  const renderSchemaPropertyTree = (schema: JSONSchema, selectedProperty: string | null, onSelectProperty: (path: string) => void) => {
    return (
      <div>
        <SchemaPropertyTree
          schema={schema}
          selectedProperty={selectedProperty}
          onSelectProperty={onSelectProperty}
          onAddProperty={(path) => {
            // Set the path for adding a new property
            onSelectProperty(path);
          }}
          onDeleteProperty={(path) => {
            // Handle property deletion
            const updatedSchema = JSON.parse(JSON.stringify(schema)) as JSONSchema;
            
            // Get the property to delete
            const pathParts = path.split('.');
            let current = updatedSchema as Record<string, any>;
            
            // Navigate to the parent of the property to delete
            for (let i = 0; i < pathParts.length - 1; i++) {
              current = current[pathParts[i]] as Record<string, any>;
              if (!current) return; // Path not found
            }
            
            // Delete the property
            const lastPart = pathParts[pathParts.length - 1];
            delete current[lastPart];
            
            // Update the JSON content
            updateJsonFromSchema(updatedSchema);
            
            // Clear selection if deleted property was selected
            if (selectedProperty === path) {
              onSelectProperty('');
            }
          }}
          renderObjectActions={(path: string, property: any) => {
            // Show edit button for complex objects and arrays at deeper levels
            if (path.split('.').length > 2 && (
              property.type === 'object' || 
              (property.type === 'array' && property.items && property.items.type === 'object') ||
              // Handle array items with object properties
              path.includes('.items.') || 
              path.includes('.items') ||
              // Handle references
              property.$ref !== undefined
            )) {
              return (
                <Tooltip label="Edit in modal">
                  <ActionIcon
                    size="xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      const propertyName = path.split('.').pop() || '';
                      openNestedObjectEditor(path, propertyName);
                    }}
                  >
                    <IconExternalLink size={14} />
                  </ActionIcon>
                </Tooltip>
              );
            }
            return null;
          }}
        />
      </div>
    );
  };
  
  // Render create definition modal
  const renderCreateModal = () => (
    <Modal
      opened={isCreateModalOpen}
      onClose={() => setIsCreateModalOpen(false)}
      title="Create Definition"
      size="xl"
      styles={{ body: { maxHeight: 'calc(90vh - 130px)', overflowY: 'auto' } }}
    >
      <TextInput
        label="Definition Name"
        placeholder="Enter definition name"
        value={definitionName}
        onChange={(e) => setDefinitionName(e.target.value)}
        required
        mb="md"
      />
      
      <Card shadow="xs" withBorder mb="md">
        <Tabs value={editMode} onChange={(value) => setEditMode(value as 'visual' | 'json')}>
          <Tabs.List>
            <Tabs.Tab value="visual" leftSection={<IconTree size={16} />}>
              Visual Editor
            </Tabs.Tab>
            <Tabs.Tab value="json" leftSection={<IconCode size={16} />}>
              JSON Editor
            </Tabs.Tab>
          </Tabs.List>
          
          <Tabs.Panel value="visual" pt="md">
            <SchemaTypeSelector 
              schema={currentDefinitionSchema} 
              onChange={updateJsonFromSchema} 
            />
            <Group style={{ alignItems: 'flex-start' }}>
              <div style={{ flex: 1 }}>
                {renderSchemaPropertyTree(
                  currentDefinitionSchema,
                  selectedPropertyPath,
                  setSelectedPropertyPath
                )}
              </div>
              <div style={{ flex: 1 }}>
                <PropertyEditor
                  schema={currentDefinitionSchema}
                  propertyPath={selectedPropertyPath}
                  onUpdateProperty={handleUpdateProperty}
                  onAddProperty={handleAddProperty}
                />
              </div>
            </Group>
          </Tabs.Panel>
          
          <Tabs.Panel value="json" pt="md">
            <JsonInput
              value={definitionContent}
              onChange={(value) => {
                setDefinitionContent(value);
                try {
                  setCurrentDefinitionSchema(JSON.parse(value) as JSONSchema);
                } catch (e) {
                  // Handle invalid JSON
                }
              }}
              minRows={20}
              formatOnBlur
              mb="md"
              styles={{ input: { minHeight: '400px' } }}
            />
          </Tabs.Panel>
        </Tabs>
      </Card>
      
      <Group justify="flex-end">
        <Button variant="light" onClick={() => setIsCreateModalOpen(false)}>Cancel</Button>
        <Button onClick={handleCreateDefinition}>Create</Button>
      </Group>
    </Modal>
  );

  // Render edit definition modal
  const renderEditModal = () => (
    <Modal
      opened={isEditModalOpen}
      onClose={() => setIsEditModalOpen(false)}
      title="Edit Definition"
      size="xl"
      styles={{ body: { maxHeight: 'calc(90vh - 130px)', overflowY: 'auto' } }}
    >
      <TextInput
        label="Definition Name"
        placeholder="Enter definition name"
        value={definitionName}
        onChange={(e) => setDefinitionName(e.target.value)}
        required
        mb="md"
      />
      
      <Card shadow="xs" withBorder mb="md">
        <Tabs value={editMode} onChange={(value) => setEditMode(value as 'visual' | 'json')}>
          <Tabs.List>
            <Tabs.Tab value="visual" leftSection={<IconTree size={16} />}>
              Visual Editor
            </Tabs.Tab>
            <Tabs.Tab value="json" leftSection={<IconCode size={16} />}>
              JSON Editor
            </Tabs.Tab>
          </Tabs.List>
          
          <Tabs.Panel value="visual" pt="md">
            <SchemaTypeSelector 
              schema={currentDefinitionSchema} 
              onChange={updateJsonFromSchema} 
            />
            <Group style={{ alignItems: 'flex-start' }}>
              <div style={{ flex: 1 }}>
                {renderSchemaPropertyTree(
                  currentDefinitionSchema,
                  selectedPropertyPath,
                  setSelectedPropertyPath
                )}
              </div>
              <div style={{ flex: 1 }}>
                <PropertyEditor
                  schema={currentDefinitionSchema}
                  propertyPath={selectedPropertyPath}
                  onUpdateProperty={handleUpdateProperty}
                  onAddProperty={handleAddProperty}
                />
              </div>
            </Group>
          </Tabs.Panel>
          
          <Tabs.Panel value="json" pt="md">
            <JsonInput
              value={definitionContent}
              onChange={(value) => {
                setDefinitionContent(value);
                try {
                  setCurrentDefinitionSchema(JSON.parse(value) as JSONSchema);
                } catch (e) {
                  // Handle invalid JSON
                }
              }}
              minRows={20}
              formatOnBlur
              mb="md"
              styles={{ input: { minHeight: '400px' } }}
            />
          </Tabs.Panel>
        </Tabs>
      </Card>
      
      <Group justify="flex-end">
        <Button variant="light" onClick={() => setIsEditModalOpen(false)}>Cancel</Button>
        <Button onClick={handleEditDefinition}>Save Changes</Button>
      </Group>
    </Modal>
  );

  // Render delete definition modal
  const renderDeleteModal = () => (
    <Modal
      opened={isDeleteModalOpen}
      onClose={() => setIsDeleteModalOpen(false)}
      title="Delete Definition"
    >
      <Text>
        Are you sure you want to delete the definition "{selectedDefinition}"? 
        This may break references to this definition in your schema.
      </Text>
      
      <Group justify="flex-end" mt="md">
        <Button variant="light" onClick={() => setIsDeleteModalOpen(false)}>Cancel</Button>
        <Button color="red" onClick={handleDeleteDefinition}>Delete</Button>
      </Group>
    </Modal>
  );

  // Render nested object editor modal
  const renderNestedObjectEditorModal = () => (
    <Modal
      opened={isNestedEditModalOpen}
      onClose={() => setIsNestedEditModalOpen(false)}
      title={`Edit ${nestedObjectTitle}`}
      size="xl"
      styles={{ body: { maxHeight: 'calc(90vh - 130px)', overflowY: 'auto' } }}
    >
      <Card shadow="xs" withBorder mb="md">
        <Tabs defaultValue="visual">
          <Tabs.List>
            <Tabs.Tab value="visual" leftSection={<IconTree size={16} />}>
              Visual Editor
            </Tabs.Tab>
            <Tabs.Tab value="json" leftSection={<IconCode size={16} />}>
              JSON Editor
            </Tabs.Tab>
          </Tabs.List>
          
          <Tabs.Panel value="visual" pt="md">
            {/* Handle reference type */}
            {nestedObjectSchema.$ref ? (
              <Card withBorder mb="md" p="md">
                <Text fw={500} mb="xs">Reference Type</Text>
                <Select
                  label="Reference To Definition"
                  value={nestedObjectSchema.$ref}
                  data={getDefinitionOptions()}
                  onChange={(value) => {
                    if (value) {
                      setNestedObjectSchema({ $ref: value });
                    }
                  }}
                  mb="md"
                />
                <Text size="sm" color="dimmed">
                  This is a reference to another schema definition. 
                  Select a different definition or switch to object type to edit inline.
                </Text>
                <Button
                  variant="light"
                  size="sm"
                  onClick={() => {
                    // Convert from reference to object
                    setNestedObjectSchema({ type: 'object', properties: {} });
                  }}
                  mt="sm"
                >
                  Convert to Object Type
                </Button>
              </Card>
            ) : (
              <>
                <SchemaTypeSelector 
                  schema={nestedObjectSchema} 
                  onChange={setNestedObjectSchema} 
                />
              
                {/* Add reference option */}
                <Card withBorder mb="md" p="md">
                  <Text fw={500} mb="xs">Use Reference Instead</Text>
                  <Select
                    label="Reference To Definition"
                    placeholder="Select a definition to reference"
                    data={getDefinitionOptions()}
                    onChange={(value) => {
                      if (value) {
                        setNestedObjectSchema({ $ref: value });
                      }
                    }}
                    mb="xs"
                    clearable
                  />
                </Card>
                
                <Group style={{ alignItems: 'flex-start' }}>
                  <div style={{ flex: 1 }}>
                    <SchemaPropertyTree
                      schema={nestedObjectSchema}
                      selectedProperty={null}
                      onSelectProperty={() => {}}
                      onAddProperty={(path) => {
                        // Handle adding a property to the nested object
                        const newProperty = { name: "newProperty", property: { type: "string" } };
                        if (path === 'root') {
                          // Add to root object's properties
                          const updatedSchema = { ...nestedObjectSchema };
                          if (!updatedSchema.properties) {
                            updatedSchema.properties = {};
                          }
                          updatedSchema.properties[newProperty.name] = newProperty.property;
                          setNestedObjectSchema(updatedSchema);
                        } else if (path.startsWith('items') || path.includes('.items')) {
                          // Handle adding properties to array items
                          const updatedSchema = { ...nestedObjectSchema };
                          if (!updatedSchema.items) {
                            updatedSchema.items = { type: 'object', properties: {} } as JSONSchema;
                          }
                          
                          // Create a typed reference to items
                          const itemsSchema = updatedSchema.items as JSONSchema;
                          if (!itemsSchema.properties) {
                            itemsSchema.properties = {};
                          }
                          itemsSchema.properties[newProperty.name] = newProperty.property;
                          setNestedObjectSchema(updatedSchema);
                        }
                      }}
                      onDeleteProperty={(path) => {
                        const updatedSchema = JSON.parse(JSON.stringify(nestedObjectSchema)) as JSONSchema;
                        const pathParts = path.split('.');
                        let current = updatedSchema as Record<string, any>;
                        
                        for (let i = 0; i < pathParts.length - 1; i++) {
                          current = current[pathParts[i]] as Record<string, any>;
                          if (!current) return;
                        }
                        
                        const lastPart = pathParts[pathParts.length - 1];
                        delete current[lastPart];
                        
                        setNestedObjectSchema(updatedSchema);
                      }}
                    />
                  </div>
                  <div style={{ flex: 1 }}>
                    <Text fw={500} mb="md">Add Properties</Text>
                    <Button
                      size="sm"
                      leftSection={<IconPlus size={16} />}
                      onClick={() => {
                        // Add a string property to the root or items based on type
                        if (nestedObjectSchema.type === 'object') {
                          const updatedSchema = { ...nestedObjectSchema };
                          if (!updatedSchema.properties) {
                            updatedSchema.properties = {};
                          }
                          updatedSchema.properties["newProperty"] = { type: "string" };
                          setNestedObjectSchema(updatedSchema);
                        } else if (nestedObjectSchema.type === 'array') {
                          const updatedSchema = { ...nestedObjectSchema };
                          if (!updatedSchema.items) {
                            updatedSchema.items = { type: 'object', properties: {} } as JSONSchema;
                          }
                          
                          // Create a typed reference to items
                          const itemsSchema = updatedSchema.items as JSONSchema;
                          if (itemsSchema.type === 'object') {
                            if (!itemsSchema.properties) {
                              itemsSchema.properties = {};
                            }
                            itemsSchema.properties["newProperty"] = { type: "string" };
                          }
                          setNestedObjectSchema(updatedSchema);
                        }
                      }}
                    >
                      Add Property
                    </Button>
                    
                    {nestedObjectSchema.type === 'array' && (
                      <Card withBorder mt="md" p="sm">
                        <Text fw={500} mb="xs">Array Items</Text>
                        <SchemaTypeSelector 
                          schema={nestedObjectSchema.items as JSONSchema || { type: 'object' } as JSONSchema} 
                          onChange={(itemsSchema) => {
                            const updatedSchema = { ...nestedObjectSchema, items: itemsSchema };
                            setNestedObjectSchema(updatedSchema);
                          }} 
                        />
                        
                        {/* Items reference option */}
                        <Text fw={500} size="sm" mt="md" mb="xs">Items Reference</Text>
                        <Select
                          placeholder="Reference a definition for items"
                          data={getDefinitionOptions()}
                          value={(nestedObjectSchema.items as JSONSchema)?.$ref}
                          onChange={(value) => {
                            if (value) {
                              const updatedSchema = { ...nestedObjectSchema, items: { $ref: value } };
                              setNestedObjectSchema(updatedSchema);
                            }
                          }}
                          clearable
                        />
                      </Card>
                    )}
                  </div>
                </Group>
              </>
            )}
          </Tabs.Panel>
          
          <Tabs.Panel value="json" pt="md">
            <JsonInput
              value={JSON.stringify(nestedObjectSchema, null, 2)}
              onChange={(value) => {
                try {
                  setNestedObjectSchema(JSON.parse(value) as JSONSchema);
                } catch (e) {
                  // Handle invalid JSON
                }
              }}
              minRows={15}
              formatOnBlur
              mb="md"
              styles={{ input: { minHeight: '300px' } }}
            />
          </Tabs.Panel>
        </Tabs>
      </Card>
      
      <Group justify="flex-end">
        <Button variant="light" onClick={() => setIsNestedEditModalOpen(false)}>Cancel</Button>
        <Button onClick={saveNestedObjectChanges}>Save Changes</Button>
      </Group>
    </Modal>
  );

  return (
    <div>
      <Group justify="space-between" mb="md">
        <Text fw={500}>Schema Definitions</Text>
        <Button 
          size="xs" 
          leftSection={<IconCirclePlus size={16} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Add Definition
        </Button>
      </Group>
      
      {Object.keys(definitions).length === 0 ? (
        <Text color="dimmed" size="sm">No definitions in this schema</Text>
      ) : (
        <Stack gap="xs">
          {Object.entries(definitions).map(([name, content]) => (
            <Card key={name} withBorder padding="xs">
              <Group justify="space-between">
                <Text size="sm" fw={500}>{name}</Text>
                <Group gap="xs">
                  <Tooltip label="Edit">
                    <ActionIcon size="sm" color="blue" onClick={() => openEditModal(name)}>
                      <IconEdit size={16} />
                    </ActionIcon>
                  </Tooltip>
                  <Tooltip label="Delete">
                    <ActionIcon size="sm" color="red" onClick={() => openDeleteModal(name)}>
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Group>
              </Group>
            </Card>
          ))}
        </Stack>
      )}
      
      {renderCreateModal()}
      {renderEditModal()}
      {renderDeleteModal()}
      {renderNestedObjectEditorModal()}
    </div>
  );
}
