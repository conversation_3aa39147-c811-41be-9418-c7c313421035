import React, { useState } from 'react';
import { TextInput, Select, Button, Group } from '@mantine/core';
import { AddChildPropertyFormProps } from './types';

export const AddChildPropertyForm: React.FC<AddChildPropertyFormProps> = ({
  onAdd,
  onCancel
}) => {
  const [name, setName] = useState('');
  const [type, setType] = useState('string');
  
  return (
    <Group align="flex-end" gap="xs">
      <TextInput
        label="Name"
        value={name}
        onChange={e => setName(e.target.value)}
        placeholder="propertyName"
        size="xs"
      />
      <Select
        label="Type"
        value={type}
        onChange={v => setType(v || 'string')}
        data={[
          { value: 'string', label: 'String' },
          { value: 'number', label: 'Number' },
          { value: 'integer', label: 'Integer' },
          { value: 'boolean', label: 'Boolean' },
          { value: 'object', label: 'Object' },
          { value: 'array', label: 'Array' }
        ]}
        size="xs"
      />
      <Button
        size="xs"
        onClick={() => {
          if (name && type) {
            onAdd(name, type);
            setName('');
            setType('string');
          }
        }}
      >
        Add
      </Button>
      {onCancel && (
        <Button size="xs" variant="light" color="gray" onClick={onCancel}>
          Cancel
        </Button>
      )}
    </Group>
  );
};
