import React from 'react';
import { Select } from '@mantine/core';
import { updatePropertyField } from '../../store/schemaSlice';
import { CommonEditorProps } from './types';

interface BooleanPropertyEditorProps extends CommonEditorProps {
  propertyDefault: string;
  setPropertyDefault: (value: string) => void;
}

export const BooleanPropertyEditor: React.FC<BooleanPropertyEditorProps> = ({
  propertyPath,
  isNewProperty,
  dispatch,
  propertyDefault,
  setPropertyDefault
}) => {
  return (
    <Select
      label="Default Value"
      placeholder="Select default value"
      data={[
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' }
      ]}
      value={propertyDefault}
      onChange={(value) => {
        setPropertyDefault(value || '');
        if (propertyPath && !isNewProperty) {
          const boolValue = value === 'true' ? true : value === 'false' ? false : undefined;
          dispatch(updatePropertyField({
            path: propertyPath,
            field: 'default',
            value: boolValue
          }));
        }
      }}
      mb="md"
      clearable
    />
  );
};
