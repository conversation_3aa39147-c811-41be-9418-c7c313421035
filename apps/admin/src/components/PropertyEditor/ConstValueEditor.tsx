import React from 'react';
import { 
  Checkbox, 
  TextInput, 
  NumberInput, 
  Select, 
  Box, 
  Text 
} from '@mantine/core';
import { updatePropertyField } from '../../store/schemaSlice';
import { CommonEditorProps } from './types';

interface ConstValueEditorProps extends CommonEditorProps {
  propertyType: string;
  useConst: boolean;
  setUseConst: (value: boolean) => void;
  propertyConst: any;
  setPropertyConst: (value: any) => void;
}

export const ConstValueEditor: React.FC<ConstValueEditorProps> = ({
  propertyPath,
  isNewProperty,
  dispatch,
  propertyType,
  useConst,
  setUseConst,
  propertyConst,
  setPropertyConst
}) => {
  if (propertyType === 'reference' || propertyType === 'object' || propertyType === 'array') {
    return null;
  }

  return (
    <>
      <Checkbox
        label="Use fixed value (const)"
        checked={useConst}
        onChange={(e) => {
          const isChecked = e.currentTarget.checked;
          setUseConst(isChecked);
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'useConst',
              value: isChecked
            }));
          }
        }}
        mb="md"
      />
      
      {useConst && (
        <Box mb="md">
          {propertyType === 'string' && (
            <TextInput
              label="Constant Value"
              placeholder="Fixed string value"
              value={propertyConst || ''}
              onChange={(e) => {
                const newValue = e.target.value;
                setPropertyConst(newValue);
                if (propertyPath && !isNewProperty) {
                  dispatch(updatePropertyField({
                    path: propertyPath,
                    field: 'propertyConst',
                    value: newValue
                  }));
                }
              }}
            />
          )}
          
          {(propertyType === 'number' || propertyType === 'integer') && (
            <NumberInput
              label="Constant Value"
              placeholder="Fixed number value"
              value={propertyConst !== undefined ? Number(propertyConst) : undefined}
              onChange={(value) => {
                const numValue = value !== '' ? Number(value) : undefined;
                setPropertyConst(numValue);
                if (propertyPath && !isNewProperty) {
                  dispatch(updatePropertyField({
                    path: propertyPath,
                    field: 'propertyConst',
                    value: numValue
                  }));
                }
              }}
            />
          )}
          
          {propertyType === 'boolean' && (
            <Select
              label="Constant Value"
              placeholder="Fixed boolean value"
              data={[
                { value: 'true', label: 'True' },
                { value: 'false', label: 'False' }
              ]}
              value={propertyConst === true ? 'true' : propertyConst === false ? 'false' : undefined}
              onChange={(value) => {
                const boolValue = value === 'true' ? true : value === 'false' ? false : undefined;
                setPropertyConst(boolValue);
                if (propertyPath && !isNewProperty) {
                  dispatch(updatePropertyField({
                    path: propertyPath,
                    field: 'propertyConst',
                    value: boolValue
                  }));
                }
              }}
            />
          )}
          
          <Text size="xs" color="dimmed" mt="xs">
            Using a constant value will make this property accept only the specified value.
          </Text>
        </Box>
      )}
    </>
  );
};
