import React from 'react';
import { 
  NumberInput, 
  TextInput, 
  Divider, 
  Group, 
  Box, 
  Button,
  Text
} from '@mantine/core';
import { updatePropertyField } from '../../store/schemaSlice';
import { CommonEditorProps } from './types';

interface NumberPropertyEditorProps extends CommonEditorProps {
  propertyEnum: string[];
  setPropertyEnum: (value: string[]) => void;
  propertyDefault: string;
  setPropertyDefault: (value: string) => void;
  propertyMinimum: number | undefined;
  setPropertyMinimum: (value: number | undefined) => void;
  propertyMaximum: number | undefined;
  setPropertyMaximum: (value: number | undefined) => void;
}

export const NumberPropertyEditor: React.FC<NumberPropertyEditorProps> = ({
  propertyPath,
  property,
  isNewProperty,
  dispatch,
  onUpdateProperty,
  propertyEnum,
  setPropertyEnum,
  propertyDefault,
  setPropertyDefault,
  propertyMinimum,
  setPropertyMinimum,
  propertyMaximum,
  setPropertyMaximum
}) => {
  
  // Creates an updates object based on current form state
  const createUpdatesObject = () => {
    if (!property) return null;
    return { ...property };
  };
  
  // Handle add enum value
  const handleAddEnumValue = (value: string) => {
    if (value && !propertyEnum.includes(value)) {
      const newEnum = [...propertyEnum, value];
      setPropertyEnum(newEnum);
      if (propertyPath) {
        dispatch(updatePropertyField({
          path: propertyPath,
          field: 'propertyEnum',
          value: newEnum
        }));
      }
    }
  };

  // Handle remove enum value
  const handleRemoveEnumValue = (value: string) => {
    const newEnum = propertyEnum.filter(v => v !== value);
    setPropertyEnum(newEnum);
    if (propertyPath) {
      dispatch(updatePropertyField({
        path: propertyPath,
        field: 'propertyEnum',
        value: newEnum
      }));
    }
  };

  return (
    <>
      <NumberInput
        label="Minimum"
        placeholder="Minimum value"
        value={propertyMinimum}
        onChange={(value) => {
          const numValue = value !== '' ? Number(value) : undefined;
          setPropertyMinimum(numValue);
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'minimum',
              value: numValue
            }));
          }
        }}
        mb="md"
      />
      
      <NumberInput
        label="Maximum"
        placeholder="Maximum value"
        value={propertyMaximum}
        onChange={(value) => {
          const numValue = value !== '' ? Number(value) : undefined;
          setPropertyMaximum(numValue);
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'maximum',
              value: numValue
            }));
          }
        }}
        mb="md"
      />
      
      <TextInput
        label="Default Value"
        placeholder="Default value"
        value={propertyDefault}
        onChange={(e) => {
          setPropertyDefault(e.target.value);
          if (propertyPath && !isNewProperty) {
            const defaultValue = e.target.value !== '' ? Number(e.target.value) : undefined;
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'default',
              value: defaultValue
            }));
          }
        }}
        mb="md"
      />
      
      <Divider my="md" label="Enum Values" labelPosition="center" />
      
      <Group mb="md">
        <TextInput
          type="number"
          placeholder="Add enum value"
          style={{ flex: 1 }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && (e.target as HTMLInputElement).value) {
              handleAddEnumValue((e.target as HTMLInputElement).value);
              (e.target as HTMLInputElement).value = '';
            }
          }}
        />
        <Button
          onClick={(e) => {
            const input = e.currentTarget.previousElementSibling as HTMLInputElement;
            if (input.value) {
              handleAddEnumValue(input.value);
              input.value = '';
            }
          }}
        >
          Add
        </Button>
      </Group>
      
      {propertyEnum.length > 0 && (
        <Box mb="md">
          {propertyEnum.map((value, index) => (
            <Button
              key={index}
              size="xs"
              variant="light"
              rightSection="×"
              onClick={() => handleRemoveEnumValue(value)}
              mb="xs"
              mr="xs"
            >
              {value}
            </Button>
          ))}
        </Box>
      )}
    </>
  );
};
