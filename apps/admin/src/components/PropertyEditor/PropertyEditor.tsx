import { useState, useEffect, useMemo } from 'react';
import { useGetSchemasQuery } from '../../store/api';
import { useSchemaEditor } from '../../hooks/useSchemaEditor';
import { useDispatch } from 'react-redux';
import { renameProperty } from '../../store/schemaSlice';
import { 
  TextInput,
  Textarea,
  Select,
  Checkbox,
  Button,
  Group,
  Text,
  Card,
  Divider,
  Box,
  Stack,
  ActionIcon,
  Tooltip
} from '@mantine/core';

import { 
  PropertyEditorProps,
  AddChildPropertyForm, 
  StringPropertyEditor,
  NumberPropertyEditor,
  BooleanPropertyEditor,
  ReferencePropertyEditor,
  ResourceReferenceEditor,
  ConstValueEditor,
  findPropertyByPath,
  getPropertyNameFromPath,
  getParentPath,
  isPropertyRequired,
  createUpdatesObject
} from './index';

export function PropertyEditor({ 
  schema, 
  propertyPath, 
  onUpdateProperty,
  onAddProperty,
  onEditDefinition,
  onCreateDefinition,
  onRenameProperty
}: PropertyEditorProps) {
  // We'll use the schema prop directly rather than accessing Redux
  // This component receives changes through props
  const dispatch = useDispatch();
  
  // Add state for showing the Add Child Property form
  const [showAddChildForm, setShowAddChildForm] = useState(false);

  const [property, setProperty] = useState<any>(null);
  const [propertyName, setPropertyName] = useState('');
  const [propertyType, setPropertyType] = useState('string');
  const [propertyDescription, setPropertyDescription] = useState('');
  // This state will be controlled by the schema prop through useEffect
  const [propertyRequired, setPropertyRequired] = useState(false); 
  const [propertyFormat, setPropertyFormat] = useState('');
  const [propertyEnum, setPropertyEnum] = useState<string[]>([]);
  const [enumInputValue, setEnumInputValue] = useState('');
  const [propertyDefault, setPropertyDefault] = useState('');
  const [propertyMinLength, setPropertyMinLength] = useState<number | undefined>(undefined);
  const [propertyMaxLength, setPropertyMaxLength] = useState<number | undefined>(undefined);
  const [propertyMinimum, setPropertyMinimum] = useState<number | undefined>(undefined);
  const [propertyMaximum, setPropertyMaximum] = useState<number | undefined>(undefined);
  const [propertyPattern, setPropertyPattern] = useState('');
  const [propertyConst, setPropertyConst] = useState<any>(undefined);
  const [useConst, setUseConst] = useState(false);
  const [propertyRef, setPropertyRef] = useState('');
  const [referenceType, setReferenceType] = useState('internal'); // internal, uuid, url
  const [isNewProperty, setIsNewProperty] = useState(false);
  const [parentPath, setParentPath] = useState('');
  const [resourceTypeConst, setResourceTypeConst] = useState('Patient'); // For Resource Reference type
  
  // Fetch available schemas for UUID references
  const { data: availableSchemas, isLoading: schemasLoading } = useGetSchemasQuery(undefined);
  
  // Prepare schema options for dropdown
  const schemaOptions = useMemo(() => {
    if (!availableSchemas) return [];
    
    return availableSchemas.map((schema: any) => ({
      value: schema.id,
      label: `${schema.name} v${schema.version}`
    }));
  }, [availableSchemas]);

  // Load property data
  useEffect(() => {
    if (!propertyPath) {
      resetForm();
      return;
    }
    
    if (propertyPath === 'root') {
      setIsNewProperty(true);
      setParentPath('root');
      return;
    }
    
    // Handle array items path specifically
    if (propertyPath.endsWith('.items')) {
      const arrayPath = propertyPath.substring(0, propertyPath.length - 6); // Remove .items
      const arrayProp = findPropertyByPath(schema, arrayPath);
      
      if (arrayProp && arrayProp.items) {
        setProperty(arrayProp.items);
        setIsNewProperty(false);
        setPropertyName('items');
        
        // Check if the items has a $ref
        if (arrayProp.items.$ref) {
          setPropertyType('reference');
          setPropertyRef(arrayProp.items.$ref);
          
          // Determine reference type
          if (arrayProp.items.$ref.startsWith('#/')) {
            setReferenceType('internal');
          } else if (arrayProp.items.$ref.startsWith('http') || arrayProp.items.$ref.startsWith('https')) {
            setReferenceType('url');
          } else {
            setReferenceType('uuid');
          }
        } else {
          setPropertyType(arrayProp.items.type || 'string');
          setPropertyRef('');
        }
        
        setPropertyDescription(arrayProp.items.description || '');
        
        // Array items don't have required property
        setPropertyRequired(false);
        
        setPropertyFormat(arrayProp.items.format || '');
        setPropertyEnum(arrayProp.items.enum || []);
        setPropertyDefault(arrayProp.items.default !== undefined ? String(arrayProp.items.default) : '');
        setPropertyMinLength(arrayProp.items.minLength);
        setPropertyMaxLength(arrayProp.items.maxLength);
        setPropertyMinimum(arrayProp.items.minimum);
        setPropertyMaximum(arrayProp.items.maximum);
        setPropertyPattern(arrayProp.items.pattern || '');
        
        return;
      }
    }
    
    const prop = findPropertyByPath(schema, propertyPath);
    
    if (!prop) {
      // Check if this is a request to add a new property
      const parentPathValue = getParentPath(propertyPath);
      const parent = findPropertyByPath(schema, parentPathValue);
      
      if (parent) {
        setIsNewProperty(true);
        setParentPath(parentPathValue);
        // Extract property name from path if it's a create operation
        const pathSegments = propertyPath.split('.');
        const propName = pathSegments[pathSegments.length - 1];
        if (propName && propName !== 'properties') {
          setPropertyName(propName);
        }
      } else {
        resetForm();
      }
      
      return;
    }
    
    setProperty(prop);
    setIsNewProperty(false);
    
    // Set form values based on property
    setPropertyName(getPropertyNameFromPath(propertyPath));
    
    // Check if the property has a $ref
    if (prop.$ref) {
      setPropertyType('reference');
      setPropertyRef(prop.$ref);
      
      // Determine reference type
      if (prop.$ref.startsWith('#/')) {
        setReferenceType('internal');
      } else if (prop.$ref.startsWith('http') || prop.$ref.startsWith('https')) {
        setReferenceType('url');
      } else {
        setReferenceType('uuid');
      }
    } else {
    // Check if this is a Resource Reference
    if (prop.type === 'object' && 
        prop.required?.length === 2 && 
        prop.required.includes('resourceType') && 
        prop.required.includes('id') &&
        prop.properties?.resourceType?.const) {
      
      setPropertyType('resourceReference');
      setResourceTypeConst(prop.properties.resourceType.const);
    } else {
      setPropertyType(prop.type || 'string');
    }
    setPropertyRef('');
    }
    
    setPropertyDescription(prop.description || '');
    
    // Check if property is in required array using the comprehensive helper function
    const actualPropertyName = getPropertyNameFromPath(propertyPath);
    const parentPath = getParentPath(propertyPath);
    
    // Use the helper function to determine if the property is required
    const isRequired = isPropertyRequired(schema, propertyPath, actualPropertyName);
    console.log('Loading required status:', { actualPropertyName, parentPath, isRequired });
    setPropertyRequired(isRequired); // Update the state based on the schema
    
    setPropertyFormat(prop.format || '');
    setPropertyEnum(prop.enum || []);
    setPropertyDefault(prop.default !== undefined ? String(prop.default) : '');
    setPropertyMinLength(prop.minLength);
    setPropertyMaxLength(prop.maxLength);
    setPropertyMinimum(prop.minimum);
    setPropertyMaximum(prop.maximum);
    setPropertyPattern(prop.pattern || '');
    
    // Check if the property has a const value
    if (prop.const !== undefined) {
      setUseConst(true);
      setPropertyConst(prop.const);
    } else {
      setUseConst(false);
      setPropertyConst(undefined);
    }
    
  }, [propertyPath, schema]); // Dependency array includes schema

  // Reset form
  const resetForm = () => {
    setProperty(null);
    setPropertyName('');
    setPropertyType('string');
    setPropertyDescription('');
    setPropertyRequired(false);
    setPropertyFormat('');
    setPropertyEnum([]);
    setPropertyDefault('');
    setPropertyMinLength(undefined);
    setPropertyMaxLength(undefined);
    setPropertyMinimum(undefined);
    setPropertyMaximum(undefined);
    setPropertyPattern('');
    setPropertyConst(undefined);
    setUseConst(false);
    setPropertyRef('');
    setReferenceType('internal');
    setIsNewProperty(false);
    setParentPath('');
  };

  // Format types for dropdown
  const propertyTypes = [
    { value: 'string', label: 'String' },
    { value: 'number', label: 'Number' },
    { value: 'integer', label: 'Integer' },
    { value: 'boolean', label: 'Boolean' },
    { value: 'object', label: 'Object' },
    { value: 'array', label: 'Array' },
    { value: 'reference', label: 'Reference ($ref)' },
    { value: 'resourceReference', label: 'Resource Reference' },
    { value: 'null', label: 'Null' }
  ];

  // Add a property or update existing property
  const saveProperty = () => {
    let updates: any;
    
    if (propertyType === 'resourceReference') {
      // Create a Resource Reference object
      updates = {
        type: 'object',
        required: ['resourceType', 'id'],
        properties: {
          id: {
            type: 'string',
            format: 'uuid'
          },
          resourceType: {
            type: 'string',
            const: resourceTypeConst || 'Patient'
          }
        }
      };
      
      // Add description if provided
      if (propertyDescription) {
        updates.description = propertyDescription;
      }
    } else {
      updates = createUpdatesObject(
        property,
        propertyName,
        propertyType,
        propertyDescription,
        propertyFormat,
        propertyEnum,
        propertyConst,
        useConst,
        propertyDefault,
        propertyMinLength,
        propertyMaxLength,
        propertyMinimum,
        propertyMaximum,
        propertyPattern,
        propertyRef
      );
    }
    
    if (!updates) return;
    
    if (isNewProperty) {
      onAddProperty(parentPath, { 
        name: propertyName, 
        property: updates, 
        required: propertyRequired 
      });
    } else if (propertyPath) {
      onUpdateProperty(propertyPath, { 
        updates, 
        required: propertyRequired 
      });
    }
  };

  // Render type-specific options
  const renderTypeSpecificOptions = () => {
    const commonProps = {
      propertyPath,
      property,
      isNewProperty,
      dispatch,
      onUpdateProperty
    };
    
    switch (propertyType) {
      case 'string':
        return (
          <StringPropertyEditor
            {...commonProps}
            propertyFormat={propertyFormat}
            setPropertyFormat={setPropertyFormat}
            propertyEnum={propertyEnum}
            setPropertyEnum={setPropertyEnum}
            enumInputValue={enumInputValue}
            setEnumInputValue={setEnumInputValue}
            propertyMinLength={propertyMinLength}
            setPropertyMinLength={setPropertyMinLength}
            propertyMaxLength={propertyMaxLength}
            setPropertyMaxLength={setPropertyMaxLength}
            propertyPattern={propertyPattern}
            setPropertyPattern={setPropertyPattern}
          />
        );
      case 'number':
      case 'integer':
        return (
          <NumberPropertyEditor
            {...commonProps}
            propertyEnum={propertyEnum}
            setPropertyEnum={setPropertyEnum}
            propertyDefault={propertyDefault}
            setPropertyDefault={setPropertyDefault}
            propertyMinimum={propertyMinimum}
            setPropertyMinimum={setPropertyMinimum}
            propertyMaximum={propertyMaximum}
            setPropertyMaximum={setPropertyMaximum}
          />
        );
      case 'boolean':
        return (
          <BooleanPropertyEditor
            {...commonProps}
            propertyDefault={propertyDefault}
            setPropertyDefault={setPropertyDefault}
          />
        );
      case 'reference':
        return (
          <ReferencePropertyEditor
            {...commonProps}
            schema={schema}
            propertyRef={propertyRef}
            setPropertyRef={setPropertyRef}
            referenceType={referenceType}
            setReferenceType={setReferenceType}
            schemaOptions={schemaOptions}
            schemasLoading={schemasLoading}
            onEditDefinition={onEditDefinition}
            onCreateDefinition={onCreateDefinition}
          />
        );
      case 'resourceReference':
        return (
          <ResourceReferenceEditor
            {...commonProps}
            resourceTypeConst={resourceTypeConst}
            setResourceTypeConst={setResourceTypeConst}
          />
        );
      case 'object':
      case 'array':
      default:
        return null;
    }
  };

  // Render form
  return (
    <div>
      {!propertyPath ? (
        <Text color="dimmed" style={{ textAlign: 'center' }} py="xl">
          Select a property from the tree to edit its details.
        </Text>
      ) : (
        <Card shadow="xs" p="md" withBorder>
          <Text fw={500} mb="md">
            {isNewProperty ? 'Add New Property' : 'Edit Property'}
          </Text>
          
          <TextInput
            label="Property Name"
            placeholder="Enter property name"
            value={propertyName}
            onChange={(e) => {
              const newName = e.target.value;
              setPropertyName(newName);
            }}
            onBlur={() => {
              // Only trigger rename if editing an existing property and the name actually changes
              if (!isNewProperty && propertyPath && propertyName && propertyName !== getPropertyNameFromPath(propertyPath)) {
                // Use the Redux dispatch to rename the property
                if (dispatch) {
                  dispatch(renameProperty({
                    path: propertyPath,
                    newName: propertyName
                  }));
                  
                  // Construct the new path for selection
                  const pathParts = propertyPath.split(".");
                  const parentPathParts = [...pathParts];
                  parentPathParts.pop();
                  const newPath = [...parentPathParts, propertyName].join('.');
                  
                  // Notify parent to update selected property path
                  if (typeof onRenameProperty === "function") {
                    onRenameProperty(newPath);
                  }
                }
              }
            }}
            required
            mb="md"
          />
          
          <Select
            label="Type"
            placeholder="Select property type"
            data={propertyTypes}
            value={propertyType}
            onChange={(value) => {
              const newType = value || 'string';
              setPropertyType(newType);
              
              if (propertyPath && !isNewProperty) {
                // Start with the new type
                let updates: any = { type: newType };
                
                // Clean up invalid metadata based on type change
                if (propertyType === 'string' && newType !== 'string') {
                  // Remove string-specific fields
                  updates.minLength = undefined;
                  updates.maxLength = undefined;
                  updates.pattern = undefined;
                  setPropertyMinLength(undefined);
                  setPropertyMaxLength(undefined);
                  setPropertyPattern('');
                }
                
                if ((propertyType === 'number' || propertyType === 'integer') && 
                    newType !== 'number' && newType !== 'integer') {
                  // Remove number-specific fields
                  updates.minimum = undefined;
                  updates.maximum = undefined;
                  setPropertyMinimum(undefined);
                  setPropertyMaximum(undefined);
                }
                
                if (propertyType === 'reference' && newType !== 'reference') {
                  // Remove reference fields
                  updates.$ref = undefined;
                  setPropertyRef('');
                }
                
                // Clear enum if changing to a type that doesn't support it
                if (newType !== 'string' && newType !== 'number' && newType !== 'integer') {
                  updates.enum = undefined;
                  setPropertyEnum([]);
                }
                
                // Clear format if changing to a type that doesn't support it
                if (newType !== 'string') {
                  updates.format = undefined;
                  setPropertyFormat('');
                }
                
                // Clear const value when changing types
                updates.const = undefined;
                setUseConst(false);
                setPropertyConst(undefined);
                
                // Add default structure for complex types
                if (newType === 'object') {
                  updates.properties = {};
                } else if (newType === 'array') {
                  updates.items = { type: 'string' };
                }
                
                onUpdateProperty(propertyPath, { 
                  updates, 
                  required: propertyRequired
                });
              }
            }}
            required
            mb="md"
          />
          
          <Textarea
            label="Description"
            placeholder="Property description"
            value={propertyDescription}
            onChange={(e) => {
              const newValue = e.target.value;
              setPropertyDescription(newValue);
              if (propertyPath && !isNewProperty) {
                // Use direct dispatch for immediate update
                dispatch({
                  type: 'schema/updatePropertyField',
                  payload: {
                    path: propertyPath,
                    field: 'description',
                    value: newValue
                  }
                });
              }
            }}
            mb="md"
          />
          
          <Checkbox
            label="Required"
            checked={propertyRequired} // Controlled by state updated via useEffect
            onChange={(e) => {
              const isChecked = e.currentTarget.checked;
              // Update local state immediately for responsive UI
              setPropertyRequired(isChecked);
              
              // Trigger the update to the schema
              if (propertyPath && !isNewProperty) {
                const updates = createUpdatesObject(
                  property,
                  propertyName,
                  propertyType,
                  propertyDescription,
                  propertyFormat,
                  propertyEnum,
                  propertyConst,
                  useConst,
                  propertyDefault,
                  propertyMinLength,
                  propertyMaxLength,
                  propertyMinimum,
                  propertyMaximum,
                  propertyPattern,
                  propertyRef
                );
                
                if (updates) {
                  console.log("Checkbox onChange - calling onUpdateProperty with required:", isChecked);
                  onUpdateProperty(propertyPath, { 
                    updates, 
                    required: isChecked // Pass the intended state
                  });
                }
              }
            }}
            mb="md"
          />
          
          {/* Const value section - not shown for resourceReference since it has a built-in const value */}
          {propertyType !== 'resourceReference' && (
            <>
              <Divider my="md" label="Constant Value" labelPosition="center" />
              <ConstValueEditor 
                propertyPath={propertyPath}
                property={property}
                isNewProperty={isNewProperty}
                dispatch={dispatch}
                onUpdateProperty={onUpdateProperty}
                propertyType={propertyType}
                useConst={useConst}
                setUseConst={setUseConst}
                propertyConst={propertyConst}
                setPropertyConst={setPropertyConst}
              />
            </>
          )}
          
          {propertyType === 'array' && (
            <>
              <Divider my="md" label="Array Items Configuration" labelPosition="center" />
              <Card shadow="sm" p="md" withBorder mb="md">
                <Text fw={500} mb="md">Array Items</Text>
                
                <Select
                  label="Items Type"
                  placeholder="Select items type"
                  data={[
                    { value: 'string', label: 'String' },
                    { value: 'number', label: 'Number' },
                    { value: 'integer', label: 'Integer' },
                    { value: 'boolean', label: 'Boolean' },
                    { value: 'object', label: 'Object' },
                    { value: 'reference', label: 'Reference ($ref)' }
                  ]}
                  value={property?.items?.$ref ? 'reference' : (property?.items?.type || 'string')}
                  onChange={(value) => {
                    // Update the items property type
                    const itemsUpdate = value === 'reference'
                      ? { $ref: '#/definitions/' }
                      : { type: value };
                      
                    if (propertyPath) {
                      onUpdateProperty(propertyPath, {
                        updates: {
                          ...property,
                          items: itemsUpdate
                        },
                        required: propertyRequired
                      });
                    }
                  }}
                  mb="md"
                />
                
                {property?.items?.$ref ? (
                  <ReferencePropertyEditor
                    propertyPath={`${propertyPath}.items`}
                    property={property?.items}
                    isNewProperty={false}
                    dispatch={dispatch}
                    onUpdateProperty={onUpdateProperty}
                    schema={schema}
                    propertyRef={property.items.$ref}
                    setPropertyRef={(value) => {
                      if (propertyPath) {
                        onUpdateProperty(propertyPath, {
                          updates: {
                            ...property,
                            items: { $ref: value }
                          },
                          required: propertyRequired
                        });
                      }
                    }}
                    referenceType={property.items.$ref.startsWith('#/') ? 'internal' : 'uuid'}
                    setReferenceType={(value) => {
                      let newRef = property.items.$ref;
                      // Convert reference based on type
                      if (value === 'internal') {
                        newRef = newRef.startsWith('#/') ? newRef : '#/definitions/Reference';
                      } else {
                        // UUID format
                        newRef = newRef.match(/^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i)
                          ? newRef
                          : '00000000-0000-0000-0000-000000000000';
                      }
                      
                      if (propertyPath) {
                        onUpdateProperty(propertyPath, {
                          updates: {
                            ...property,
                            items: { $ref: newRef }
                          },
                          required: propertyRequired
                        });
                      }
                    }}
                    schemaOptions={schemaOptions}
                    schemasLoading={schemasLoading}
                    onEditDefinition={onEditDefinition}
                    onCreateDefinition={onCreateDefinition}
                  />
                ) : (
                  <>
                    <Text size="sm" color="dimmed" mb="md">
                      Edit the array property to configure its items. For complex item structures, 
                      expand the array in the tree view and click on the "items" property.
                    </Text>
                  </>
                )}
              </Card>
            </>
          )}
          
          {renderTypeSpecificOptions()}

          {/* Object child properties editor */}
          {propertyType === 'object' && property && (
            <Box mt="md">
              <Divider my="md" label="Child Properties" labelPosition="center" />
              {property.properties && Object.keys(property.properties).length > 0 ? (
                <Stack gap="xs">
                  {Object.entries(property.properties).map(([childName, childProp], index, array) => {
                    const isFirstProperty = index === 0;
                    const isLastProperty = index === array.length - 1;
                    
                    return (
                      <Group key={childName} align="center" justify="space-between">
                        <Group>
                          <Text fw={500}>{childName}</Text>
                          <Text size="xs" color="dimmed">
                            {(childProp as any).type || 'object'}
                          </Text>
                        </Group>
                        
                        <Group gap="xs">
                          {/* Order controls */}
                          <Tooltip label="Move Up">
                            <ActionIcon
                              size="sm"
                              color="indigo"
                              variant="filled"
                              disabled={isFirstProperty}
                              onClick={() => {
                                if (propertyPath && !isFirstProperty) {
                                  dispatch({
                                    type: 'schema/reorderProperty',
                                    payload: {
                                      parentPath: propertyPath,
                                      propertyName: childName,
                                      direction: 'up'
                                    }
                                  });
                                }
                              }}
                            >
                              ▲
                            </ActionIcon>
                          </Tooltip>
                          
                          <Tooltip label="Move Down">
                            <ActionIcon
                              size="sm"
                              color="indigo"
                              variant="filled"
                              disabled={isLastProperty}
                              onClick={() => {
                                if (propertyPath && !isLastProperty) {
                                  dispatch({
                                    type: 'schema/reorderProperty',
                                    payload: {
                                      parentPath: propertyPath,
                                      propertyName: childName,
                                      direction: 'down'
                                    }
                                  });
                                }
                              }}
                            >
                              ▼
                            </ActionIcon>
                          </Tooltip>
                          
                          {/* Edit/Delete controls */}
                          <Button
                            size="xs"
                            variant="light"
                            onClick={() => {
                              // Select this child property for editing
                              if (propertyPath) {
                                const childPath = `${propertyPath}.properties.${childName}`;
                                if (typeof onRenameProperty === "function") {
                                  onRenameProperty(childPath);
                                }
                              }
                            }}
                          >
                            Edit
                          </Button>
                          
                          <Button
                            size="xs"
                            color="red"
                            variant="light"
                            onClick={() => {
                              // Remove this child property
                              if (propertyPath) {
                                const updatedProps = { ...property.properties };
                                delete updatedProps[childName];
                                onUpdateProperty(propertyPath, {
                                  updates: { ...property, properties: updatedProps },
                                  required: propertyRequired
                                });
                              }
                            }}
                          >
                            Delete
                          </Button>
                        </Group>
                      </Group>
                    );
                  })}
                </Stack>
              ) : (
                <Text color="dimmed" size="sm" mb="xs">
                  No child properties defined.
                </Text>
              )}

              <Button 
                size="sm"
                variant="outline"
                onClick={() => setShowAddChildForm(true)}
                mt="md"
              >
                Add Child Property
              </Button>

              {showAddChildForm && (
                <Box mt="md">
                  <AddChildPropertyForm 
                    onAdd={(name, type) => {
                      // Add a new property to this object
                      if (propertyPath) {
                        // Create new property with proper type information
                        const newProperty: Record<string, any> = { type };
                        if (type === 'object') {
                          newProperty.properties = {};
                        } else if (type === 'array') {
                          newProperty.items = { type: 'string' };
                        }
                        
                        // Create updated properties object
                        const updatedProps = { 
                          ...(property.properties || {}),
                          [name]: newProperty
                        };
                        
                        // Update the object with the new child property
                        onUpdateProperty(propertyPath, {
                          updates: { ...property, properties: updatedProps },
                          required: propertyRequired
                        });
                        
                        // Hide the form after adding
                        setShowAddChildForm(false);
                      }
                    }}
                    onCancel={() => setShowAddChildForm(false)}
                  />
                </Box>
              )}
            </Box>
          )}

          <Group justify="flex-end" mt="md">
            <Button onClick={saveProperty}>
              {isNewProperty ? 'Add Property' : 'Update Property'}
            </Button>
          </Group>
        </Card>
      )}
    </div>
  );
}
