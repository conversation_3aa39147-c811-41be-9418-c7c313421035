import React from 'react';
import { 
  Select, 
  TextInput, 
  Box, 
  Group, 
  Text, 
  <PERSON><PERSON>, 
  Card 
} from '@mantine/core';
import { CommonEditorProps } from './types';

interface ReferencePropertyEditorProps extends CommonEditorProps {
  schema: any;
  propertyRef: string;
  setPropertyRef: (value: string) => void;
  referenceType: string;
  setReferenceType: (value: string) => void;
  schemaOptions: { value: string; label: string }[];
  schemasLoading: boolean;
  onEditDefinition?: (definitionName: string) => void;
  onCreateDefinition?: () => void;
}

export const ReferencePropertyEditor: React.FC<ReferencePropertyEditorProps> = ({
  propertyPath,
  schema,
  propertyRef,
  setPropertyRef,
  referenceType,
  setReferenceType,
  schemaOptions,
  schemasLoading,
  onEditDefinition,
  onCreateDefinition
}) => {
  return (
    <>
      <Select
        label="Reference Type"
        placeholder="Select reference type"
        data={[
          { value: 'internal', label: 'Internal (#/definitions/...)' },
          { value: 'uuid', label: 'UUID' }
        ]}
        value={referenceType}
        onChange={(value) => setReferenceType(value || 'internal')}
        mb="md"
      />
      
      {referenceType === 'internal' && (
        <>
          <Box mb="md">
            {schema && schema.definitions && Object.keys(schema.definitions).length > 0 ? (
              <Select
                label="Definition Name"
                placeholder="Select a definition"
                data={
                  Object.keys(schema.definitions).map(def => ({
                    value: `definitions/${def}`,
                    label: def
                  }))
                }
                value={propertyRef.startsWith('#/') ? propertyRef.substring(2) : propertyRef}
                onChange={(value) => setPropertyRef(value ? `#/${value}` : '')}
                searchable
                clearable
                mb="xs"
              />
            ) : (
              <>
                <TextInput
                  label="Definition path"
                  placeholder="definitions/CustomType"
                  value={propertyRef.startsWith('#/') ? propertyRef.substring(2) : propertyRef}
                  onChange={(e) => setPropertyRef(e.target.value ? `#/${e.target.value}` : '')}
                  mb="xs"
                />
                <Text size="xs" color="dimmed">
                  No existing definitions available. You can create one using the button below.
                </Text>
              </>
            )}
          </Box>

          {propertyRef.startsWith('#/definitions/') && schema.definitions && (
            <Box mb="md">
              <Group>
                <Button 
                  size="xs" 
                  variant="light"
                  color="blue"
                  onClick={() => {
                    // Extract definition name from the path
                    const defName = propertyRef.substring('#/definitions/'.length);
                    if (onEditDefinition) {
                      onEditDefinition(defName);
                    }
                  }}
                >
                  Edit Referenced Definition
                </Button>

                <Button 
                  size="xs" 
                  variant="light"
                  color="grape"
                  onClick={() => {
                    // Extract definition name from the path
                    const defName = propertyRef.substring('#/definitions/'.length);
                    if (schema.definitions[defName]) {
                      // Show the definition structure
                      alert(`Definition Structure: ${JSON.stringify(schema.definitions[defName], null, 2)}`);
                    }
                  }}
                >
                  View Definition Structure
                </Button>
              </Group>

              {schema.definitions[propertyRef.substring('#/definitions/'.length)] && (
                <Card withBorder p="xs" mt="xs" style={{ backgroundColor: '#f8f9fa' }}>
                  <Text size="xs" fw={500}>Definition Preview:</Text>
                  <Text size="xs" style={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                    {JSON.stringify(schema.definitions[propertyRef.substring('#/definitions/'.length)], null, 2).substring(0, 200)}
                    {JSON.stringify(schema.definitions[propertyRef.substring('#/definitions/'.length)], null, 2).length > 200 ? '...' : ''}
                  </Text>
                </Card>
              )}
            </Box>
          )}

          <Group mb="md">
            <Button 
              size="xs" 
              leftSection="+"
              variant="outline" 
              color="teal"
              onClick={() => {
                if (onCreateDefinition) {
                  onCreateDefinition();
                }
              }}
            >
              Create New Definition
            </Button>
          </Group>

          <Text size="xs" color="dimmed" mb="md">
            Tip: Add definitions in the Definitions tab to make them available here.
          </Text>
        </>
      )}
      
      {referenceType === 'uuid' && (
        <>
          <Box mb="md">
            <Group mb="xs" align="flex-end">
              <Box style={{ flex: 1 }}>
                <Select
                  label="Available Schemas"
                  placeholder="Select an existing schema"
                  data={schemaOptions}
                  disabled={schemasLoading}
                  value={propertyRef.match(/^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i) ? propertyRef : ""}
                  onChange={(value) => setPropertyRef(value || '')}
                  searchable
                  clearable
                />
              </Box>
            </Group>
            
            <TextInput
              label="Custom Schema UUID"
              placeholder="12345678-1234-1234-1234-123456789012"
              value={propertyRef}
              onChange={(e) => setPropertyRef(e.target.value)}
              mb="xs"
            />
            
            <Text size="xs" color="dimmed">
              Select an existing schema or enter a custom UUID
            </Text>
          </Box>

          {propertyRef && (
            <Card withBorder p="xs" mt="xs" style={{ backgroundColor: '#f8f9fa' }}>
              <Group justify="space-between" mb="xs">
                <Text size="xs" fw={500}>Schema UUID:</Text>
                <Button 
                  size="xs" 
                  variant="subtle" 
                  component="a" 
                  href={`/schemas/${propertyRef}`} 
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Open Schema
                </Button>
              </Group>
              <Text size="xs" style={{ fontFamily: 'monospace' }}>
                {propertyRef}
              </Text>
            </Card>
          )}
        </>
      )}
    </>
  );
};
