import { TextInput } from '@mantine/core';
import { CommonEditorProps } from './types';

interface ResourceReferenceEditorProps extends CommonEditorProps {
  resourceTypeConst: string;
  setResourceTypeConst: (value: string) => void;
}

export function ResourceReferenceEditor({
  propertyPath,
  property,
  isNewProperty,
  dispatch,
  onUpdateProperty,
  resourceTypeConst,
  setResourceTypeConst
}: ResourceReferenceEditorProps) {
  // Handle resource type change
  const handleResourceTypeChange = (value: string) => {
    setResourceTypeConst(value);
    
    if (propertyPath && !isNewProperty) {
      // Update the resourceType const value directly
      const updatedProperty = {
        ...property,
        properties: {
          ...property.properties,
          resourceType: {
            ...property.properties.resourceType,
            const: value
          }
        }
      };
      
      onUpdateProperty(propertyPath, {
        updates: updatedProperty,
        required: true // Resource References always have required fields
      });
    }
  };
  
  return (
    <>
      <TextInput
        label="Resource Type"
        placeholder="Enter resource type (e.g., Patient, Practitioner)"
        value={resourceTypeConst}
        onChange={(e) => handleResourceTypeChange(e.target.value)}
        mb="md"
      />
    </>
  );
}
