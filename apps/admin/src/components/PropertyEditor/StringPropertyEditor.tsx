import React from 'react';
import { 
  Select, 
  NumberInput, 
  TextInput, 
  Divider, 
  Group, 
  Box, 
  Button,
  Text
} from '@mantine/core';
import { updatePropertyField } from '../../store/schemaSlice';
import { CommonEditorProps } from './types';

// String format options
const stringFormatOptions = [
  { value: '', label: 'None' },
  { value: 'date', label: 'Date' },
  { value: 'date-time', label: 'Date-Time' },
  { value: 'email', label: 'Email' },
  { value: 'uri', label: 'URI' },
  { value: 'uuid', label: 'UUID' },
  { value: 'password', label: 'Password' }
];

interface StringPropertyEditorProps extends CommonEditorProps {
  propertyFormat: string;
  setPropertyFormat: (value: string) => void;
  propertyEnum: string[];
  setPropertyEnum: (value: string[]) => void;
  enumInputValue: string;
  setEnumInputValue: (value: string) => void;
  propertyMinLength: number | undefined;
  setPropertyMinLength: (value: number | undefined) => void;
  propertyMaxLength: number | undefined;
  setPropertyMaxLength: (value: number | undefined) => void;
  propertyPattern: string;
  setPropertyPattern: (value: string) => void;
}

export const StringPropertyEditor: React.FC<StringPropertyEditorProps> = ({
  propertyPath,
  isNewProperty,
  dispatch,
  propertyFormat,
  setPropertyFormat,
  propertyEnum,
  setPropertyEnum,
  enumInputValue,
  setEnumInputValue,
  propertyMinLength,
  setPropertyMinLength,
  propertyMaxLength,
  setPropertyMaxLength,
  propertyPattern,
  setPropertyPattern
}) => {
  
  // Handle add enum value
  const handleAddEnumValue = (value: string) => {
    if (value && !propertyEnum.includes(value)) {
      const newEnum = [...propertyEnum, value];
      setPropertyEnum(newEnum);
      if (propertyPath) {
        dispatch(updatePropertyField({
          path: propertyPath,
          field: 'propertyEnum',
          value: newEnum
        }));
      }
    }
  };

  // Handle remove enum value
  const handleRemoveEnumValue = (value: string) => {
    const newEnum = propertyEnum.filter(v => v !== value);
    setPropertyEnum(newEnum);
    if (propertyPath) {
      dispatch(updatePropertyField({
        path: propertyPath,
        field: 'propertyEnum',
        value: newEnum
      }));
    }
  };

  return (
    <>
      <Select
        label="Format"
        placeholder="Select format"
        data={stringFormatOptions}
        value={propertyFormat}
        onChange={(value) => {
          setPropertyFormat(value || '');
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'format',
              value: value || ''
            }));
          }
        }}
        mb="md"
        clearable
      />
      
      <NumberInput
        label="Min Length"
        placeholder="Minimum length"
        value={propertyMinLength}
        onChange={(value) => {
          const numValue = value !== '' ? Number(value) : undefined;
          setPropertyMinLength(numValue);
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'minLength',
              value: numValue
            }));
          }
        }}
        min={0}
        mb="md"
      />
      
      <NumberInput
        label="Max Length"
        placeholder="Maximum length"
        value={propertyMaxLength}
        onChange={(value) => {
          const numValue = value !== '' ? Number(value) : undefined;
          setPropertyMaxLength(numValue);
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'maxLength',
              value: numValue
            }));
          }
        }}
        min={0}
        mb="md"
      />
      
      <TextInput
        label="Pattern (Regex)"
        placeholder="Regular expression pattern"
        value={propertyPattern}
        onChange={(e) => {
          const newValue = e.target.value;
          setPropertyPattern(newValue);
          if (propertyPath && !isNewProperty) {
            dispatch(updatePropertyField({
              path: propertyPath,
              field: 'pattern',
              value: newValue
            }));
          }
        }}
        mb="md"
      />
      
      <Divider my="md" label="Enum Values" labelPosition="center" />
      
      <Group mb="md">
        <TextInput
          placeholder="Add enum value"
          value={enumInputValue}
          onChange={(e) => setEnumInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && enumInputValue.trim()) {
              handleAddEnumValue(enumInputValue.trim());
              setEnumInputValue('');
            }
          }}
          style={{ flex: 1 }}
        />
        <Button
          onClick={() => {
            if (enumInputValue.trim()) {
              handleAddEnumValue(enumInputValue.trim());
              setEnumInputValue('');
            }
          }}
        >
          Add
        </Button>
      </Group>
      
      {propertyEnum.length > 0 && (
        <Box mb="md">
          {propertyEnum.map((value, index) => (
            <Button
              key={index}
              size="xs"
              variant="light"
              rightSection="×"
              onClick={() => handleRemoveEnumValue(value)}
              mb="xs"
              mr="xs"
            >
              {value}
            </Button>
          ))}
        </Box>
      )}
    </>
  );
};
