import { Dispatch } from '@reduxjs/toolkit';

export interface PropertyEditorProps {
  schema: any;
  propertyPath: string | null;
  onUpdateProperty: (path: string, updates: any) => void;
  onAddProperty: (path: string, property: any) => void;
  onEditDefinition?: (definitionName: string) => void;
  onCreateDefinition?: () => void;
  onRenameProperty?: (newPath: string) => void;
}

export interface CommonEditorProps {
  propertyPath: string | null;
  property: any;
  isNewProperty: boolean;
  dispatch: Dispatch<any>;
  onUpdateProperty: (path: string, updates: any) => void;
}

export interface AddChildPropertyFormProps {
  onAdd: (name: string, type: string) => void;
  onCancel?: () => void;
}
