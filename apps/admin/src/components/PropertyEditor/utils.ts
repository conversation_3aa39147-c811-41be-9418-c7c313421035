/**
 * Helper functions for the PropertyEditor component
 */

// Find property in schema based on path
export const findPropertyByPath = (schema: any, path: string) => {
  if (!path || path === 'root') return null;
  
  const segments = path.split('.');
  let current = schema;
  
  for (const segment of segments) {
    if (!current) return null;
    current = current[segment];
  }
  
  return current;
};

// Extract property name from path
export const getPropertyNameFromPath = (path: string) => {
  if (!path || path === 'root') return '';
  const segments = path.split('.');
  return segments[segments.length - 1];
};

// Get parent path
export const getParentPath = (path: string) => {
  if (!path || path === 'root') return 'root';
  const segments = path.split('.');
  segments.pop();
  return segments.join('.');
};

// Helper function to check if a property is required
export const isPropertyRequired = (schema: any, path: string, propertyName: string) => {
  console.group('Checking required status');
  console.log('Full path:', path);
  console.log('Property name:', propertyName);
  
  // Handle root properties
  if (path === 'root') {
    console.log('Root property - checking schema.required');
    const isRequired = schema.required?.includes(propertyName) ?? false;
    console.log('Result:', isRequired);
    console.groupEnd();
    return isRequired;
  }

  // For properties.X paths, check the root required array
  if (path.match(/^properties\.[^.]+$/)) {
    const propName = path.split('.')[1];
    const isRequired = schema.required?.includes(propName) ?? false;
    console.log('Checking root required array for:', propName, isRequired);
    console.groupEnd();
    return isRequired;
  }

  // Extract the property name and parent path from the full path
  let actualPropertyName = '';
  let parentPath = '';
  let parentObj = null;

  // Handle nested property paths (including grandchild properties)
  if (path.includes('.properties.')) {
    // Split the path at each .properties. segment
    const segments = path.split('.properties.');
    // The last segment is our property name
    actualPropertyName = segments[segments.length - 1];
    
    // Build the parent path by joining all segments except the last one
    if (segments.length > 1) {
      // For paths like "properties.parent.properties.child"
      // We need to find the parent object (in this case "parent")
      
      // Start with the schema object
      parentObj = schema;
      
      // Navigate through the path to find the parent object
      for (let i = 0; i < segments.length - 1; i++) {
        const segment = segments[i];
        if (i === 0) {
          // First segment is special (properties.X)
          const propName = segment.split('.')[1];
          parentObj = parentObj.properties?.[propName];
        } else {
          // Subsequent segments are just property names
          parentObj = parentObj.properties?.[segment];
        }
        
        if (!parentObj) {
          console.log('Could not find parent object');
          console.groupEnd();
          return false;
        }
      }
      
      console.log('Found parent object:', parentObj);
      
      // Check if the property is in the parent's required array
      if (parentObj.required?.includes(actualPropertyName)) {
        console.log('Property is required in parent');
        console.groupEnd();
        return true;
      }
    }
  } else {
    // Normalize path to handle different path formats
    const normalizePath = (p: string) => p.replace(/^properties\./, '').replace(/\.properties\./g, '.');
    const normalizedPath = normalizePath(path);
    const pathParts = normalizedPath.split('.');
    
    // Get the immediate parent path (one level up)
    parentPath = pathParts.slice(0, -1).join('.');
    console.log('Parent path:', parentPath);
    
    // Get the actual property name (last segment of path)
    actualPropertyName = pathParts[pathParts.length - 1];
    console.log('Actual property name from path:', actualPropertyName);
    
    // Check the immediate parent's required array
    parentObj = findPropertyByPath(schema, parentPath);
    if (parentObj?.required?.includes(actualPropertyName)) {
      console.log('Property is required in immediate parent');
      console.groupEnd();
      return true;
    }
  }
  
  console.log('Property is NOT required');
  console.groupEnd();
  return false;
};

// Creates an updates object based on current form state
export const createUpdatesObject = (
  property: any,
  propertyName: string,
  propertyType: string,
  propertyDescription: string,
  propertyFormat: string,
  propertyEnum: string[],
  propertyConst: any,
  useConst: boolean,
  propertyDefault: string,
  propertyMinLength: number | undefined,
  propertyMaxLength: number | undefined,
  propertyMinimum: number | undefined,
  propertyMaximum: number | undefined,
  propertyPattern: string,
  propertyRef: string
) => {
  console.group('Creating updates object');
  try {
    if (!propertyName.trim()) {
      console.log('No property name - returning null');
      return null;
    }
    
    // Start with existing property data if available, or create a new object
    let updates: any = property ? { ...property } : {};
    
    // Set the type (unless it's a reference type)
    if (propertyType !== 'reference') {
      updates.type = propertyType;
    }
    console.log('Base updates:', updates);
    
    // Handle reference type ($ref) properties differently
    if (propertyType === 'reference') {
      if (!propertyRef) {
        console.log('No reference value - returning null');
        return null;
      }
      
      // For reference types, we need to replace the entire object
      updates = { $ref: propertyRef };
      console.log('Reference updates:', updates);
    }
    
    // Handle Resource Reference type
    if (propertyType === 'resourceReference') {
      // Create a Resource Reference object
      updates = {
        type: 'object',
        required: ['resourceType', 'id'],
        properties: {
          id: {
            type: 'string',
            format: 'uuid'
          },
          resourceType: {
            type: 'string',
            const: propertyConst || 'Patient'
          }
        }
      };
      console.log('Resource Reference updates:', updates);
    }
    
    // Common fields
    if (propertyDescription.trim()) {
      updates.description = propertyDescription.trim();
      console.log('Added description:', updates.description);
    } else {
      // Remove description if it's empty
      delete updates.description;
    }
    
    if (propertyFormat) {
      updates.format = propertyFormat;
      console.log('Added format:', updates.format);
    } else {
      // Remove format if it's empty
      delete updates.format;
    }
    
    if (propertyEnum.length > 0) {
      updates.enum = propertyEnum;
      console.log('Added enum:', updates.enum);
    } else {
      // Remove enum if it's empty
      delete updates.enum;
    }
    
    // Handle const value
    if (useConst && propertyConst !== undefined) {
      let constValue: any;
      
      // Convert const value based on type
      switch (propertyType) {
        case 'number':
        case 'integer':
          constValue = Number(propertyConst);
          break;
        case 'boolean':
          constValue = constValue === 'true' || constValue === true;
          break;
        default:
          constValue = propertyConst;
      }
      
      updates.const = constValue;
      console.log('Added const:', updates.const);
    } else {
      // Remove const if it's not used
      delete updates.const;
    }
    
    // Handle default value
    if (propertyDefault) {
      let defaultValue: any;
      
      // Convert default value based on type
      switch (propertyType) {
        case 'number':
        case 'integer':
          defaultValue = Number(propertyDefault);
          break;
        case 'boolean':
          defaultValue = propertyDefault === 'true';
          break;
        default:
          defaultValue = propertyDefault;
      }
      
      updates.default = defaultValue;
      console.log('Added default:', updates.default);
    } else {
      // Remove default if it's empty
      delete updates.default;
    }
    
    // String-specific fields
    if (propertyType === 'string') {
      if (propertyMinLength !== undefined) {
        updates.minLength = propertyMinLength;
        console.log('Added minLength:', updates.minLength);
      } else {
        delete updates.minLength;
      }
      
      if (propertyMaxLength !== undefined) {
        updates.maxLength = propertyMaxLength;
        console.log('Added maxLength:', updates.maxLength);
      } else {
        delete updates.maxLength;
      }
      
      if (propertyPattern) {
        updates.pattern = propertyPattern;
        console.log('Added pattern:', updates.pattern);
      } else {
        delete updates.pattern;
      }
    } else {
      // Remove string-specific fields if type is not string
      delete updates.minLength;
      delete updates.maxLength;
      delete updates.pattern;
    }
    
    // Number-specific fields
    if (propertyType === 'number' || propertyType === 'integer') {
      if (propertyMinimum !== undefined) {
        updates.minimum = propertyMinimum;
        console.log('Added minimum:', updates.minimum);
      } else {
        delete updates.minimum;
      }
      
      if (propertyMaximum !== undefined) {
        updates.maximum = propertyMaximum;
        console.log('Added maximum:', updates.maximum);
      } else {
        delete updates.maximum;
      }
    } else {
      // Remove number-specific fields if type is not number or integer
      delete updates.minimum;
      delete updates.maximum;
    }
    
    // Complex types
    if (propertyType === 'object') {
      updates.properties = property?.properties || {};
      console.log('Added properties object');
    } else if (propertyType !== 'reference') {
      // Remove properties if type is not object and not reference
      delete updates.properties;
    }
    
    if (propertyType === 'array') {
      updates.items = property?.items || { type: 'string' };
      console.log('Added items object');
    } else if (propertyType !== 'reference') {
      // Remove items if type is not array and not reference
      delete updates.items;
    }
    
    console.log('Final updates object:', updates);
    return updates;
  } finally {
    console.groupEnd();
  }
};
