import { useState, useEffect, useMemo } from 'react';
import { format, parseISO, isValid, parse } from 'date-fns';
import { 
  Title, 
  Text, 
  Button, 
  Group, 
  Card, 
  TextInput,
  Textarea,
  NumberInput,
  Select,
  Checkbox,
  Switch,
  Loader,
  Stack,
  MultiSelect,
  JsonInput,
  ActionIcon,
  Tooltip,
  Modal,
  Paper,
  Divider,
  Box,
  Collapse,
  Badge,
  Breadcrumbs,
  Anchor,
  ThemeIcon,
  Menu,
  rem
} from '@mantine/core';
import { 
  IconClipboard, 
  IconClipboardText, 
  IconRobot, 
  IconSearch, 
  IconPlus, 
  IconTrash, 
  IconChevronDown, 
  IconChevronUp,
  IconMaximize,
  IconMinimize,
  IconFolder,
  IconFolderOpen,
  IconList,
  IconBrackets,
  IconDotsVertical,
  IconEye,
  IconEyeOff,
  IconArrowsMinimize,
  IconArrowsMaximize
} from '@tabler/icons-react';
import { useGetSchemaQuery, useGetUISchemasBySchemaQuery, useGetResourcesQuery, useGetResourcesByTypeAndStatusQuery } from '../store/api';
import { UISchema, UISchemaWidgetProps } from '../types/uiSchema';
import { applyUIOrder, shouldDisplayProperty, getWidgetType, getWidgetOptions } from '../utils/uiSchemaUtils';

// Helper function to resolve schema references potentially at multiple levels
const resolveSchemaRef = (
  refPath: string, 
  rootSchema: any, 
  definitions: Record<string, any> = {},
  currentSchema: any = null // The schema containing the $ref
): any => {
  console.log('Resolving schema reference:', refPath, currentSchema?.definitions ? 'has local definitions' : 'no local definitions');
  
  // If reference path is internal to the schema
  if (refPath.startsWith('#/')) {
    // First, check if the reference is in the current schema's definitions
    if (refPath.startsWith('#/definitions/') && currentSchema?.definitions) {
      const defName = refPath.split('/').pop() || '';
      if (currentSchema.definitions[defName]) {
        console.log('Found definition in current schema:', defName);
        return currentSchema.definitions[defName];
      }
    }
    
    // Then, try to resolve using a JSON pointer in the root schema
    const parts = refPath.substring(2).split('/');
    let current = rootSchema;
    
    for (const part of parts) {
      if (!current) return null;
      current = current[decodeURIComponent(part)];
    }
    
    return current;
  }
  
  // Handle direct definition reference
  const refName = refPath.split('/').pop() || '';
  
  // Check in provided definitions
  if (definitions[refName]) {
    return definitions[refName];
  }
  
  // Check in root schema definitions (both modern and legacy formats)
  if (rootSchema.$defs && rootSchema.$defs[refName]) {
    return rootSchema.$defs[refName];
  }
  
  if (rootSchema.definitions && rootSchema.definitions[refName]) {
    return rootSchema.definitions[refName];
  }
  
  // Not found
  return null;
};

interface SchemaFormProps {
  schemaId: string;
  resolvedSchema?: any;
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
  schemaDefinitions?: Record<string, any>;
  agencyId?: string;
}

export function SchemaForm({ 
  schemaId, 
  resolvedSchema,
  initialData = {}, 
  onSubmit, 
  onCancel,
  isSubmitting = false,
  schemaDefinitions = {},
  agencyId
}: SchemaFormProps) {
  const [formData, setFormData] = useState<any>(initialData);
  const [selectedUISchemaId, setSelectedUISchemaId] = useState<string | null>(null);
  const [isGeneratingAiData, setIsGeneratingAiData] = useState(false);
  const [resourcePickerOpen, setResourcePickerOpen] = useState(false);
  const [collapsedItems, setCollapsedItems] = useState<Record<string, boolean>>({});
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const [expandedView, setExpandedView] = useState<string | null>(null);
  const [currentResourceField, setCurrentResourceField] = useState<{
    field: string;
    resourceType: string;
    multiple: boolean;
    onSelect?: (selectedResources: any) => void;
  } | null>(null);
  
  // Toggle collapsed state for an item
  const toggleItemCollapse = (itemPath: string) => {
    setCollapsedItems((prev: Record<string, boolean>) => ({
      ...prev,
      [itemPath]: !prev[itemPath]
    }));
  };
  
  // Fetch schema if resolvedSchema is not provided
  const { 
    data: schema, 
    isLoading: isLoadingSchema, 
    error: schemaError 
  } = useGetSchemaQuery(schemaId, {
    skip: !!resolvedSchema
  });
  useEffect(() => {
    console.log('resolvedSchema', resolvedSchema);
  }, [resolvedSchema]);
  
  // Use the resolved schema if provided, otherwise use the fetched schema
  const schemaToUse = resolvedSchema || (schema?.schema);
  
  // Fetch UI schemas for this schema
  const { 
    data: uiSchemas, 
    isLoading: isLoadingUISchemas 
  } = useGetUISchemasBySchemaQuery(schemaId);
  
  // Fetch resources for the resource picker
  const { 
    data: resources, 
    isLoading: isLoadingResources 
  } = useGetResourcesByTypeAndStatusQuery(
    { 
      type: currentResourceField?.resourceType || '', 
      status: 'all',
      agencyId
    }, 
    { 
      skip: !resourcePickerOpen || !currentResourceField 
    }
  );
  
  // Combined definitions from all sources
  const allDefinitions = {
    ...schemaDefinitions,
    ...(schemaToUse?.$defs || {}),
    ...(schemaToUse?.definitions || {})
  };
  
  // Set initial form data
  useEffect(() => {
    if (schemaToUse && Object.keys(formData).length === 0) {
      // Initialize form data with default values from schema
      const initialFormData = generateDefaultValues(schemaToUse);
      setFormData({ ...initialFormData, ...initialData });
    }
  }, [schemaToUse, initialData, formData]);
  
  // Handle UI schema selection
  const handleUISchemaChange = (value: string | null) => {
    setSelectedUISchemaId(value);
  };
  
  // Get the selected UI schema
  const selectedUISchema = uiSchemas?.find((s: any) => s.id === selectedUISchemaId)?.content;
  
  // Handle form field change
  const handleFieldChange = (field: string, value: any) => {
    setFormData((prev: any) => {
      // Check if this is an array item update (field name contains brackets like "arrayField[0]")
      const arrayItemMatch = field.match(/^([^\[]+)\[(\d+)\]$/);
      
      if (arrayItemMatch) {
        // Extract array name and index
        const [_, arrayName, indexStr] = arrayItemMatch;
        const index = parseInt(indexStr, 10);
        
        // Make sure the array exists and is actually an array
        const currentArray = Array.isArray(prev[arrayName]) ? [...prev[arrayName]] : [];
        
        // Update the array item at the specified index
        currentArray[index] = value;
        
        // Return updated state with the modified array
        return {
          ...prev,
          [arrayName]: currentArray
        };
      }
      
      // Handle regular field updates (non-array items)
      return {
        ...prev,
        [field]: value
      };
    });
  };
  
  // Handle paste from clipboard for a specific field
  const handlePasteFromClipboard = async (field: string) => {
    try {
      const text = await navigator.clipboard.readText();
      try {
        // Try to parse as JSON
        const jsonData = JSON.parse(text);
        handleFieldChange(field, jsonData);
      } catch (e: any) {
        // If not valid JSON, just set as string
        handleFieldChange(field, text);
      }
    } catch (e: any) {
      alert('Failed to read from clipboard. Make sure you have granted clipboard permission.');
    }
  };
  
  // Handle generate sample data with AI
  const handleGenerateSampleData = async () => {
    try {
      if (!schemaToUse) {
        alert('No schema available to generate sample data');
        return;
      }
      
      setIsGeneratingAiData(true);
      
      // Check if OpenAI API key is configured
      if (!import.meta.env.VITE_OPENAI_API_KEY) {
        throw new Error('OpenAI API key is not configured. Please add VITE_OPENAI_API_KEY to your .env file.');
      }
      
      // Create a prompt for the AI based on the schema
      // Clone the schema and remove any complex references that might confuse the AI
      const schemaForAI = JSON.parse(JSON.stringify(schemaToUse));
      
      // Remove $id, $schema, and other metadata that's not needed for sample generation
      delete schemaForAI.$id;
      delete schemaForAI.$schema;
      
      const schemaJson = JSON.stringify(schemaForAI, null, 2);
      const prompt = `Generate a realistic sample JSON object that conforms to this JSON schema. The object should include values for all properties. Only return the JSON object, no explanations or markdown:\n\n${schemaJson}`;
      
      console.log('Generating sample data with schema:', schemaForAI);
      
      // Call OpenAI API
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            { role: 'system', content: 'You are a helpful assistant that generates sample data based on JSON schemas. Return only valid JSON that matches the schema.' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.statusText}. ${errorText}`);
      }
      
      const data = await response.json();
      const generatedText = data.choices[0]?.message?.content;
      
      if (!generatedText) {
        throw new Error('No sample data was generated');
      }
      
      console.log('Generated text:', generatedText);
      
      // Extract JSON from the response
      try {
        // Try to find JSON in the response
        const jsonMatch = generatedText.match(/```json\n([\s\S]*?)\n```/) || 
                          generatedText.match(/```\n([\s\S]*?)\n```/) || 
                          [null, generatedText];
        
        const jsonText = jsonMatch[1] || generatedText;
        
        // Clean up the text - remove any non-JSON content
        const cleanedText = jsonText.trim().replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        
        console.log('Cleaned JSON text:', cleanedText);
        
        const sampleData = JSON.parse(cleanedText);
        
        // Validate that it's an object
        if (typeof sampleData !== 'object' || sampleData === null || Array.isArray(sampleData)) {
          throw new Error('Generated data is not a valid object');
        }
        
        console.log('Parsed sample data:', sampleData);
        
        // Update form data with the generated sample
        setFormData((prev: any) => {
          const newData = { ...prev, ...sampleData };
          console.log('Updated form data:', newData);
          return newData;
        });
      } catch (e: any) {
        console.error('Failed to parse generated JSON:', e);
        alert(`Failed to parse the generated sample data: ${e.message || 'Unknown error'}`);
      }
    } catch (e: any) {
      console.error('Error generating sample data:', e);
      alert(`Failed to generate sample data: ${e.message || 'Unknown error'}`);
    } finally {
      setIsGeneratingAiData(false);
    }
  };
  
  // Handle paste entire object from clipboard
  const handlePasteEntireObject = async () => {
    try {
      const text = await navigator.clipboard.readText();
      try {
        // Try to parse as JSON
        const jsonData = JSON.parse(text);
        
        // Validate that it's an object
        if (typeof jsonData !== 'object' || jsonData === null || Array.isArray(jsonData)) {
          alert('Clipboard content is not a valid object');
          return;
        }
        
        // Update form data with the pasted object
        setFormData((prev: any) => ({
          ...prev,
          ...jsonData
        }));
      } catch (e: any) {
        alert('Clipboard content is not valid JSON');
      }
    } catch (e: any) {
      alert('Failed to read from clipboard. Make sure you have granted clipboard permission.');
    }
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };
  
  if ((isLoadingSchema && !resolvedSchema) || isLoadingUISchemas) {
    return <Loader size="md" />;
  }
  
  if ((schemaError && !resolvedSchema) || (!schemaToUse)) {
    return <Text color="red">Error loading schema</Text>;
  }
  
  // Get schema properties
  const schemaProperties = schemaToUse.properties || {};
  
  // Get property names and apply UI schema ordering if available
  let propertyNames = Object.keys(schemaProperties);
  if (selectedUISchema && selectedUISchema['ui:order']) {
    propertyNames = applyUIOrder(propertyNames, selectedUISchema['ui:order']);
  }
  
  // Handle resource selection
  const handleResourceSelect = (selectedResources: any) => {
    if (!currentResourceField) return;
    
    const { field, multiple, resourceType, onSelect } = currentResourceField;
    
    if (multiple) {
      // For multiple selection, create an array of reference objects with id and resourceType
      const referenceObjects = selectedResources.map((id: string) => ({
        id,
        resourceType
      }));
      if (onSelect) {
        onSelect(referenceObjects);
      } else {
        handleFieldChange(field, referenceObjects);
      }
    } else {
      // For single selection, create a reference object with id and resourceType
      console.log('selectedResources', selectedResources);
      console.log('field', field);
      if (onSelect) {
        onSelect({ id: selectedResources[0], resourceType });
      } else {
        handleFieldChange(field, { id: selectedResources[0], resourceType });
      }
    }
    
    setResourcePickerOpen(false);
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Resource Picker Modal */}
      <Modal
        opened={resourcePickerOpen}
        onClose={() => setResourcePickerOpen(false)}
        title={`Select ${currentResourceField?.resourceType || 'Resource'}`}
        size="lg"
      >
        {isLoadingResources ? (
          <Loader size="md" />
        ) : (
          <div>
            {resources && resources.length > 0 ? (
              <Stack>
                {resources.map((resource: any) => (
                  <Card key={resource.id} withBorder p="sm" mb={5}>
                    <Group justify="space-between">
                      <div>
                        <Text fw={500}>{resource.name || resource.title || resource.summary || resource.content || resource.email || resource.id}</Text>
                        {resource.description && (
                          <Text size="sm" color="dimmed">{resource.description}</Text>
                        )}
                      </div>
                      <Button
                        onClick={() => handleResourceSelect(currentResourceField?.multiple ? [resource.id] : [resource.id])}
                      >
                        Select
                      </Button>
                    </Group>
                  </Card>
                ))}
              </Stack>
            ) : (
              <Text>No {currentResourceField?.resourceType} resources found.</Text>
            )}
          </div>
        )}
      </Modal>
      
      {uiSchemas && uiSchemas.length > 0 && (
        <Select
          label="UI Schema"
          placeholder="Select a UI schema"
          data={uiSchemas.map((s: any) => ({ value: s.id, label: s.name }))}
          value={selectedUISchemaId}
          onChange={handleUISchemaChange}
          clearable
          mb="md"
        />
      )}
      
      <Card shadow="sm" padding="lg" radius="md" withBorder mb="xl">
        <Group justify="space-between" mb="md">
          <Title order={5}>Form Fields</Title>
          <Group>
            <Tooltip label="Generate sample data with AI">
              <ActionIcon 
                color="green" 
                onClick={handleGenerateSampleData}
                loading={isGeneratingAiData}
                disabled={isGeneratingAiData}
              >
                <IconRobot size={20} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Paste entire object from clipboard">
              <ActionIcon 
                color="blue" 
                onClick={handlePasteEntireObject}
              >
                <IconClipboardText size={20} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Expand all sections">
              <ActionIcon 
                color="gray" 
                onClick={() => setCollapsedItems({})}
              >
                <IconArrowsMaximize size={20} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Collapse all sections">
              <ActionIcon 
                color="gray" 
                onClick={() => {
                  // Create a new object with all items collapsed
                  const allCollapsed: Record<string, boolean> = {};
                  
                  // Helper function to recursively find all collapsible paths
                  const findCollapsiblePaths = (schema: any, path: string = '') => {
                    if (!schema || typeof schema !== 'object') return;
                    
                    // If it's an object with properties, mark it as collapsible
                    if (schema.type === 'object' && schema.properties) {
                      allCollapsed[path] = true;
                      
                      // Process nested properties
                      Object.entries(schema.properties).forEach(([propName, propSchema]: [string, any]) => {
                        findCollapsiblePaths(propSchema, path ? `${path}.${propName}` : propName);
                      });
                    }
                    
                    // If it's an array with items, mark each potential item as collapsible
                    if (schema.type === 'array' && schema.items) {
                      // We can't know how many items there are, but we can mark the pattern
                      for (let i = 0; i < (Array.isArray(formData[path]) ? formData[path].length : 0); i++) {
                        allCollapsed[`${path}[${i}]`] = true;
                      }
                      
                      // Process array item schema
                      findCollapsiblePaths(schema.items, '');
                    }
                  };
                  
                  // Start the recursive process with the root schema
                  if (schemaToUse && schemaToUse.properties) {
                    Object.entries(schemaToUse.properties).forEach(([propName, propSchema]: [string, any]) => {
                      findCollapsiblePaths(propSchema, propName);
                    });
                  }
                  
                  setCollapsedItems(allCollapsed);
                }}
              >
                <IconArrowsMinimize size={20} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
        
        {/* Breadcrumb navigation for nested context */}
        {currentPath.length > 0 && (
          <Box mb="md">
            <Breadcrumbs separator="→">
              <Anchor component="button" onClick={() => setCurrentPath([])}>
                Root
              </Anchor>
              {currentPath.map((segment, index) => {
                const isLast = index === currentPath.length - 1;
                const pathSoFar = currentPath.slice(0, index + 1).join('.');
                
                return isLast ? (
                  <Text key={index} fw={500}>{segment}</Text>
                ) : (
                  <Anchor 
                    key={index} 
                    component="button" 
                    onClick={() => setCurrentPath(currentPath.slice(0, index + 1))}
                  >
                    {segment}
                  </Anchor>
                );
              })}
            </Breadcrumbs>
          </Box>
        )}
        <Stack>
          {/* Render fields based on current path */}
          {currentPath.length === 0 ? (
            // Render root level fields when no path is selected
            propertyNames.map(propName => {
              const propSchema = schemaProperties[propName];
              
              // Skip if property shouldn't be displayed based on UI schema conditions
              if (selectedUISchema && !shouldDisplayProperty(propName, selectedUISchema, formData)) {
                return null;
              }
              
              // Get widget type from UI schema
              const widgetType = selectedUISchema ? getWidgetType(propName, selectedUISchema) : undefined;
              
              // Skip hidden widgets
              if (widgetType === 'hidden') {
                return null;
              }
              
              // Get widget options
              const widgetOptions = selectedUISchema ? getWidgetOptions(propName, selectedUISchema) : undefined;
              
              // Get field props from UI schema
              const fieldUISchema = selectedUISchema?.[propName] as UISchemaWidgetProps;
              
              // Get field label
              const fieldLabel = propSchema.title || formatPropertyName(propName);
              
              // Get field description
              const fieldDescription = propSchema.description || fieldUISchema?.['ui:description'];
              
              // Check if field is required
              const isRequired = schemaToUse.required?.includes(propName);
              
              // Check if field is read-only
              const isReadOnly = fieldUISchema?.['ui:readonly'] === true;
              
              return (
                <div key={propName}>
                  {renderFormField(
                    propName,
                    propSchema,
                    formData[propName],
                    (value) => handleFieldChange(propName, value),
                    {
                      label: fieldLabel,
                      description: fieldDescription,
                      required: isRequired,
                      readOnly: isReadOnly,
                      widgetType,
                      widgetOptions
                    },
                    {
                      setCurrentResourceField,
                      setResourcePickerOpen,
                      rootSchema: schemaToUse,
                      definitions: allDefinitions,
                      currentSchema: schemaToUse,
                      collapsedItems,
                      toggleItemCollapse,
                      setCurrentPath,
                      currentPath
                    }
                  )}
                </div>
              );
            })
          ) : (
            // Render focused object fields when a path is selected
            (() => {
              // Navigate to the focused object based on the current path
              let currentObj = formData;
              let currentObjSchema = schemaToUse;
              let currentPropName = '';
              
              // Follow the path to find the current object
              for (let i = 0; i < currentPath.length; i++) {
                const segment = currentPath[i];
                
                // Handle array items (e.g., "items[0]")
                const arrayMatch = segment.match(/^([^\[]+)\[(\d+)\]$/);
                if (arrayMatch) {
                  const [_, arrayName, indexStr] = arrayMatch;
                  const index = parseInt(indexStr, 10);
                  
                  // Update current object to the array item
                  currentObj = currentObj?.[arrayName]?.[index];
                  
                  // Update schema to the array item schema
                  if (currentObjSchema?.properties?.[arrayName]?.type === 'array') {
                    currentObjSchema = currentObjSchema.properties[arrayName].items;
                  }
                  
                  currentPropName = `${arrayName}[${index}]`;
                } else {
                  // Regular property
                  currentObj = currentObj?.[segment];
                  
                  // Update schema to the property schema
                  if (currentObjSchema?.properties?.[segment]) {
                    currentObjSchema = currentObjSchema.properties[segment];
                  }
                  
                  currentPropName = segment;
                }
              }
              
              // If we found a valid object and schema
              if (currentObj && currentObjSchema && currentObjSchema.properties) {
                // Get property names for the focused object
                let focusedPropertyNames = Object.keys(currentObjSchema.properties);
                
                // Apply UI schema ordering if available
                const focusedUISchema = selectedUISchema?.[currentPropName];
                if (focusedUISchema && focusedUISchema['ui:order']) {
                  focusedPropertyNames = applyUIOrder(focusedPropertyNames, focusedUISchema['ui:order']);
                }
                
                // Render fields for the focused object
                return focusedPropertyNames.map(propName => {
                  const propSchema = currentObjSchema.properties[propName];
                  
                  // Skip if property shouldn't be displayed
                  if (propName === 'id') return null;
                  
                  // Get field label
                  const fieldLabel = propSchema.title || formatPropertyName(propName);
                  
                  // Get field description
                  const fieldDescription = propSchema.description;
                  
                  // Check if field is required
                  const isFieldRequired = currentObjSchema.required?.includes(propName);
                  
                  // Get the full path for this property
                  const fullPath = [...currentPath, propName].join('.');
                  
                  return (
                    <Paper key={propName} p="xs" withBorder shadow="xs" style={{ borderColor: '#e0e0e0' }}>
                      {renderFormField(
                        propName,
                        propSchema,
                        currentObj[propName],
                        (newValue) => {
                          // Create a deep copy of the form data
                          const newFormData = { ...formData };
                          
                          // Navigate to the parent object
                          let parent = newFormData;
                          for (let i = 0; i < currentPath.length - 1; i++) {
                            const segment = currentPath[i];
                            parent = parent[segment];
                          }
                          
                          // Update the property in the parent object
                          const lastSegment = currentPath[currentPath.length - 1];
                          if (parent[lastSegment]) {
                            parent[lastSegment][propName] = newValue;
                          }
                          
                          // Update the form data
                          setFormData(newFormData);
                        },
                        {
                          label: fieldLabel,
                          description: fieldDescription,
                          required: isFieldRequired,
                          readOnly: false
                        },
                        {
                          setCurrentResourceField,
                          setResourcePickerOpen,
                          rootSchema: schemaToUse,
                          definitions: allDefinitions,
                          currentSchema: currentObjSchema,
                          collapsedItems,
                          toggleItemCollapse,
                          setCurrentPath,
                          currentPath
                        }
                      )}
                    </Paper>
                  );
                });
              }
              
              // If we couldn't find a valid object or schema, show an error
              return (
                <Text color="red">
                  Could not find object at path: {currentPath.join('.')}
                </Text>
              );
            })()
          )}
        </Stack>
      </Card>
      
      <Group justify="flex-end">
        <Button variant="light" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" loading={isSubmitting}>
          Submit
        </Button>
      </Group>
    </form>
  );
}

/**
 * Format a date value for a date input field
 * @param value The date value to format
 * @returns The formatted date string (YYYY-MM-DD)
 */
function formatDateForInput(value: any): string {
  if (!value) return '';
  
  try {
    // If it's already a date string in YYYY-MM-DD format, return it
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
      return value;
    }
    
    // If it's a date object, format it
    if (value instanceof Date) {
      return format(value, 'yyyy-MM-dd');
    }
    
    // If it's a string, try to parse it
    if (typeof value === 'string') {
      // Try to parse various date formats
      let date;
      
      // Try ISO format first
      date = parseISO(value);
      if (isValid(date)) {
        return format(date, 'yyyy-MM-dd');
      }
      
      // Try other common formats
      const formats = ['MM/dd/yyyy', 'MM-dd-yyyy', 'dd/MM/yyyy', 'dd-MM-yyyy', 'yyyy/MM/dd', 'yyyy.MM.dd'];
      for (const formatStr of formats) {
        date = parse(value, formatStr, new Date());
        if (isValid(date)) {
          return format(date, 'yyyy-MM-dd');
        }
      }
      
      // Try Unix timestamp (milliseconds)
      if (/^\d+$/.test(value)) {
        date = new Date(parseInt(value, 10));
        if (isValid(date)) {
          return format(date, 'yyyy-MM-dd');
        }
      }
    }
    
    return '';
  } catch (e) {
    console.error('Error formatting date:', e);
    return '';
  }
}

/**
 * Format a datetime value for a datetime-local input field
 * @param value The datetime value to format
 * @returns The formatted datetime string (YYYY-MM-DDTHH:MM)
 */
function formatDateTimeForInput(value: any): string {
  if (!value) return '';
  
  try {
    // If it's already a datetime string in YYYY-MM-DDTHH:MM format, return it
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?/.test(value)) {
      // Ensure it's in the format YYYY-MM-DDTHH:MM
      const date = parseISO(value);
      if (isValid(date)) {
        return format(date, "yyyy-MM-dd'T'HH:mm");
      }
      return value;
    }
    
    // If it's a date object, format it
    if (value instanceof Date) {
      return format(value, "yyyy-MM-dd'T'HH:mm");
    }
    
    // If it's a string, try to parse it
    if (typeof value === 'string') {
      // Try to parse the date string in various formats
      let date;
      
      // Try ISO format first
      date = parseISO(value);
      
      // If not valid, try other common formats
      if (!isValid(date)) {
        const formats = [
          'MM/dd/yyyy', 'MM-dd-yyyy', 'dd/MM/yyyy', 'dd-MM-yyyy', 'yyyy/MM/dd', 'yyyy.MM.dd',
          'MM/dd/yyyy HH:mm', 'MM-dd-yyyy HH:mm', 'dd/MM/yyyy HH:mm', 'yyyy/MM/dd HH:mm',
          'MM/dd/yyyy h:mm a', 'dd/MM/yyyy h:mm a', 'yyyy/MM/dd h:mm a'
        ];
        for (const formatStr of formats) {
          date = parse(value, formatStr, new Date());
          if (isValid(date)) break;
        }
      }
      if (isValid(date)) {
        return format(date, "yyyy-MM-dd'T'HH:mm");
      }
    }
    
    return '';
  } catch (e) {
    console.error('Error formatting datetime:', e);
    return '';
  }
}

/**
 * Format a property name for display
 * @param propertyName The property name to format
 * @returns The formatted property name
 */
function formatPropertyName(propertyName: string): string {
  // Insert a space before each capital letter and convert to lowercase
  const spaceSeparated = propertyName
    .replace(/([A-Z])/g, ' $1')
    .trim();
  
  // Capitalize the first letter of each word
  return spaceSeparated
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Generate default values based on a JSON schema
 * @param schema The JSON schema
 * @returns An object with default values
 */
function generateDefaultValues(schema: any): any {
  if (!schema || !schema.properties) {
    return {};
  }
  
  const result: any = {};
  
  for (const [propName, propSchema] of Object.entries<any>(schema.properties)) {
    // Use default value if provided
    if (propSchema.default !== undefined) {
      result[propName] = propSchema.default;
      continue;
    }
    
    // Generate default value based on type
    switch (propSchema.type) {
      case 'string':
        result[propName] = '';
        break;
      case 'number':
      case 'integer':
        result[propName] = null;
        break;
      case 'boolean':
        result[propName] = false;
        break;
      case 'array':
        result[propName] = [];
        break;
      case 'object':
        if (propSchema.properties) {
          result[propName] = generateDefaultValues(propSchema);
        } else {
          result[propName] = {};
        }
        break;
      default:
        result[propName] = null;
    }
  }
  
  return result;
}

/**
 * Render a form field based on the schema property
 * @param name Field name
 * @param schema Field schema
 * @param value Current field value
 * @param onChange Change handler
 * @param options Additional options
 * @returns Rendered form field
 */
function renderFormField(
  name: string,
  schema: any,
  value: any,
  onChange: (value: any) => void,
  options: {
    label: string;
    description?: string;
    required?: boolean;
    readOnly?: boolean;
    widgetType?: string;
    widgetOptions?: any;
  },
  context?: {
    setCurrentResourceField?: (field: { field: string; resourceType: string; multiple: boolean; onSelect?: (selectedResources: any) => void; } | null) => void;
    setResourcePickerOpen?: (open: boolean) => void;
    rootSchema?: any;
    definitions?: Record<string, any>;
    currentSchema?: any; // Added to track the current schema
    collapsedItems?: Record<string, boolean>;
    toggleItemCollapse?: (itemPath: string) => void;
    setCurrentPath?: (path: string[]) => void;
    currentPath?: string[];
  }
): React.ReactNode {
  // Skip rendering for ID field as it will be auto-generated
  if (name === 'id') {
    return null;
  }
  
  // For array items or nested properties, the schema itself might contain definitions
  const currentSchema = schema && typeof schema === 'object' ? schema : context?.currentSchema || context?.rootSchema;
  
  // Function to handle paste from clipboard for a field
  const handleFieldPaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      try {
        // Try to parse as JSON
        const jsonData = JSON.parse(text);
        onChange(jsonData);
      } catch (e) {
        // If not valid JSON, just set as string
        onChange(text);
      }
    } catch (e) {
      alert('Failed to read from clipboard. Make sure you have granted clipboard permission.');
    }
  };
  const { 
    label, 
    description, 
    required = false, 
    readOnly = false,
    widgetType,
    widgetOptions
  } = options;
  
  // Handle custom widget types
  if (widgetType) {
    switch (widgetType) {
      case 'textarea':
        return (
          <Textarea
            label={label}
            description={description}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            required={required}
            readOnly={readOnly}
            minRows={5}
          />
        );
      case 'date':
        return (
          <TextInput
            label={label}
            description={description}
            value={formatDateForInput(value) || ''}
            onChange={(e) => onChange(e.target.value)}
            required={required}
            readOnly={readOnly}
            type="date"
          />
        );
      case 'datetime':
        return (
          <TextInput
            label={label}
            description={description}
            value={formatDateTimeForInput(value) || ''}
            onChange={(e) => onChange(e.target.value)}
            required={required}
            readOnly={readOnly}
            type="datetime-local"
          />
        );
      case 'select':
        if (widgetOptions?.options) {
          return (
            <Select
              label={label}
              description={description}
              value={value || null}
              onChange={onChange}
              data={widgetOptions.options}
              required={required}
              readOnly={readOnly}
              clearable
            />
          );
        }
        break;
      case 'multiselect':
        if (widgetOptions?.options) {
          return (
            <MultiSelect
              label={label}
              description={description}
              value={value || []}
              onChange={onChange}
              data={widgetOptions.options}
              required={required}
              readOnly={readOnly}
            />
          );
        }
        break;
      case 'json':
        return (
          <div>
            <Group justify="space-between" mb={5}>
              <Text fw={500} size="sm">{label}</Text>
              <Tooltip label="Paste from clipboard">
                <ActionIcon 
                  size="sm" 
                  onClick={handleFieldPaste}
                  disabled={readOnly}
                >
                  <IconClipboard size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
            {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
            <JsonInput
              value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value || ''}
              onChange={(jsonStr) => {
                try {
                  const parsed = JSON.parse(jsonStr);
                  onChange(parsed);
                } catch (e: any) {
                  // If not valid JSON, store as string
                  onChange(jsonStr);
                }
              }}
              required={required}
              readOnly={readOnly}
              formatOnBlur
              minRows={5}
            />
          </div>
        );
    }
  }
  
  // There are two types of references in this system:
  // 1. Schema $refs: These point to schema definitions that need to be resolved
  // 2. Resource references: These are fields that reference other resources by ID
  
  // Handle resource reference fields (has format: 'reference' or specific pattern)
  if (schema.type === 'string' && schema.format === 'reference') {
    const referenceType = schema.referenceType;
    
    if (referenceType) {
      return (
        <div>
          <Group justify="space-between" mb={5}>
            <Text fw={500} size="sm">{label}</Text>
            <Tooltip label={`Select a ${referenceType}`}>
              <ActionIcon 
                size="sm" 
                onClick={() => {
                  if (context?.setCurrentResourceField && context?.setResourcePickerOpen) {
                    context.setCurrentResourceField({
                      field: name,
                      resourceType: referenceType,
                      multiple: false,
                      onSelect: onChange
                    });
                    context.setResourcePickerOpen(true);
                  } else {
                    alert(`Search for ${referenceType} references not implemented yet`);
                  }
                }}
                disabled={readOnly}
              >
                <IconSearch size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>
          {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
          <TextInput
            value={value?.id || value || ''}
            onChange={(e) => {
              // If we have a structured reference object format, maintain it
              if (value && typeof value === 'object' && 'id' in value) {
                onChange({
                  ...value,
                  id: e.target.value
                });
              } else {
                onChange(e.target.value);
              }
            }}
            placeholder={`Select a ${referenceType} reference`}
            required={required}
            readOnly={readOnly}
            disabled={!!context?.setResourcePickerOpen}
          />
        </div>
      );
    }
  }
  
  // Handle schema $refs (actual schema references, not resource references)
  if (schema.$ref) {
    // Try to resolve the reference
    const rootSchema = context?.rootSchema || {};
    const definitions = context?.definitions || {};
    const resolvedSchema = resolveSchemaRef(schema.$ref, rootSchema, definitions, currentSchema);
    
    if (resolvedSchema) {
      // If we successfully resolved the schema, render it
      return renderFormField(
        name,
        resolvedSchema,
        value,
        onChange,
        options,
        {
          ...context,
          currentSchema: currentSchema // Pass current schema down
        }
      );
    }
    
    // If we couldn't resolve it, show a placeholder
    return (
      <div>
        <Text fw={500} size="sm">{label}</Text>
        {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
        <Card withBorder p="sm" mb={10}>
          <Stack>
            <Text>
              This field references another schema: <code>{schema.$ref}</code>
            </Text>
            <Text size="sm" color="dimmed">
              Schema reference could not be resolved. Definitions may exist at multiple levels 
              (root, parent schemas, or UUID references).
            </Text>
            
            <JsonInput
              value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value || ''}
              onChange={(jsonStr) => {
                try {
                  const parsed = JSON.parse(jsonStr);
                  onChange(parsed);
                } catch (e: any) {
                  onChange(jsonStr);
                }
              }}
              formatOnBlur
              minRows={3}
            />
          </Stack>
        </Card>
      </div>
    );
  }
  
  // Handle resourceType const fields with id field
  if (schema.properties && 
      schema.properties.resourceType && 
      schema.properties.resourceType.const && 
      schema.properties.id) {
    const resourceType = schema.properties.resourceType.const;
    const isArray = schema.type === 'array';
    
    // Determine if we're dealing with a single resource or multiple resources
    const multiple = isArray;
    
    return (
      <div>
        <Group justify="space-between" mb={5}>
          <Text fw={500} size="sm">{label}</Text>
          <Tooltip label={`Select ${multiple ? 'multiple' : 'a'} ${resourceType}`}>
            <ActionIcon 
              size="sm" 
              onClick={() => {
                if (context?.setCurrentResourceField && context?.setResourcePickerOpen) {
                  context.setCurrentResourceField({
                    field: name,
                    resourceType,
                    multiple,
                    onSelect: onChange
                  });
                  context.setResourcePickerOpen(true);
                }
              }}
              disabled={readOnly}
            >
              <IconSearch size={16} />
            </ActionIcon>
          </Tooltip>
        </Group>
        {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
        
        {multiple ? (
          <MultiSelect
            value={Array.isArray(value) ? value.map(v => v.id || v) : []}
            onChange={(newValue) => {
              // Convert array of IDs to array of reference objects
              const referenceObjects = newValue.map((id: string) => ({
                id,
                resourceType
              }));
              onChange(referenceObjects);
            }}
            placeholder={`Select ${resourceType} resources`}
            data={[]}
            readOnly={readOnly}
            disabled
          />
        ) : (
          <TextInput
            value={value?.id || value || ''}
            onChange={(e) => {
              // Convert ID to reference object
              onChange({
                id: e.target.value,
                resourceType
              });
            }}
            placeholder={`Select a ${resourceType} resource`}
            required={required}
            readOnly={readOnly}
            disabled
          />
        )}
      </div>
    );
  }
  
  // Default rendering based on schema type
  switch (schema.type) {
    case 'string':
      // Handle enum as select
      if (schema.enum) {
        return (
          <Select
            label={label}
            description={description}
            value={value || null}
            onChange={onChange}
            data={schema.enum.map((item: string) => ({ value: item, label: item }))}
            required={required}
            readOnly={readOnly}
            clearable
          />
        );
      }
      
      // Handle specific formats
      if (schema.format) {
        switch (schema.format) {
          case 'date':
            return (
              <TextInput
                label={label}
                description={description}
                value={formatDateForInput(value) || ''}
                onChange={(e) => onChange(e.target.value)}
                required={required}
                readOnly={readOnly}
                type="date"
              />
            );
          case 'date-time':
            return (
              <TextInput
                label={label}
                description={description}
                value={formatDateTimeForInput(value) || ''}
                onChange={(e) => onChange(e.target.value)}
                required={required}
                readOnly={readOnly}
                type="datetime-local"
              />
            );
          case 'email':
            return (
              <TextInput
                label={label}
                description={description}
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                required={required}
                readOnly={readOnly}
                type="email"
              />
            );
          case 'uri':
          case 'url':
            return (
              <TextInput
                label={label}
                description={description}
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                required={required}
                readOnly={readOnly}
                type="url"
              />
            );
        }
      }
      
      // Default to text input
      return (
        <TextInput
          label={label}
          description={description}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={required}
          readOnly={readOnly}
        />
      );
    
    case 'number':
    case 'integer':
      return (
        <NumberInput
          label={label}
          description={description}
          value={value}
          onChange={onChange}
          required={required}
          readOnly={readOnly}
          min={schema.minimum}
          max={schema.maximum}
          step={schema.type === 'integer' ? 1 : 0.01}
        />
      );
    
    case 'boolean':
      return (
        <Switch
          label={label}
          description={description}
          checked={value || false}
          onChange={(e) => onChange(e.currentTarget.checked)}
          readOnly={readOnly}
        />
      );
    
    case 'array':
      // If the array has items with a defined schema, render a dynamic array form
      if (schema.items && (schema.items.type || schema.items.properties || schema.items.$ref)) {
        const arrayValue = Array.isArray(value) ? value : [];
        
        // If schema.items has a $ref, try to resolve it before rendering
        let itemsSchema = schema.items;
        if (schema.items.$ref) {
          const rootSchema = context?.rootSchema || {};
          const definitions = context?.definitions || {};
          const resolvedItemsSchema = resolveSchemaRef(
            schema.items.$ref, 
            rootSchema, 
            definitions, 
            schema // Pass the current schema (array schema) which may contain definitions
          );
          if (resolvedItemsSchema) {
            itemsSchema = { ...resolvedItemsSchema, title: resolvedItemsSchema.title || itemsSchema.title };
          }
        }
        
        return (
          <div>
            <Text fw={500} size="sm" mb={5}>{label}</Text>
            {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
            
            {arrayValue.map((item, index) => {
              // Create a unique path for this array item
              const itemPath = `${name}[${index}]`;
              // Check if this item is collapsed
              const isCollapsed = context?.collapsedItems?.[itemPath] || false;
              
              return (
                <Card key={index} withBorder p="sm" mb={10}>
                  <Group justify="space-between" mb={10}>
                    <Group>
                      {/* Collapse/Expand toggle button */}
                      <ActionIcon
                        onClick={() => context?.toggleItemCollapse?.(itemPath)}
                        size="sm"
                        variant="subtle"
                      >
                        {isCollapsed ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
                      </ActionIcon>
                      <Text size="sm" fw={500}>
                        {itemsSchema.title || `Item ${index + 1}`}
                      </Text>
                    </Group>
                    {!readOnly && (
                      <ActionIcon 
                        color="red" 
                        onClick={() => {
                          const newArray = [...arrayValue];
                          newArray.splice(index, 1);
                          onChange(newArray);
                        }}
                        disabled={readOnly}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    )}
                  </Group>
                  
                  {/* Only render the form field if not collapsed */}
                  {!isCollapsed && renderFormField(
                    itemPath,
                    itemsSchema,
                    item,
                    (newValue) => {
                      const newArray = [...arrayValue];
                      newArray[index] = newValue;
                      onChange(newArray);
                    },
                    {
                      label: '', // Remove the duplicate label
                      readOnly: readOnly
                    },
                    {
                      ...context,
                      // Pass down the rootSchema and definitions to nested fields
                      rootSchema: context?.rootSchema,
                      definitions: context?.definitions,
                      currentSchema: schema // Pass the array schema which may contain definitions
                    }
                  )}
                </Card>
              );
            })}
            
            {!readOnly && (
              <Button 
                leftSection={<IconPlus size={16} />} 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  // Create a default value for the new item based on the items schema
                  let newItem;
                  
                  if (schema.items.$ref) {
                    // For schema references, create default values based on resolved schema
                    const rootSchema = context?.rootSchema || {};
                    const definitions = context?.definitions || {};
                    const resolvedItemsSchema = resolveSchemaRef(
                      schema.items.$ref, 
                      rootSchema, 
                      definitions,
                      schema // Pass the current schema (array schema)
                    );
                    if (resolvedItemsSchema) {
                      newItem = generateDefaultValues(resolvedItemsSchema);
                    } else {
                      // Fallback to empty object if schema can't be resolved
                      newItem = {};
                    }
                  } else if (schema.items.type === 'object' && schema.items.properties) {
                    newItem = generateDefaultValues(schema.items);
                  } else if (schema.items.type === 'string') {
                    newItem = '';
                  } else if (schema.items.type === 'number' || schema.items.type === 'integer') {
                    newItem = null;
                  } else if (schema.items.type === 'boolean') {
                    newItem = false;
                  } else if (schema.items.type === 'array') {
                    newItem = [];
                  } else {
                    newItem = null;
                  }
                  
                  onChange([...arrayValue, newItem]);
                }}
              >
                Add {itemsSchema.title || 'Item'}
              </Button>
            )}
          </div>
        );
      } else {
        // For arrays without a defined item schema, use a JSON input
        return (
          <div>
            <Group justify="space-between" mb={5}>
              <Text fw={500} size="sm">{label}</Text>
              <Tooltip label="Paste from clipboard">
                <ActionIcon 
                  size="sm" 
                  onClick={handleFieldPaste}
                  disabled={readOnly}
                >
                  <IconClipboard size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
            {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
            <JsonInput
              value={Array.isArray(value) ? JSON.stringify(value, null, 2) : '[]'}
              onChange={(jsonStr) => {
                try {
                  const parsed = JSON.parse(jsonStr);
                  onChange(Array.isArray(parsed) ? parsed : []);
                } catch (e: any) {
                  // If not valid JSON, store as empty array
                  onChange([]);
                }
              }}
              required={required}
              readOnly={readOnly}
              formatOnBlur
              minRows={5}
            />
          </div>
        );
      }
    
    case 'object':
      // If the object has properties, render a nested form
      if (schema.properties) {
        // Create a unique path for this object
        const objectPath = name;
        // Check if this object is collapsed
        const isCollapsed = context?.collapsedItems?.[objectPath] || false;

        return (
          <div>
            <Card withBorder shadow="xs" p="md" mb={10} 
                  style={{ 
                    border: '1px solid #e0e0e0',
                    borderLeft: '3px solid #228be6'
                  }}>
              <Group justify="space-between" mb={isCollapsed ? 0 : 10}>
                <Group>
                  {/* Collapse/Expand toggle button */}
                  <ActionIcon
                    onClick={() => context?.toggleItemCollapse?.(objectPath)}
                    size="sm"
                    variant="subtle"
                  >
                    {isCollapsed ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
                  </ActionIcon>
                  <div>
                    <Text fw={500} size="sm">{label}</Text>
                    {description && <Text size="xs" color="dimmed">{description}</Text>}
                  </div>
                </Group>
                
                {/* Object type indicator */}
                <Group>
                  <Badge color="blue" size="sm" leftSection={<IconBrackets size={12} />}>
                    Object
                  </Badge>
                  {/* Focus button to navigate into this object */}
                  <Tooltip label="Focus on this object">
                    <ActionIcon 
                      size="sm" 
                      color="blue" 
                      variant="light"
                      onClick={() => {
                        if (context?.setCurrentPath) {
                          // Extract the object name from the path
                          const objectName = name.includes('.') 
                            ? name.split('.').pop() || name 
                            : name;
                          
                          // Update the current path to focus on this object
                          context.setCurrentPath([...(context.currentPath || []), objectName]);
                        }
                      }}
                    >
                      <IconMaximize size={14} />
                    </ActionIcon>
                  </Tooltip>
                </Group>
              </Group>
              
              {/* Only render the form fields if not collapsed */}
              <Collapse in={!isCollapsed}>
                <Box mt={15}>
                  <Stack style={{ gap: '8px' }}>
                    {Object.entries(schema.properties).map(([propName, propSchema]: [string, any]) => {
                      // Skip if property shouldn't be displayed
                      if (propName === 'id') return null;
                      
                      // Get field label
                      const fieldLabel = propSchema.title || formatPropertyName(propName);
                      
                      // Get field description
                      const fieldDescription = propSchema.description;
                      
                      // Check if field is required
                      const isFieldRequired = schema.required?.includes(propName);
                      
                      const nestedValue = value && typeof value === 'object' ? value[propName] : undefined;
                      
                      // Create a nested field path for context
                      const nestedPath = `${name}.${propName}`;
                      
                      return (
                        <Paper key={propName} p="xs" withBorder shadow="xs"
                               style={{
                                 borderColor: '#e0e0e0'
                               }}>
                          {renderFormField(
                            propName,
                            propSchema,
                            nestedValue,
                            (newValue) => {
                              const updatedObj = { ...(typeof value === 'object' ? value : {}) };
                              updatedObj[propName] = newValue;
                              onChange(updatedObj);
                            },
                            {
                              label: fieldLabel,
                              description: fieldDescription,
                              required: isFieldRequired,
                              readOnly: readOnly
                            },
                            {
                              ...context,
                              // Pass down the rootSchema and definitions to nested fields
                              rootSchema: context?.rootSchema,
                              definitions: context?.definitions,
                              currentSchema: schema // Pass the object schema which may contain definitions
                            }
                          )}
                        </Paper>
                      );
                    })}
                  </Stack>
                </Box>
              </Collapse>
            </Card>
          </div>
        );
      } else {
        // For objects without defined properties, use a JSON input
        return (
          <div>
            <Group justify="space-between" mb={5}>
              <Text fw={500} size="sm">{label}</Text>
              <Tooltip label="Paste from clipboard">
                <ActionIcon 
                  size="sm" 
                    onClick={handleFieldPaste}
                  disabled={readOnly}
                >
                  <IconClipboard size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
            {description && <Text size="xs" color="dimmed" mb={5}>{description}</Text>}
            <JsonInput
              value={typeof value === 'object' ? JSON.stringify(value, null, 2) : '{}'}
              onChange={(jsonStr) => {
                try {
                  const parsed = JSON.parse(jsonStr);
                  onChange(parsed);
                } catch (e: any) {
                  // If not valid JSON, store as empty object
                  onChange({});
                }
              }}
              required={required}
              readOnly={readOnly}
              formatOnBlur
              minRows={5}
            />
          </div>
        );
      }
    
    default:
      // Fallback for unknown types
      return (
        <TextInput
          label={label}
          description={description}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={required}
          readOnly={readOnly}
        />
      );
  }
}
