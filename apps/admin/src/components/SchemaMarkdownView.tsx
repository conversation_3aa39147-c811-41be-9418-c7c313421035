import { useState, useEffect, useCallback } from 'react';
import { UISchema } from '../types/uiSchema';
import { 
  Grid, 
  Paper, 
  Title, 
  JsonInput, 
  Box, 
  Text,
  Tabs,
  Code,
  Group,
  CopyButton,
  ActionIcon,
  Tooltip
} from '@mantine/core';
import { IconCopy, IconCheck } from '@tabler/icons-react';
import { CollapsibleMarkdown } from './CollapsibleMarkdown';
import '../styles/markdown.css';
import { schemaToMarkdown } from '../utils/schemaToMarkdown';
import { useGetSchemaQuery } from '../store/api';

interface SchemaMarkdownViewProps {
  schema: any;
  schemaContent: string;
  uiSchema?: UISchema;
}

export function SchemaMarkdownView({ schema, schemaContent, uiSchema }: SchemaMarkdownViewProps) {
  const [markdownContent, setMarkdownContent] = useState('');
  
  // Function to fetch a schema by ID
  const fetchSchema = useCallback(async (schemaId: string) => {
    try {
      // Use RTK Query's fetchBaseQuery directly to get the schema
      const response = await fetch(`${import.meta.env.API_BASE_URL}/api/admin/schemas/${schemaId}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch schema: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.schema; // Return just the schema part
    } catch (error) {
      console.error('Error fetching schema:', error);
      return null;
    }
  }, []);
  
  // Generate markdown when schema or uiSchema changes
  useEffect(() => {
    if (schema) {
      const generateMarkdown = async () => {
        const markdown = await schemaToMarkdown(schema, fetchSchema, uiSchema);
        setMarkdownContent(markdown);
      };
      
      generateMarkdown();
    }
  }, [schema, fetchSchema, uiSchema]);
  
  return (
    <Box>
      <Title order={4} mb="md">Schema Data as Markdown</Title>
      
      <Tabs defaultValue="side-by-side">
        <Tabs.List>
          <Tabs.Tab value="side-by-side">Side by Side</Tabs.Tab>
          <Tabs.Tab value="markdown">Markdown View</Tabs.Tab>
          <Tabs.Tab value="json">JSON Schema</Tabs.Tab>
        </Tabs.List>
        
        <Tabs.Panel value="side-by-side" pt="md">
          <Grid>
            <Grid.Col span={6}>
              <Paper p="md" withBorder>
                <Group justify="space-between" mb="md">
                  <Text fw={500}>JSON Schema</Text>
                  <CopyButton value={schemaContent} timeout={2000}>
                    {({ copied, copy }) => (
                      <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                        <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                          {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                        </ActionIcon>
                      </Tooltip>
                    )}
                  </CopyButton>
                </Group>
                <JsonInput
                  value={schemaContent}
                  readOnly
                  minRows={20}
                  formatOnBlur
                  autosize
                  styles={{ input: { fontFamily: 'monospace' } }}
                />
              </Paper>
            </Grid.Col>
            
            <Grid.Col span={6}>
              <Paper p="md" withBorder>
                <Group justify="space-between" mb="md">
                  <Text fw={500}>Markdown View</Text>
                  <CopyButton value={markdownContent} timeout={2000}>
                    {({ copied, copy }) => (
                      <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                        <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                          {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                        </ActionIcon>
                      </Tooltip>
                    )}
                  </CopyButton>
                </Group>
                <Paper p="md" withBorder style={{ maxHeight: '500px', overflow: 'auto' }}>
                  <CollapsibleMarkdown markdown={markdownContent} />
                </Paper>
              </Paper>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
        
        <Tabs.Panel value="json" pt="md">
          <Paper p="md" withBorder>
            <Group justify="space-between" mb="md">
              <Text fw={500}>JSON Schema</Text>
              <CopyButton value={schemaContent} timeout={2000}>
                {({ copied, copy }) => (
                  <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                    <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                      {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                    </ActionIcon>
                  </Tooltip>
                )}
              </CopyButton>
            </Group>
            <JsonInput
              value={schemaContent}
              readOnly
              minRows={30}
              formatOnBlur
              autosize
              styles={{ input: { fontFamily: 'monospace' } }}
            />
          </Paper>
        </Tabs.Panel>
        
        <Tabs.Panel value="markdown" pt="md">
          <Paper p="md" withBorder>
            <Group justify="space-between" mb="md">
              <Text fw={500}>Markdown View</Text>
              <CopyButton value={markdownContent} timeout={2000}>
                {({ copied, copy }) => (
                  <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
                    <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy}>
                      {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                    </ActionIcon>
                  </Tooltip>
                )}
              </CopyButton>
            </Group>
            <Paper p="md" withBorder style={{ maxHeight: '600px', overflow: 'auto' }}>
              <CollapsibleMarkdown markdown={markdownContent} />
            </Paper>
          </Paper>
        </Tabs.Panel>
      </Tabs>
    </Box>
  );
}
