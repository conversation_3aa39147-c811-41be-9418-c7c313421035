import { useState, useEffect, useMemo } from 'react';
import { useGetSchemasQuery } from '../store/api';
import { useSchemaEditor } from '../hooks/useSchemaEditor';
import { 
  Card, 
  Text, 
  Group, 
  Badge, 
  Accordion, 
  Button, 
  ActionIcon, 
  Tooltip,
  Menu,
  TextInput,
  Stack
} from '@mantine/core';
import { IconPlus, IconTrash, IconSettings, IconChevronDown, IconChevronRight, IconDragDrop, IconArrowUp, IconArrowDown } from '@tabler/icons-react';

interface SchemaProperty {
  name: string;
  type: string;
  format?: string;
  required?: boolean;
  properties?: Record<string, SchemaProperty>;
  items?: SchemaProperty;
  description?: string;
  [key: string]: any;
}

interface SchemaPropertyTreeProps {
  schema: any;
  selectedProperty: string | null;
  onSelectProperty: (path: string) => void;
  onAddProperty: (path: string) => void;
  onDeleteProperty: (path: string) => void;
  renderObjectActions?: (path: string, property: any) => React.ReactNode | null;
}

export function SchemaPropertyTree({ 
  schema, 
  selectedProperty, 
  onSelectProperty, 
  onAddProperty, 
  onDeleteProperty,
  renderObjectActions
}: SchemaPropertyTreeProps) {
  // Get reorderSchemaProperty function from the hook
  const { reorderSchemaProperty } = useSchemaEditor();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedProperties, setExpandedProperties] = useState<string[]>([]);
  
  // Fetch available schemas for reference lookup
  const { data: availableSchemas } = useGetSchemasQuery(undefined);
  
  // Create lookup map of schema IDs to names
  const schemaNameMap = useMemo(() => {
    const map: Record<string, string> = {};
    if (availableSchemas) {
      availableSchemas.forEach((schema: any) => {
        map[schema.id] = `${schema.name} v${schema.version}`;
      });
    }
    return map;
  }, [availableSchemas]);
  
  // Get schema name by ID
  const getSchemaName = (id: string): string => {
    if (id.includes('://')) {
      // Extract ID from URL format
      const schemaId = id.split('/').pop() || '';
      return schemaNameMap[schemaId] || 'Unknown Schema';
    }
    return schemaNameMap[id] || 'Unknown Schema';
  };

  // Flatten properties for search
  const flattenProperties = (props: Record<string, any> | undefined, parentPath = ''): Record<string, { path: string; prop: any }> => {
    if (!props) return {};
    
    let flattened: Record<string, { path: string; prop: any }> = {};
    
    Object.entries(props).forEach(([name, prop]) => {
      const currentPath = parentPath ? `${parentPath}.properties.${name}` : `properties.${name}`;
      flattened[currentPath] = { path: currentPath, prop };
      
      if (prop.properties) {
        const nestedFlattened = flattenProperties(prop.properties, currentPath);
        flattened = { ...flattened, ...nestedFlattened };
      }
      
      if (prop.items?.properties) {
        const nestedFlattened = flattenProperties(prop.items.properties, `${currentPath}.items`);
        flattened = { ...flattened, ...nestedFlattened };
      }
    });
    
    return flattened;
  };

  // Filter properties based on search
  const filterProperties = () => {
    if (!searchQuery.trim()) return null;
    
    const flattened = flattenProperties(schema.properties);
    const filtered = Object.entries(flattened).filter(([path, { prop }]) => {
      return prop.name?.toLowerCase().includes(searchQuery.toLowerCase()) || 
             path.toLowerCase().includes(searchQuery.toLowerCase()) ||
             prop.description?.toLowerCase().includes(searchQuery.toLowerCase());
    });
    
    return filtered.map(([path, { prop }]) => ({
      path,
      name: prop.name || path.split('.').pop(),
      type: prop.type
    }));
  };

  // Toggle property expansion
  const toggleExpand = (path: string) => {
    if (expandedProperties.includes(path)) {
      setExpandedProperties(expandedProperties.filter(p => p !== path));
    } else {
      setExpandedProperties([...expandedProperties, path]);
    }
  };

  // Check if a property is expanded
  const isExpanded = (path: string) => {
    return expandedProperties.includes(path);
  };

  // Expand path to a specific property
  const expandPathTo = (path: string) => {
    const segments = path.split('.');
    const expandPaths: string[] = [];
    
    let currentPath = '';
    for (const segment of segments) {
      if (currentPath) {
        currentPath += '.' + segment;
      } else {
        currentPath = segment;
      }
      
      if (currentPath !== path) { // Don't include the final path
        expandPaths.push(currentPath);
      }
    }
    
    setExpandedProperties([...new Set([...expandedProperties, ...expandPaths])]);
  };

  // Handle property selection
  const handleSelectProperty = (path: string) => {
    // Always log the path being selected to diagnose the issue
    console.log("Selecting property path:", path);
    onSelectProperty(path);
  };

  // Get property display type
  const getPropertyDisplayType = (property: any): string => {
    if (property.$ref) {
      return 'reference';
    }
    return property.type || 'unknown';
  };

  // Get reference type and target
  const getRefInfo = (ref: string): { type: string, target: string } => {
    if (ref.startsWith('#/')) {
      return { type: 'internal', target: ref.substring(2) };
    } else if (ref.startsWith('http') || ref.startsWith('https')) {
      return { type: 'url', target: ref };
    } else {
      return { type: 'uuid', target: ref };
    }
  };

  // Helper to find parent object and check required status
  const isPropertyRequiredInParent = (schema: any, path: string): boolean => {
    if (!path || !path.includes('.')) return false; // Root properties aren't required *by* a parent

    const pathParts = path.split('.');
    const propertyName = pathParts[pathParts.length - 1];
    
    // Get parent path
    const parentPathParts = [...pathParts];
    parentPathParts.pop(); // Remove property name
    if (parentPathParts[parentPathParts.length - 1] === 'properties') {
      parentPathParts.pop(); // Remove 'properties'
    }
    const parentPath = parentPathParts.join('.');

    // Find parent object
    let parentObject = parentPath ? getPropertyByPath(schema, parentPath) : schema;
    
    // Special handling for allOf items
    if (parentPath.startsWith('allOf.')) {
      const match = parentPath.match(/^allOf\.(\d+)$/);
      if (match) {
        const index = parseInt(match[1], 10);
        if (schema.allOf && schema.allOf[index]) {
          parentObject = schema.allOf[index];
        }
      }
    }

    return parentObject?.required?.includes(propertyName) ?? false;
  };

  // Get property by path (needed for parent lookup)
  const getPropertyByPath = (obj: any, path: string) => {
    if (!path) return obj;
    const parts = path.split('.');
    let current = obj;
    for (const part of parts) {
      if (current?.[part] === undefined) return undefined;
      current = current[part];
    }
    return current;
  };

  // Render a property node
  const renderPropertyNode = (name: string, property: any, path: string) => {
    const propertyType = getPropertyDisplayType(property);
    const isObject = propertyType === 'object';
    const isArray = propertyType === 'array';
    const isReference = propertyType === 'reference';
    const hasProperties = isObject && property.properties && Object.keys(property.properties).length > 0;
    const hasArrayItems = isArray && property.items;
    const hasRefInItems = isArray && property.items && property.items.$ref;
    const isSelected = selectedProperty === path;
    
    return (
      <Card 
        key={path} 
        shadow="xs" 
        p="xs" 
        mb="xs" 
        withBorder
        style={{ 
          cursor: 'pointer',
          backgroundColor: isSelected ? '#f0f9ff' : undefined,
          border: isSelected ? '1px solid #228be6' : undefined
        }}
        onClick={(e) => {
          // Only trigger selection if the click is not on an ActionIcon or Button
          const tag = (e.target as HTMLElement).tagName;
          if (tag !== 'BUTTON' && tag !== 'svg' && tag !== 'path') {
            handleSelectProperty(path);
          }
        }}
      >
        <Group justify="space-between">
          <Group>
            {(hasProperties || (hasArrayItems && property.items.type === 'object')) && (
              <ActionIcon 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpand(path);
                }}
              >
                {isExpanded(path) ? <IconChevronDown size={16} /> : <IconChevronRight size={16} />}
              </ActionIcon>
            )}
            
            <div>
              <Group>
                <Text fw={500}>{name}</Text>
                {property.$ref ? (
                  <Badge color="purple">
                    {getRefInfo(property.$ref).type === 'internal' 
                      ? 'ref: ' + getRefInfo(property.$ref).target 
                      : getRefInfo(property.$ref).type + ' ref'}
                  </Badge>
                ) : (
                  <Badge color={getTypeColor(property.type)}>{property.type}</Badge>
                )}
                {isPropertyRequiredInParent(schema, path) && <Badge color="red">Required</Badge>}
                {property.format && <Badge color="grape">{property.format}</Badge>}
                {isArray && property.items && property.items.$ref && (
                  <Badge color="pink">items: {getRefInfo(property.items.$ref).type} ref</Badge>
                )}
              </Group>
              
              {property.description && (
                <Text size="xs" color="dimmed">{property.description}</Text>
              )}
            </div>
          </Group>
          
          <Group>
            <Group gap={5}>
              {/* Reorder controls for root properties */}
              {path.startsWith('properties.') && !path.includes('.properties.') && (
                <>
                  <Tooltip label="Move Up">
                    <ActionIcon
                      size="sm"
                      color="indigo"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Extract the property name from path
                        const propName = path.split('.')[1];
                        // Directly call the reorder function
                        reorderSchemaProperty('root', propName, 'up');
                      }}
                    >
                      <IconArrowUp size={16} />
                    </ActionIcon>
                  </Tooltip>
                  
                  <Tooltip label="Move Down">
                    <ActionIcon
                      size="sm"
                      color="indigo"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Extract the property name from path
                        const propName = path.split('.')[1];
                        // Directly call the reorder function
                        reorderSchemaProperty('root', propName, 'down');
                      }}
                    >
                      <IconArrowDown size={16} />
                    </ActionIcon>
                  </Tooltip>
                </>
              )}
              
              {/* Custom object actions */}
              {renderObjectActions && renderObjectActions(path, property)}
              
              {/* Add explicit Edit button */}
              <Button
                size="xs"
                variant="light"
                color="blue"
                onClick={(e) => {
                  e.stopPropagation();
                  console.log("Explicit edit button clicked for path:", path);
                  handleSelectProperty(path);
                }}
              >
                Edit
              </Button>
              
              {/* Add child property button for object types */}
              {isObject && (
                <Button
                  size="xs"
                  variant="light"
                  color="green"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log("Add child property button clicked for path:", path);
                    onAddProperty(path);
                  }}
                >
                  Add Child
                </Button>
              )}
              
              <Tooltip label="Delete Property">
                <ActionIcon 
                  size="sm" 
                  color="red"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteProperty(path);
                  }}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>
        </Group>
        
        {isExpanded(path) && (
          <div style={{ paddingLeft: 20, marginTop: 8 }}>
            {isObject && hasProperties && Object.entries(property.properties).map(([childName, childProp]) => 
              renderPropertyNode(childName, childProp, `${path}.properties.${childName}`)
            )}
            
            {isArray && (
              <>
                {/* Render the items property as a node */}
                <Card 
                  shadow="xs" 
                  p="xs" 
                  mb="xs" 
                  withBorder
                  style={{ 
                    cursor: 'pointer',
                    backgroundColor: selectedProperty === `${path}.items` ? '#f0f9ff' : undefined,
                    border: selectedProperty === `${path}.items` ? '1px solid #228be6' : undefined
                  }}
                  onClick={() => handleSelectProperty(`${path}.items`)}
                >
                  <Stack>
                  <Group justify="space-between">
                    <Group>
                      <div>
                        <Group>
                          <Text fw={500}>Array Items</Text>
                          {property.items.$ref ? (
                            <Badge color="purple">
                              {getRefInfo(property.items.$ref).type === 'internal' 
                                ? 'ref: ' + getRefInfo(property.items.$ref).target 
                                : getRefInfo(property.items.$ref).type + ' ref'}
                            </Badge>
                          ) : (
                            <Badge color={getTypeColor(property.items.type)}>{property.items.type}</Badge>
                          )}
                        </Group>
                        
                        {property.items.description && (
                          <Text size="xs" color="dimmed">{property.items.description}</Text>
                        )}
                      </div>
                      <Button
                        size="xs"
                        variant="light"
                        color="blue"
                        onClick={(e) => {
                          e.stopPropagation();
                          console.log("Explicit edit button clicked for path:", path);
                          handleSelectProperty(`${path}.items`);
                        }}
                      >
                        Edit
                      </Button>
                    </Group>
                  </Group>
                  
                  {/* If items is an object with properties, render those properties */}
                  {hasArrayItems && property.items.type === 'object' && property.items.properties && (
                    <>
                      {Object.entries(property.items.properties).map(([childName, childProp]) => 
                        renderPropertyNode(childName, childProp, `${path}.items.properties.${childName}`)
                      )}
                    </>
                  )}
                  </Stack>
                </Card>
              </>
            )}
          </div>
        )}
      </Card>
    );
  };

  // Get color based on property type
  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'string': return 'blue';
      case 'number': return 'green';
      case 'integer': return 'teal';
      case 'boolean': return 'yellow';
      case 'object': return 'indigo';
      case 'array': return 'violet';
      default: return 'gray';
    }
  };

  // Render filtered search results
  const renderSearchResults = () => {
    const filtered = filterProperties();
    if (!filtered || filtered.length === 0) return null;
    
    return (
      <Card shadow="xs" p="md" withBorder mb="md">
        <Text fw={500} mb="sm">Search Results</Text>
        {filtered.map((result) => (
          <Button 
            key={result.path} 
            variant="light" 
            size="xs" 
            mb="xs" 
            fullWidth
            onClick={() => {
              expandPathTo(result.path);
              handleSelectProperty(result.path);
            }}
            leftSection={<Badge color={getTypeColor(result.type)}>{result.type}</Badge>}
          >
            {result.name}
          </Button>
        ))}
      </Card>
    );
  };

  // Render the allOf section
  const renderAllOf = () => {
    if (!schema.allOf || !Array.isArray(schema.allOf) || schema.allOf.length === 0) {
      return null;
    }

    return (
      <div>
        <Text fw={500} mb="sm">Schema Inheritance (allOf)</Text>
        {schema.allOf.map((item: any, index: number) => (
          <Card key={index} shadow="xs" p="xs" mb="md" withBorder>
            {item.$ref ? (
              // Base schema reference
              <Group justify="space-between">
                <Group>
                  <Text fw={500}>Base Schema:</Text>
                  <Badge 
                      color="cyan" 
                      size="sm" 
                    >
                       {getSchemaName(item.$ref.includes('://') ? item.$ref.split('/').pop() || '' : item.$ref)
                       + ' (' + (item.$ref.includes('://') ? item.$ref.split('/').pop() : item.$ref)+')'}
                  </Badge>
                </Group>
                <Button
                  component="a"
                  href={`/schemas/${item.$ref.includes('://') ? 
                    item.$ref.split('/').pop() : 
                    item.$ref}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Open Schema
                </Button>
              </Group>
            ) : (
              <div>
                {/* Properties from this allOf item */}
                {item.properties && Object.entries(item.properties).map(([name, property]) => 
                  renderPropertyNode(name, property, `allOf.${index}.properties.${name}`)
                )}
                
                {/* Required properties badge */}
                {item.required && item.required.length > 0 && (
                  <Group mt="xs">
                    <Text size="xs" fw={500}>Required Properties:</Text>
                    {item.required.map((name: string) => (
                      <Badge key={name} size="sm" color="red">{name}</Badge>
                    ))}
                  </Group>
                )}
              </div>
            )}
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div>
      <TextInput
        placeholder="Search properties..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        mb="md"
      />
      
      {searchQuery.trim() ? (
        renderSearchResults()
      ) : (
        <>
          <Group mb="md">
            <Button 
              size="xs" 
              leftSection={<IconPlus size={16} />}
              onClick={() => onAddProperty('root')}
            >
              Add Root Property
            </Button>
            {/* Base Schema Management */}
            <Button 
              size="xs" 
              leftSection={<IconPlus size={16} />}
              onClick={() => onAddProperty('baseSchema')}
              color="blue"
              variant="outline"
            >
              {schema.allOf?.some((item: any) => item.$ref) 
                ? "Change Base Schema" 
                : "Add Base Schema"}
            </Button>
            
            {/* Add a button to add an allOf item if supported */}
            {schema.allOf && (
              <Button 
                size="xs" 
                leftSection={<IconPlus size={16} />}
                onClick={() => onAddProperty('allOf')}
                color="cyan"
              >
                Add Properties Group
              </Button>
            )}
          </Group>
          
          {/* Handle allOf pattern for schema inheritance */}
          {schema.allOf && renderAllOf()}
          
          {/* Still render direct properties if they exist */}
          {schema.properties && Object.entries(schema.properties).map(([name, property]) => 
            renderPropertyNode(name, property, `properties.${name}`)
          )}
          
          {(!schema.properties && (!schema.allOf || schema.allOf.length === 0)) && (
            <Text color="dimmed" style={{ textAlign: 'center' }} py="xl">
              No properties defined. Click "Add Root Property" to get started.
            </Text>
          )}
        </>
      )}
    </div>
  );
}
