import { useState, useEffect, useMemo, useRef } from 'react';
import { 
  Card, 
  Grid, 
  Button, 
  Group, 
  Text, 
  Modal, 
  TextInput,
  Stack,
  Notification,
  Tabs,
  Select,
  Title,
  Badge
} from '@mantine/core';
import { useGetSchemasQuery } from '../store/api';
import { SchemaPropertyTree } from './SchemaPropertyTree';
import { PropertyEditor } from './PropertyEditor';
import { DefinitionsManager } from './DefinitionsManager';
import { IconInfoCircle, IconCode, IconTree, IconBook } from '@tabler/icons-react';
import { useSchemaEditor } from '../hooks/useSchemaEditor';

interface SchemaVisualizerProps {
  initialSchema: any;
  onChange: (updatedSchema: any) => void;
}

export function SchemaVisualizer({ initialSchema, onChange }: SchemaVisualizerProps) {
  // Use our custom hook for schema state management
  const { 
    schema: workingSchema, 
    selectedProperty, 
    notification, 
    setWorkingSchema,
    selectProperty,
    updatePropertyDetails,
    addNewProperty,
    removeProperty,
    addOrUpdateBaseSchema,
    showNotification,
    clearNotification: dismissNotification
  } = useSchemaEditor();

  const [newPropertyModal, setNewPropertyModal] = useState(false);
  const [newPropertyPath, setNewPropertyPath] = useState('');
  const [newPropertyName, setNewPropertyName] = useState('');
  const [baseSchemaModal, setBaseSchemaModal] = useState(false);
  const [baseSchemaRef, setBaseSchemaRef] = useState('');
  const [resourceReferenceModal, setResourceReferenceModal] = useState(false);
  const [resourceReferenceType, setResourceReferenceType] = useState('Patient');
  
  // Fetch available schemas for reference selection
  const { data: availableSchemas, isLoading: schemasLoading } = useGetSchemasQuery(undefined);
  
  // Prepare schema options for dropdown
  const schemaOptions = useMemo(() => {
    if (!availableSchemas) return [];
    
    return availableSchemas.map((schema: any) => ({
      value: schema.id,
      label: `${schema.name} v${schema.version}`
    }));
  }, [availableSchemas]);

  // Initialize working schema when component mounts
  const initialSchemaRef = useRef(false);
  
  useEffect(() => {
    if (!initialSchemaRef.current && initialSchema) {
      setWorkingSchema({ ...initialSchema });
      initialSchemaRef.current = true;
    }
  }, []); // Run only once on mount

  // Handle property selection
  const handleSelectProperty = (path: string) => {
    selectProperty(path);
    
    // Check if the selected property is a Resource Reference
    if (path) {
      const property = getPropertyByPath(workingSchema, path);
      if (isResourceReference(property)) {
        // Show a notification to inform the user about the restrictions
        showNotification('success', 'This is a Resource Reference. Only the resourceType const value can be edited.');
      }
    }
  };

  // Get property by path (supporting nested paths like 'properties.name')
  const getPropertyByPath = (obj: any, path: string) => {
    if (!path) return obj;
    
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current[part] === undefined) {
        return undefined;
      }
      current = current[part];
    }
    
    return current;
  };

  // Set property by path (immutable deep update)
  const setPropertyByPathImmutable = (obj: any, path: string, value: any): any => {
    const parts = path.split('.');
    
    const updateRecursively = (currentObj: any, index: number): any => {
      const part = parts[index];

      // Clone current level
      // Handle potential null/undefined and arrays explicitly
      let newLevel: any;
      if (currentObj === null || typeof currentObj !== 'object') {
        // If current level is not an object/array, create an object
        newLevel = {}; 
      } else if (Array.isArray(currentObj)) {
        newLevel = [...currentObj];
      } else {
        newLevel = { ...currentObj };
      }

      if (index === parts.length - 1) {
        // Last part of the path, set the value
        if (value === undefined) {
          delete newLevel[part]; // Handle deletion if value is undefined
        } else {
          newLevel[part] = value;
        }
      } else {
        // Recurse to the next level
        const nextObj = currentObj?.[part];
        newLevel[part] = updateRecursively(nextObj, index + 1);
        // Clean up empty objects created during recursion if the final value was undefined
        if (newLevel[part] === undefined || (typeof newLevel[part] === 'object' && Object.keys(newLevel[part]).length === 0)) {
           // Optional: delete empty objects if value was undefined, depends on desired behavior
           // delete newLevel[part]; 
        }
      }

      // Return the new level only if it has keys or if it's the root being set
      // This helps prevent creating empty objects unnecessarily deep in the structure
      // However, for schema manipulation, we might need to keep empty objects (like 'properties: {}')
      // Let's keep potentially empty objects for now.
      return newLevel;
    };

    // Handle edge case: setting value at the root
    if (!path) {
       if (value === undefined) return {}; // Or handle deletion as appropriate
       // If obj is not an object, or value is not an object, simple return value?
       if (typeof obj !== 'object' || obj === null || typeof value !== 'object' || value === null) {
         return value;
       }
       return { ...obj, ...value };
    }

    return updateRecursively(obj, 0);
  };

  // Delete property by path
  const deletePropertyByPath = (obj: any, path: string) => {
    if (!path) return obj;
    
    const parts = path.split('.');
    const lastPart = parts.pop();
    let current = obj;
    
    // Navigate to the parent object
    for (const part of parts) {
      if (current[part] === undefined) {
        return obj;
      }
      current = current[part];
    }
    
    // Delete the property
    if (lastPart !== undefined && current[lastPart] !== undefined) {
      const result = { ...obj };
      delete current[lastPart];
      return result;
    }
    
    return obj;
  };

  // Clear notification after 3 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (notification) {
      timer = setTimeout(() => {
        dismissNotification();
      }, 3000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [notification, dismissNotification]);
  
  // This effect ensures any changes to workingSchema get propagated to the parent
  useEffect(() => {
    onChange(workingSchema);
  }, [workingSchema, onChange]);

  // Update a property
  const handleUpdateProperty = (path: string, updates: any) => {
    // Check if this is a Resource Reference property
    const property = getPropertyByPath(workingSchema, path);
    
    if (isResourceReference(property)) {
      // For Resource Reference, only allow updating the resourceType const value
      const resourceTypeConstValue = updates.updates?.properties?.resourceType?.const;
      
      if (resourceTypeConstValue !== undefined) {
        // Create a special update that only changes the resourceType const value
        const restrictedUpdates = {
          ...property,
          properties: {
            ...property.properties,
            resourceType: {
              ...property.properties.resourceType,
              const: resourceTypeConstValue
            }
          }
        };
        
        // Apply the restricted update
        updatePropertyDetails(path, { updates: restrictedUpdates, required: updates.required });
        showNotification('success', 'Resource Reference updated successfully');
      } else {
        // Don't allow other changes to Resource Reference
        showNotification('error', 'Resource Reference properties cannot be modified except for resourceType const value');
      }
    } else {
      // Normal property update
      updatePropertyDetails(path, updates);
    }
    
    // Synchronize with parent component
    onChange(workingSchema);
  };

  // Handle base schema reference
  const handleAddBaseSchema = () => {
    if (!baseSchemaRef.trim()) {
      showNotification('error', 'Please enter a valid schema reference');
      return;
    }
    
    addOrUpdateBaseSchema(baseSchemaRef);
    
    // Close modal and reset
    setBaseSchemaModal(false);
    setBaseSchemaRef('');
    
    // Synchronize with parent component
    onChange(workingSchema);
  };
  
  // Add a property
  const handleAddProperty = (path: string, property: any) => {
    // Special handling for base schema
    if (path === 'baseSchema') {
      // Open the base schema modal
      const existingBaseSchema = workingSchema.allOf?.find((item: any) => item.$ref);
      if (existingBaseSchema) {
        setBaseSchemaRef(existingBaseSchema.$ref);
      }
      setBaseSchemaModal(true);
      return;
    }
    
    addNewProperty(path, property);
    
    // Synchronize with parent component
    onChange(workingSchema);
  };

  // Delete a property
  const handleDeleteProperty = (path: string) => {
    removeProperty(path);
    
    // Synchronize with parent component
    onChange(workingSchema);
  };

  // Remove automatic onChange effect to prevent update loops
  // We'll call onChange explicitly when needed

  // State for active tab
  const [activeTab, setActiveTab] = useState<string | null>('properties');
  
  // Store the definition to edit
  const [definitionToEdit, setDefinitionToEdit] = useState<string | null>(null);
  
  // State to track whether to open the create definition modal
  const [openCreateDefinitionModal, setOpenCreateDefinitionModal] = useState(false);
  
  // Reset open create modal flag when tab changes
  useEffect(() => {
    if (activeTab === 'definitions' && openCreateDefinitionModal) {
      // Will be consumed by the DefinitionsManager component
      setOpenCreateDefinitionModal(false);
    }
  }, [activeTab]);
  
  // Handle edit definition request from property editor
  const handleEditDefinition = (definitionName: string) => {
    setDefinitionToEdit(definitionName);
    setActiveTab('definitions');
  };

  // Handle create definition request from property editor
  const handleCreateDefinition = () => {
    setOpenCreateDefinitionModal(true);
    setActiveTab('definitions');
  };
  
  // Helper function to detect if a property is a Resource Reference
  const isResourceReference = (property: any): boolean => {
    if (!property || property.type !== 'object') return false;
    
    // Check if it has exactly the required properties
    const hasRequiredProps = property.required && 
      property.required.length === 2 && 
      property.required.includes('resourceType') && 
      property.required.includes('id');
      
    // Check if it has the correct property structure
    const hasCorrectProps = property.properties && 
      Object.keys(property.properties).length === 2 &&
      property.properties.id && 
      property.properties.resourceType &&
      property.properties.id.type === 'string' &&
      property.properties.id.format === 'uuid' &&
      property.properties.resourceType.type === 'string' &&
      property.properties.resourceType.const !== undefined;
      
    return hasRequiredProps && hasCorrectProps;
  };

  return (
    <div>
      {notification && (
        <Notification
          title={notification.type === 'success' ? 'Success' : 'Error'}
          color={notification.type === 'success' ? 'green' : 'red'}
          onClose={() => dismissNotification()}
          mb="md"
        >
          {notification.message}
        </Notification>
      )}
      
      <Stack gap="md">
        <Card shadow="xs" p="md" withBorder>
          {/* Display schema title if available */}
          {workingSchema.title && (
            <Title order={3} mb="md">
              {workingSchema.title}
            </Title>
          )}
          
          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="properties" leftSection={<IconTree size={16} />}>
                Properties
              </Tabs.Tab>
              <Tabs.Tab value="definitions" leftSection={<IconCode size={16} />}>
                Definitions
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="properties" pt="md">
              <Text size="sm" color="dimmed" mb="lg">
                <IconInfoCircle size={16} style={{ marginRight: 8, verticalAlign: 'middle' }} />
                Use this visual editor to create and modify your JSON Schema. Click on properties to edit them.
              </Text>
              
              {/* No buttons needed here */}
              
              <Grid>
                <Grid.Col span={7}>
                  <SchemaPropertyTree
                    schema={workingSchema}
                    selectedProperty={selectedProperty}
                    onSelectProperty={handleSelectProperty}
                    onAddProperty={(path) => {
                      // Special handling for baseSchema
                      if (path === 'baseSchema') {
                        // Open the base schema modal
                        const existingBaseSchema = workingSchema.allOf?.find((item: any) => item.$ref);
                        if (existingBaseSchema) {
                          setBaseSchemaRef(existingBaseSchema.$ref);
                        }
                        setBaseSchemaModal(true);
                      } else if (path === 'resourceReference') {
                        // Open the resource reference modal
                        setResourceReferenceModal(true);
                      } else {
                        // Normal property handling
                        setNewPropertyPath(path);
                        setNewPropertyModal(true);
                      }
                    }}
                    onDeleteProperty={handleDeleteProperty}
                    renderObjectActions={(path, property) => {
                      // Check if this is a Resource Reference
                      if (isResourceReference(property)) {
                        return (
                          <Badge color="teal" size="sm">Resource Reference</Badge>
                        );
                      }
                      return null;
                    }}
                  />
                </Grid.Col>
                
                <Grid.Col span={5}>
                  <PropertyEditor
                    schema={workingSchema}
                    propertyPath={selectedProperty}
                    onUpdateProperty={handleUpdateProperty}
                    onAddProperty={handleAddProperty}
                    onEditDefinition={handleEditDefinition}
                    onCreateDefinition={handleCreateDefinition}
                    onRenameProperty={(newPath: string) => selectProperty(newPath)}
                  />
                </Grid.Col>
              </Grid>
            </Tabs.Panel>

            <Tabs.Panel value="definitions" pt="md">
              <Text size="sm" color="dimmed" mb="lg">
                <IconInfoCircle size={16} style={{ marginRight: 8, verticalAlign: 'middle' }} />
                Manage schema definitions that can be referenced by properties in your schema.
              </Text>
              
                <DefinitionsManager 
                schema={workingSchema}
                initialDefinitionName={definitionToEdit || undefined}
                createNewDefinition={openCreateDefinitionModal}
                onChange={(updatedSchema) => {
                  setWorkingSchema(updatedSchema);
                  setDefinitionToEdit(null); // Reset after handling
                  showNotification('success', 'Definitions updated successfully');
                  
                  // Synchronize with parent component
                  onChange(updatedSchema);
                }} 
              />
            </Tabs.Panel>
          </Tabs>
        </Card>
      </Stack>
      
      {/* New Property Modal */}
      <Modal
        opened={newPropertyModal}
        onClose={() => setNewPropertyModal(false)}
        title="Add New Property"
        size="sm"
      >
        <TextInput
          label="Property Name"
          placeholder="Enter property name"
          value={newPropertyName}
          onChange={(e) => setNewPropertyName(e.target.value)}
          required
          mb="md"
        />
        
        <Group justify="flex-end">
          <Button variant="light" onClick={() => setNewPropertyModal(false)}>
            Cancel
          </Button>
          <Button onClick={() => {
            if (newPropertyName.trim()) {
              // Create a default property with the given name
              const defaultProperty = {
                name: newPropertyName,
                property: { type: 'string' },
                required: false
              };
              
              // Add the property to the schema
              handleAddProperty(newPropertyPath, defaultProperty);
              
              // Close the modal and reset
              setNewPropertyModal(false);
              setNewPropertyName('');
            }
          }}>
            Create
          </Button>
        </Group>
      </Modal>
      
      {/* Base Schema Modal */}
      <Modal
        opened={baseSchemaModal}
        onClose={() => setBaseSchemaModal(false)}
        title="Base Schema Reference"
        size="md"
      >
        <Text mb="md">
          Select or enter the reference to the base schema. This will be added as an item in the allOf array.
        </Text>
        
        {/* Get schemas from API, same as in PropertyEditor */}
        <Select
          label="Available Schemas"
          placeholder="Select a base schema"
          data={schemaOptions || []}
          value={baseSchemaRef}
          onChange={(value) => setBaseSchemaRef(value || '')}
          searchable
          clearable
          mb="md"
        />
        
        <Group justify="flex-end">
          <Button variant="light" onClick={() => setBaseSchemaModal(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddBaseSchema}>
            {workingSchema.allOf?.some((item: any) => item.$ref) ? "Update" : "Add"}
          </Button>
        </Group>
      </Modal>
      
      {/* Resource Reference Modal */}
      <Modal
        opened={resourceReferenceModal}
        onClose={() => setResourceReferenceModal(false)}
        title="Add Resource Reference"
        size="md"
      >
        <Text mb="md">
          Create a Resource Reference - an object that references another resource. You can only edit the resourceType const value.
        </Text>
        
        <TextInput
          label="Property Name"
          placeholder="Enter property name"
          value={newPropertyName}
          onChange={(e) => setNewPropertyName(e.target.value)}
          required
          mb="md"
        />
        
        <TextInput
          label="Resource Type"
          placeholder="Resource type"
          value={resourceReferenceType}
          onChange={(e) => setResourceReferenceType(e.target.value)}
          required
          mb="md"
        />
        
        <Group justify="flex-end">
          <Button variant="light" onClick={() => setResourceReferenceModal(false)}>
            Cancel
          </Button>
          <Button onClick={() => {
            if (newPropertyName.trim() && resourceReferenceType.trim()) {
              // Create the Resource Reference template
              const resourceReferenceSchema = {
                type: "object",
                required: [
                  "resourceType",
                  "id"
                ],
                properties: {
                  id: {
                    type: "string",
                    format: "uuid"
                  },
                  resourceType: {
                    type: "string",
                    const: resourceReferenceType
                  }
                }
              };
              
              // Add the property to the schema
              handleAddProperty('root', {
                name: newPropertyName,
                property: resourceReferenceSchema,
                required: false
              });
              
              // Close the modal and reset
              setResourceReferenceModal(false);
              setNewPropertyName('');
            }
          }}>
            Create
          </Button>
        </Group>
      </Modal>
    </div>
  );
}
