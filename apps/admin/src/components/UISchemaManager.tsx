import { useState, useEffect } from 'react';
import { 
  Title, 
  Text, 
  Button, 
  Group, 
  Card, 
  TextInput,
  Textarea,
  Loader,
  Badge,
  Tabs,
  JsonInput,
  Modal,
  Select,
  ActionIcon,
  Tooltip,
  Paper,
  Stack
} from '@mantine/core';
import { IconPlus, IconEdit, IconTrash, IconCheck, IconX } from '@tabler/icons-react';
import { JsonEditor } from 'json-edit-react';
import { 
  useGetUISchemasBySchemaQuery,
  useGetUISchemaQuery,
  useCreateUISchemaMutation,
  useUpdateUISchemaMutation,
  useDeleteUISchemaMutation,
  useGetSchemaQuery
} from '../store/api';
import { UISchemaEditor, UISchemaPreview } from '@hospice-os/ui-components';

interface UISchema {
  id: string;
  name: string;
  description?: string;
  schemaId: string;
  content: any;
  createdAt: string;
  updatedAt: string;
}

interface CreateUISchemaRequest {
  name: string;
  description: string;
  schemaId: string;
  content: any;
}

interface UpdateUISchemaRequest {
  id: string;
  name: string;
  description: string;
  content: any;
}

interface UISchemaManagerProps {
  schemaId: string;
  onSelectUISchema: (uiSchema: UISchema | null) => void;
  selectedUISchemaId?: string;
}

export function UISchemaManager({ schemaId, onSelectUISchema, selectedUISchemaId }: UISchemaManagerProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [newUISchemaName, setNewUISchemaName] = useState('');
  const [newUISchemaDescription, setNewUISchemaDescription] = useState('');
  const [newUISchemaContent, setNewUISchemaContent] = useState<any>({});
  const [editingUISchemaId, setEditingUISchemaId] = useState<string | null>(null);
  const [deletingUISchemaId, setDeletingUISchemaId] = useState<string | null>(null);
  const [editMode, setEditMode] = useState<'json' | 'visual'>('visual');
  
  // Fetch schema
  const { data: schema } = useGetSchemaQuery(schemaId);
  
  // Fetch UI schemas for this schema
  const { 
    data: uiSchemas, 
    isLoading: isLoadingUISchemas, 
    error: uiSchemasError 
  } = useGetUISchemasBySchemaQuery(schemaId);
  
  // Fetch selected UI schema details
  const { 
    data: selectedUISchema, 
    isLoading: isLoadingSelectedUISchema 
  } = useGetUISchemaQuery(editingUISchemaId || '', { 
    skip: !editingUISchemaId 
  });
  
  // Mutations
  const [createUISchema, { isLoading: isCreating }] = useCreateUISchemaMutation();
  const [updateUISchema, { isLoading: isUpdating }] = useUpdateUISchemaMutation();
  const [deleteUISchema, { isLoading: isDeleting }] = useDeleteUISchemaMutation();
  
  // Set form values when editing
  useEffect(() => {
    if (selectedUISchema && isEditModalOpen) {
      setNewUISchemaName(selectedUISchema.name);
      setNewUISchemaDescription(selectedUISchema.description || '');
      setNewUISchemaContent(selectedUISchema.content);
    }
  }, [selectedUISchema, isEditModalOpen]);
  
  // Handle create UI schema
  const handleCreateUISchema = async () => {
    if (newUISchemaName.trim() && newUISchemaContent) {
      try {
        const createRequest: CreateUISchemaRequest = {
          name: newUISchemaName.trim(),
          description: newUISchemaDescription.trim(),
          schemaId,
          content: newUISchemaContent
        };
        
        await createUISchema(createRequest);
        
        // Reset form
        setNewUISchemaName('');
        setNewUISchemaDescription('');
        setNewUISchemaContent({});
        setIsCreateModalOpen(false);
      } catch (e) {
        alert('Error creating UI schema');
      }
    }
  };
  
  // Handle update UI schema
  const handleUpdateUISchema = async () => {
    if (editingUISchemaId && newUISchemaName.trim() && newUISchemaContent) {
      try {
        const updateRequest: UpdateUISchemaRequest = {
          id: editingUISchemaId,
          name: newUISchemaName.trim(),
          description: newUISchemaDescription.trim(),
          content: newUISchemaContent
        };
        
        await updateUISchema(updateRequest);
        
        // Reset form
        setEditingUISchemaId(null);
        setNewUISchemaName('');
        setNewUISchemaDescription('');
        setNewUISchemaContent({});
        setIsEditModalOpen(false);
      } catch (e) {
        alert('Error updating UI schema');
      }
    }
  };
  
  // Handle delete UI schema
  const handleDeleteUISchema = async () => {
    if (deletingUISchemaId) {
      await deleteUISchema(deletingUISchemaId);
      
      // Reset
      setDeletingUISchemaId(null);
      setIsDeleteModalOpen(false);
      
      // If the deleted UI schema was selected, clear the selection
      if (selectedUISchemaId === deletingUISchemaId) {
        onSelectUISchema(null);
      }
    }
  };
  
  // Handle select UI schema
  const handleSelectUISchema = (uiSchema: UISchema) => {
    onSelectUISchema(uiSchema);
  };
  
  if (isLoadingUISchemas) {
    return <Loader size="sm" />;
  }
  
  if (uiSchemasError) {
    return <Text color="red">Error loading UI schemas</Text>;
  }
  
  return (
    <div>
      <Group justify="space-between" mb="md">
        <Title order={5}>UI Schemas</Title>
        <Button 
          size="xs" 
          leftSection={<IconPlus size={16} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Add UI Schema
        </Button>
      </Group>
      
      {(!uiSchemas || uiSchemas.length === 0) ? (
        <Text color="dimmed" size="sm">No UI schemas defined for this schema</Text>
      ) : (
        <div>
          {uiSchemas.map((uiSchema: UISchema) => (
            <Card 
              key={uiSchema.id} 
              withBorder 
              mb="xs"
              padding="xs"
              style={{ 
                cursor: 'pointer',
                backgroundColor: selectedUISchemaId === uiSchema.id ? '#f0f9ff' : undefined
              }}
              onClick={() => handleSelectUISchema(uiSchema)}
            >
              <Group justify="space-between">
                <div>
                  <Text fw={500}>{uiSchema.name}</Text>
                  {uiSchema.description && (
                    <Text size="xs" color="dimmed">{uiSchema.description}</Text>
                  )}
                </div>
                <Group gap="xs">
                  <Tooltip label="Edit">
                    <ActionIcon 
                      size="sm" 
                      color="blue"
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingUISchemaId(uiSchema.id);
                        setIsEditModalOpen(true);
                      }}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                  </Tooltip>
                  <Tooltip label="Delete">
                    <ActionIcon 
                      size="sm" 
                      color="red"
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeletingUISchemaId(uiSchema.id);
                        setIsDeleteModalOpen(true);
                      }}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Group>
              </Group>
            </Card>
          ))}
        </div>
      )}
      
      {/* Create UI Schema Modal */}
      <Modal
        opened={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create UI Schema"
        size="xl"
      >
        <TextInput
          label="Name"
          placeholder="Display name for this UI schema"
          value={newUISchemaName}
          onChange={(e) => setNewUISchemaName(e.target.value)}
          required
          mb="md"
        />
        
        <Textarea
          label="Description"
          placeholder="Optional description"
          value={newUISchemaDescription}
          onChange={(e) => setNewUISchemaDescription(e.target.value)}
          mb="md"
        />
        
        <Tabs defaultValue="visual">
          <Tabs.List>
            <Tabs.Tab value="visual" onClick={() => setEditMode('visual')}>Visual Editor</Tabs.Tab>
            <Tabs.Tab value="json" onClick={() => setEditMode('json')}>JSON Editor</Tabs.Tab>
            <Tabs.Tab value="preview">Preview</Tabs.Tab>
          </Tabs.List>
          
          <Tabs.Panel value="visual" pt="md">
            {schema && (
              <UISchemaEditor 
                schema={schema.schema}
                uiSchema={newUISchemaContent}
                onChange={setNewUISchemaContent}
              />
            )}
          </Tabs.Panel>
          
          <Tabs.Panel value="json" pt="md">
            <JsonEditor
              data={newUISchemaContent || {}}
              setData={(e) => setNewUISchemaContent(e)}
            />
          </Tabs.Panel>
          
          <Tabs.Panel value="preview" pt="md">
            {schema && (
              <UISchemaPreview 
                schema={schema.schema}
                uiSchema={newUISchemaContent}
              />
            )}
          </Tabs.Panel>
        </Tabs>
        
        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={() => setIsCreateModalOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateUISchema} loading={isCreating}>Create</Button>
        </Group>
      </Modal>
      
      {/* Edit UI Schema Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit UI Schema"
        size="xl"
      >
        {isLoadingSelectedUISchema ? (
          <Loader />
        ) : (
          <>
            <TextInput
              label="Name"
              placeholder="Display name for this UI schema"
              value={newUISchemaName}
              onChange={(e) => setNewUISchemaName(e.target.value)}
              required
              mb="md"
            />
            
            <Textarea
              label="Description"
              placeholder="Optional description"
              value={newUISchemaDescription}
              onChange={(e) => setNewUISchemaDescription(e.target.value)}
              mb="md"
            />
            
            <Tabs defaultValue="visual">
              <Tabs.List>
                <Tabs.Tab value="visual" onClick={() => setEditMode('visual')}>Visual Editor</Tabs.Tab>
                <Tabs.Tab value="json" onClick={() => setEditMode('json')}>JSON Editor</Tabs.Tab>
                <Tabs.Tab value="preview">Preview</Tabs.Tab>
              </Tabs.List>
              
              <Tabs.Panel value="visual" pt="md">
                {schema && (
                  <UISchemaEditor 
                    schema={schema.schema}
                    uiSchema={newUISchemaContent}
                    onChange={setNewUISchemaContent}
                  />
                )}
              </Tabs.Panel>
              
              <Tabs.Panel value="json" pt="md">
                <JsonEditor
                  data={newUISchemaContent || {}}
                  setData={(e) => setNewUISchemaContent(e)}
                />
              </Tabs.Panel>
              
              <Tabs.Panel value="preview" pt="md">
                {schema && (
                  <UISchemaPreview 
                    schema={schema.schema}
                    uiSchema={newUISchemaContent}
                  />
                )}
              </Tabs.Panel>
            </Tabs>
            
            <Group justify="flex-end" mt="md">
              <Button variant="light" onClick={() => setIsEditModalOpen(false)}>Cancel</Button>
              <Button onClick={handleUpdateUISchema} loading={isUpdating}>Save Changes</Button>
            </Group>
          </>
        )}
      </Modal>
      
      {/* Delete UI Schema Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete UI Schema"
      >
        <Text>Are you sure you want to delete this UI schema? This action cannot be undone.</Text>
        
        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={() => setIsDeleteModalOpen(false)}>Cancel</Button>
          <Button color="red" onClick={handleDeleteUISchema} loading={isDeleting}>Delete</Button>
        </Group>
      </Modal>
    </div>
  );
}
