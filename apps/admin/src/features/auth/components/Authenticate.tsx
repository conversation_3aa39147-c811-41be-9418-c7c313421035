import { useStytchMemberSession, useStytchB2BClient } from "@stytch/react/b2b";
import { Navigate } from "react-router-dom";
import { useEffect } from "react";
import { LoginOrSignup } from "./LoginOrSignup";

export const Authenticate = () => {
  const { session } = useStytchMemberSession();
  const stytchClient = useStytchB2BClient();

  // The session JWT is automatically stored as a cookie by <PERSON><PERSON><PERSON>
  // We don't need to manually store it

  if (session) {
    return <Navigate to="/" />;
  }

  return <LoginOrSignup />;
};
