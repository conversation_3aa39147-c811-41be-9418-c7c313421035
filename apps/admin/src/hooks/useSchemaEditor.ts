import { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  setSchema, 
  setSelectedProperty, 
  updateProperty, 
  addProperty, 
  deleteProperty, 
  addBaseSchema,
  setNotification,
  clearNotification,
  reorderProperty,
  selectSchema,
  selectSelectedProperty,
  selectNotification,
  selectIsModified
} from '../store/schemaSlice';

/**
 * Custom hook for working with the schema editor
 * Provides access to schema state and actions
 */
export function useSchemaEditor() {
  const dispatch = useDispatch();
  const schema = useSelector(selectSchema);
  const selectedProperty = useSelector(selectSelectedProperty);
  const notification = useSelector(selectNotification);
  const isModified = useSelector(selectIsModified);
  
  // Keep a memoized version of the schema to prevent unnecessary re-renders
  const memoizedSchema = useMemo(() => schema, [JSON.stringify(schema)]);

  const setWorkingSchema = useCallback((newSchema: any) => {
    // Only update if the schema is actually different to prevent unnecessary renders
    const currentSchemaStr = JSON.stringify(schema);
    const newSchemaStr = JSON.stringify(newSchema);
    
    if (currentSchemaStr !== newSchemaStr) {
      dispatch(setSchema(newSchema));
    }
  }, [dispatch, schema]);

  const selectProperty = useCallback((path: string | null) => {
    dispatch(setSelectedProperty(path));
  }, [dispatch]);

  const updatePropertyDetails = useCallback((path: string, updates: any) => {
    // Handle regular property updates
    if (!updates.reorderAction) {
      dispatch(updateProperty({ path, updates }));
    } 
    // Handle reordering actions specifically
    else {
      dispatch(updates.reorderAction);
    }
  }, [dispatch]);
  
  // Direct function to reorder properties
  const reorderSchemaProperty = useCallback((parentPath: string, propertyName: string, direction: 'up' | 'down') => {
    dispatch(reorderProperty({
      parentPath,
      propertyName,
      direction
    }));
  }, [dispatch]);

  const addNewProperty = useCallback((path: string, property: any) => {
    dispatch(addProperty({ path, property }));
  }, [dispatch]);

  const removeProperty = useCallback((path: string) => {
    dispatch(deleteProperty(path));
  }, [dispatch]);

  const addOrUpdateBaseSchema = useCallback((ref: string) => {
    dispatch(addBaseSchema(ref));
  }, [dispatch]);

  const showNotification = useCallback((type: 'success' | 'error', message: string) => {
    dispatch(setNotification({ type, message }));
    // Auto-clear handled in component to avoid memory leaks
  }, [dispatch]);

  return {
    // State
    schema,
    selectedProperty,
    notification,
    isModified,
    
    // Actions
    setWorkingSchema,
    selectProperty,
    updatePropertyDetails,
    reorderSchemaProperty,
    addNewProperty,
    removeProperty,
    addOrUpdateBaseSchema,
    showNotification,
    clearNotification: () => dispatch(clearNotification())
  };
}
