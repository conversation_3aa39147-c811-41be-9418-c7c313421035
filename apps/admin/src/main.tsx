import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { MantineProvider, createTheme } from '@mantine/core';
import '@mantine/core/styles.css';
import './index.css';
import { store } from './store';
import { Router } from './Router';
import { StytchB2BProvider } from '@stytch/react/b2b';
import { StytchB2BUIClient } from '@stytch/vanilla-js/b2b';

// Create Mantine theme
const theme = createTheme({
  defaultRadius: 'md',
});


const stytch = new StytchB2BUIClient(
  import.meta.env.STYTCH_PUBLIC_TOKEN,
  {
    cookieOptions: {
      path: "",
      availableToSubdomains: import.meta.env.PROD,
      domain: import.meta.env.PROD ? ".tallio.com" : "",
    }
  }
);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      <StytchB2BProvider stytch={stytch}>
        <MantineProvider theme={theme} defaultColorScheme="light">
          <Router />
        </MantineProvider>
      </StytchB2BProvider>
    </Provider>
  </StrictMode>,
);
