import { useState } from 'react';
import { Title, Text, Button, Table, Group, ActionIcon, Modal, TextInput, Box } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useGetAgenciesQuery, useCreateAgencyMutation, useDeleteAgencyMutation } from '../store/api';

export function AgenciesPage() {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [newAgencyName, setNewAgencyName] = useState('');
  const [newStytchOrganizationId, setNewStytchOrganizationId] = useState('');
  
  // Fetch agencies
  const { data: agencies, isLoading, error } = useGetAgenciesQuery(undefined);
  
  // Mutations
  const [createAgency, { isLoading: isCreating }] = useCreateAgencyMutation();
  const [deleteAgency, { isLoading: isDeleting }] = useDeleteAgencyMutation();

  // Handle create agency
  const handleCreateAgency = async () => {
    if (newAgencyName.trim()) {
      await createAgency({ 
        name: newAgencyName.trim(),
        stytchOrganizationId: newStytchOrganizationId.trim() || undefined
      });
      setNewAgencyName('');
      setNewStytchOrganizationId('');
      setCreateModalOpen(false);
    }
  };

  // Handle delete agency
  const handleDeleteAgency = async (id: string) => {
    if (confirm('Are you sure you want to delete this agency? This action cannot be undone.')) {
      await deleteAgency(id);
    }
  };

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">Agencies</Title>
            <Text className="page-description">
              Manage agencies and their settings
            </Text>
          </div>
          <Button onClick={() => setCreateModalOpen(true)}>Add Agency</Button>
        </Group>
      </div>

      {isLoading ? (
        <Text>Loading agencies...</Text>
      ) : error ? (
        <Text color="red">Error loading agencies</Text>
      ) : (
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>ID</Table.Th>
              <Table.Th>Name</Table.Th>
              <Table.Th style={{ width: 120 }}>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {agencies?.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={3} align="center">No agencies found</Table.Td>
              </Table.Tr>
            ) : (
              agencies?.map((agency: { id: string; name: string }) => (
                <Table.Tr key={agency.id}>
                  <Table.Td>{agency.id}</Table.Td>
                  <Table.Td>{agency.name}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Button
                        component={Link}
                        to={`/agencies/${agency.id}`}
                        size="xs"
                        variant="light"
                      >
                        View
                      </Button>
                      <Button
                        size="xs"
                        color="red"
                        variant="light"
                        onClick={() => handleDeleteAgency(agency.id)}
                        loading={isDeleting}
                      >
                        Delete
                      </Button>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      )}

      {/* Create Agency Modal */}
      <Modal
        opened={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        title="Create New Agency"
        centered
      >
        <Box maw={400} mx="auto">
          <TextInput
            label="Agency Name"
            placeholder="Enter agency name"
            value={newAgencyName}
            onChange={(e) => setNewAgencyName(e.target.value)}
            required
            mb="md"
          />
          
          <TextInput
            label="Stytch Organization ID"
            placeholder="Optional - For Stytch B2B integration"
            value={newStytchOrganizationId}
            onChange={(e) => setNewStytchOrganizationId(e.target.value)}
            mb="md"
          />
          <Group justify="flex-end">
            <Button variant="light" onClick={() => setCreateModalOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateAgency} loading={isCreating}>Create</Button>
          </Group>
        </Box>
      </Modal>
    </div>
  );
}
