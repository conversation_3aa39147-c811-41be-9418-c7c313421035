import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Title, 
  Text, 
  Button, 
  Group, 
  Card, 
  TextInput, 
  Divider,
  Tabs,
  Table,
  Badge,
  Loader
} from '@mantine/core';
import { 
  useGetAgencyQuery, 
  useUpdateAgencyMutation,
  useGetUsersQuery
} from '../store/api';

export function AgencyDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [editMode, setEditMode] = useState(false);
  const [agencyName, setAgencyName] = useState('');
  const [stytchOrganizationId, setStytchOrganizationId] = useState('');
  
  // Fetch agency details
  const { 
    data: agency, 
    isLoading, 
    error 
  } = useGetAgencyQuery(id || '', { 
    skip: !id
  });

  // Set agency data when loaded
  // Use useEffect to avoid infinite re-renders
  useEffect(() => {
    if (agency && !editMode) {
      setAgencyName(agency.name);
      setStytchOrganizationId(agency.stytchOrganizationId || '');
    }
  }, [agency, editMode]);
  
  // Fetch users for this agency
  const { 
    data: users, 
    isLoading: isLoadingUsers 
  } = useGetUsersQuery({ role: 'all', agencyId: null }, {
    skip: !id
  });
  
  // Filter users by agency
  const agencyUsers = users?.filter((user: any) => user.agencyId === id);
  
  // Update agency mutation
  const [updateAgency, { isLoading: isUpdating }] = useUpdateAgencyMutation();

  // Handle save changes
  const handleSaveChanges = async () => {
    if (id && agencyName.trim()) {
      await updateAgency({
        id,
        name: agencyName.trim(),
        stytchOrganizationId: stytchOrganizationId.trim() || undefined
      } as {id: string, name: string, stytchOrganizationId?: string});
      setEditMode(false);
    }
  };

  if (isLoading) {
    return <div className="admin-content"><Loader size="md" /></div>;
  }

  if (error || !agency) {
    return (
      <div className="admin-content">
        <Text color="red">Error loading agency details</Text>
        <Button onClick={() => navigate('/agencies')} variant="light" mt="md">
          Back to Agencies
        </Button>
      </div>
    );
  }

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">Agency Details</Title>
            <Text className="page-description">
              View and manage agency settings
            </Text>
          </div>
          <Group>
            <Button 
              variant="light" 
              onClick={() => navigate('/agencies')}
            >
              Back to List
            </Button>
            {!editMode ? (
              <Button onClick={() => setEditMode(true)}>Edit</Button>
            ) : (
              <Group>
                <Button variant="light" onClick={() => {
                  setEditMode(false);
                  setAgencyName(agency.name);
                  setStytchOrganizationId(agency.stytchOrganizationId || '');
                }}>
                  Cancel
                </Button>
                <Button onClick={handleSaveChanges} loading={isUpdating}>
                  Save Changes
                </Button>
              </Group>
            )}
          </Group>
        </Group>
      </div>

      <Tabs defaultValue="details">
        <Tabs.List>
          <Tabs.Tab value="details">Details</Tabs.Tab>
          <Tabs.Tab value="users">Users</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="details" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Title order={4} mb="md">Agency Information</Title>
            
            <Group mb="md">
              <Text fw={500}>ID:</Text>
              <Text>{agency.id}</Text>
            </Group>
            
            {editMode ? (
              <TextInput
                label="Agency Name"
                value={agencyName}
                onChange={(e) => setAgencyName(e.target.value)}
                mb="md"
              />
            ) : (
              <Group mb="md">
                <Text fw={500}>Name:</Text>
                <Text>{agency.name}</Text>
              </Group>
            )}
            
            {editMode ? (
              <TextInput
                label="Stytch Organization ID"
                value={stytchOrganizationId}
                onChange={(e) => setStytchOrganizationId(e.target.value)}
                mb="md"
                placeholder="Optional - For Stytch B2B integration"
              />
            ) : agency.stytchOrganizationId ? (
              <Group mb="md">
                <Text fw={500}>Stytch Organization ID:</Text>
                <Text>{agency.stytchOrganizationId}</Text>
              </Group>
            ) : null}
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="users" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="md">
              <Title order={4}>Agency Users</Title>
              <Button variant="light" size="sm">Add User</Button>
            </Group>
            
            {isLoadingUsers ? (
              <Loader size="sm" />
            ) : (
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>ID</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {agencyUsers?.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={4} align="center">No users found for this agency</Table.Td>
                    </Table.Tr>
                  ) : (
                    agencyUsers?.map((user: any) => (
                      <Table.Tr key={user.id}>
                        <Table.Td>{user.id}</Table.Td>
                        <Table.Td>{user.email}</Table.Td>
                        <Table.Td>
                          <Badge>{user.resourceType}</Badge>
                        </Table.Td>
                        <Table.Td>
                          <Button size="xs" variant="light">View</Button>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            )}
          </Card>
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
