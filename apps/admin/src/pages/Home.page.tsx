import { Title, Text, SimpleGrid, Card, Group, Button, Badge } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useGetAgenciesQuery, useGetResourcesQuery } from '../store/api';
import { useGetSchemasQuery } from '../store/api';
import { useGetResourcesByTypeAndStatusQuery } from '../store/api';

export function HomePage() {
  // Fetch data for dashboard
  const { data: agencies, isLoading: isLoadingAgencies } = useGetAgenciesQuery(undefined);
  const { data: schemas, isLoading: isLoadingSchemas } = useGetSchemasQuery(undefined);
  const { data: pendingRequests, isLoading: isLoadingRequests } = useGetResourcesByTypeAndStatusQuery({ type: 'Request', status: 'pending_review' });

  return (
    <div className="admin-content">
      <div className="page-header">
        <Title order={2} className="page-title">Admin Dashboard</Title>
        <Text className="page-description">
          Manage agencies, users, schemas, and resources
        </Text>
      </div>

      <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
        {/* Agencies Card */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Group justify="space-between">
              <Title order={3}>Agencies</Title>
              <Badge size="lg">{isLoadingAgencies ? '...' : agencies?.length || 0}</Badge>
            </Group>
          </Card.Section>

          <Text mt="md" mb="md">
            Manage agencies and their settings
          </Text>

          <Button component={Link} to="/agencies" variant="light" fullWidth mt="md">
            View Agencies
          </Button>
        </Card>

        {/* Schemas Card */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Group justify="space-between">
              <Title order={3}>Schemas</Title>
              <Badge size="lg">{isLoadingSchemas ? '...' : schemas?.length || 0}</Badge>
            </Group>
          </Card.Section>

          <Text mt="md" mb="md">
            Manage data schemas and validation rules
          </Text>

          <Button component={Link} to="/schemas" variant="light" fullWidth mt="md">
            View Schemas
          </Button>
        </Card>

        {/* Resources Card */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Group justify="space-between">
              <Title order={3}>Pending Requests</Title>
              <Badge size="lg" color="orange">{isLoadingRequests ? '...' : pendingRequests?.length || 0}</Badge>
            </Group>
          </Card.Section>

          <Text mt="md" mb="md">
            Review and approve pending resources
          </Text>

          <Button component={Link} to="/resources" variant="light" fullWidth mt="md">
            Review Resources
          </Button>
        </Card>
      </SimpleGrid>
    </div>
  );
}
