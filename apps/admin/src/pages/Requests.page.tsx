import { useState, useEffect } from 'react';
import { Title, Text, Button, Table, Group, Card, Badge, Tabs, Select, Loader } from '@mantine/core';
import { Link, useNavigate } from 'react-router-dom';
import { useGetAgenciesQuery, useGetResourcesByTypeAndStatusQuery, useGetResourcesQuery } from '../store/api';

export function RequestsPage() {
  const navigate = useNavigate();
  const [resourceStatus, setResourceStatus] = useState('all');
  const [resourceType, setResourceType] = useState('Request');
  const [selectedAgency, setSelectedAgency] = useState('all');
  const { data: agencies, isLoading: agenciesLoading, error: agenciesError } = useGetAgenciesQuery(undefined);

  
  // Fetch resources using both type and status
  const { data: resources, isLoading, error } = useGetResourcesByTypeAndStatusQuery({
    type: resourceType,
    status: resourceStatus,
    agencyId: selectedAgency
  });

  // Resource status options
  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending_review', label: 'Pending Review' },
    { value: 'completed', label: 'Completed' }
  ];

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">Requests</Title>
            <Text className="page-description">
              Review and manage requests
            </Text>
          </div>
          <Group>
          <Select
              label="Agency"
              value={selectedAgency}
              onChange={setSelectedAgency}
              data={[{ value: 'all', label: 'All' }, ...(agencies?.map((agency) => ({ value: agency.id, label: agency.name })) || [])]}
              w={200}
            />
            <Select
              label="Status"
              value={resourceStatus}
              onChange={(value) => setResourceStatus(value || 'all')}
              data={statusOptions}
              w={200}
            />
            <Button
              onClick={() => navigate('/resources/create')}
            >
              Create Request
            </Button>
          </Group>
        </Group>
      </div>

      {isLoading ? (
        <Loader size="md" />
      ) : error ? (
        <Text color="red">Error loading resources</Text>
      ) : (
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>ID</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Agency</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Created</Table.Th>
              <Table.Th style={{ width: 120 }}>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {resources?.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} align="center">No resources found</Table.Td>
              </Table.Tr>
            ) : (
              resources?.map((resource: any) => (
                <Table.Tr key={resource.id}>
                  <Table.Td>{resource.id}</Table.Td>
                  <Table.Td>{resource.resourceType}</Table.Td>
                  <Table.Td>{resource.agencyName}</Table.Td>
                  <Table.Td>
                    <Badge 
                      color={
                        resource.status === 'pending_review' ? 'yellow' : 
                        resource.status === 'completed' ? 'green' : 'gray'
                      }
                    >
                      {resource.status || 'Unknown'}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    {resource.createdAt ? new Date(resource.createdAt).toLocaleDateString() : 'N/A'}
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Button
                        component={Link}
                        to={`/resources/${resource.id}`}
                        size="xs"
                        variant="light"
                      >
                        Review
                      </Button>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      )}
    </div>
  );
}
