import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Title,
  Text,
  Button,
  Group,
  Card,
  Select,
  TextInput,
  Loader,
} from "@mantine/core";
import { SchemaForm } from "../components/SchemaForm";
import {
  useGetSchemasQuery,
  useGetAgenciesQuery,
  useCreateResourceMutation,
  useGetSchemaQuery,
  useGetResolvedSchemaQuery,
} from "../store/api";

export function ResourceCreatePage() {
  const navigate = useNavigate();
  const [selectedSchemaId, setSelectedSchemaId] = useState<string | null>(null);
  const [selectedAgencyId, setSelectedAgencyId] = useState<string | null>(null);
  const [resourceType, setResourceType] = useState<string>("");

  // Fetch schemas
  const {
    data: schemas,
    isLoading: isLoadingSchemas,
    error: schemasError,
  } = useGetSchemasQuery(undefined);

  // Fetch agencies
  const {
    data: agencies,
    isLoading: isLoadingAgencies,
    error: agenciesError,
  } = useGetAgenciesQuery(undefined);

  // Create resource mutation
  const [createResource, { isLoading: isCreating }] =
    useCreateResourceMutation();

  // Fetch selected schema details
  const { data: selectedSchema } = useGetSchemaQuery(selectedSchemaId || "", {
    skip: !selectedSchemaId,
  });

  // Fetch resolved schema for the form
  const { data: resolvedSchema } = useGetResolvedSchemaQuery(
    selectedSchemaId || "",
    {
      skip: !selectedSchemaId,
    },
  );

  // Infer resource type from schema when schema is selected
  useEffect(() => {
    if (selectedSchema) {
      let resourceTypeField = null;
      if (resolvedSchema) {
        // Use the resolved schema
        console.log("resolvedSchema", resolvedSchema);
        resourceTypeField = resolvedSchema.properties?.resourceType;
      } else {
        // First check if the schema has a resourceType field with a const value
        resourceTypeField = selectedSchema.schema?.properties?.resourceType;
      }
      if (resourceTypeField && resourceTypeField.const) {
        // Use the const value as the resource type
        setResourceType(resourceTypeField.const);
      } else {
        // Fall back to inferring from schema name
        const schemaName = selectedSchema.name || "";

        // Extract resource type from schema name
        // Remove "Schema" suffix if present
        let inferredType = schemaName.replace(/Schema$/i, "");

        // Convert to camelCase
        inferredType = inferredType
          .replace(/\s+(.)/g, (_: string, char: string) => char.toUpperCase())
          .replace(/\s/g, "")
          .replace(/^(.)/, (_: string, char: string) => char.toLowerCase());

        if (inferredType) {
          setResourceType(inferredType);
        }
      }
    }
  }, [selectedSchema]);

  // Handle schema selection
  const handleSchemaChange = (value: string | null) => {
    setSelectedSchemaId(value);
  };

  // Handle agency selection
  const handleAgencyChange = (value: string | null) => {
    setSelectedAgencyId(value);
  };

  // Handle resource type change
  const handleResourceTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setResourceType(e.target.value);
  };

  // Generate a unique ID
  const generateUniqueId = () => {
    return (
      "res_" +
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  };

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    if (!selectedSchemaId || !selectedAgencyId || !resourceType) {
      alert("Please select a schema, agency, and resource type");
      return;
    }

    try {
      // Add resource type, and status to form data
      const resourceData = {
        ...formData,
        resourceType,
      };

      // Create resource
      await createResource({
        ...resourceData,
        schemaId: selectedSchemaId,
        agencyId: selectedAgencyId,
      });

      // Navigate back to resources list
      navigate("/resources");
    } catch (error) {
      console.error("Error creating resource:", error);
      alert("Error creating resource");
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate("/resources");
  };

  if (isLoadingSchemas || isLoadingAgencies) {
    return (
      <div className="admin-content">
        <Loader size="md" />
      </div>
    );
  }

  if (schemasError || agenciesError) {
    return (
      <div className="admin-content">
        <Text color="red">Error loading data</Text>
        <Button onClick={() => navigate("/resources")} variant="light" mt="md">
          Back to Resources
        </Button>
      </div>
    );
  }

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">
              Create Resource
            </Title>
            <Text className="page-description">
              Create a new resource based on a schema
            </Text>
          </div>
          <Button variant="light" onClick={() => navigate("/resources")}>
            Back to List
          </Button>
        </Group>
      </div>

      <Card shadow="sm" padding="lg" radius="md" withBorder mb="xl">
        <Title order={4} mb="md">
          Resource Information
        </Title>

        <Select
          label="Schema"
          placeholder="Select a schema"
          data={
            schemas?.map((schema: any) => ({
              value: schema.id,
              label: schema.name,
            })) || []
          }
          value={selectedSchemaId}
          onChange={handleSchemaChange}
          required
          mb="md"
        />

        <Select
          label="Agency"
          placeholder="Select an agency"
          data={
            agencies?.map((agency: any) => ({
              value: agency.id,
              label: agency.name,
            })) || []
          }
          value={selectedAgencyId}
          onChange={handleAgencyChange}
          required
          mb="md"
        />

        <TextInput
          label="Resource Type"
          placeholder="Enter resource type"
          value={resourceType}
          onChange={handleResourceTypeChange}
          required
          mb="xl"
          readOnly={!!selectedSchema?.schema?.properties?.resourceType?.const} // Make readonly only if schema has a const resourceType
          description={
            selectedSchema?.schema?.properties?.resourceType?.const
              ? "Resource type defined in schema (cannot be changed)"
              : selectedSchema
                ? "Resource type inferred from schema name (can be edited)"
                : ""
          }
        />
      </Card>

      {selectedSchemaId ? (
        <SchemaForm
          schemaId={selectedSchemaId}
          resolvedSchema={resolvedSchema}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isCreating}
          agencyId={selectedAgencyId}
        />
      ) : (
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Text ta="center" color="dimmed">
            Please select a schema to continue
          </Text>
        </Card>
      )}
    </div>
  );
}
