import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Title,
  Text,
  Button,
  Group,
  Card,
  Textarea,
  Loader,
  Badge,
  Tabs,
  JsonInput,
  Divider,
  Radio,
  Stack,
  TextInput,
  ScrollArea,
} from "@mantine/core";
import { SchemaForm } from "../components/SchemaForm";
import {
  useGetResourceQuery,
  useUpdateResourceMutation,
  useDeleteResourceMutation,
  useGetResolvedSchemaQuery,
  useReprocessRequestMutation,
  useCopyAndReprocessRequestMutation,
  useReviewRequestMutation,
  useProcessRequestNoteSchemaMutation,
  useReferralPdfMutation,
  useGeneratePlanOfCareMutation,
  useGeneratePatientChartMutation,
  useClassifySubdocumentsMutation,
  useProcessSubdocumentsMutation,
  useCreateResourcesForSubdocumentsMutation,
} from "../store/api";
import { CollapsibleMarkdown } from "../components/CollapsibleMarkdown";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useGetPresignedDownloadUrlMutation } from "../store/api";
export function ResourceDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [reviewStatus, setReviewStatus] = useState<string>("");
  const [reviewComment, setReviewComment] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  const [getPresignedDownloadUrl, { isLoading: isDownloading }] =
    useGetPresignedDownloadUrlMutation();
  const [reprocessRequest, { isLoading: isReprocessing }] =
    useReprocessRequestMutation();
  const [copyAndReprocessRequest, { isLoading: isCopying }] =
    useCopyAndReprocessRequestMutation();
  const [simulateReview, { isLoading: isSimulatingReview }] =
    useReviewRequestMutation();
  const [processNoteSchema, { isLoading: isProcessingNoteSchema }] =
    useProcessRequestNoteSchemaMutation();
  const [generatePlanOfCare, { isLoading: isGeneratingPlanOfCare }] =
    useGeneratePlanOfCareMutation();
  const [generatePatientChart, { isLoading: isGeneratingPatientChart }] =
    useGeneratePatientChartMutation();
  const [classifySubdocuments, { isLoading: isClassifyingSubdocuments }] =
    useClassifySubdocumentsMutation();
  const [processSubdocuments, { isLoading: isProcessingSubdocuments }] =
    useProcessSubdocumentsMutation();
  const [
    createResourcesForSubdocuments,
    { isLoading: isCreatingResourcesForSubdocuments },
  ] = useCreateResourcesForSubdocumentsMutation();
  const [noteSchemaIdInput, setNoteSchemaIdInput] = useState("");
  const [referralPdf, { isLoading: isReferralPdfing }] =
    useReferralPdfMutation();
  const [referralPdfUrlInput, setReferralPdfUrlInput] = useState("");

  // Fetch resource details
  const {
    data: resource,
    isLoading,
    error,
  } = useGetResourceQuery(id || "", {
    skip: !id,
  });

  useEffect(() => {
    console.log("resource", resource);
    if (resource && resource.noteSchemaId && !noteSchemaIdInput) {
      setNoteSchemaIdInput(resource.noteSchemaId);
    }
  }, [resource]);

  // Fetch resolved schema when in edit mode
  const { data: resolvedSchema } = useGetResolvedSchemaQuery(
    resource?.schemaId || "",
    {
      skip: !resource?.schemaId || !isEditMode,
    },
  );

  // Set initial review status when resource is loaded
  if (resource && !reviewStatus) {
    setReviewStatus(resource.status || "pending_review");
  }

  // Update and delete resource mutations
  const [updateResource, { isLoading: isUpdating }] =
    useUpdateResourceMutation();
  const [deleteResource, { isLoading: isDeleting }] =
    useDeleteResourceMutation();

  // Handle edit resource data
  const handleEditResource = () => {
    setIsEditMode(true);
  };

  // Handle save resource data
  const handleSaveResourceData = async (formData: any) => {
    if (id && resource) {
      try {
        // Create a copy of the resource with updated data
        const updatedResource = {
          ...resource,
          ...formData,
        };

        const updatedResourceWithSchemaId = {
          ...updatedResource,
          schemaId: resource.schemaId,
          id,
        };

        await updateResource(updatedResourceWithSchemaId);

        setIsEditMode(false);
      } catch (e) {
        alert("Error updating resource");
      }
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditMode(false);
  };

  // Handle delete resource
  const handleDeleteResource = async () => {
    if (
      id &&
      window.confirm("Are you sure you want to delete this resource?")
    ) {
      try {
        await deleteResource(id);
        navigate("/resources");
      } catch (e) {
        alert("Error deleting resource");
      }
    }
  };

  const handleReprocess = async () => {
    if (id) {
      try {
        await reprocessRequest(id).unwrap();
        alert("Request reprocessing started");
      } catch (e) {
        alert("Error reprocessing request");
      }
    }
  };

  const handleCopyAndReprocess = async () => {
    if (id) {
      try {
        const result = await copyAndReprocessRequest(id).unwrap();
        alert(`Copy created with ID ${result.id}`);
      } catch (e) {
        alert("Error copying and reprocessing request");
      }
    }
  };

  const handleSimulateReview = async () => {
    if (id) {
      try {
        await simulateReview(id).unwrap();
        alert("Review workflow invoked");
      } catch (e) {
        alert("Error invoking review");
      }
    }
  };

  const handleReferralPdf = async () => {
    if (id) {
      try {
        await referralPdf({
          id,
          pdfUrl: referralPdfUrlInput,
        }).unwrap();
        alert("Referral PDF workflow invoked");
      } catch (e) {
        alert("Error invoking referral PDF workflow");
      }
    }
  };

  const handleProcessNoteSchema = async () => {
    if (id && noteSchemaIdInput) {
      try {
        await processNoteSchema({
          id,
          noteSchemaId: noteSchemaIdInput,
        }).unwrap();
        alert("Note schema processing started");
      } catch (e) {
        alert("Error processing note schema");
      }
    }
  };

  const handleGeneratePlanOfCare = async () => {
    if (id) {
      try {
        await generatePlanOfCare({ id, planOfCare: {} }).unwrap();
        alert("Plan of care generation started");
      } catch (e) {
        alert("Error generating plan of care");
      }
    }
  };

  const handleGeneratePatientChart = async () => {
    if (id) {
      try {
        await generatePatientChart({ id, patientChart: {} }).unwrap();
        alert("Patient chart generation started");
      } catch (e) {
        alert("Error generating patient chart");
      }
    }
  };

  const handleClassifySubdocuments = async () => {
    if (id) {
      try {
        await classifySubdocuments({ id }).unwrap();
        alert("Subdocument classification started");
      } catch (e) {
        alert("Error classifying subdocuments");
      }
    }
  };

  const handleProcessSubdocuments = async () => {
    if (id) {
      try {
        await processSubdocuments({ id }).unwrap();
        alert("Subdocument processing started");
      } catch (e) {
        alert("Error processing subdocuments");
      }
    }
  };

  const handleCreateResourcesForSubdocuments = async () => {
    if (id) {
      try {
        await createResourcesForSubdocuments({ id }).unwrap();
        alert("Resources for subdocuments creation started");
      } catch (e) {
        alert("Error creating resources for subdocuments");
      }
    }
  };

  const handleSubmitReview = async () => {
    if (id && reviewStatus) {
      try {
        // Create a copy of the resource with updated status
        const updatedResource = {
          ...resource,
          status: reviewStatus,
          reviewComment: reviewComment || undefined,
        };

        await updateResource({
          id,
          ...updatedResource,
          schemaId: resource.schemaId,
        });

        // Navigate back to resources list
        navigate("/resources");
      } catch (e) {
        alert("Error updating resource");
      }
    }
  };

  if (isLoading) {
    return (
      <div className="admin-content">
        <Loader size="md" />
      </div>
    );
  }

  if (error || !resource) {
    return (
      <div className="admin-content">
        <Text color="red">Error loading resource details</Text>
        <Button onClick={() => navigate("/resources")} variant="light" mt="md">
          Back to Resources
        </Button>
      </div>
    );
  }

  // Format resource data for display
  const resourceData = { ...resource };

  // Remove internal fields for display
  delete resourceData.schemaId;

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">
              Resource Review
            </Title>
            <Text className="page-description">
              Review and approve or reject resource
            </Text>
          </div>
          <Group>
            <Button
              variant="light"
              onClick={() =>
                navigate(
                  resource.resourceType === "Request"
                    ? "/requests"
                    : "/resources",
                )
              }
            >
              Back to List
            </Button>
            {!isEditMode && (
              <>
                <Button
                  variant="outline"
                  color="blue"
                  onClick={handleEditResource}
                >
                  Edit
                </Button>
                <Button
                  variant="outline"
                  color="red"
                  onClick={handleDeleteResource}
                  loading={isDeleting}
                >
                  Delete
                </Button>
              </>
            )}
          </Group>
        </Group>
      </div>

      {isEditMode ? (
        <SchemaForm
          schemaId={resource.schemaId}
          resolvedSchema={resolvedSchema}
          initialData={resource}
          onSubmit={handleSaveResourceData}
          onCancel={handleCancelEdit}
          isSubmitting={isUpdating}
          agencyId={resource.agencyId}
        />
      ) : (
        <Tabs defaultValue="details">
          <Tabs.List>
            <Tabs.Tab value="details">Details</Tabs.Tab>
            <Tabs.Tab value="data">Data</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="details" pt="md">
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Title order={4} mb="md">
                Resource Information
              </Title>

              <Group mb="md">
                <Text fw={500}>ID:</Text>
                <Text>{resource.id}</Text>
              </Group>

              <Group mb="md">
                <Text fw={500}>Type:</Text>
                <Text>{resource.resourceType}</Text>
              </Group>

              <Group mb="md">
                <Text fw={500}>Agency:</Text>
                <Text>{resource.agencyId}</Text>
              </Group>

              <Group mb="md">
                <Text fw={500}>Schema:</Text>
                <Text>{resource.schemaId}</Text>
              </Group>

              <Group mb="md">
                <Text fw={500}>Status:</Text>
                <Badge
                  color={
                    resource.status === "pending_review"
                      ? "yellow"
                      : resource.status === "approved"
                        ? "green"
                        : resource.status === "rejected"
                          ? "red"
                          : "gray"
                  }
                >
                  {resource.status || "Pending Review"}
                </Badge>
              </Group>

              <Divider />

              <Group mb="md">
                <Text fw={500}>Transcriptions:</Text>
                {resource.transcription &&
                  Array.isArray(resource.transcription) &&
                  resource.transcription.length > 0 &&
                  resource.transcription.map((transcription, index) => (
                    <Stack
                      key={index}
                      p="md"
                      gap="md"
                      style={{ width: "100%", wordWrap: "break-word" }}
                    >
                      <Text key={index}>{transcription.urlPath}</Text>
                      <Button
                        variant="outline"
                        color="blue"
                        onClick={() => {
                          getPresignedDownloadUrl(transcription.urlPath)
                            .unwrap()
                            .then((url) => {
                              console.log("path", transcription.urlPath);
                              console.log("url", url);
                              window.open(url.presignedUrl, "_blank");
                            });
                        }}
                      >
                        Download
                      </Button>
                      <ScrollArea h={400}>
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {transcription.text || "no transcript"}
                        </ReactMarkdown>
                      </ScrollArea>
                    </Stack>
                  ))}
              </Group>

              {resource.reviewComment && (
                <Group mb="md">
                  <Text fw={500}>Review Comment:</Text>
                  <Text>{resource.reviewComment}</Text>
                </Group>
              )}

              {resource.resourceType === "Request" && (
                <>
                  <Divider mb="md" />
                  <Stack gap="sm">
                    <Group>
                      <Button
                        onClick={handleReprocess}
                        loading={isReprocessing}
                      >
                        Reprocess Request
                      </Button>
                      <Button
                        onClick={handleCopyAndReprocess}
                        loading={isCopying}
                      >
                        Copy and Reprocess
                      </Button>
                      <Button
                        onClick={handleSimulateReview}
                        loading={isSimulatingReview}
                        variant="filled"
                        color="red"
                      >
                        Simulate Review
                      </Button>
                    </Group>
                    <Group>
                      <TextInput
                        placeholder="Referral PDF URL"
                        value={referralPdfUrlInput}
                        onChange={(e) => setReferralPdfUrlInput(e.target.value)}
                        w={250}
                      />
                      <Button
                        onClick={handleReferralPdf}
                        loading={isReferralPdfing}
                        variant="outline"
                      >
                        Convert PDF to Markdown
                      </Button>
                    </Group>
                    <Group>
                      <Button
                        onClick={handleGeneratePlanOfCare}
                        loading={isGeneratingPlanOfCare}
                        variant="outline"
                      >
                        Generate Plan of Care
                      </Button>
                    </Group>
                    <Group>
                      <Button
                        onClick={handleGeneratePatientChart}
                        loading={isGeneratingPatientChart}
                        variant="outline"
                      >
                        Generate Patient Chart
                      </Button>
                    </Group>
                    <Group>
                      <Button
                        onClick={handleClassifySubdocuments}
                        loading={isClassifyingSubdocuments}
                        variant="outline"
                      >
                        Classify Subdocuments
                      </Button>
                    </Group>
                    <Group>
                      <Button
                        onClick={handleProcessSubdocuments}
                        loading={isProcessingSubdocuments}
                        variant="outline"
                      >
                        Process Subdocuments
                      </Button>
                    </Group>
                    <Group>
                      <Button
                        onClick={handleCreateResourcesForSubdocuments}
                        loading={isCreatingResourcesForSubdocuments}
                        variant="outline"
                      >
                        Create Resources for Subdocuments
                      </Button>
                    </Group>
                    {!resource.noteSchemaId && (
                      <Group>
                        <TextInput
                          placeholder="Note Schema ID"
                          value={noteSchemaIdInput}
                          onChange={(e) => setNoteSchemaIdInput(e.target.value)}
                          w={250}
                        />
                        <Button
                          onClick={handleProcessNoteSchema}
                          loading={isProcessingNoteSchema}
                          variant="outline"
                        >
                          Process Note Schema
                        </Button>
                      </Group>
                    )}
                  </Stack>
                </>
              )}
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="data" pt="md">
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Title order={4} mb="md">
                Resource Data
              </Title>

              <JsonInput
                value={JSON.stringify(resourceData, null, 2)}
                readOnly
                minRows={20}
                formatOnBlur
                autosize
                styles={{ input: { fontFamily: "monospace" } }}
              />
            </Card>
          </Tabs.Panel>
        </Tabs>
      )}
    </div>
  );
}
