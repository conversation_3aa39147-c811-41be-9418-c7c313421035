import { useState, useEffect } from 'react';
import { Title, Text, Button, Table, Group, Card, Badge, Tabs, Select, Loader } from '@mantine/core';
import { Link, useNavigate } from 'react-router-dom';
import { useGetAgenciesQuery, useGetResourcesByTypeAndStatusQuery, useGetResourcesQuery } from '../store/api';

export function ResourcesPage() {
  const navigate = useNavigate();
  const [resourceStatus, setResourceStatus] = useState('all');
  const [resourceType, setResourceType] = useState('Patient');
  const [selectedAgency, setSelectedAgency] = useState('all');
  const { data: agencies, isLoading: agenciesLoading, error: agenciesError } = useGetAgenciesQuery(undefined);

  
  // Fetch resources using both type and status
  const { data: resources, isLoading, error } = useGetResourcesByTypeAndStatusQuery({
    type: resourceType,
    status: resourceStatus,
    agencyId: selectedAgency
  });

  
  // Resource type options
  const typeOptions = [
    { value: 'Patient', label: 'Patient' },
    { value: 'Medication', label: 'Medication' },
    { value: 'Note', label: 'Note' },
  ];

  const getResourceTitle = (resource: any) => {
    if (resource.name) return resource.name;
    if (resource.title) return resource.title;
    if (resource.firstName && resource.lastName) return resource.firstName + ' ' + resource.lastName;
    return 'Unknown';
  }

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">Resources</Title>
            <Text className="page-description">
              Review and manage resources
            </Text>
          </div>
          <Group>
          <Select
              label="Agency"
              value={selectedAgency}
              onChange={setSelectedAgency}
              data={[{ value: 'all', label: 'All' }, ...(agencies?.map((agency) => ({ value: agency.id, label: agency.name })) || [])]}
              w={200}
            />
            <Select
              label="Resource Type"
              value={resourceType}
              onChange={(value) => setResourceType(value || 'Patient')}
              data={typeOptions}
              w={200}
            />
            <Button
              onClick={() => navigate('/resources/create')}
            >
              Create Resource
            </Button>
          </Group>
        </Group>
      </div>

      {isLoading ? (
        <Loader size="md" />
      ) : error ? (
        <Text color="red">Error loading resources</Text>
      ) : (
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Title</Table.Th>
              <Table.Th>ID</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Agency</Table.Th>
              <Table.Th>Created</Table.Th>
              <Table.Th style={{ width: 120 }}>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {resources?.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} align="center">No resources found</Table.Td>
              </Table.Tr>
            ) : ( 
              resources?.map((resource: any) => (
                <Table.Tr key={resource.id}>
                  <Table.Td>{getResourceTitle(resource)}</Table.Td>
                  <Table.Td>{resource.id}</Table.Td>
                  <Table.Td>{resource.resourceType}</Table.Td>
                  <Table.Td>{resource.agencyName}</Table.Td>
                  <Table.Td>
                    {resource.createdAt ? new Date(resource.createdAt).toLocaleDateString() : 'N/A'}
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Button
                        component={Link}
                        to={`/resources/${resource.id}`}
                        size="xs"
                        variant="light"
                      >
                        Edit
                      </Button>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      )}
    </div>
  );
}
