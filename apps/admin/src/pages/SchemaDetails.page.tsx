import { useState, useEffect, useCallback } from 'react';
import { JsonEditor } from 'json-edit-react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Title, 
  Text, 
  Button, 
  Group, 
  Card, 
  Textarea, 
  TextInput,
  Loader,
  Badge,
  Tabs,
  JsonInput,
  Alert
} from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
import { SchemaMarkdownView } from '../components/SchemaMarkdownView';
import { UISchemaManager } from '../components/UISchemaManager';
import { SchemaVisualizer } from '../components/SchemaVisualizer';
import { 
  useGetSchemaQuery, 
  useUpdateSchemaMutation,
  useValidateSchemaMutation
} from '../store/api';

export function SchemaDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [editMode, setEditMode] = useState(false);
  const [schemaVersion, setSchemaVersion] = useState('');
  const [schemaContent, setSchemaContent] = useState('');
  const [selectedUISchema, setSelectedUISchema] = useState<any>(null);
  const [testData, setTestData] = useState('{\n  \n}');
  const [validationResult, setValidationResult] = useState<any>(null);
  
  // Fetch schema details
  const { 
    data: schema, 
    isLoading, 
    error 
  } = useGetSchemaQuery(id || '', { 
    skip: !id
  });

  // Set schema content when data is loaded
  useEffect(() => {
    if (schema && !editMode) {
      setSchemaVersion(schema.version);
      setSchemaContent(JSON.stringify(schema.schema, null, 2));
    }
  }, [schema, editMode]);
  
  // Handle UI schema selection
  const handleSelectUISchema = useCallback((uiSchema: any) => {
    setSelectedUISchema(uiSchema);
  }, []);
  
  // Update schema mutation
  const [updateSchema, { isLoading: isUpdating }] = useUpdateSchemaMutation();
  
  // Validate schema mutation
  const [validateSchema, { isLoading: isValidating }] = useValidateSchemaMutation();

  // Handle save changes
  const handleSaveChanges = async () => {
    if (id && schemaVersion.trim() && schemaContent.trim()) {
      try {
        const parsedSchema = JSON.parse(schemaContent);
        let parsedUISchema = {};
        
        const updateRequest = {
          id,
          version: schemaVersion.trim(),
          schema: parsedSchema
        };
        
        await updateSchema(updateRequest);
        
        setEditMode(false);
      } catch (e) {
        alert('Invalid JSON schema format');
      }
    }
  };
  
  // Handle validate data
  const handleValidateData = async () => {
    if (id && testData.trim()) {
      try {
        const parsedData = JSON.parse(testData);
        
        const validateRequest = {
          schemaId: id,
          data: parsedData
        };
        
        const result = await validateSchema(validateRequest).unwrap();
        
        setValidationResult(result);
      } catch (e) {
        alert('Invalid JSON data format');
      }
    }
  };

  if (isLoading) {
    return <div className="admin-content"><Loader size="md" /></div>;
  }

  if (error || !schema) {
    return (
      <div className="admin-content">
        <Text color="red">Error loading schema details</Text>
        <Button onClick={() => navigate('/schemas')} variant="light" mt="md">
          Back to Schemas
        </Button>
      </div>
    );
  }

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">{schema.name} Schema Details</Title>
            <Text className="page-description">
              View and manage schema definition
            </Text>
          </div>
          <Group>
            <Button 
              variant="light" 
              onClick={() => navigate('/schemas')}
            >
              Back to List
            </Button>
            {!editMode ? (
              <Button onClick={() => setEditMode(true)}>Edit</Button>
            ) : (
              <Group>
                <Button variant="light" onClick={() => {
                  setEditMode(false);
                  setSchemaVersion(schema.version);
                  setSchemaContent(JSON.stringify(schema.schema, null, 2));
                }}>
                  Cancel
                </Button>
                <Button onClick={handleSaveChanges} loading={isUpdating}>
                  Save Changes
                </Button>
              </Group>
            )}
          </Group>
        </Group>
      </div>

      <Tabs defaultValue="visualEditor">
        <Tabs.List>
          <Tabs.Tab value="details">Details</Tabs.Tab>
          <Tabs.Tab value="visualEditor">Visual Editor</Tabs.Tab>
          <Tabs.Tab value="schema">JSON Schema</Tabs.Tab>
          <Tabs.Tab value="uiSchema">UI Schema</Tabs.Tab>
          <Tabs.Tab value="markdown">Markdown</Tabs.Tab>
          <Tabs.Tab value="validate">Validate</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="details" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Title order={4} mb="md">Schema Information</Title>
            
            <Group mb="md">
              <Text fw={500}>ID:</Text>
              <Text>{schema.id}</Text>
            </Group>
            
            <Group mb="md">
              <Text fw={500}>Name:</Text>
              <Text>{schema.name}</Text>
            </Group>
            
            {editMode ? (
              <TextInput
                label="Version"
                value={schemaVersion}
                onChange={(e) => setSchemaVersion(e.target.value)}
                mb="md"
              />
            ) : (
              <Group mb="md">
                <Text fw={500}>Version:</Text>
                <Text>{schema.version}</Text>
              </Group>
            )}
            
            <Group mb="md">
              <Text fw={500}>Agency:</Text>
              <Text>{schema.agencyId}</Text>
            </Group>
            
            <Group mb="md">
              <Text fw={500}>Base Schema:</Text>
              {schema.baseSchemaId ? (
                <Badge color="blue">{schema.baseSchemaId}</Badge>
              ) : (
                <Badge color="gray">None</Badge>
              )}
            </Group>
            
            <Group mb="md">
              <Text fw={500}>Created:</Text>
              <Text>{new Date(schema.createdAt).toLocaleString()}</Text>
            </Group>
            
            <Group mb="md">
              <Text fw={500}>Updated:</Text>
              <Text>{new Date(schema.updatedAt).toLocaleString()}</Text>
            </Group>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="visualEditor" pt="md">
          <Alert color="blue" title="Visual Schema Editor" mb="md">
            <Text>Use this visual editor to create and modify your JSON Schema structure. Changes made here will be synchronized with the JSON editor.</Text>
          </Alert>
          
          {editMode ? (
            <SchemaVisualizer 
              initialSchema={schemaContent ? JSON.parse(schemaContent) : {}}
              onChange={(updatedSchema) => {
                setSchemaContent(JSON.stringify(updatedSchema, null, 2));
              }}
            />
          ) : (
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Text mb="md">Click the "Edit" button above to enable the visual schema editor.</Text>
              <Button onClick={() => setEditMode(true)}>Edit Schema</Button>
            </Card>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="schema" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Title order={4} mb="md">Schema Definition</Title>

              <JsonEditor
                data={schemaContent ? JSON.parse(schemaContent) : {}}
                setData={(e) => setSchemaContent(JSON.stringify(e, null, 2))}
              />
          </Card>
        </Tabs.Panel>
        
        <Tabs.Panel value="uiSchema" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="md">
              <div>
                <Title order={4}>UI Schemas</Title>
                <Text size="sm" color="dimmed">
                  UI schemas customize how the schema is displayed in the markdown view.
                </Text>
              </div>
            </Group>
            
            <div style={{ display: 'grid', gridTemplateColumns: '300px 1fr', gap: '20px' }}>
              {/* UI Schema Manager */}
              <div>
                {schema && (
                  <UISchemaManager 
                    schemaId={schema.id} 
                    onSelectUISchema={handleSelectUISchema}
                    selectedUISchemaId={selectedUISchema?.id}
                  />
                )}
              </div>
              
              {/* UI Schema Content */}
              <div>
                {selectedUISchema ? (
                  <>
                    <Title order={5} mb="md">{selectedUISchema.name}</Title>
                    {selectedUISchema.description && (
                      <Text size="sm" color="dimmed" mb="md">{selectedUISchema.description}</Text>
                    )}
                    <JsonInput
                      value={JSON.stringify(selectedUISchema.content, null, 2)}
                      readOnly
                      minRows={20}
                      formatOnBlur
                      autosize
                      styles={{ input: { fontFamily: 'monospace' } }}
                    />
                  </>
                ) : (
                  <Text color="dimmed">Select a UI schema to view its content</Text>
                )}
              </div>
            </div>
          </Card>
        </Tabs.Panel>
        
        <Tabs.Panel value="markdown" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <SchemaMarkdownView 
              schema={schema.schema} 
              schemaContent={schemaContent}
              uiSchema={selectedUISchema?.content}
            />
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="validate" pt="md">
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Title order={4} mb="md">Validate Data</Title>
            
            <Text mb="md">
              Test data against this schema to validate it.
            </Text>
            
            <Textarea
              label="Test Data (JSON)"
              value={testData}
              onChange={(e) => setTestData(e.target.value)}
              minRows={10}
              mb="md"
              styles={{ input: { fontFamily: 'monospace' } }}
            />
            
            <Button 
              onClick={handleValidateData} 
              loading={isValidating}
              mb="xl"
            >
              Validate
            </Button>
            
            {validationResult && (
              <div>
                <Title order={5} mb="md">Validation Result</Title>
                
                {validationResult.valid ? (
                  <Badge color="green" size="lg">Valid</Badge>
                ) : (
                  <>
                    <Badge color="red" size="lg" mb="md">Invalid</Badge>
                    
                    {validationResult.errors && (
                      <div>
                        <Text fw={500} mb="xs">Errors:</Text>
                        <JsonInput
                          value={JSON.stringify(validationResult.errors, null, 2)}
                          readOnly
                          minRows={10}
                          formatOnBlur
                          autosize
                          styles={{ input: { fontFamily: 'monospace' } }}
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </Card>
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
