import { useState } from 'react';
import { Title, Text, Button, Table, Group, Card, Badge, Modal, TextInput, Textarea, Box } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useGetSchemasQuery, useGetBaseSchemasQuery, useCreateSchemaMutation } from '../store/api';

export function SchemasPage() {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [newSchemaName, setNewSchemaName] = useState('');
  const [newSchemaVersion, setNewSchemaVersion] = useState('1.0');
  const [newSchemaContent, setNewSchemaContent] = useState('{\n  "type": "object",\n  "properties": {\n    \n  }\n}');
  const [selectedBaseSchema, setSelectedBaseSchema] = useState<string | null>(null);
  
  // Fetch schemas
  const { data: schemas, isLoading, error } = useGetSchemasQuery(undefined);
  const { data: baseSchemas } = useGetBaseSchemasQuery(undefined);
  
  // Mutations
  const [createSchema, { isLoading: isCreating }] = useCreateSchemaMutation();

  // Handle create schema
  const handleCreateSchema = async () => {
    if (newSchemaName.trim() && newSchemaVersion.trim()) {
      try {
        const schemaContent = JSON.parse(newSchemaContent);
        
        await createSchema({
          name: newSchemaName.trim(),
          version: newSchemaVersion.trim(),
          schema: schemaContent,
          baseSchemaId: selectedBaseSchema || undefined
        });
        
        // Reset form
        setNewSchemaName('');
        setNewSchemaVersion('1.0');
        setNewSchemaContent('{\n  "type": "object",\n  "properties": {\n    \n  }\n}');
        setSelectedBaseSchema(null);
        setCreateModalOpen(false);
      } catch (e) {
        alert('Invalid JSON schema format');
      }
    }
  };

  // Handle view schema details
  const handleViewSchema = (id: string) => {
    // Navigate to schema details page
  };

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">Schemas</Title>
            <Text className="page-description">
              Manage data schemas and validation rules
            </Text>
          </div>
          <Button onClick={() => setCreateModalOpen(true)}>Add Schema</Button>
        </Group>
      </div>

      {isLoading ? (
        <Text>Loading schemas...</Text>
      ) : error ? (
        <Text color="red">Error loading schemas</Text>
      ) : (
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>ID</Table.Th>
              <Table.Th>Name</Table.Th>
              <Table.Th>Version</Table.Th>
              <Table.Th>Agency</Table.Th>
              <Table.Th>Base Schema</Table.Th>
              <Table.Th style={{ width: 120 }}>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {schemas?.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} align="center">No schemas found</Table.Td>
              </Table.Tr>
            ) : (
              schemas?.map((schema: any) => (
                <Table.Tr key={schema.id}>
                  <Table.Td>{schema.id}</Table.Td>
                  <Table.Td>{schema.name}</Table.Td>
                  <Table.Td>{schema.version}</Table.Td>
                  <Table.Td>{schema.agencyId}</Table.Td>
                  <Table.Td>
                    {schema.baseSchemaId ? (
                      <Badge color="blue">Has Base Schema</Badge>
                    ) : (
                      <Badge color="gray">None</Badge>
                    )}
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Button
                        component={Link}
                        to={`/schemas/${schema.id}`}
                        size="xs"
                        variant="light"
                      >
                        View
                      </Button>
                      <Button
                        size="xs"
                        color="blue"
                        variant="light"
                        onClick={() => handleViewSchema(schema.id)}
                      >
                        Details
                      </Button>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      )}

      {/* Create Schema Modal */}
      <Modal
        opened={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        title="Create New Schema"
        size="lg"
        centered
      >
        <Box mx="auto">
          <TextInput
            label="Schema Name"
            placeholder="Enter schema name"
            value={newSchemaName}
            onChange={(e) => setNewSchemaName(e.target.value)}
            required
            mb="md"
          />
          
          <TextInput
            label="Version"
            placeholder="1.0"
            value={newSchemaVersion}
            onChange={(e) => setNewSchemaVersion(e.target.value)}
            required
            mb="md"
          />
          
          {baseSchemas && baseSchemas.length > 0 && (
            <div className="form-section">
              <Text fw={500} mb="xs">Base Schema (Optional)</Text>
              <Group mb="md">
                <Button 
                  variant={selectedBaseSchema === null ? "filled" : "light"}
                  onClick={() => setSelectedBaseSchema(null)}
                  size="xs"
                >
                  None
                </Button>
                {baseSchemas.map((baseSchema: any) => (
                  <Button
                    key={baseSchema.id}
                    variant={selectedBaseSchema === baseSchema.id ? "filled" : "light"}
                    onClick={() => setSelectedBaseSchema(baseSchema.id)}
                    size="xs"
                  >
                    {baseSchema.name} v{baseSchema.version}
                  </Button>
                ))}
              </Group>
            </div>
          )}
          
          <Textarea
            label="Schema Definition (JSON)"
            placeholder="Enter JSON schema"
            value={newSchemaContent}
            onChange={(e) => setNewSchemaContent(e.target.value)}
            required
            minRows={10}
            mb="md"
            styles={{ input: { fontFamily: 'monospace' } }}
          />
          
          <Group justify="flex-end">
            <Button variant="light" onClick={() => setCreateModalOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateSchema} loading={isCreating}>Create</Button>
          </Group>
        </Box>
      </Modal>
    </div>
  );
}
