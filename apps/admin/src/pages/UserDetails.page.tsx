import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Title, 
  Text, 
  Button, 
  Group, 
  Card, 
  TextInput, 
  Loader,
  Badge,
  Tabs,
  Select
} from '@mantine/core';
import { 
  useGetUserQuery, 
  useUpdateUserMutation,
  useGetAgenciesQuery
} from '../store/api';

export function UserDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [editMode, setEditMode] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [userRole, setUserRole] = useState('');
  const [userAgency, setUserAgency] = useState('');
  const [userStytchId, setUserStytchId] = useState('');
  
  // Fetch user details
  const { 
    data: user, 
    isLoading, 
    error 
  } = useGetUserQuery(id || '', { 
    skip: !id
  });

  // Fetch agencies for the dropdown
  const { data: agencies } = useGetAgenciesQuery(undefined);

  // Set user data when loaded
  // Use useEffect to avoid potential re-render issues
  useEffect(() => {
    if (user && !editMode && !userEmail) {
      setUserEmail(user.email);
      setUserRole(user.role || '');
      setUserAgency(user.agencyId);
      setUserStytchId(user.stytchId || '');
    }
  }, [user, editMode, userEmail]);
  
  // Update user mutation
  const [updateUser, { isLoading: isUpdating }] = useUpdateUserMutation();

  // Handle save changes
  const handleSaveChanges = async () => {
    if (id && userEmail.trim() && userRole && userAgency) {
      await updateUser({
        id,
        email: userEmail.trim(),
        resourceType: 'User',
        agencyId: userAgency,
        schemaId: user.schemaId,
        role: userRole,
        stytchId: userStytchId.trim() || undefined
      } as {id: string, email: string, resourceType: string, agencyId: string, schemaId: string, role: string, stytchId?: string});
      
      setEditMode(false);
    }
  };

  if (isLoading) {
    return <div className="admin-content"><Loader size="md" /></div>;
  }

  if (error || !user) {
    return (
      <div className="admin-content">
        <Text color="red">Error loading user details</Text>
        <Button onClick={() => navigate('/users')} variant="light" mt="md">
          Back to Users
        </Button>
      </div>
    );
  }

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">User Details</Title>
            <Text className="page-description">
              View and manage user information
            </Text>
          </div>
          <Group>
            <Button 
              variant="light" 
              onClick={() => navigate('/users')}
            >
              Back to List
            </Button>
            {!editMode ? (
              <Button onClick={() => setEditMode(true)}>Edit</Button>
            ) : (
              <Group>
                <Button variant="light" onClick={() => {
                  setEditMode(false);
                  setUserEmail(user.email);
                  setUserRole(user.role || '');
                  setUserAgency(user.agencyId);
                  setUserStytchId(user.stytchId || '');
                }}>
                  Cancel
                </Button>
                <Button onClick={handleSaveChanges} loading={isUpdating}>
                  Save Changes
                </Button>
              </Group>
            )}
          </Group>
        </Group>
      </div>

      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Title order={4} mb="md">User Information</Title>
        
        <Group mb="md">
          <Text fw={500}>ID:</Text>
          <Text>{user.id}</Text>
        </Group>
        
        {editMode ? (
          <TextInput
            label="Email"
            value={userEmail}
            onChange={(e) => setUserEmail(e.target.value)}
            mb="md"
          />
        ) : (
          <Group mb="md">
            <Text fw={500}>Email:</Text>
            <Text>{user.email}</Text>
          </Group>
        )}
        
        {editMode ? (
          <Select
            label="User Role"
            value={userRole}
            onChange={(value) => setUserRole(value || user.role || '')}
            data={[
              { value: 'Admin', label: 'Administrator' },
              { value: 'Nurse', label: 'Nurse' },
              { value: 'Social Worker', label: 'Social Worker' },
              { value: 'Chaplain', label: 'Chaplain' },
              { value: 'Physician', label: 'Physician' },
              { value: 'Volunteer', label: 'Volunteer' },
              { value: 'Aide', label: 'Aide' },
              { value: 'Surveyor', label: 'Surveyor' }
            ]}
            mb="md"
          />
        ) : (
          <Group mb="md">
            <Text fw={500}>Role:</Text>
            <Badge color={user.role === 'Admin' ? 'blue' : 'green'}>
              {user.role || 'None'}
            </Badge>
          </Group>
        )}
        
        {editMode ? (
          <Select
            label="Agency"
            value={userAgency}
            onChange={(value) => setUserAgency(value || user.agencyId)}
            data={agencies?.map((agency: any) => ({
              value: agency.id,
              label: agency.name
            })) || []}
            mb="md"
          />
        ) : (
          <Group mb="md">
            <Text fw={500}>Agency:</Text>
            <Text>{user.agencyId}</Text>
          </Group>
        )}
        
        <Group mb="md">
          <Text fw={500}>Type:</Text>
          <Text>{user.resourceType}</Text>
        </Group>
        
        {editMode ? (
          <TextInput
            label="Stytch Member ID"
            value={userStytchId}
            onChange={(e) => setUserStytchId(e.target.value)}
            mb="md"
            placeholder="Optional - For Stytch B2B integration"
          />
        ) : user.stytchId ? (
          <Group mb="md">
            <Text fw={500}>Stytch Member ID:</Text>
            <Text>{user.stytchId}</Text>
          </Group>
        ) : null}
        
        {user.createdAt && (
          <Group mb="md">
            <Text fw={500}>Created:</Text>
            <Text>{new Date(user.createdAt).toLocaleString()}</Text>
          </Group>
        )}
        
        {user.updatedAt && (
          <Group mb="md">
            <Text fw={500}>Updated:</Text>
            <Text>{new Date(user.updatedAt).toLocaleString()}</Text>
          </Group>
        )}
      </Card>
    </div>
  );
}
