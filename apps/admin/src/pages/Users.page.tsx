import { useState, useEffect } from "react";
import {
  Title,
  Text,
  Button,
  Table,
  Group,
  Card,
  Badge,
  Modal,
  TextInput,
  Box,
  Select,
  Loader,
} from "@mantine/core";
import { Link } from "react-router-dom";
import {
  useGetUsersQuery,
  useGetAgenciesQuery,
  useCreateUserMutation,
  useGetBaseSchemasQuery,
} from "../store/api";

export function UsersPage() {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [newUserFirstName, setNewUserFirstName] = useState("");
  const [newUserLastName, setNewUserLastName] = useState("");
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserRole, setNewUserRole] = useState("Admin");
  const [selectedAgency, setSelectedAgency] = useState<string>("all");
  const [newUserStytchId, setNewUserStytchId] = useState("");

  // Fetch users
  const {
    data: users,
    isLoading,
    error,
  } = useGetUsersQuery({ role: "all", agencyId: selectedAgency });

  // Fetch agencies for the dropdown
  const { data: agencies } = useGetAgenciesQuery(undefined);

  // Fetch base schemas to get the UserBase schema ID
  const { data: baseSchemas, isLoading: isSchemasLoading } =
    useGetBaseSchemasQuery(undefined);

  // Find the UserBase schema ID
  const [userSchemaId, setUserSchemaId] = useState<string | null>(null);

  useEffect(() => {
    if (baseSchemas) {
      const userBaseSchema = baseSchemas.find(
        (schema: any) => schema.name === "UserBase",
      );
      if (userBaseSchema) {
        setUserSchemaId(userBaseSchema.id);
      }
    }
  }, [baseSchemas]);

  // Mutations
  const [createUser, { isLoading: isCreating }] = useCreateUserMutation();

  // Handle create user
  const handleCreateUser = async () => {
    if (newUserEmail.trim() && newUserRole && selectedAgency && userSchemaId) {
      await createUser({
        firstName: newUserFirstName.trim(),
        lastName: newUserLastName.trim(),
        email: newUserEmail.trim(),
        resourceType: "User",
        agencyId: selectedAgency,
        schemaId: userSchemaId,
        role: newUserRole,
        stytchId: newUserStytchId.trim() || undefined,
      });

      // Reset form
      setNewUserFirstName("");
      setNewUserLastName("");
      setNewUserEmail("");
      setNewUserRole("Admin");
      setSelectedAgency("all");
      setNewUserStytchId("");
      setCreateModalOpen(false);
    }
  };

  return (
    <div className="admin-content">
      <div className="page-header">
        <Group justify="space-between">
          <div>
            <Title order={2} className="page-title">
              Users
            </Title>
            <Text className="page-description">
              Manage users across all agencies
            </Text>
          </div>
          <Select
            label="Agency"
            value={selectedAgency}
            onChange={setSelectedAgency}
            data={[
              { value: "all", label: "All" },
              ...(agencies?.map((agency) => ({
                value: agency.id,
                label: agency.name,
              })) || []),
            ]}
            w={200}
          />
          <Button onClick={() => setCreateModalOpen(true)}>Add User</Button>
        </Group>
      </div>

      {isLoading || isSchemasLoading ? (
        <Text>Loading...</Text>
      ) : error ? (
        <Text color="red">Error loading users</Text>
      ) : (
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>ID</Table.Th>
              <Table.Th>Email</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Agency</Table.Th>
              <Table.Th style={{ width: 120 }}>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {users?.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={5} align="center">
                  No users found
                </Table.Td>
              </Table.Tr>
            ) : (
              users?.map((user: any) => (
                <Table.Tr key={user.id}>
                  <Table.Td>{user.id}</Table.Td>
                  <Table.Td>{user.email}</Table.Td>
                  <Table.Td>
                    <Badge color={user.role === "Admin" ? "blue" : "green"}>
                      {user.role}
                    </Badge>
                  </Table.Td>
                  <Table.Td>{user.agencyName}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Button
                        component={Link}
                        to={`/users/${user.id}`}
                        size="xs"
                        variant="light"
                      >
                        View
                      </Button>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      )}

      {/* Create User Modal */}
      <Modal
        opened={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        title="Create New User"
        centered
      >
        <Box maw={400} mx="auto">
          <TextInput
            label="Name"
            placeholder="Enter user's first name"
            value={newUserFirstName}
            onChange={(e) => setNewUserFirstName(e.target.value)}
            required
            mb="md"
          />

        <TextInput
            label="Name"
            placeholder="Enter user's last name"
            value={newUserLastName}
            onChange={(e) => setNewUserLastName(e.target.value)}
            required
            mb="md"
          />

          <TextInput
            label="Email"
            placeholder="Enter user email"
            value={newUserEmail}
            onChange={(e) => setNewUserEmail(e.target.value)}
            required
            mb="md"
          />

          <Select
            label="User Role"
            placeholder="Select user role"
            value={newUserRole}
            onChange={(value) => setNewUserRole(value || "Admin")}
            data={[
              { value: "Admin", label: "Administrator" },
              { value: "Nurse", label: "Nurse" },
              { value: "Social Worker", label: "Social Worker" },
              { value: "Chaplain", label: "Chaplain" },
              { value: "Physician", label: "Physician" },
              { value: "Volunteer", label: "Volunteer" },
              { value: "Aide", label: "Aide" },
              { value: "Surveyor", label: "Surveyor" },
            ]}
            required
            mb="md"
          />

          <Select
            label="Agency"
            placeholder="Select agency"
            value={selectedAgency}
            onChange={setSelectedAgency}
            data={
              agencies?.map((agency: any) => ({
                value: agency.id,
                label: agency.name,
              })) || []
            }
            required
            mb="md"
          />

          {/* <TextInput
            label="Stytch Member ID"
            placeholder="Optional - For Stytch B2B integration"
            value={newUserStytchId}
            onChange={(e) => setNewUserStytchId(e.target.value)}
            mb="md"
          /> */}

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setCreateModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateUser}
              loading={isCreating}
              disabled={!userSchemaId}
            >
              Create
            </Button>
          </Group>
        </Box>
      </Modal>
    </div>
  );
}
