import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const api = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.API_BASE_URL + "/api/admin",
    credentials: "include", // Include cookies in the request
  }),
  tagTypes: ["Agencies", "Users", "Schemas", "Resources", "UISchemas"],
  endpoints: (builder) => ({
    // Agency endpoints
    getAgencies: builder.query({
      query: () => "/agencies",
      providesTags: ["Agencies"],
    }),
    getAgency: builder.query({
      query: (id: string) => `/agencies/${id}`,
      providesTags: (result, error, id: string) => [{ type: "Agencies", id }],
    }),
    createAgency: builder.mutation({
      query: (agency) => ({
        url: "/agencies",
        method: "POST",
        body: agency,
      }),
      invalidatesTags: ["Agencies"],
    }),
    updateAgency: builder.mutation({
      query: ({ id, ...agency }: { id: string; [key: string]: any }) => ({
        url: `/agencies/${id}`,
        method: "PUT",
        body: agency,
      }),
      invalidatesTags: (result, error, { id }: { id: string }) => [
        { type: "Agencies", id },
      ],
    }),
    deleteAgency: builder.mutation({
      query: (id: string) => ({
        url: `/agencies/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Agencies"],
    }),

    // User endpoints
    getUsers: builder.query({
      query: ({ role, agencyId }: { role: string; agencyId: string | null }) =>
        `/users/role/${role}?agencyId=${agencyId}`,
      providesTags: ["Users"],
    }),
    getUser: builder.query({
      query: (id: string) => `/users/id/${id}`,
      providesTags: (result, error, id: string) => [{ type: "Users", id }],
    }),
    createUser: builder.mutation({
      query: (user) => ({
        url: "/users",
        method: "POST",
        body: user,
      }),
      invalidatesTags: ["Users"],
    }),
    updateUser: builder.mutation({
      query: ({ id, ...user }: { id: string; [key: string]: any }) => ({
        url: `/users/${id}`,
        method: "PUT",
        body: user,
      }),
      invalidatesTags: (result, error, { id }: { id: string }) => [
        { type: "Users", id },
      ],
    }),
    deleteUser: builder.mutation({
      query: (id: string) => ({
        url: `/users/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Users"],
    }),

    // Schema endpoints
    getSchemas: builder.query({
      query: () => "/schemas",
      providesTags: ["Schemas"],
    }),
    getBaseSchemas: builder.query({
      query: () => "/schemas/base",
      providesTags: ["Schemas"],
    }),
    getSchema: builder.query({
      query: (id: string) => `/schemas/${id}`,
      providesTags: (result, error, id: string) => [{ type: "Schemas", id }],
    }),
    getResolvedSchema: builder.query({
      query: (id: string) => `/schemas/${id}/resolved`,
      providesTags: (result, error, id: string) => [{ type: "Schemas", id }],
    }),
    createSchema: builder.mutation({
      query: (schema) => ({
        url: "/schemas",
        method: "POST",
        body: schema,
      }),
      invalidatesTags: ["Schemas"],
    }),
    updateSchema: builder.mutation({
      query: ({
        id,
        version,
        schema,
        uiSchema,
      }: {
        id: string;
        version: string;
        schema: any;
        uiSchema?: any;
      }) => ({
        url: `/schemas/${id}`,
        method: "PUT",
        body: { version, schema, uiSchema },
      }),
      invalidatesTags: (result, error, { id }: { id: string }) => [
        { type: "Schemas", id },
      ],
    }),
    validateSchema: builder.mutation({
      query: (validation) => ({
        url: "/schemas/validate",
        method: "POST",
        body: validation,
      }),
    }),

    // UI Schema endpoints
    getUISchemasBySchema: builder.query({
      query: (schemaId: string) => `/ui-schemas/schema/${schemaId}`,
      providesTags: (result, error, schemaId: string) => [
        { type: "UISchemas", id: `schema-${schemaId}` },
        ...(result
          ? result.map(({ id }: { id: string }) => ({
              type: "UISchemas" as const,
              id,
            }))
          : []),
      ],
    }),
    getUISchema: builder.query({
      query: (id: string) => `/ui-schemas/${id}`,
      providesTags: (result, error, id: string) => [{ type: "UISchemas", id }],
    }),
    createUISchema: builder.mutation({
      query: (uiSchema: any) => ({
        url: "/ui-schemas",
        method: "POST",
        body: uiSchema,
      }),
      invalidatesTags: (result: any, error: any, arg: any) => [
        "UISchemas",
        { type: "UISchemas", id: `schema-${result?.schemaId}` },
      ],
    }),
    updateUISchema: builder.mutation({
      query: ({ id, ...uiSchema }: { id: string; [key: string]: any }) => ({
        url: `/ui-schemas/${id}`,
        method: "PUT",
        body: uiSchema,
      }),
      invalidatesTags: (result, error, { id }: { id: string }) => [
        { type: "UISchemas", id },
        { type: "UISchemas", id: `schema-${result?.schemaId}` },
      ],
    }),
    deleteUISchema: builder.mutation({
      query: (id: string) => ({
        url: `/ui-schemas/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["UISchemas"],
    }),

    // Resource endpoints
    getResources: builder.query({
      query: (status: string) => `/resources/${status}`,
      providesTags: ["Resources"],
    }),
    getResourcesByTypeAndStatus: builder.query({
      query: ({
        type,
        status,
        agencyId,
      }: {
        type: string;
        status: string;
        agencyId?: string;
      }) => `/resources?type=${type}&status=${status}&agencyId=${agencyId}`,
      providesTags: ["Resources"],
    }),
    getResource: builder.query({
      query: (id: string) => `/resources/id/${id}`,
      providesTags: (result, error, id: string) => [{ type: "Resources", id }],
    }),
    createResource: builder.mutation({
      query: (resource) => ({
        url: "/resources",
        method: "POST",
        body: resource,
      }),
      invalidatesTags: ["Resources"],
    }),
    updateResource: builder.mutation({
      query: ({ id, ...resource }: { id: string; [key: string]: any }) => ({
        url: `/resources/${id}`,
        method: "PUT",
        body: { ...resource, id }, // Include id in the body
      }),

      invalidatesTags: (result, error, { id }: { id: string }) => [
        { type: "Resources", id },
      ],
    }),
    deleteResource: builder.mutation({
      query: (id: string) => ({
        url: `/resources/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Resources"],
    }),
    getPresignedDownloadUrl: builder.mutation({
      query: (urlPath: string) => ({
        url: `/presigned-download-url?urlPath=${urlPath}`,
        method: "GET",
      }),
    }),

    // Workflow management endpoints
    reprocessRequest: builder.mutation<{ invocationId: string }, string>({
      query: (id: string) => ({
        url: `/requests/${id}/reprocess`,
        method: "POST",
      }),
    }),
    copyAndReprocessRequest: builder.mutation<
      { id: string; invocationId: string },
      string
    >({
      query: (id: string) => ({
        url: `/requests/${id}/copy-reprocess`,
        method: "POST",
      }),
    }),
    reviewRequest: builder.mutation<{ invocationId: string }, string>({
      query: (id: string) => ({
        url: `/requests/${id}/review`,
        method: "POST",
      }),
    }),
    processRequestNoteSchema: builder.mutation<
      { invocationId: string },
      { id: string; noteSchemaId: string }
    >({
      query: ({ id, noteSchemaId }) => ({
        url: `/requests/${id}/process-note-schema`,
        method: "POST",
        body: { noteSchemaId },
      }),
    }),
    referralPdf: builder.mutation<
      { invocationId: string },
      { id: string; pdfUrl: string }
    >({
      query: ({ id, pdfUrl }) => ({
        url: `/requests/${id}/referral-pdf`,
        method: "POST",
        body: { pdfUrl },
      }),
    }),
    generatePlanOfCare: builder.mutation<
      { invocationId: string },
      { id: string; planOfCare: any }
    >({
      query: ({ id, planOfCare }) => ({
        url: `/requests/${id}/generate-plan-of-care`,
        method: "POST",
        body: { planOfCare },
      }),
    }),
    generatePatientChart: builder.mutation<
      { invocationId: string },
      { id: string; patientChart: any }
    >({
      query: ({ id, patientChart }) => ({
        url: `/requests/${id}/generate-patient-chart`,
        method: "POST",
        body: { patientChart },
      }),
    }),
    classifySubdocuments: builder.mutation<
      { invocationId: string },
      { id: string }
    >({
      query: ({ id }) => ({
        url: `/requests/${id}/classify-subdocuments`,
        method: "POST",
      }),
    }),
    processSubdocuments: builder.mutation<
      { invocationId: string },
      { id: string }
    >({
      query: ({ id }) => ({
        url: `/requests/${id}/process-subdocuments`,
        method: "POST",
      }),
    }),
    createResourcesForSubdocuments: builder.mutation<
      { invocationId: string },
      { id: string }
    >({
      query: ({ id }) => ({
        url: `/requests/${id}/create-resources-for-subdocuments`,
        method: "POST",
      }),
    }),
  }),
});

export const {
  // Agency hooks
  useGetAgenciesQuery,
  useGetAgencyQuery,
  useCreateAgencyMutation,
  useUpdateAgencyMutation,
  useDeleteAgencyMutation,

  // User hooks
  useGetUsersQuery,
  useGetUserQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,

  // Schema hooks
  useGetSchemasQuery,
  useGetBaseSchemasQuery,
  useGetSchemaQuery,
  useGetResolvedSchemaQuery,
  useCreateSchemaMutation,
  useUpdateSchemaMutation,
  useValidateSchemaMutation,

  // Resource hooks
  useGetResourcesQuery,
  useGetResourcesByTypeAndStatusQuery,
  useGetResourceQuery,
  useCreateResourceMutation,
  useUpdateResourceMutation,
  useDeleteResourceMutation,

  // UI Schema hooks
  useGetUISchemasBySchemaQuery,
  useGetUISchemaQuery,
  useCreateUISchemaMutation,
  useUpdateUISchemaMutation,
  useDeleteUISchemaMutation,

  // Resource hooks
  useGetPresignedDownloadUrlMutation,
  useReprocessRequestMutation,
  useCopyAndReprocessRequestMutation,
  useReviewRequestMutation,
  useProcessRequestNoteSchemaMutation,
  useReferralPdfMutation,
  useGeneratePlanOfCareMutation,
  useGeneratePatientChartMutation,
  useClassifySubdocumentsMutation,
  useProcessSubdocumentsMutation,
  useCreateResourcesForSubdocumentsMutation,
} = api;
