import { configureStore } from '@reduxjs/toolkit';
import { api } from './api';
import schemaReducer from './schemaSlice';

export const store = configureStore({
  reducer: {
    [api.reducerPath]: api.reducer,
    schema: schemaReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(api.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
