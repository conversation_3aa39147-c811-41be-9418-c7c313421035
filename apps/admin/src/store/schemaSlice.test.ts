// @ts-nocheck
import { configureStore } from '@reduxjs/toolkit';
import schemaReducer, { 
  setSchema, 
  renameProperty, 
  updateProperty,
  addProperty
} from './schemaSlice';

describe('schemaSlice', () => {
  let store: ReturnType<typeof configureStore>;
  
  beforeEach(() => {
    store = configureStore({
      reducer: {
        schema: schemaReducer
      }
    });
  });

  describe('renameProperty', () => {
    test('should rename a top-level property', () => {
      // Initialize schema
      store.dispatch(setSchema({
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number' }
        },
        required: ['name']
      }));
      
      // Rename property
      store.dispatch(renameProperty({
        path: 'properties.name',
        newName: 'fullName'
      }));
      
      console.log('Schema after rename:', JSON.stringify(store.getState().schema.currentSchema, null, 2));
      
      // Check result
      const state = store.getState().schema;
      const schema = state.currentSchema as any;
      
      expect(schema.properties.fullName).toBeDefined();
      expect(schema.properties.name).toBeUndefined();
      expect(schema.required).toContain('fullName');
      expect(schema.required).not.toContain('name');
    });

    test('should rename a nested property', () => {
      // Initialize schema with nested properties
      store.dispatch(setSchema({
        type: 'object',
        properties: {
          person: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              age: { type: 'number' }
            },
            required: ['name']
          }
        }
      }));
      
      // Rename nested property
      store.dispatch(renameProperty({
        path: 'properties.person.properties.name',
        newName: 'fullName'
      }));
      
      console.log('Schema after nested rename:', JSON.stringify(store.getState().schema.currentSchema, null, 2));
      
      // Check result
      const state = store.getState().schema;
      const schema = state.currentSchema as any;
      
      expect(schema.properties.person.properties.fullName).toBeDefined();
      expect(schema.properties.person.properties.name).toBeUndefined();
      expect(schema.properties.person.required).toContain('fullName');
      expect(schema.properties.person.required).not.toContain('name');
    });

    test('should handle deeply nested properties', () => {
      // Initialize schema with deeply nested properties
      store.dispatch(setSchema({
        type: 'object',
        properties: {
          company: {
            type: 'object',
            properties: {
              department: {
                type: 'object',
                properties: {
                  employees: {
                    type: 'object',
                    properties: {
                      manager: { type: 'string' }
                    },
                    required: ['manager']
                  }
                }
              }
            }
          }
        }
      }));
      
      // Rename deeply nested property
      store.dispatch(renameProperty({
        path: 'properties.company.properties.department.properties.employees.properties.manager',
        newName: 'teamLead'
      }));
      
      // Check result
      const state = store.getState().schema;
      const schema = state.currentSchema as any;
      
      const employees = schema.properties.company.properties.department.properties.employees;
      expect(employees.properties.teamLead).toBeDefined();
      expect(employees.properties.manager).toBeUndefined();
      expect(employees.required).toContain('teamLead');
      expect(employees.required).not.toContain('manager');
    });

    test('should handle non-existent paths gracefully', () => {
      // Initialize schema
      store.dispatch(setSchema({
        type: 'object',
        properties: {
          name: { type: 'string' }
        }
      }));
      
      // Try to rename a non-existent property
      store.dispatch(renameProperty({
        path: 'properties.nonExistent',
        newName: 'newName'
      }));
      
      // Check that the schema is unchanged
      const state = store.getState().schema;
      const schema = state.currentSchema as any;
      
      expect(schema.properties.name).toBeDefined();
      expect(schema.properties.newName).toBeUndefined();
      expect(schema.properties.nonExistent).toBeUndefined();
    });
  });
});
