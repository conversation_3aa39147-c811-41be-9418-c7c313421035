import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './index';

// Helper function to get property by path (supporting nested paths like 'properties.name')
const getPropertyByPath = (obj: any, path: string) => {
  if (!path) return obj;
  
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (current[part] === undefined) {
      return undefined;
    }
    current = current[part];
  }
  
  return current;
};

// Set property by path (immutable deep update)
const setPropertyByPathImmutable = (obj: any, path: string, value: any): any => {
  const parts = path.split('.');
  
  const updateRecursively = (currentObj: any, index: number): any => {
    const part = parts[index];

    // Clone current level
    // Handle potential null/undefined and arrays explicitly
    let newLevel: any;
    if (currentObj === null || typeof currentObj !== 'object') {
      // If current level is not an object/array, create an object
      newLevel = {}; 
    } else if (Array.isArray(currentObj)) {
      newLevel = [...currentObj];
    } else {
      newLevel = { ...currentObj };
    }

    if (index === parts.length - 1) {
      // Last part of the path, set the value
      if (value === undefined) {
        delete newLevel[part]; // Handle deletion if value is undefined
      } else {
        newLevel[part] = value;
      }
    } else {
      // Recurse to the next level
      const nextObj = currentObj?.[part];
      newLevel[part] = updateRecursively(nextObj, index + 1);
      // Clean up empty objects created during recursion if the final value was undefined
      if (newLevel[part] === undefined || (typeof newLevel[part] === 'object' && Object.keys(newLevel[part]).length === 0)) {
         // Optional: delete empty objects if value was undefined, depends on desired behavior
         // delete newLevel[part]; 
      }
    }

    return newLevel;
  };

  // Handle edge case: setting value at the root
  if (!path) {
     if (value === undefined) return {}; // Or handle deletion as appropriate
     // If obj is not an object, or value is not an object, simple return value?
     if (typeof obj !== 'object' || obj === null || typeof value !== 'object' || value === null) {
       return value;
     }
     return { ...obj, ...value };
  }

  return updateRecursively(obj, 0);
};

// Helper to get the parent path and property name from a path
const getParentPathAndName = (path: string): { parentPath: string | null, propertyName: string } => {
  const pathParts = path.split('.');
  const propertyName = pathParts[pathParts.length - 1];
  
  // Remove the property name from path
  const parentPathParts = [...pathParts];
  parentPathParts.pop();
  
  // Adjust for 'properties' segment
  let parentPath: string | null = null;
  if (parentPathParts.length > 0) {
     if (parentPathParts[parentPathParts.length - 1] === 'properties') {
       parentPathParts.pop(); // Remove 'properties'
     }
     parentPath = parentPathParts.join('.');
  } else {
     // If only one part (e.g., 'properties.name'), parent is root
     parentPath = null; 
  }

  return { parentPath, propertyName };
};

// Update required array immutably
const updateRequiredArrayImmutable = (parentObj: any, propName: string, required: boolean): string[] | undefined => {
  const currentRequired = parentObj?.required || [];
  let newRequired: string[];

  if (required && !currentRequired.includes(propName)) {
    newRequired = [...currentRequired, propName];
  } else if (!required && currentRequired.includes(propName)) {
    newRequired = currentRequired.filter((name: string) => name !== propName);
  } else {
    return parentObj?.required; // No change needed
  }
  
  // Return undefined if empty, otherwise the new array
  return newRequired.length > 0 ? newRequired : undefined; 
};

interface SchemaState {
  currentSchema: any;
  selectedProperty: string | null;
  notification: { type: 'success' | 'error'; message: string } | null;
  modifiedSinceLastSave: boolean;
}

const initialState: SchemaState = {
  currentSchema: {},
  selectedProperty: null,
  notification: null,
  modifiedSinceLastSave: false,
};

export const schemaSlice = createSlice({
  name: 'schema',
  initialState,
  reducers: {
    setSchema: (state, action: PayloadAction<any>) => {
      state.currentSchema = action.payload;
      state.modifiedSinceLastSave = false;
    },
    setSelectedProperty: (state, action: PayloadAction<string | null>) => {
      state.selectedProperty = action.payload;
    },
    setNotification: (state, action: PayloadAction<{ type: 'success' | 'error'; message: string } | null>) => {
      state.notification = action.payload;
    },
    markAsSaved: (state) => {
      state.modifiedSinceLastSave = false;
    },
    updateProperty: (state, action: PayloadAction<{
      path: string;
      updates: { updates: any; required: boolean };
    }>) => {
      try {
        const { path, updates } = action.payload;
        const { updates: propertyUpdates, required } = updates;
        
        // Handle special cases first (array items, allOf)
        
        // Handle array items path specifically
        if (path.endsWith('.items')) {
          const arrayPath = path.substring(0, path.length - 6); // Remove .items
          const arrayProp = getPropertyByPath(state.currentSchema, arrayPath);
          
          if (!arrayProp) return;
          
          // Update array items
          const updatedItems = { ...arrayProp.items, ...propertyUpdates };
          
          // Set the updated array property back to the schema
          state.currentSchema = setPropertyByPathImmutable(
            state.currentSchema, 
            `${arrayPath}.items`, 
            updatedItems
          );
          
          state.modifiedSinceLastSave = true;
          return;
        }
        
        // Handle allOf structure
        if (path.startsWith('allOf.')) {
          const allOfPattern = /^allOf\.(\d+)\.(.+)$/;
          const matches = path.match(allOfPattern);
          
          if (matches) {
            const allOfIndex = parseInt(matches[1], 10);
            const remainingPath = matches[2];
            
            // Ensure allOf exists
            if (!state.currentSchema.allOf || !Array.isArray(state.currentSchema.allOf)) {
              state.currentSchema.allOf = [];
            }
            
            // Ensure the allOf item exists
            if (!state.currentSchema.allOf[allOfIndex]) {
              state.currentSchema.allOf[allOfIndex] = {};
            }
            
            // If the item has a $ref, we can't modify it directly
            if (state.currentSchema.allOf[allOfIndex].$ref) {
              state.notification = {
                type: 'error',
                message: 'Cannot modify a referenced schema directly'
              };
              return;
            }
            
            // Handle required property updates in allOf context
            if (remainingPath.includes('properties.')) {
              const propNameMatch = remainingPath.match(/properties\.([^.]+)$/);
              if (propNameMatch) {
                const propName = propNameMatch[1];
                
                // Initialize required array if needed
                if (!state.currentSchema.allOf[allOfIndex].required) {
                  state.currentSchema.allOf[allOfIndex].required = [];
                }
                
                // Update required array
                if (required && !state.currentSchema.allOf[allOfIndex].required.includes(propName)) {
                  state.currentSchema.allOf[allOfIndex].required.push(propName);
                } else if (!required && state.currentSchema.allOf[allOfIndex].required.includes(propName)) {
                  state.currentSchema.allOf[allOfIndex].required = 
                    state.currentSchema.allOf[allOfIndex].required.filter((name: string) => name !== propName);
                    
                  if (state.currentSchema.allOf[allOfIndex].required.length === 0) {
                    delete state.currentSchema.allOf[allOfIndex].required;
                  }
                }
              }
            }
            
            // Get current property
            const allOfItem = state.currentSchema.allOf[allOfIndex];
            const target = getPropertyByPath(allOfItem, remainingPath);
            
            if (target !== undefined) {
              // Update the property
              const updatedProperty = { ...target, ...propertyUpdates };
              
              // Set the updated property in the allOf item
              state.currentSchema.allOf[allOfIndex] = setPropertyByPathImmutable(
                allOfItem, 
                remainingPath, 
                updatedProperty
              );
            }
            
            state.modifiedSinceLastSave = true;
            return;
          }
        }
        
        // Handle regular property updates
        
        // Get current property
        const currentProperty = getPropertyByPath(state.currentSchema, path);
        if (currentProperty === undefined) return;
        
        // Update property immutably
        const updatedProperty = { ...currentProperty, ...propertyUpdates };
        
        // Set the updated property in the schema
        state.currentSchema = setPropertyByPathImmutable(
          state.currentSchema, 
          path, 
          updatedProperty
        );
        
        // Update required status
        if (path) {
          const { parentPath, propertyName } = getParentPathAndName(path);
          const parentObject = parentPath ? getPropertyByPath(state.currentSchema, parentPath) : state.currentSchema;
          
          if (parentObject) {
            const newRequiredArray = updateRequiredArrayImmutable(
              parentObject, 
              propertyName, 
              required
            );
            
            // Construct the path to the parent's 'required' property
            const requiredPath = parentPath ? `${parentPath}.required` : 'required';
            
            // Update the schema with the new required array (or remove it if undefined)
            state.currentSchema = setPropertyByPathImmutable(
              state.currentSchema, 
              requiredPath, 
              newRequiredArray
            );
          }
        }
        
        state.modifiedSinceLastSave = true;
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error updating property: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    },
    addProperty: (state, action: PayloadAction<{
      path: string;
      property: { name: string; property: any; required: boolean };
    }>) => {
      try {
        const { path, property } = action.payload;
        
        // Handle base schema management
        if (path === 'baseSchema') {
          // This would be handled by a separate action
          return;
        }
        
        if (path === 'allOf') {
          // Add new item to allOf array
          if (!state.currentSchema.allOf) {
            state.currentSchema.allOf = [];
          }
          
          // Create a new object for the allOf item with the property
          const newAllOfItem: { 
            properties: { [key: string]: any }; 
            required?: string[];
          } = {
            properties: {
              [property.name]: property.property
            }
          };
          
          // Add required property if needed
          if (property.required) {
            newAllOfItem.required = [property.name];
          }
          
          // Add to allOf array
          state.currentSchema.allOf.push(newAllOfItem);
          
          // Set selection to the new property
          state.selectedProperty = `allOf.${state.currentSchema.allOf.length - 1}.properties.${property.name}`;
        } else if (path.startsWith('allOf.')) {
          // Add property to an existing allOf item
          const allOfPattern = /^allOf\.(\d+)$/;
          const matches = path.match(allOfPattern);
          
          if (matches) {
            const allOfIndex = parseInt(matches[1], 10);
            
            // Ensure allOf exists and has the item
            if (!state.currentSchema.allOf) {
              state.currentSchema.allOf = [];
            }
            
            if (!state.currentSchema.allOf[allOfIndex]) {
              state.currentSchema.allOf[allOfIndex] = {};
            }
            
            // Add property to allOf item's properties
            if (!state.currentSchema.allOf[allOfIndex].properties) {
              state.currentSchema.allOf[allOfIndex].properties = {};
            }
            
            state.currentSchema.allOf[allOfIndex].properties[property.name] = property.property;
            
            // Update required array if needed
            if (property.required) {
              if (!state.currentSchema.allOf[allOfIndex].required) {
                state.currentSchema.allOf[allOfIndex].required = [];
              }
              state.currentSchema.allOf[allOfIndex].required.push(property.name);
            }
            
            // Set selection to the new property
            state.selectedProperty = `allOf.${allOfIndex}.properties.${property.name}`;
          }
        } else if (path === 'root') {
          // Add to root properties
          if (!state.currentSchema.properties) {
            state.currentSchema.properties = {};
          }
          
          state.currentSchema.properties[property.name] = property.property;
          
          // Update required array if needed
          if (property.required) {
            if (!state.currentSchema.required) {
              state.currentSchema.required = [];
            }
            state.currentSchema.required.push(property.name);
          }
          
          // Set selection to the new property
          state.selectedProperty = `properties.${property.name}`;
        } else {
          // Add to specified path
          const targetObject = getPropertyByPath(state.currentSchema, path);
          
          if (targetObject) {
            // If path includes "items", we're adding to an array's items object
            if (path.includes('.items')) {
              // Check if we're dealing with an array item that's a reference
              if (targetObject.$ref) {
                // Can't add properties to a reference
                state.notification = {
                  type: 'error',
                  message: `Can't add properties to a reference. You need to modify the target of the reference instead.`
                };
                return;
              }
              
              // Add to array items
              if (targetObject.type === 'object') {
                if (!targetObject.properties) {
                  targetObject.properties = {};
                }
                
                targetObject.properties[property.name] = property.property;
                
                // Update required array if needed
                if (property.required) {
                  if (!targetObject.required) {
                    targetObject.required = [];
                  }
                  targetObject.required.push(property.name);
                }
                
                // Set selection to the new property
                state.selectedProperty = `${path}.properties.${property.name}`;
              }
            } else {
              // Regular object property
              if (!targetObject.properties) {
                targetObject.properties = {};
              }
              
              targetObject.properties[property.name] = property.property;
              
              // Update required array if needed
              if (property.required) {
                if (!targetObject.required) {
                  targetObject.required = [];
                }
                targetObject.required.push(property.name);
              }
              
              // Set selection to the new property
              state.selectedProperty = `${path}.properties.${property.name}`;
            }
          }
        }
        
        state.modifiedSinceLastSave = true;
        state.notification = {
          type: 'success',
          message: `Property '${property.name}' added successfully`
        };
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error adding property: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    },
    deleteProperty: (state, action: PayloadAction<string>) => {
      try {
        const path = action.payload;
        
        // Extract property name and parent path
        const pathParts = path.split('.');
        const propertyName = pathParts[pathParts.length - 1];
        
        // Find parent path
        const parentPathParts = [...pathParts];
        parentPathParts.pop();
        const parentPath = parentPathParts.join('.');
        
        // First, clean up required array in parent if needed
        const parentObject = getPropertyByPath(state.currentSchema, parentPath);
        if (parentObject && parentObject.required) {
          parentObject.required = parentObject.required.filter((name: string) => name !== propertyName);
          if (parentObject.required.length === 0) {
            delete parentObject.required;
          }
        }
        
        // Then delete the property - use undefined to mark for deletion
        state.currentSchema = setPropertyByPathImmutable(state.currentSchema, path, undefined);
        
        // Reset selection if the deleted property was selected
        if (state.selectedProperty === path) {
          state.selectedProperty = null;
        }
        
        state.modifiedSinceLastSave = true;
        state.notification = {
          type: 'success',
          message: 'Property deleted successfully'
        };
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error deleting property: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    },
    addBaseSchema: (state, action: PayloadAction<string>) => {
      try {
        const baseSchemaRef = action.payload;
        
        if (!baseSchemaRef.trim()) {
          state.notification = {
            type: 'error',
            message: 'Please enter a valid schema reference'
          };
          return;
        }
        
        // Initialize allOf array if it doesn't exist
        if (!state.currentSchema.allOf) {
          state.currentSchema.allOf = [];
        }
        
        // Find existing base schema reference (assuming it's the first item with a $ref)
        const baseSchemaIndex = state.currentSchema.allOf.findIndex((item: any) => item.$ref);
        
        if (baseSchemaIndex >= 0) {
          // Replace existing base schema
          state.currentSchema.allOf[baseSchemaIndex] = { $ref: baseSchemaRef };
        } else {
          // Add new base schema reference as the first item
          state.currentSchema.allOf.unshift({ $ref: baseSchemaRef });
        }
        
        state.modifiedSinceLastSave = true;
        state.notification = {
          type: 'success',
          message: 'Base schema reference updated successfully'
        };
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error updating base schema: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    },
    clearNotification: (state) => {
      state.notification = null;
    },
    updatePropertyField: (state, action: PayloadAction<{
      path: string;
      field: string;
      value: any;
    }>) => {
      try {
        const { path, field, value } = action.payload;
        
        // Get the property by path
        const currentProperty = getPropertyByPath(state.currentSchema, path);
        if (currentProperty === undefined) {
          console.warn(`Property not found at path: ${path}`);
          return;
        }
        
        // Create a copy of the property to update immutably
        let updatedProperty = { ...currentProperty };
        
        // Handle specific field updates
        if (field === 'required') {
          // Update required status
          const { parentPath, propertyName } = getParentPathAndName(path);
          const parentObject = parentPath ? getPropertyByPath(state.currentSchema, parentPath) : state.currentSchema;
          
          if (parentObject) {
            const newRequiredArray = updateRequiredArrayImmutable(
              parentObject, 
              propertyName, 
              value // value is the boolean required state
            );
            
            // Construct the path to the parent's 'required' property
            const requiredPath = parentPath ? `${parentPath}.required` : 'required';
            
            // Update the schema with the new required array (or remove it if undefined)
            state.currentSchema = setPropertyByPathImmutable(
              state.currentSchema, 
              requiredPath, 
              newRequiredArray
            );
          }
        } else if (field === 'useConst') {
          // Toggle useConst
          if (value) {
            // If enabling const, add a default const value based on type if none exists
            if (updatedProperty.const === undefined) {
              switch (updatedProperty.type) {
                case 'string': updatedProperty.const = ''; break;
                case 'number':
                case 'integer': updatedProperty.const = 0; break;
                case 'boolean': updatedProperty.const = false; break;
                default: updatedProperty.const = null;
              }
            }
          } else {
            // If disabling const, remove the const property
            delete updatedProperty.const;
          }
          // Update the property in the schema
          state.currentSchema = setPropertyByPathImmutable(
            state.currentSchema, 
            path, 
            updatedProperty
          );
        } else if (field === 'propertyConst') {
           // Update the const value
           updatedProperty.const = value;
           // Update the property in the schema
           state.currentSchema = setPropertyByPathImmutable(
             state.currentSchema, 
             path, 
             updatedProperty
           );
        } else if (field === 'propertyEnum') {
           // Update the enum array
           updatedProperty.enum = value;
           // Update the property in the schema
           state.currentSchema = setPropertyByPathImmutable(
             state.currentSchema, 
             path, 
             updatedProperty
           );
        } else if (field === 'propertyRef') {
           // Update the $ref value
           updatedProperty.$ref = value;
           // Update the property in the schema
           state.currentSchema = setPropertyByPathImmutable(
             state.currentSchema, 
             path, 
             updatedProperty
           );
        }
        else {
          // For other fields, directly update the property
          updatedProperty[field] = value;
          
          // Update the property in the schema
          state.currentSchema = setPropertyByPathImmutable(
            state.currentSchema, 
            path, 
            updatedProperty
          );
        }
        
        state.modifiedSinceLastSave = true;
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error updating property field: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    },
    renameProperty: (state, action: PayloadAction<{
      path: string;
      newName: string;
    }>) => {
      try {
        const { path, newName } = action.payload;
        
        // Extract the correct property name and parent object path from the given path
        let parentPath: string = '';
        let oldName: string = '';
        
        // paths are in format like "properties.name" or "properties.person.properties.name"
        const pathParts = path.split('.');
        
        // Find the position of the last "properties" segment
        let lastPropertiesIndex = -1;
        for (let i = pathParts.length - 1; i >= 0; i--) {
          if (pathParts[i] === 'properties') {
            lastPropertiesIndex = i;
            break;
          }
        }
        
        if (lastPropertiesIndex >= 0 && lastPropertiesIndex < pathParts.length - 1) {
          // The property name is the segment after the last "properties"
          oldName = pathParts[lastPropertiesIndex + 1];
          
          // The parent path is everything up to and including the last "properties"
          parentPath = pathParts.slice(0, lastPropertiesIndex + 1).join('.');
          
          console.log(`Extracted parent path: ${parentPath}, old name: ${oldName}`);
        } else {
          console.warn(`Invalid path format: ${path}`);
          return;
        }
        
        // Get parent object using the extracted path
        const parentObject = getPropertyByPath(state.currentSchema, parentPath);

        if (!parentObject) {
          console.warn(`Parent object not found at path: ${parentPath}`);
          return;
        }
        
        // Make a copy of the old property
        const oldProperty = parentObject[oldName];
        if (!oldProperty) {
          console.warn(`Property "${oldName}" not found in parent object`);
          return;
        }

        // Using Immer's direct mutation approach:
        // 1. Create a copy of the old property
        // 2. Add it with the new name
        // 3. Delete the old property
        const newProperty = { ...oldProperty };
        parentObject[newName] = newProperty;
        delete parentObject[oldName];
        
        // Extract parent schema object (the object containing the "required" array)
        // This is typically one level up from the "properties" object
        let parentSchemaPath = '';
        if (lastPropertiesIndex > 0) {
          parentSchemaPath = pathParts.slice(0, lastPropertiesIndex).join('.');
        }
        
        const parentSchema = parentSchemaPath 
          ? getPropertyByPath(state.currentSchema, parentSchemaPath) 
          : state.currentSchema;
          
        // Update required array if needed
        if (parentSchema && parentSchema.required && parentSchema.required.includes(oldName)) {
          const requiredIndex = parentSchema.required.indexOf(oldName);
          if (requiredIndex !== -1) {
            parentSchema.required[requiredIndex] = newName;
          }
        }

        // Update selectedProperty path if it matches the renamed property
        if (state.selectedProperty === path) {
          const newPathParts = [...pathParts];
          newPathParts[lastPropertiesIndex + 1] = newName;
          const newPath = newPathParts.join('.');
          state.selectedProperty = newPath;
        }

        state.modifiedSinceLastSave = true;
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error renaming property: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    },
    
    // Add reorderProperty reducer to handle reordering properties in an object
    reorderProperty: (state, action: PayloadAction<{
      parentPath: string;
      propertyName: string;
      direction: 'up' | 'down';
    }>) => {
      try {
        const { parentPath, propertyName, direction } = action.payload;
        
        // Get the parent object that contains the property to be reordered
        const parentObject = parentPath === 'root' 
          ? state.currentSchema
          : getPropertyByPath(state.currentSchema, parentPath);
          
        if (!parentObject || !parentObject.properties) {
          state.notification = {
            type: 'error',
            message: 'Cannot reorder: parent object not found or has no properties'
          };
          return;
        }
        
        // Get ordered keys of properties
        const propertyKeys = Object.keys(parentObject.properties);
        
        // Find the index of the property to reorder
        const currentIndex = propertyKeys.indexOf(propertyName);
        if (currentIndex === -1) {
          state.notification = {
            type: 'error',
            message: `Property "${propertyName}" not found in object`
          };
          return;
        }
        
        // Calculate new index based on direction
        let newIndex: number;
        if (direction === 'up') {
          newIndex = Math.max(0, currentIndex - 1);
        } else { // down
          newIndex = Math.min(propertyKeys.length - 1, currentIndex + 1);
        }
        
        // If the property is already at the boundary, don't do anything
        if (newIndex === currentIndex) return;
        
        // Create a new ordered properties object
        const newProperties: Record<string, any> = {};
        
        // Reorder the properties by creating a new array with the swapped positions
        const reorderedKeys = [...propertyKeys];
        
        // If moving up, swap with the previous property
        if (direction === 'up' && currentIndex > 0) {
          // Swap current property with the one before it
          const temp = reorderedKeys[currentIndex - 1];
          reorderedKeys[currentIndex - 1] = reorderedKeys[currentIndex];
          reorderedKeys[currentIndex] = temp;
        }
        // If moving down, swap with the next property
        else if (direction === 'down' && currentIndex < propertyKeys.length - 1) {
          // Swap current property with the one after it
          const temp = reorderedKeys[currentIndex + 1];
          reorderedKeys[currentIndex + 1] = reorderedKeys[currentIndex];
          reorderedKeys[currentIndex] = temp;
        }
        
        // Create the new properties object with the reordered keys
        reorderedKeys.forEach(key => {
          newProperties[key] = parentObject.properties[key];
        });
        
        // Replace the properties object in the parent
        if (parentPath === 'root') {
          // Handle root properties
          state.currentSchema.properties = newProperties;
        } else {
          // Handle nested properties
          parentObject.properties = newProperties;
        }
        
        state.modifiedSinceLastSave = true;
      } catch (error) {
        state.notification = {
          type: 'error',
          message: `Error reordering property: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    }
  },
});

// Export actions
export const { 
  setSchema, 
  setSelectedProperty, 
  setNotification,
  markAsSaved,
  updateProperty, 
  addProperty, 
  deleteProperty, 
  addBaseSchema,
  clearNotification,
  updatePropertyField,
  renameProperty,
  reorderProperty
} = schemaSlice.actions;

// Export selectors
export const selectSchema = (state: RootState) => state.schema.currentSchema;
export const selectSelectedProperty = (state: RootState) => state.schema.selectedProperty;
export const selectNotification = (state: RootState) => state.schema.notification;
export const selectIsModified = (state: RootState) => state.schema.modifiedSinceLastSave;

// Export reducer
export default schemaSlice.reducer;
