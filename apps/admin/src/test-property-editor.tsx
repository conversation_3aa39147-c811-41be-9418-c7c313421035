import React, { useState } from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { SchemaVisualizer } from './components/SchemaVisualizer';
import { MantineProvider } from '@mantine/core';
import '@mantine/core/styles.css';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import schemaReducer from './store/schemaSlice';

// Create a mock API with the minimum required endpoints
const mockApi = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['Schemas'],
  endpoints: (builder) => ({
    getSchemas: builder.query({
      query: () => '/schemas',
      // Return mock data
      transformResponse: () => [
        { id: '1', name: 'Test Schema', version: '1.0' },
        { id: '2', name: 'Another Schema', version: '1.0' }
      ]
    }),
  }),
});

// Configure the store with our mock API and schema reducer
const store = configureStore({
  reducer: {
    [mockApi.reducerPath]: mockApi.reducer,
    schema: schemaReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(mockApi.middleware),
});

// Set up listeners
setupListeners(store.dispatch);

// Sample schema for testing
const initialSchema = {
  title: "Test Schema",
  type: "object",
  properties: {
    name: {
      type: "string",
      description: "The name of the person"
    },
    age: {
      type: "integer",
      description: "The age of the person"
    },
    email: {
      type: "string",
      format: "email",
      description: "The email address"
    },
    address: {
      type: "object",
      description: "The person's address",
      properties: {
        street: {
          type: "string",
          description: "Street address"
        },
        city: {
          type: "string",
          description: "City"
        },
        zipCode: {
          type: "string",
          description: "ZIP code"
        }
      },
      required: ["street"] // Initially, only street is required in the address object
    }
  },
  required: ["name"] // Initially, only name is required
};

// Helper function to log schema changes
const logSchemaChanges = (updatedSchema: any) => {
  console.log('Schema updated:');
  console.log('Required properties:', updatedSchema.required || []);
  
  // Check which properties have the "Required" badge in the tree
  const requiredBadges = document.querySelectorAll('.mantine-Badge-root');
  const requiredProperties: string[] = [];
  
  requiredBadges.forEach(badge => {
    if (badge.textContent === 'REQUIRED') {
      // Find the property name by looking at the parent element
      const propertyCard = badge.closest('.mantine-Card-root');
      if (propertyCard) {
        const propertyNameElement = propertyCard.querySelector('.mantine-Text-root[fw="500"]');
        if (propertyNameElement) {
          requiredProperties.push(propertyNameElement.textContent || '');
        }
      }
    }
  });
  
  console.log('Properties with "Required" badge:', requiredProperties);
  
  return updatedSchema;
};

function TestApp() {
  const [schema, setSchema] = useState(initialSchema);

  return (
    <div style={{ padding: '20px' }}>
      <h1>Property Editor Test</h1>
      <p>This page tests the "Required" checkbox functionality in the PropertyEditor component.</p>
      <p>Instructions:</p>
      <ol>
        <li>Click on a property in the tree view (e.g., "name", "age", or "email")</li>
        <li>Toggle the "Required" checkbox in the property editor</li>
        <li>Verify that the "Required" badge appears/disappears in the tree view</li>
        <li>Verify that toggling the checkbox multiple times works correctly</li>
      </ol>
      
      <div style={{ marginTop: '20px', border: '1px solid #ccc', padding: '20px' }}>
        <SchemaVisualizer 
          initialSchema={schema} 
          onChange={(updatedSchema) => {
            setSchema(logSchemaChanges(updatedSchema));
          }} 
        />
      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Provider store={store}>
      <MantineProvider>
        <TestApp />
      </MantineProvider>
    </Provider>
  </React.StrictMode>,
);
