import { describe, it, expect } from 'vitest';
import reducer, {
  setSchema,
  updateProperty,
  updatePropertyField,
  renameProperty,
} from '../store/schemaSlice';

describe('Schema Slice', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, { type: 'UNKNOWN_ACTION' as any })).toEqual({
      currentSchema: {},
      selectedProperty: null,
      notification: null,
      modifiedSinceLastSave: false,
    });
  });

  it('should handle setting a new schema', () => {
    const schema = {
      type: 'object',
      properties: { name: { type: 'string' } },
    };
    const state = reducer(undefined, setSchema(schema));
    expect(state.currentSchema).toEqual(schema);
  });

  it('should handle updating property type', () => {
    const startState = {
      currentSchema: {
        type: 'object',
        properties: { name: { type: 'string' } },
      },
      selectedProperty: null,
      notification: null,
      modifiedSinceLastSave: false,
    };
    const path = 'properties.name';
    const updates = { updates: { type: 'number' }, required: false };
    const state = reducer(startState, updateProperty({ path, updates }));
    expect(state.currentSchema.properties.name.type).toBe('number');
  });

  it('should handle updating required status', () => {
    const startState = {
      currentSchema: {
        type: 'object',
        properties: { name: { type: 'string' } },
      },
      selectedProperty: null,
      notification: null,
      modifiedSinceLastSave: false,
    };
    const path = 'properties.name';
    const field = 'required';
    const value = true;
    const state = reducer(startState, updatePropertyField({ path, field, value }));
    expect(state.currentSchema.required).toEqual(['name']);
  });

  it('should handle updating description field', () => {
    const startState = {
      currentSchema: {
        type: 'object',
        properties: { name: { type: 'string' } },
      },
      selectedProperty: null,
      notification: null,
      modifiedSinceLastSave: false,
    };
    const path = 'properties.name';
    const field = 'description';
    const value = 'User full name';
    const state = reducer(startState, updatePropertyField({ path, field, value }));
    expect(state.currentSchema.properties.name.description).toBe('User full name');
  });

  it('should handle renaming a property', () => {
    // Create a fresh test state to avoid any mutation issues
    const testSchema = {
      type: 'object',
      properties: { 
        oldName: { type: 'string', description: 'Test' } 
      },
      required: ['oldName']
    };
    
    const testState = {
      currentSchema: JSON.parse(JSON.stringify(testSchema)), // Create a deep copy
      selectedProperty: 'properties.oldName',
      notification: null,
      modifiedSinceLastSave: false,
    };
    
    // Apply the rename action
    const action = renameProperty({ 
      path: 'properties.oldName', 
      newName: 'newName' 
    });
    
    // Get the resulting state
    const resultState = reducer(testState, action);
    
    // Log both the original and result schemas for debugging
    console.log('Original schema:', JSON.stringify(testState.currentSchema, null, 2));
    console.log('Result schema:', JSON.stringify(resultState.currentSchema, null, 2));
    console.log('Result properties:', Object.keys(resultState.currentSchema.properties));
    
    // Only check for the existence of the new property
    expect(resultState.currentSchema.properties.newName).toBeDefined();
    expect(resultState.currentSchema.properties.newName.type).toBe('string');
    expect(resultState.currentSchema.properties.newName.description).toBe('Test');
    
    // Verify required array was updated
    expect(resultState.currentSchema.required).toContain('newName');
    expect(resultState.currentSchema.required).not.toContain('oldName');
    
    // Verify selectedProperty was updated
    expect(resultState.selectedProperty).toBe('properties.newName');
    
    // Check directly if oldName exists in the properties object
    const hasOldProperty = 'oldName' in resultState.currentSchema.properties;
    expect(hasOldProperty).toBe(false);
  });

  it('should handle reordering properties', () => {
    // Create a test schema with three properties in specific order
    const testSchema = {
      type: 'object',
      properties: { 
        first: { type: 'string' },
        second: { type: 'number' },
        third: { type: 'boolean' }
      }
    };
    
    const testState = {
      currentSchema: JSON.parse(JSON.stringify(testSchema)), // Create a deep copy
      selectedProperty: null,
      notification: null,
      modifiedSinceLastSave: false,
    };
    
    // 1. Move 'second' property up (should swap with 'first')
    let action = {
      type: 'schema/reorderProperty',
      payload: {
        parentPath: 'root',
        propertyName: 'second',
        direction: 'up'
      }
    };
    
    // Apply the reorder action
    let resultState = reducer(testState, action);
    
    // Check that the order of properties has changed
    const propertiesAfterFirstMove = Object.keys(resultState.currentSchema.properties);
    expect(propertiesAfterFirstMove).toEqual(['second', 'first', 'third']);
    
    // 2. Now move 'first' property down (should swap with 'third')
    action = {
      type: 'schema/reorderProperty',
      payload: {
        parentPath: 'root',
        propertyName: 'first',
        direction: 'down'
      }
    };
    
    // Apply the second reorder action
    resultState = reducer(resultState, action);
    
    // Check that the order of properties has changed again
    const propertiesAfterSecondMove = Object.keys(resultState.currentSchema.properties);
    expect(propertiesAfterSecondMove).toEqual(['second', 'third', 'first']);
    
    // Verify that state.modifiedSinceLastSave was set to true
    expect(resultState.modifiedSinceLastSave).toBe(true);
  });
});
