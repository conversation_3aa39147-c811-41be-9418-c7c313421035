/**
 * Type definitions for UI Schema to customize the display of JSON schemas
 */

/**
 * Options for UI schema widgets
 */
export interface UISchemaOptions {
  /**
   * Resource type for resourceDisplay widget
   */
  resourceType?: string;
  
  /**
   * Field to use for resource lookup
   */
  lookupBy?: string;
  
  /**
   * Fields to display from the resource
   */
  displayFields?: string[];
  
  /**
   * Columns to display in table widget
   */
  columns?: string[];
  
  /**
   * Any additional options
   */
  [key: string]: any;
}

/**
 * Properties for a specific field in the UI schema
 */
export interface UISchemaWidgetProps {
  /**
   * Widget type to use for rendering
   */
  "ui:widget"?: "hidden" | "resourceDisplay" | "table" | string;
  
  /**
   * Options for the widget
   */
  "ui:options"?: UISchemaOptions;
  
  /**
   * Order of child properties
   */
  "ui:order"?: string[];
  
  /**
   * Whether to show the label
   */
  "ui:label"?: boolean;
  
  /**
   * Whether the field is read-only
   */
  "ui:readonly"?: boolean;
  
  /**
   * Tooltip text
   */
  "ui:tooltip"?: string;
  
  /**
   * Description text
   */
  "ui:description"?: string;
  
  /**
   * Conditional display based on other field values
   */
  "ui:if"?: {
    [key: string]: any;
  };
  
  /**
   * Any additional properties
   */
  [key: string]: any;
}

/**
 * UI Schema for customizing the display of JSON schemas
 */
export interface UISchema {
  /**
   * Order of properties at the root level
   */
  "ui:order"?: string[];
  
  /**
   * Properties for specific fields or additional properties
   */
  [key: string]: UISchemaWidgetProps | any;
}
