/**
 * Utilities for fetching and working with resources
 */

/**
 * Fetch a resource by type and ID
 * @param resourceType The type of resource to fetch
 * @param id The ID of the resource
 * @returns The resource data or null if not found
 */
export async function fetchResource(resourceType: string, id: string): Promise<any> {
  try {
    const apiUrl = import.meta.env.API_BASE_URL + '/api/admin';
    const response = await fetch(`${apiUrl}/resources/${resourceType}/id/${id}`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch resource: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching ${resourceType} resource with ID ${id}:`, error);
    return null;
  }
}

/**
 * Extract specific fields from a resource
 * @param resource The resource object
 * @param fields Array of field names to extract
 * @returns Object containing only the specified fields
 */
export function extractResourceFields(resource: any, fields?: string[]): any {
  if (!resource) return null;
  if (!fields || fields.length === 0) return resource;
  
  const result: any = {};
  
  for (const field of fields) {
    if (resource[field] !== undefined) {
      result[field] = resource[field];
    }
  }
  
  return result;
}

/**
 * Get a nested property from an object using a path string
 * @param obj The object to get the property from
 * @param path The path to the property (e.g., "user.address.city")
 * @returns The property value or undefined if not found
 */
export function getNestedProperty(obj: any, path: string): any {
  if (!obj || !path) return undefined;
  
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (current === null || current === undefined) return undefined;
    current = current[part];
  }
  
  return current;
}

/**
 * Format a resource as a string for display
 * @param resource The resource object
 * @returns A string representation of the resource
 */
export function formatResourceForDisplay(resource: any): string {
  if (!resource) return 'Resource not found';
  
  // If the resource has a name or title, use that
  if (resource.name) return resource.name;
  if (resource.title) return resource.title;
  
  // If the resource has an ID, use that
  if (resource.id) return `${resource.id}`;
  
  // Otherwise, return a generic string
  return 'Resource';
}
