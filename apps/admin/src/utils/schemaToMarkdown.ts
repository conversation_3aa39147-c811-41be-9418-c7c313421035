/**
 * Utility functions to convert a JSON schema to markdown format and generate sample data
 */
import { UISchema } from '../types/uiSchema';
import { 
  applyUIOrder, 
  shouldDisplayProperty, 
  getWidgetType, 
  getWidgetOptions 
} from './uiSchemaUtils';
import { 
  fetchResource, 
  extractResourceFields, 
  formatResourceForDisplay 
} from './resourceUtils';

/**
 * Format a property name for display in markdown
 * - Converts camelCase to space-separated words
 * - Capitalizes the first letter of each word
 * 
 * @param propertyName The property name to format
 * @returns The formatted property name
 */
function formatPropertyName(propertyName: string): string {
  // Insert a space before each capital letter and convert to lowercase
  const spaceSeparated = propertyName
    .replace(/([A-Z])/g, ' $1')
    .trim();
  
  // Capitalize the first letter of each word
  return spaceSeparated
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Convert a JSON schema to markdown format
 * @param schema The JSON schema object to convert
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @param uiSchema Optional UI schema for customizing the display
 * @returns A markdown string representation of the schema
 */
export async function schemaToMarkdown(
  schema: any, 
  fetchExternalSchema?: (schemaId: string) => Promise<any>,
  uiSchema?: UISchema
): Promise<string> {
  if (!schema) return '';
  
  let markdown = '';
  
  // Add title and description if available
  if (schema.title) {
    markdown += `# ${schema.title}\n\n`;
  } else {
    markdown += `# Sample Data\n\n`;
  }
  
  if (schema.description) {
    markdown += `${schema.description}\n\n`;
  }
  
  try {
    // Generate sample data based on the schema
    const sampleData = await generateSampleData(schema, undefined, fetchExternalSchema);
    
    // Generate markdown representation of the sample data
    markdown += await dataToMarkdown(sampleData, schema.title || 'Data', uiSchema);
    
    // Add JSON representation at the bottom for reference
    markdown += `## JSON Representation\n\n`;
    markdown += `\`\`\`json\n${JSON.stringify(sampleData, null, 2)}\n\`\`\`\n\n`;
  } catch (error) {
    markdown += `Error generating sample data: ${error}\n\n`;
  }
  
  return markdown;
}

/**
 * Generate sample data based on a JSON schema
 * @param schema The JSON schema to generate sample data for
 * @param rootSchema The root schema for resolving references (defaults to schema if not provided)
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @returns Sample data that conforms to the schema
 */
async function generateSampleData(
  schema: any, 
  rootSchema?: any,
  fetchExternalSchema?: (schemaId: string) => Promise<any>
): Promise<any> {
  if (!schema) return null;
  
  // Use the provided rootSchema or default to the schema itself
  const root = rootSchema || schema;
  
  // Handle $ref
  if (schema.$ref) {
    // Try to resolve the reference internally first
    let resolvedSchema = resolveReference(schema.$ref, root);
    
    // If not found internally and it looks like an external reference, try to fetch it
    if (!resolvedSchema && fetchExternalSchema && isExternalReference(schema.$ref)) {
      const schemaId = extractSchemaId(schema.$ref);
      if (schemaId) {
        const externalSchema = await fetchExternalSchema(schemaId);
        if (externalSchema) {
          // If the reference has a path within the external schema, resolve that too
          const pathWithinSchema = extractPathWithinSchema(schema.$ref);
          if (pathWithinSchema) {
            resolvedSchema = resolveReference(pathWithinSchema, externalSchema);
          } else {
            resolvedSchema = externalSchema;
          }
        }
      }
    }
    
    if (resolvedSchema) {
      return generateSampleData(resolvedSchema, root, fetchExternalSchema);
    } else {
      // If reference can't be resolved, return a placeholder
      return `[Reference to ${schema.$ref}]`;
    }
  }
  
  // Handle different schema types
  switch (schema.type) {
    case 'object':
      return generateObjectData(schema, root, fetchExternalSchema);
    case 'array':
      return generateArrayData(schema, root, fetchExternalSchema);
    case 'string':
      return generateStringData(schema);
    case 'number':
    case 'integer':
      return generateNumberData(schema);
    case 'boolean':
      return generateBooleanData(schema);
    case 'null':
      return null;
    default:
      // If type is not specified, assume it's an object
      if (schema.properties) {
        return generateObjectData(schema, root, fetchExternalSchema);
      }
      // If it has enum values, return the first one
      if (schema.enum && schema.enum.length > 0) {
        return schema.enum[0];
      }
      // Default fallback
      return {};
  }
}

/**
 * Check if a reference is to an external schema
 * @param ref The reference string
 * @returns True if the reference is to an external schema
 */
function isExternalReference(ref: string): boolean {
  // External references might be in formats like:
  // - "schemaId:path/within/schema"
  // - "schemaId"
  return !ref.startsWith('#/');
}

/**
 * Extract the schema ID from an external reference
 * @param ref The reference string
 * @returns The schema ID or null if not found
 */
function extractSchemaId(ref: string): string | null {
  // If the reference contains a colon, the part before the colon is the schema ID
  const colonIndex = ref.indexOf(':');
  if (colonIndex !== -1) {
    return ref.substring(0, colonIndex);
  }
  
  // If there's no colon, the entire reference might be the schema ID
  return ref;
}

/**
 * Extract the path within a schema from an external reference
 * @param ref The reference string
 * @returns The path within the schema or null if not found
 */
function extractPathWithinSchema(ref: string): string | null {
  const colonIndex = ref.indexOf(':');
  if (colonIndex !== -1 && colonIndex < ref.length - 1) {
    const path = ref.substring(colonIndex + 1);
    // Convert to JSON Pointer format if it's not already
    return path.startsWith('#/') ? path : `#/${path}`;
  }
  return null;
}

/**
 * Resolve a JSON Schema reference
 * @param ref The reference string (e.g., "#/definitions/Person")
 * @param rootSchema The root schema containing the definitions
 * @returns The resolved schema or null if not found
 */
function resolveReference(ref: string, rootSchema: any): any {
  // Only handle local references for now (starting with #/)
  if (!ref.startsWith('#/')) {
    return null;
  }
  
  // Remove the #/ prefix and split the path
  const path = ref.substring(2).split('/');
  
  // Navigate through the schema to find the referenced definition
  let current = rootSchema;
  for (const segment of path) {
    // Handle JSON pointer escaping
    const key = segment.replace(/~1/g, '/').replace(/~0/g, '~');
    
    if (current[key] === undefined) {
      // Check alternative locations (for backward compatibility)
      if (key === 'definitions' && current['$defs']) {
        current = current['$defs'];
      } else if (key === '$defs' && current['definitions']) {
        current = current['definitions'];
      } else {
        return null; // Reference not found
      }
    } else {
      current = current[key];
    }
  }
  
  return current;
}

/**
 * Generate sample data for an object schema
 * @param schema The object schema
 * @param rootSchema The root schema for resolving references
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @returns Sample object data
 */
async function generateObjectData(
  schema: any, 
  rootSchema: any,
  fetchExternalSchema?: (schemaId: string) => Promise<any>
): Promise<any> {
  const result: any = {};
  
  if (!schema.properties) return result;
  
  // Process each property
  for (const [propName, propSchema] of Object.entries(schema.properties)) {
    result[propName] = await generateSampleData(propSchema, rootSchema, fetchExternalSchema);
  }
  
  return result;
}

/**
 * Generate sample data for an array schema
 * @param schema The array schema
 * @param rootSchema The root schema for resolving references
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @returns Sample array data
 */
async function generateArrayData(
  schema: any, 
  rootSchema: any,
  fetchExternalSchema?: (schemaId: string) => Promise<any>
): Promise<any[]> {
  if (!schema.items) return [];
  
  // Generate 1-3 items for the array
  const count = Math.min(schema.minItems || 1, 3);
  const result = [];
  
  for (let i = 0; i < count; i++) {
    result.push(await generateSampleData(schema.items, rootSchema, fetchExternalSchema));
  }
  
  return result;
}

/**
 * Generate sample data for a string schema
 * @param schema The string schema
 * @returns Sample string data
 */
function generateStringData(schema: any): string {
  // Handle specific formats
  if (schema.format) {
    switch (schema.format) {
      case 'date':
        return '2025-04-18';
      case 'date-time':
        return '2025-04-18T13:45:30Z';
      case 'email':
        return '<EMAIL>';
      case 'uri':
      case 'url':
        return 'https://example.com';
      case 'uuid':
        return '123e4567-e89b-12d3-a456-************';
      case 'hostname':
        return 'example.com';
      case 'ipv4':
        return '***********';
      case 'ipv6':
        return '2001:0db8:85a3:0000:0000:8a2e:0370:7334';
      default:
        // Fall through to default handling
    }
  }
  
  // Use example if provided
  if (schema.example) return String(schema.example);
  
  // Use default if provided
  if (schema.default !== undefined) return String(schema.default);
  
  // Use enum value if available
  if (schema.enum && schema.enum.length > 0) return schema.enum[0];
  
  // Use title or description if available
  if (schema.title) return `Sample ${schema.title}`;
  if (schema.description) return `Sample ${schema.description}`;
  
  // Default value
  return 'Sample string value';
}

/**
 * Generate sample data for a number schema
 * @param schema The number schema
 * @returns Sample number data
 */
function generateNumberData(schema: any): number {
  // Use example if provided
  if (schema.example !== undefined) return Number(schema.example);
  
  // Use default if provided
  if (schema.default !== undefined) return Number(schema.default);
  
  // Use enum value if available
  if (schema.enum && schema.enum.length > 0) return schema.enum[0];
  
  // Generate a value within the specified range
  let min = schema.minimum !== undefined ? schema.minimum : 0;
  let max = schema.maximum !== undefined ? schema.maximum : 100;
  
  // Adjust for exclusive bounds
  if (schema.exclusiveMinimum && min === schema.exclusiveMinimum) min += 1;
  if (schema.exclusiveMaximum && max === schema.exclusiveMaximum) max -= 1;
  
  // Ensure min <= max
  if (min > max) [min, max] = [max, min];
  
  // Generate a random number within the range
  const value = min + Math.random() * (max - min);
  
  // Return an integer if the schema type is integer
  return schema.type === 'integer' ? Math.floor(value) : Number(value.toFixed(2));
}

/**
 * Generate sample data for a boolean schema
 * @param schema The boolean schema
 * @returns Sample boolean data
 */
function generateBooleanData(schema: any): boolean {
  // Use example if provided
  if (schema.example !== undefined) return Boolean(schema.example);
  
  // Use default if provided
  if (schema.default !== undefined) return Boolean(schema.default);
  
  // Default to true
  return true;
}

/**
 * Convert data to markdown representation
 * @param data The data to convert to markdown
 * @param title Optional title for the data
 * @param uiSchema Optional UI schema for customizing the display
 * @returns Markdown representation of the data
 */
async function dataToMarkdown(
  data: any, 
  title: string = 'Data', 
  uiSchema?: UISchema
): Promise<string> {
  if (data === null || data === undefined) {
    return 'null\n\n';
  }
  
  let markdown = '';
  
  if (Array.isArray(data)) {
    // Handle array data
    for (let i = 0; i < data.length; i++) {
      markdown += `### Item ${i + 1}\n\n`;
      markdown += await dataToMarkdown(data[i], '');
    }
    return markdown;
  } 
  
  if (typeof data === 'object') {
    // Handle object data
    let properties = Object.keys(data);
    
    // Apply UI schema ordering if available
    if (uiSchema && uiSchema['ui:order']) {
      properties = applyUIOrder(properties, uiSchema['ui:order']);
    }
    
    // Process each property
    for (const key of properties) {
      const value = data[key];
      
      // Skip hidden properties
      if (uiSchema && getWidgetType(key, uiSchema) === 'hidden') {
        continue;
      }
      
      // Skip properties that don't meet conditional display criteria
      if (uiSchema && !shouldDisplayProperty(key, uiSchema, data)) {
        continue;
      }
      
      // Get widget type and options
      const widgetType = uiSchema ? getWidgetType(key, uiSchema) : undefined;
      const widgetOptions = uiSchema ? getWidgetOptions(key, uiSchema) : undefined;
      
      // Handle different widget types
      if (widgetType === 'resourceDisplay' && widgetOptions) {
        markdown += await renderResourceDisplay(key, value, widgetOptions);
      } else if (widgetType === 'table' && Array.isArray(value) && widgetOptions) {
        markdown += renderTable(key, value, widgetOptions);
      } else if (Array.isArray(value) && (!widgetType || (widgetType !== 'resourceDisplay' && widgetType !== 'table'))) {
        // Default array handling if no special widget
        markdown += `## ${formatPropertyName(key)}\n\n`;
        
        if (value.length === 0) {
          markdown += 'Empty array\n\n';
        } else if (typeof value[0] === 'object' && value[0] !== null) {
          // Array of objects - create a table
          const keys = Object.keys(value[0]);
          
          // Table header
          markdown += '| ' + keys.join(' | ') + ' |\n';
          markdown += '| ' + keys.map(() => '---').join(' | ') + ' |\n';
          
          // Table rows
          value.forEach((item: any) => {
            markdown += '| ' + keys.map(k => {
              const cellValue = item[k];
              if (cellValue === null || cellValue === undefined) return '';
              if (typeof cellValue === 'object') return 'Object';
              return String(cellValue).replace(/\n/g, ' ');
            }).join(' | ') + ' |\n';
          });
          
          markdown += '\n';
        } else {
          // Simple array - create a list
          value.forEach((item: any) => {
            if (typeof item === 'object' && item !== null) {
              markdown += '- ' + JSON.stringify(item) + '\n';
            } else {
              markdown += '- ' + String(item) + '\n';
            }
          });
          markdown += '\n';
        }
      } else if (typeof value === 'object' && value !== null) {
        // Nested object
        markdown += `## ${formatPropertyName(key)}\n\n`;
        markdown += await dataToMarkdown(value, '', uiSchema && uiSchema[key] as UISchema);
      } else {
        // Simple key-value pair
        markdown += `**${formatPropertyName(key)}:** ${value}\n\n`;
      }
    }
    
    return markdown;
  }
  
  // Handle primitive data
  return `${data}\n\n`;
}

/**
 * Render a resource display widget
 * @param key Property key
 * @param value Property value (resource ID)
 * @param options Widget options
 * @returns Markdown representation of the resource
 */
async function renderResourceDisplay(key: string, value: any, options: any): Promise<string> {
  if (!value) return `**${formatPropertyName(key)}:** Not specified\n\n`;
  
  let markdown = `## ${formatPropertyName(key)}\n\n`;
  
  try {
    // Fetch the resource
    const resource = await fetchResource(options.resourceType, value);
    
    if (!resource) {
      return `${markdown}**Resource not found:** ${options.resourceType} with ID ${value}\n\n`;
    }
    
    // Extract the fields to display
    const displayData = extractResourceFields(resource, options.displayFields);
    
    // Add resource title
    markdown += `**${options.resourceType}:** ${formatResourceForDisplay(resource)}\n\n`;
    
    // Add resource fields
    if (displayData) {
      markdown += await dataToMarkdown(displayData);
    }
  } catch (error) {
    markdown += `**Error loading resource:** ${error}\n\n`;
  }
  
  return markdown;
}

/**
 * Render a table widget
 * @param key Property key
 * @param value Array of objects to display in a table
 * @param options Widget options
 * @returns Markdown table representation
 */
function renderTable(key: string, value: any[], options: any): string {
  if (!Array.isArray(value) || value.length === 0) {
    return `**${formatPropertyName(key)}:** Empty list\n\n`;
  }
  
  let markdown = `## ${formatPropertyName(key)}\n\n`;
  
  // Get columns to display
  const columns = options.columns || Object.keys(value[0]);
  
  // Table header
  markdown += '| ' + columns.map((col: string) => formatPropertyName(col)).join(' | ') + ' |\n';
  markdown += '| ' + columns.map(() => '---').join(' | ') + ' |\n';
  
  // Table rows
  value.forEach(item => {
    markdown += '| ' + columns.map((col: string) => {
      const cellValue = item[col];
      if (cellValue === null || cellValue === undefined) return '';
      if (typeof cellValue === 'object') return 'Object';
      return String(cellValue).replace(/\n/g, ' ');
    }).join(' | ') + ' |\n';
  });
  
  markdown += '\n';
  
  return markdown;
}

/**
 * Process a property of a JSON schema
 * @param propName The name of the property
 * @param propSchema The schema of the property
 * @param required Array of required property names
 * @param level The nesting level (for indentation)
 * @returns A markdown string representation of the property
 */
function processProperty(propName: string, propSchema: any, required?: string[], level: number = 0): string {
  const isRequired = required && required.includes(propName);
  const indent = '  '.repeat(level);
  
  let markdown = `${indent}### ${propName}${isRequired ? ' (required)' : ''}\n\n`;
  
  if (propSchema.description) {
    markdown += `${indent}${propSchema.description}\n\n`;
  }
  
  markdown += `${indent}**Type:** ${propSchema.type || 'any'}\n\n`;
  
  if (propSchema.enum) {
    markdown += `${indent}**Enum Values:** ${propSchema.enum.map((v: any) => `\`${v}\``).join(', ')}\n\n`;
  }
  
  if (propSchema.format) {
    markdown += `${indent}**Format:** ${propSchema.format}\n\n`;
  }
  
  if (propSchema.default !== undefined) {
    markdown += `${indent}**Default:** \`${JSON.stringify(propSchema.default)}\`\n\n`;
  }
  
  if (propSchema.minimum !== undefined) {
    markdown += `${indent}**Minimum:** ${propSchema.minimum}\n\n`;
  }
  
  if (propSchema.maximum !== undefined) {
    markdown += `${indent}**Maximum:** ${propSchema.maximum}\n\n`;
  }
  
  if (propSchema.minLength !== undefined) {
    markdown += `${indent}**Min Length:** ${propSchema.minLength}\n\n`;
  }
  
  if (propSchema.maxLength !== undefined) {
    markdown += `${indent}**Max Length:** ${propSchema.maxLength}\n\n`;
  }
  
  if (propSchema.pattern) {
    markdown += `${indent}**Pattern:** \`${propSchema.pattern}\`\n\n`;
  }
  
  // Handle nested objects
  if (propSchema.type === 'object' && propSchema.properties) {
    markdown += `${indent}**Properties:**\n\n`;
    
    Object.entries(propSchema.properties).forEach(([nestedPropName, nestedPropSchema]: [string, any]) => {
      markdown += processProperty(nestedPropName, nestedPropSchema, propSchema.required, level + 1);
    });
  }
  
  // Handle arrays
  if (propSchema.type === 'array' && propSchema.items) {
    markdown += `${indent}**Items:**\n\n`;
    markdown += processArrayItems(propSchema.items, level + 1);
  }
  
  return markdown;
}

/**
 * Process array items of a JSON schema
 * @param itemsSchema The schema of the array items
 * @param level The nesting level (for indentation)
 * @returns A markdown string representation of the array items
 */
function processArrayItems(itemsSchema: any, level: number = 0): string {
  const indent = '  '.repeat(level);
  
  let markdown = '';
  
  if (itemsSchema.type) {
    markdown += `${indent}**Type:** ${itemsSchema.type}\n\n`;
    
    if (itemsSchema.description) {
      markdown += `${indent}${itemsSchema.description}\n\n`;
    }
    
    // Handle object type array items
    if (itemsSchema.type === 'object' && itemsSchema.properties) {
      markdown += `${indent}**Properties:**\n\n`;
      
      Object.entries(itemsSchema.properties).forEach(([propName, propSchema]: [string, any]) => {
        markdown += processProperty(propName, propSchema, itemsSchema.required, level + 1);
      });
    }
    
    // Handle enum type array items
    if (itemsSchema.enum) {
      markdown += `${indent}**Enum Values:** ${itemsSchema.enum.map((v: any) => `\`${v}\``).join(', ')}\n\n`;
    }
  } else if (Array.isArray(itemsSchema)) {
    // Handle tuple type arrays
    itemsSchema.forEach((itemSchema, index) => {
      markdown += `${indent}**Item ${index + 1}:**\n\n`;
      markdown += `${indent}**Type:** ${itemSchema.type}\n\n`;
      
      if (itemSchema.description) {
        markdown += `${indent}${itemSchema.description}\n\n`;
      }
    });
  }
  
  return markdown;
}
