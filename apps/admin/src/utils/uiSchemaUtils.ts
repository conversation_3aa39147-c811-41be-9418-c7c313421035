import { UISchema, UISchemaWidgetProps, UISchemaOptions } from '../types/uiSchema';

/**
 * Validate a UI schema against a JSON schema
 * @param uiSchema The UI schema to validate
 * @param jsonSchema The JSON schema to validate against
 * @returns Array of validation errors, empty if valid
 */
export function validateUISchema(uiSchema: UISchema, jsonSchema: any): string[] {
  const errors: string[] = [];
  
  if (!jsonSchema || typeof jsonSchema !== 'object') {
    errors.push('JSON schema must be a valid object');
    return errors;
  }
  
  // Check ui:order contains valid properties
  if (uiSchema['ui:order']) {
    const schemaProperties = jsonSchema.properties ? Object.keys(jsonSchema.properties) : [];
    
    for (const prop of uiSchema['ui:order']) {
      if (!schemaProperties.includes(prop)) {
        errors.push(`Property "${prop}" in ui:order does not exist in the schema`);
      }
    }
  }
  
  // Check each property in the UI schema
  for (const [key, value] of Object.entries(uiSchema)) {
    // Skip ui: properties at the root level
    if (key.startsWith('ui:')) continue;
    
    // Check if the property exists in the JSON schema
    if (jsonSchema.properties && !jsonSchema.properties[key]) {
      errors.push(`Property "${key}" in UI schema does not exist in the JSON schema`);
      continue;
    }
    
    // Validate widget props if they exist
    if (value && typeof value === 'object') {
      const widgetProps = value as UISchemaWidgetProps;
      
      // Validate widget type
      if (widgetProps['ui:widget']) {
        const widget = widgetProps['ui:widget'];
        
        // Validate resourceDisplay widget
        if (widget === 'resourceDisplay') {
          const options = widgetProps['ui:options'] as UISchemaOptions;
          
          if (!options) {
            errors.push(`resourceDisplay widget for "${key}" requires ui:options`);
          } else {
            if (!options.resourceType) {
              errors.push(`resourceDisplay widget for "${key}" requires resourceType option`);
            }
            
            if (!options.lookupBy) {
              errors.push(`resourceDisplay widget for "${key}" requires lookupBy option`);
            }
          }
        }
        
        // Validate table widget
        if (widget === 'table') {
          const options = widgetProps['ui:options'] as UISchemaOptions;
          const propertySchema = jsonSchema.properties?.[key];
          
          if (!options) {
            errors.push(`table widget for "${key}" requires ui:options`);
          } else if (!options.columns || !Array.isArray(options.columns)) {
            errors.push(`table widget for "${key}" requires columns option as an array`);
          }
          
          // Check if the property is an array
          if (propertySchema && propertySchema.type !== 'array') {
            errors.push(`table widget can only be used for array properties, but "${key}" is not an array`);
          }
        }
      }
      
      // Validate ui:if conditions
      if (widgetProps['ui:if']) {
        const conditions = widgetProps['ui:if'];
        
        for (const conditionField of Object.keys(conditions)) {
          if (jsonSchema.properties && !jsonSchema.properties[conditionField]) {
            errors.push(`Condition field "${conditionField}" in ui:if for "${key}" does not exist in the schema`);
          }
        }
      }
    }
  }
  
  return errors;
}

/**
 * Apply UI schema ordering to properties
 * @param properties Object properties to order
 * @param uiOrder Order specified in the UI schema
 * @returns Ordered properties
 */
export function applyUIOrder(properties: string[], uiOrder?: string[]): string[] {
  if (!uiOrder || uiOrder.length === 0) return properties;
  
  const result: string[] = [];
  
  // Add properties in the specified order
  for (const prop of uiOrder) {
    if (properties.includes(prop)) {
      result.push(prop);
    }
  }
  
  // Add remaining properties that weren't in the order
  for (const prop of properties) {
    if (!result.includes(prop)) {
      result.push(prop);
    }
  }
  
  return result;
}

/**
 * Check if a property should be displayed based on ui:if conditions
 * @param propName Property name
 * @param uiSchema UI schema for the property
 * @param data Current data object
 * @returns True if the property should be displayed
 */
export function shouldDisplayProperty(propName: string, uiSchema: UISchema, data: any): boolean {
  const propUISchema = uiSchema[propName] as UISchemaWidgetProps;
  
  // If no UI schema or no ui:if, always display
  if (!propUISchema || !propUISchema['ui:if']) return true;
  
  // If ui:widget is hidden, don't display
  if (propUISchema['ui:widget'] === 'hidden') return false;
  
  // Check conditions
  const conditions = propUISchema['ui:if'];
  
  for (const [field, expectedValue] of Object.entries(conditions)) {
    // If the field doesn't exist in the data, condition fails
    if (!data || data[field] === undefined) return false;
    
    // If the value doesn't match, condition fails
    if (data[field] !== expectedValue) return false;
  }
  
  // All conditions passed
  return true;
}

/**
 * Get widget type for a property
 * @param propName Property name
 * @param uiSchema UI schema
 * @returns Widget type or undefined if not specified
 */
export function getWidgetType(propName: string, uiSchema: UISchema): string | undefined {
  const propUISchema = uiSchema[propName] as UISchemaWidgetProps;
  return propUISchema?.['ui:widget'];
}

/**
 * Get widget options for a property
 * @param propName Property name
 * @param uiSchema UI schema
 * @returns Widget options or undefined if not specified
 */
export function getWidgetOptions(propName: string, uiSchema: UISchema): UISchemaOptions | undefined {
  const propUISchema = uiSchema[propName] as UISchemaWidgetProps;
  return propUISchema?.['ui:options'];
}
