{"root": ["./src/app.tsx", "./src/router.tsx", "./src/main.tsx", "./src/test-property-editor.tsx", "./src/vite-env.d.ts", "./src/components/collapsiblemarkdown.tsx", "./src/components/definitionsmanager.tsx", "./src/components/propertyeditor.tsx", "./src/components/schemaform.tsx", "./src/components/schemamarkdownview.tsx", "./src/components/schemapropertytree.tsx", "./src/components/schemavisualizer.tsx", "./src/components/uischemamanager.tsx", "./src/components/propertyeditor/addchildpropertyform.tsx", "./src/components/propertyeditor/booleanpropertyeditor.tsx", "./src/components/propertyeditor/constvalueeditor.tsx", "./src/components/propertyeditor/numberpropertyeditor.tsx", "./src/components/propertyeditor/propertyeditor.tsx", "./src/components/propertyeditor/referencepropertyeditor.tsx", "./src/components/propertyeditor/resourcereferenceeditor.tsx", "./src/components/propertyeditor/stringpropertyeditor.tsx", "./src/components/propertyeditor/index.ts", "./src/components/propertyeditor/types.ts", "./src/components/propertyeditor/utils.ts", "./src/features/auth/index.ts", "./src/features/auth/components/authenticate.tsx", "./src/features/auth/components/loginorsignup.tsx", "./src/features/auth/components/protectedroute.tsx", "./src/hooks/useschemaeditor.ts", "./src/pages/agencies.page.tsx", "./src/pages/agencydetails.page.tsx", "./src/pages/auth.page.tsx", "./src/pages/home.page.tsx", "./src/pages/requests.page.tsx", "./src/pages/resourcecreate.page.tsx", "./src/pages/resourcedetails.page.tsx", "./src/pages/resources.page.tsx", "./src/pages/schemadetails.page.tsx", "./src/pages/schemas.page.tsx", "./src/pages/userdetails.page.tsx", "./src/pages/users.page.tsx", "./src/store/api.ts", "./src/store/hooks.ts", "./src/store/index.ts", "./src/store/schemaslice.test.ts", "./src/store/schemaslice.ts", "./src/test/schemaslice.test.ts", "./src/test/setup.ts", "./src/types/uischema.ts", "./src/utils/resourceutils.ts", "./src/utils/schematomarkdown.ts", "./src/utils/uischemautils.ts"], "errors": true, "version": "5.7.3"}