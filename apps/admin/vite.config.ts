import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
  envPrefix: ["STYTCH_", "VITE_", "API_"],
  plugins: [react()],
  base: "/",
  server: {
    port: 3001, // Use a different port than the main web app
    open: true, // Automatically open the browser
  },
  resolve: {
    alias: {
      "@": "/src",
    },
  },
});
