# Hospice OS API Documentation

This document provides documentation for the Hospice OS API endpoints.

## Authentication

All API endpoints require authentication using an API key. The API key should be provided in the `x-api-key` header.

```
x-api-key: your-api-key
```

## Base URL

The base URL for all API endpoints is:

```
http://localhost:3000/api
```

## Endpoints

### Health Check

```
GET /health
```

Returns the health status of the API.

**Response:**

```json
{
  "status": "ok"
}
```

### Schemas

#### List Schemas

```
GET /api/schemas
```

Returns a list of all schemas for the authenticated agency.

**Response:**

```json
[
  {
    "id": "schema-id",
    "name": "SchemaName",
    "version": "1.0.0",
    "agencyId": "agency-id",
    "baseSchemaId": "base-schema-id",
    "schema": { ... },
    "createdAt": "2025-04-17T13:00:00.000Z",
    "updatedAt": "2025-04-17T13:00:00.000Z"
  }
]
```

#### List Base Schemas

```
GET /api/schemas/base
```

Returns a list of all base schemas.

**Response:**

```json
[
  {
    "id": "schema-id",
    "name": "SchemaName",
    "version": "1.0.0",
    "agencyId": "agency-id",
    "schema": { ... },
    "createdAt": "2025-04-17T13:00:00.000Z",
    "updatedAt": "2025-04-17T13:00:00.000Z"
  }
]
```

#### Get Schema by ID

```
GET /api/schemas/:id
```

Returns a schema by ID.

**Response:**

```json
{
  "id": "schema-id",
  "name": "SchemaName",
  "version": "1.0.0",
  "agencyId": "agency-id",
  "baseSchemaId": "base-schema-id",
  "schema": { ... },
  "createdAt": "2025-04-17T13:00:00.000Z",
  "updatedAt": "2025-04-17T13:00:00.000Z"
}
```

#### Get Schema by Name and Version

```
GET /api/schemas/name/:name/version/:version
```

Returns a schema by name and version for the authenticated agency.

**Response:**

```json
{
  "id": "schema-id",
  "name": "SchemaName",
  "version": "1.0.0",
  "agencyId": "agency-id",
  "baseSchemaId": "base-schema-id",
  "schema": { ... },
  "createdAt": "2025-04-17T13:00:00.000Z",
  "updatedAt": "2025-04-17T13:00:00.000Z"
}
```

#### Create Schema

```
POST /api/schemas
```

Creates a new schema.

**Request:**

```json
{
  "name": "SchemaName",
  "version": "1.0.0",
  "agencyId": "agency-id",
  "baseSchemaId": "base-schema-id",
  "schema": {
    "type": "object",
    "properties": {
      "id": { "type": "string" },
      "resourceType": { "type": "string" },
      "name": { "type": "string" }
    },
    "required": ["id", "resourceType", "name"]
  }
}
```

**Response:**

```json
{
  "id": "schema-id",
  "name": "SchemaName",
  "version": "1.0.0",
  "agencyId": "agency-id",
  "baseSchemaId": "base-schema-id",
  "schema": { ... },
  "createdAt": "2025-04-17T13:00:00.000Z",
  "updatedAt": "2025-04-17T13:00:00.000Z"
}
```

#### Update Schema

```
PUT /api/schemas/:id
```

Updates an existing schema.

**Request:**

```json
{
  "version": "1.0.1",
  "schema": {
    "type": "object",
    "properties": {
      "id": { "type": "string" },
      "resourceType": { "type": "string" },
      "name": { "type": "string" },
      "description": { "type": "string" }
    },
    "required": ["id", "resourceType", "name"]
  }
}
```

**Response:**

```json
{
  "id": "schema-id",
  "name": "SchemaName",
  "version": "1.0.1",
  "agencyId": "agency-id",
  "baseSchemaId": "base-schema-id",
  "schema": { ... },
  "createdAt": "2025-04-17T13:00:00.000Z",
  "updatedAt": "2025-04-17T13:00:00.000Z"
}
```

#### Validate Against Schema

```
POST /api/schemas/validate
```

Validates data against a schema.

**Request:**

```json
{
  "schemaId": "schema-id",
  "data": {
    "id": "resource-id",
    "resourceType": "ResourceType",
    "name": "Resource Name"
  }
}
```

**Response:**

```json
{
  "valid": true,
  "errors": null
}
```

### Resources

#### Get Resource by ID

```
GET /api/resources/:id
```

Returns a resource by ID.

**Response:**

```json
{
  "id": "resource-id",
  "resourceType": "ResourceType",
  "name": "Resource Name",
  "description": "Resource Description"
}
```

#### Query Resources by Type

```
GET /api/resources/type/:type
```

Returns a list of resources by type for the authenticated agency.

**Response:**

```json
[
  {
    "id": "resource-id",
    "resourceType": "ResourceType",
    "name": "Resource Name",
    "description": "Resource Description"
  }
]
```

#### Create Resource

```
POST /api/resources
```

Creates a new resource.

**Request:**

```json
{
  "id": "resource-id",
  "resourceType": "ResourceType",
  "name": "Resource Name",
  "description": "Resource Description",
  "schemaId": "schema-id",
  "agencyId": "agency-id"
}
```

**Response:**

```json
{
  "id": "resource-id",
  "resourceType": "ResourceType",
  "name": "Resource Name",
  "description": "Resource Description"
}
```

#### Update Resource

```
PUT /api/resources/:id
```

Updates an existing resource.

**Request:**

```json
{
  "resourceType": "ResourceType",
  "name": "Updated Resource Name",
  "description": "Updated Resource Description",
  "schemaId": "schema-id"
}
```

**Response:**

```json
{
  "id": "resource-id",
  "resourceType": "ResourceType",
  "name": "Updated Resource Name",
  "description": "Updated Resource Description"
}
```

#### Delete Resource

```
DELETE /api/resources/:id
```

Deletes a resource.

**Response:**

```
204 No Content
```

### Users

#### Get User by ID

```
GET /api/users/:id
```

Returns a user by ID.

**Response:**

```json
{
  "id": "user-id",
  "resourceType": "User",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "Admin"
}
```

#### Get User by Email

```
GET /api/users/email/:email
```

Returns a user by email.

**Response:**

```json
{
  "id": "user-id",
  "resourceType": "User",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "Admin"
}
```

#### Query Users by Type

```
GET /api/users/type/:type
```

Returns a list of users by type for the authenticated agency.

**Response:**

```json
[
  {
    "id": "user-id",
    "resourceType": "User",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "Admin"
  }
]
```

#### Create User

```
POST /api/users
```

Creates a new user.

**Request:**

```json
{
  "id": "user-id",
  "resourceType": "User",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "Admin",
  "schemaId": "schema-id",
  "agencyId": "agency-id"
}
```

**Response:**

```json
{
  "id": "user-id",
  "resourceType": "User",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "Admin"
}
```

#### Update User

```
PUT /api/users/:id
```

Updates an existing user.

**Request:**

```json
{
  "resourceType": "User",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Smith",
  "role": "Admin",
  "schemaId": "schema-id"
}
```

**Response:**

```json
{
  "id": "user-id",
  "resourceType": "User",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Smith",
  "role": "Admin"
}
```

#### Delete User

```
DELETE /api/users/:id
```

Deletes a user.

**Response:**

```
204 No Content
```

### Agencies

#### List Agencies

```
GET /api/agencies
```

Returns a list of all agencies.

**Response:**

```json
[
  {
    "id": "agency-id",
    "name": "Agency Name"
  }
]
```

#### Get Agency by ID

```
GET /api/agencies/:id
```

Returns an agency by ID.

**Response:**

```json
{
  "id": "agency-id",
  "name": "Agency Name"
}
```

#### Create Agency

```
POST /api/agencies
```

Creates a new agency.

**Request:**

```json
{
  "name": "Agency Name"
}
```

**Response:**

```json
{
  "id": "agency-id",
  "name": "Agency Name"
}
```

#### Update Agency

```
PUT /api/agencies/:id
```

Updates an existing agency.

**Request:**

```json
{
  "name": "Updated Agency Name"
}
```

**Response:**

```json
{
  "id": "agency-id",
  "name": "Updated Agency Name"
}
```

#### Delete Agency

```
DELETE /api/agencies/:id
```

Deletes an agency.

**Response:**

```
204 No Content
```

## Error Handling

The API returns appropriate HTTP status codes and error messages for different types of errors:

- `400 Bad Request`: Invalid request parameters or validation errors
- `401 Unauthorized`: Missing or invalid API key
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

Error responses have the following format:

```json
{
  "error": "Error message"
}
```

For validation errors, the response includes details about the validation errors:

```json
{
  "error": "Validation error",
  "details": [
    {
      "keyword": "required",
      "dataPath": "",
      "schemaPath": "#/required",
      "params": {
        "missingProperty": "name"
      },
      "message": "should have required property 'name'"
    }
  ]
}
```
