# Hospice OS Server

This is the server component of the Hospice OS application. It provides a data layer for storing and retrieving resources using a PostgreSQL database or in-memory repositories for development and testing.

## Data Layer Architecture

The data layer is designed to support dynamic schema management and multi-tenancy:

- **Resources**: All resources (patients, notes, etc.) are stored in a PostgreSQL database with a flexible schema.
- **Schema Validation**: Resources are validated against JSON Schema definitions stored in the database.
- **Multi-tenancy**: Resources are associated with agencies for multi-tenant support.
- **Users**: Users are stored in a separate table but follow the same pattern as resources.
- **Repository Pattern**: The data layer uses the repository pattern to abstract the storage mechanism, allowing for both PostgreSQL and in-memory implementations.

### Database Structure

- **agencies**: Stores information about different agencies (tenants).
- **schemas**: Stores JSON Schema definitions for validating resources.
- **resources**: Stores all resources (patients, notes, etc.) with their content as JSONB.
- **users**: Stores user information with their content as JSONB.

## Setup

### Prerequisites

- Node.js (v18 or later)
- PostgreSQL (v14 or later) - only required for PostgreSQL mode

### Environment Variables

The following environment variables can be set to configure the database connection:

- `DB_HOST`: PostgreSQL host (default: localhost)
- `DB_PORT`: PostgreSQL port (default: 5432)
- `DB_NAME`: PostgreSQL database name (default: hospice_os)
- `DB_USER`: PostgreSQL username (default: postgres)
- `DB_PASSWORD`: PostgreSQL password (default: postgres)
- `USE_IN_MEMORY_REPOSITORIES`: Set to 'true' to use in-memory repositories instead of PostgreSQL

### Installation

1. Install dependencies:

```bash
npm install
```

2. Initialize the database (only required for PostgreSQL mode):

```bash
npm run db:init
```

3. Seed the base schemas (only required for PostgreSQL mode):

```bash
npm run db:seed
```

4. Start the server:

```bash
# Using PostgreSQL
npm run app-dev

# Using in-memory repositories
npm run app-dev:memory
```

## Repository Implementations

The data layer supports two repository implementations:

### PostgreSQL Repositories

The PostgreSQL repositories store data in a PostgreSQL database. This is the default implementation and is suitable for production use.

### In-Memory Repositories

The in-memory repositories store data in memory. This implementation is suitable for development and testing, as it doesn't require a PostgreSQL database. Data is lost when the server is restarted.

To use the in-memory repositories, set the `USE_IN_MEMORY_REPOSITORIES` environment variable to 'true' or use the `app-dev:memory` script.

## API Services

The server provides the following services:

### Schema Service

- `createSchema`: Create a new schema
- `updateSchema`: Update an existing schema
- `getSchema`: Get a schema by ID
- `getSchemaByName`: Get a schema by name, version, and agency ID
- `listSchemas`: List all schemas for an agency
- `listBaseSchemas`: List all base schemas
- `validateAgainstSchema`: Validate data against a schema

### Resource Service

- `createResource`: Create a new resource
- `readResource`: Get a resource by ID
- `updateResource`: Update an existing resource
- `deleteResource`: Delete a resource
- `queryResourcesByType`: Query resources by type and agency ID

### User Service

- `createUser`: Create a new user
- `readUser`: Get a user by ID
- `updateUser`: Update an existing user
- `deleteUser`: Delete a user
- `queryUsersByType`: Query users by type and agency ID
- `getUserByEmail`: Get a user by email

### Agency Service

- `createAgency`: Create a new agency
- `getAgency`: Get an agency by ID
- `listAgencies`: List all agencies
- `updateAgency`: Update an existing agency
- `deleteAgency`: Delete an agency

## Development

### Building

```bash
npm run build
```

### Linting

```bash
npm run lint
```

### Bundling for Deployment

```bash
npm run bundle
```

### Testing

The in-memory repositories can be used for testing. The `setupTestEnvironment` function in `src/db/test-utils.ts` can be used to set up a test environment with in-memory repositories and seeded base schemas.

```typescript
import { setupTestEnvironment } from './db/test-utils';

// Set up test environment
const { factory, systemAgency, testAgency } = await setupTestEnvironment();

// Use repositories
const schemaRepository = factory.getSchemaRepository();
const resourceRepository = factory.getResourceRepository();
const userRepository = factory.getUserRepository();
const agencyRepository = factory.getAgencyRepository();
```
