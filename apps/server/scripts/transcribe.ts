import { AssemblyAI } from 'assemblyai'
import fs from 'fs';
const assemblyAiClient = new AssemblyAI({
    apiKey: '********************************'
})

const getTranscription = async (audioUrl: string) => {
    const transcript = await assemblyAiClient.transcripts.transcribe({
        audio: fs.createReadStream(audioUrl)
    })
    console.log(transcript.text)
}

getTranscription('/Users/<USER>/Downloads/Recording from Hospice Uploads.webm')
