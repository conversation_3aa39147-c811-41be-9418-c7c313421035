import express from 'express';
import { agencyService } from '../services/agency-service';
import { validateRequest } from './middleware/validation';
import { NotFoundError } from './middleware/error-handler';

const router = express.Router();

/**
 * GET /api/agencies
 * List all agencies
 */
router.get('/', async (req, res, next) => {
  try {
    const agencies = await agencyService.listAgencies();
    res.json(agencies);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/agencies/stytch/:stytchOrganizationId
 * Get an agency by Stytch organization ID
 */
router.get('/stytch/:stytchOrganizationId', async (req, res, next) => {
  try {
    const agency = await agencyService.getAgencyByStytchOrganizationId(req.params.stytchOrganizationId);
    
    if (!agency) {
      throw new NotFoundError(`Agency with Stytch organization ID ${req.params.stytchOrganizationId} not found`);
    }
    
    res.json(agency);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/agencies/:id
 * Get an agency by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const agency = await agencyService.getAgency(req.params.id);
    
    if (!agency) {
      throw new NotFoundError(`Agency with ID ${req.params.id} not found`);
    }
    
    res.json(agency);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/agencies
 * Create a new agency
 */
router.post('/', validateRequest('createAgency'), async (req, res, next) => {
  try {
    const { name, stytchOrganizationId } = req.body;
    
    const newAgency = await agencyService.createAgency(name, stytchOrganizationId);
    
    res.status(201).json(newAgency);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/agencies/:id
 * Update an agency
 */
router.put('/:id', validateRequest('updateAgency'), async (req, res, next) => {
  try {
    const { name, stytchOrganizationId } = req.body;
    
    const updatedAgency = await agencyService.updateAgency(
      req.params.id,
      name,
      stytchOrganizationId
    );
    
    if (!updatedAgency) {
      throw new NotFoundError(`Agency with ID ${req.params.id} not found`);
    }
    
    res.json(updatedAgency);
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/agencies/:id
 * Delete an agency
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const success = await agencyService.deleteAgency(req.params.id);
    
    if (!success) {
      throw new NotFoundError(`Agency with ID ${req.params.id} not found`);
    }
    
    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

export default router;
