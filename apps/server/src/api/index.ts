import express from 'express';
import schemaRoutes from './schemas';
import resourceRoutes from './resources';
import userRoutes from './users';
import agencyRoutes from './agencies';
import uiSchemaRoutes from './ui-schemas';
import requestRoutes from './requests';
import { authenticate } from './middleware/auth';
import { errorHandler } from './middleware/error-handler';

const router = express.Router();

// Apply authentication to all API routes
router.use(authenticate);

// Mount route handlers
router.use('/schemas', schemaRoutes);
router.use('/resources', resourceRoutes);
router.use('/users', userRoutes);
router.use('/agencies', agencyRoutes);
router.use('/ui-schemas', uiSchemaRoutes);
router.use('/requests', requestRoutes);

// Apply error handler
router.use(errorHandler);

export default router;
