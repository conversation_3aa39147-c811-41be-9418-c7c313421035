import { Request, Response, NextFunction } from "express";
import { agencyService } from "../../services/agency-service";
import { stytchClient } from "../../services/stytch-service";
import { userService } from "../../services/user-service";

/**
 * Admin authentication middleware
 * This middleware validates Stytch session tokens for the admin application
 */
export const adminAuthenticate = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  // Get the session JWT from the cookie or Authorization header
  let sessionJWT = req.cookies.stytch_session_jwt;

  // If not in cookies, check Authorization header
  if (!sessionJWT) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      sessionJWT = authHeader.substring(7); // Remove 'Bearer ' prefix
    }
  }

  if (!sessionJWT) {
    return res.status(401).json({ error: "Authentication token required" });
  }

  try {
    // Authenticate the session with Stytch
    const response = await stytchClient.sessions.authenticateJwt({
      session_jwt: sessionJWT,
    });

    // Get the member session from the response
    const memberSession = response.member_session;

    // Get the agency details
    const orgResponse = await stytchClient.organizations.get({
      organization_id: memberSession.organization_id,
    });

    // Verify that the agency slug is 'super_admin'
    if (orgResponse.organization.organization_slug !== "super_admin") {
      return res.status(403).json({
        error:
          "Access denied. Only super_admin agency members can access the admin API.",
      });
    }

    // Set admin context
    req.isAdmin = true;

    // Get the member details
    const memberResponse = await stytchClient.organizations.members.get({
      organization_id: memberSession.organization_id,
      member_id: memberSession.member_id,
    });

    // Add member and agency info to the request for use in controllers
    req.member = memberResponse.member;

    // Store the Stytch organization object with its ID in the agency field
    const stytchOrg = orgResponse.organization;
    req.agency = {
      ...stytchOrg,
      stytchOrganizationId: stytchOrg.organization_id,
    };

    // Try to find an agency with this Stytch organization ID
    try {
      const agency = await agencyService.getAgencyByStytchOrganizationId(
        stytchOrg.organization_id,
      );

      if (agency) {
        // If found, attach the agency to the request and set the agencyId
        req.dbAgency = agency;
        req.agencyId = agency.id;
      }
    } catch (error) {
      // Ignore errors in agency lookup
      console.warn("Error looking up agency by Stytch organization ID:", error);
    }

    // Try to find a user with this Stytch member ID
    try {
      const user = await userService.getUserByStytchId(memberSession.member_id);

      if (user) {
        // If found, attach the user to the request
        req.user = user;
      }
    } catch (error) {
      console.error("Error looking up user by Stytch ID:", error);
      res.status(401).json({
        error: "Invalid authentication token, cannot find matching user",
      });
      return;
    }

    next();
  } catch (error) {
    console.error("Error validating admin token:", error);
    return res.status(401).json({ error: "Invalid authentication token" });
  }
};

/**
 * Middleware to ensure the request is from an admin
 */
export const requireAdmin = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (!req.isAdmin) {
    return res.status(403).json({ error: "Admin access required" });
  }

  next();
};
