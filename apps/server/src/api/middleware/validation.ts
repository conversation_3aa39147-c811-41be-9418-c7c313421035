import { Request, Response, NextFunction } from 'express';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

// Create AJV instance
const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// Define validation schemas for each endpoint
const validationSchemas = {
  // UI Schema endpoints
  createUISchema: {
    type: 'object',
    required: ['name', 'schemaId', 'content'],
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      schemaId: { type: 'string' },
      content: { type: 'object' }
    }
  },
  updateUISchema: {
    type: 'object',
    required: ['name', 'content'],
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      content: { type: 'object' }
    }
  },
  // Schema endpoints
  createSchema: {
    type: 'object',
    required: ['name', 'version', 'schema'],
    properties: {
      name: { type: 'string' },
      version: { type: 'string' },
      baseSchemaId: { type: 'string' },
      schema: { type: 'object' }
    }
  },
  updateSchema: {
    type: 'object',
    required: ['version', 'schema'],
    properties: {
      version: { type: 'string' },
      schema: { type: 'object' }
    }
  },
  validateAgainstSchema: {
    type: 'object',
    required: ['schemaId', 'data'],
    properties: {
      schemaId: { type: 'string' },
      data: { type: 'object' }
    }
  },
  
  // Resource endpoints
  createResource: {
    type: 'object',
    required: ['resourceType', 'schemaId', 'agencyId'],
    properties: {
      id: { type: 'string' },
      resourceType: { type: 'string' },
      schemaId: { type: 'string' },
      agencyId: { type: 'string' }
    },
    additionalProperties: true
  },
  updateResource: {
    type: 'object',
    required: ['resourceType', 'schemaId'],
    properties: {
      resourceType: { type: 'string' },
      schemaId: { type: 'string' }
    },
    additionalProperties: true
  },
  
  // User endpoints
  createUser: {
    type: 'object',
    required: ['resourceType', 'schemaId', 'agencyId', 'email'],
    properties: {
      id: { type: 'string' },
      resourceType: { type: 'string' },
      schemaId: { type: 'string' },
      agencyId: { type: 'string' },
      email: { type: 'string', format: 'email' }
    },
    additionalProperties: true
  },
  updateUser: {
    type: 'object',
    required: ['resourceType', 'schemaId'],
    properties: {
      resourceType: { type: 'string' },
      schemaId: { type: 'string' },
      email: { type: 'string', format: 'email' }
    },
    additionalProperties: true
  },
  
  // Agency endpoints
  createAgency: {
    type: 'object',
    required: ['name'],
    properties: {
      name: { type: 'string' }
    }
  },
  updateAgency: {
    type: 'object',
    required: ['name'],
    properties: {
      name: { type: 'string' }
    }
  }
};

/**
 * Request validation middleware
 * Validates the request body against a JSON schema
 * @param schemaName The name of the schema to validate against
 */
export const validateRequest = (schemaName: keyof typeof validationSchemas) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // const validate = ajv.compile(validationSchemas[schemaName]);
    // const valid = validate(req.body);
    
    // if (!valid) {
    //   return res.status(400).json({ 
    //     error: 'Validation error', 
    //     details: validate.errors 
    //   });
    // }
    
    next();
  };
};
