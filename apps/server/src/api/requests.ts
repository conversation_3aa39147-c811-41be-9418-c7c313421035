/* eslint-disable @typescript-eslint/no-explicit-any */
import express from "express";
import { resourceService } from "../services/resource-service";
import { validateRequest } from "./middleware/validation";
import { NotFoundError } from "./middleware/error-handler";
import { v4 as uuidv4 } from "uuid";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { config } from "../config";
import { s3Client } from "../s3";
import { Request } from "@hospice-os/apptypes";
import {
  getPatientRequestWorkflowHandle,
  startPdfImportWorkflow,
  getPdfImportWorkflowHandle,
} from "../temporal/client";
import {
  modifyChangesetSignal,
  sendMoreAudioSignal,
  reviewSignal,
} from "../temporal/patient/patientRequestWorkflow";
import { convertPDFToMarkdownSignal } from "../temporal/pdf/pdfWorkflow";

const router = express.Router();

/**
 * GET /api/requests?patientId=:patientId&status=:status
 * Get requests by patient ID
 */
router.get("/", async (req, res, next) => {
  try {
    const { patientId, status } = req.query;

    if (!patientId) {
      return res.status(400).json({ error: "Patient ID is required" });
    }

    const fields = [
      "patient",
      "approvedBy",
      "noteCreated",
      "noteSchemaId",
      "madeBy",
      "title",
      "summary",
      "changes",
      "resourceType",
      "status",
      "tags",
      "hideOnTimeline",
      "validationErrors"
    ];
    const pathFilters: Record<string, unknown> = { "patient.id": patientId };

    if (status && status !== "undefined" && status !== "null")
      pathFilters["status"] = status;

    const requests = await resourceService.queryResourcesByType(
      "Request",
      req.agencyId,
      pathFilters,
      fields,
    );

    res.json(requests);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/requests/:id
 * Get request by ID
 */
router.get("/:id", async (req, res, next) => {
  try {
    const request = await resourceService.readResource(req.params.id);

    if (!request || request.resourceType !== "Request") {
      throw new NotFoundError(`Request with ID ${req.params.id} not found`);
    }

    res.json(request);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/requests
 * Create a new request
 */
router.post("/", validateRequest("createResource"), async (req, res, next) => {
  try {
    const requestData: Request = {
      ...req.body,
      resourceType: "Request",
      createdAt: new Date().toISOString(),
      madeBy: {
        id: req.user?.id,
        date: new Date().toISOString(),
        name: req.user?.firstName + " " + req.user?.lastName,
      },
    };

    const newRequest = await resourceService.createResource(
      requestData as any,
      req.body.schemaId,
      req.agencyId,
    );

    try {
      if (
        process.env.NODE_ENV !== "development" &&
        req.agencyId !== "bd6e787b-b665-4f7b-9b32-1df1797620cd"
      ) {
        await fetch(
          "*******************************************************************************",
          {
            method: "POST",
            body: JSON.stringify({
              text: `<!channel> New request. Agency: ${req.dbAgency.name} - User: ${req.user?.firstName + " " + req.user?.lastName}`,
            }),
            headers: {
              "Content-Type": "application/json",
            },
          },
        );
      }
    } catch (error) {
      console.error("Error sending slack webhook: ", error);
    }

    res.status(201).json(newRequest);
  } catch (error) {
    next(error);
  }
});

router.post(
  "/file-upload",
  validateRequest("createResource"),
  async (req, res, next) => {
    try {
      const { fileName, fileType, patientId } = req.body;

      if (!fileName || !fileType) {
        return res
          .status(400)
          .json({ error: "File name and type are required" });
      }

      // Generate a unique file ID
      const fileId = uuidv4();

      const filePath = `uploads/${patientId}/${fileId}-${fileName}`;

      // Generate pre-signed S3 URL for PUT operation
      const s3Params = {
        Bucket: config.aws.s3Bucket,
        Key: filePath,
        ContentType: fileType,
      };

      const command = new PutObjectCommand(s3Params);
      const url = await getSignedUrl(s3Client, command, { expiresIn: 60 * 5 });

      res.json({ url, filePath });
    } catch (error) {
      console.error(error);
      next(error);
    }
  },
);

/**
 * POST /api/requests/:id/attachments
 * Add file attachment to request
 */
router.post("/:id/attachments", async (req, res, next) => {
  try {
    const { fileId } = req.body;

    if (!fileId) {
      return res.status(400).json({ error: "File ID is required" });
    }

    const request = await resourceService.readResource(req.params.id);

    if (!request || request.resourceType !== "Request") {
      throw new NotFoundError(`Request with ID ${req.params.id} not found`);
    }

    // Add file attachment to request
    const fileAttachments = (request.fileAttachments as string[]) || [];
    fileAttachments.push(fileId);

    const updatedRequest = await resourceService.updateResource(
      req.params.id,
      {
        ...request,
        fileAttachments,
      },
      request.schemaId,
    );

    res.json(updatedRequest);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/requests/:id/responses
 * Add response to request
 */
router.post("/:id/responses", async (req, res, next) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({ error: "Response content is required" });
    }

    const request = await resourceService.readResource(req.params.id);

    if (!request || request.resourceType !== "Request") {
      throw new NotFoundError(`Request with ID ${req.params.id} not found`);
    }

    // Add response to request
    const responses =
      (request.responses as Array<{ sentBy: string; content: string }>) || [];
    responses.push({
      sentBy: req.user?.id || "unknown", // Get user ID from the authenticated user
      content,
    });

    const updatedRequest = await resourceService.updateResource(
      req.params.id,
      {
        ...request,
        responses,
      },
      request.schemaId,
    );

    res.json(updatedRequest);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/requests/:id/modify-changeset
 * Specifically modify the changeset for the request
 */
router.post("/:id/modify-changeset", async (req, res, next) => {
  try {
    const { audioUrl } = req.body;

    if (!audioUrl) {
      return res.status(400).json({ error: "Audio URL is required" });
    }

    const handle = await getPatientRequestWorkflowHandle(
      req.params.id,
      req.agencyId,
    );

    // Check if workflow is still running
    const description = await handle.describe();
    if (description.status.name !== "RUNNING") {
      res.status(400).json({
        error: "Workflow has already completed",
        status: description.status.name,
        workflowId: req.params.id,
      });
      return;
    }

    await handle.signal(modifyChangesetSignal, {
      audioUrl,
      requestId: req.params.id,
    });

    res.json({ invocationId: req.params.id });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/requests/:id/more-audio
 * Send additional audio for processing
 */
router.post("/:id/more-audio", async (req, res, next) => {
  try {
    const { audioUrls } = req.body;

    if (!audioUrls || !Array.isArray(audioUrls)) {
      return res.status(400).json({ error: "audioUrls is required" });
    }
    const handle = await getPatientRequestWorkflowHandle(
      req.params.id,
      req.agencyId,
    );

    // Check if workflow is still running
    const description = await handle.describe();
    if (description.status.name !== "RUNNING") {
      res.status(400).json({
        error: "Workflow has already completed",
        status: description.status.name,
        workflowId: req.params.id,
      });
      return;
    }

    await handle.signal(sendMoreAudioSignal, {
      audioUrls,
      requestId: req.params.id,
    });

    res.json({ invocationId: req.params.id });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/requests/:id/review
 * Finalize request processing
 */
router.post("/:id/review", async (req, res, next) => {
  const manualChangeset = req.body.manualChangeset;
  //TODO: Uncomment this when we have a way to get the user id from the request
  const userId = req.user?.id;
  if (!req.user?.id) {
    // res.status(401).json({ error: "Unauthorized" });
    // return;
    console.log("no user id found when reviewing request");
  }

  try {
    const handle = await getPatientRequestWorkflowHandle(
      req.params.id,
      req.agencyId,
    );

    // Check if workflow is still running
    const description = await handle.describe();
    if (description.status.name !== "RUNNING") {
      res.status(400).json({
        error: "Workflow has already completed",
        status: description.status.name,
        workflowId: req.params.id,
      });
      return;
    }

    await handle.signal(reviewSignal, {
      userId,
      requestId: req.params.id,
      manualChangeset,
    });

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

router.post("/:id/referral-pdf", async (req, res, next) => {
  console.log("req.body", req.body);
  try {
    const { pdfUrl } = req.body;
    console.log("pdfUrl", pdfUrl);
    // Start or get existing PDF workflow
    let handle;
    try {
      handle = await getPdfImportWorkflowHandle(req.params.id, req.agencyId);

      // Check if existing workflow is still running
      const description = await handle.describe();
      if (description.status.name !== "RUNNING") {
        // Workflow completed, start a new one
        handle = await startPdfImportWorkflow(req.params.id, req.agencyId);
      }
    } catch (error) {
      // Workflow doesn't exist, start a new one
      handle = await startPdfImportWorkflow(req.params.id, req.agencyId);
    }

    await handle.signal(convertPDFToMarkdownSignal, {
      requestId: req.params.id,
      pdfUrl,
    });

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

export default router;
