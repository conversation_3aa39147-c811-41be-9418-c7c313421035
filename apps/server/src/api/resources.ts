import express from "express";
import { resourceService } from "../services/resource-service";
import { validateRequest } from "./middleware/validation";
import { NotFoundError } from "./middleware/error-handler";
import { getSignedUrl } from "../services/storage-service";
import { Patient, Request } from "@hospice-os/apptypes";
import {
  generateLastNoteSummary,
  generatePatientSummaryForIDG,
} from "../services/llm-service";
import { requestActivities } from "../temporal/request/requestActivities";

const router = express.Router();

/**
 * GET /api/resources/:id
 * Get a resource by ID
 */
router.get("/:type/:id", async (req, res, next) => {
  try {
    const resource = await resourceService.readResource(req.params.id);

    if (req.params.type !== resource.resourceType) {
      throw new NotFoundError(
        `Resource of type ${req.params.type} with ID ${req.params.id} not found, make sure the resource type is correct`,
      );
    }

    if (!resource) {
      throw new NotFoundError(`Resource with ID ${req.params.id} not found`);
    }

    res.json(resource);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/resources/type/:type
 * Query resources by type and agency ID
 * Can filter on JSONB content paths using query parameters
 * Can limit fields in the response using the fields query parameter
 */
router.get("/:type", async (req, res, next) => {
  try {
    const agencyId = req.agencyId;

    if (!agencyId) {
      return res.status(400).json({ error: "Agency ID is required" });
    }

    // Extract query parameters for JSONB path filtering
    // Exclude special query parameters that shouldn't be used for content filtering
    const excludedParams = ["page", "limit", "sort", "order", "fields"];
    const jsonPathFilters: Record<string, unknown> = {};

    let limit: number | undefined;

    for (const [key, value] of Object.entries(req.query)) {
      if (!excludedParams.includes(key)) {
        jsonPathFilters[key] = value;
      } else if (key === "limit") {
        limit = Number(value);
      }
    }

    // Extract fields parameter if present
    let fields: string[] | undefined;
    if (req.query.fields) {
      // Handle both string and array formats
      if (typeof req.query.fields === "string") {
        fields = req.query.fields.split(",").map((field) => field.trim());
      } else if (Array.isArray(req.query.fields)) {
        fields = (req.query.fields as string[]).map((field) => field.trim());
      }
    }

    console.log("jsonPathFilters", jsonPathFilters);
    console.log("fields", fields);
    console.log("agencyId", agencyId);
    console.log("type", req.params.type);

    const resources = await resourceService.queryResourcesByType(
      req.params.type,
      agencyId,
      Object.keys(jsonPathFilters).length > 0 ? jsonPathFilters : undefined,
      fields,
      limit,
    );

    res.json(resources);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/resources
 * Create a new resource
 */
router.post("/", validateRequest("createResource"), async (req, res, next) => {
  try {
    const { schemaId, ...resourceData } = req.body;

    const newResource = await resourceService.createResource(
      resourceData,
      schemaId,
      req.agencyId,
    );

    res.status(201).json(newResource);
  } catch (error) {
    console.error(error);
    next(error);
  }
});

/**
 * GET /api/resources/presigned-download-url?urlPath=/path/to/file.pdf
 * Get a presigned URL for a resource
 */
router.get("/presigned-download-url", async (req, res, next) => {
  try {
    const { urlPath } = req.query;
    console.log("urlPath", urlPath);
    const presignedUrl = await getSignedUrl(urlPath as string);
    console.log("presignedUrl", presignedUrl);
    res.json({ presignedUrl });
  } catch (error) {
    next(error);
  }
});
/**
 * PUT /api/resources/:id
 * Update a resource
 */
router.put(
  "/:id",
  validateRequest("updateResource"),
  async (req, res, next) => {
    try {
      const { schemaId, ...resourceData } = req.body;

      const updatedResource = await resourceService.updateResource(
        req.params.id,
        resourceData,
        schemaId,
      );

      if (!updatedResource) {
        throw new NotFoundError(`Resource with ID ${req.params.id} not found`);
      }

      res.json(updatedResource);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * DELETE /api/resources/:id
 * Delete a resource
 */
router.delete("/:id", async (req, res, next) => {
  try {
    const success = await resourceService.deleteResource(req.params.id);

    if (!success) {
      throw new NotFoundError(`Resource with ID ${req.params.id} not found`);
    }

    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/resources/Patient/:id/summary
 * Gets the summary of a patient
 */
router.get("/Patient/:id/summary", async (req, res, next) => {
  try {
    const { id: patientId } = req.params;

    if (!patientId) {
      return res.status(400).json({ error: "Patient ID is required" });
    }

    const fields = [
      "noteCreated",
      "madeBy",
      "title",
      "summary",
      "changes",
      "resourceType",
      "status",
    ];
    let pathFilters: Record<string, unknown> = {
      "patient.id": patientId,
      status: "completed",
    };

    // TODO: Potentially move this to a dedicated query instead of having to use two separate queries?...
    /** select jsonb_agg(
      jsonb_build_object(
        'note', notes.content,
        'changes', patient_resources.content->'changes',
      'madeBy', patient_resources.content->'madeBy'->>'name'
      )
    ) as enriched_note_resources
    from resources as patient_resources
    join resources as notes
      on notes.content->>'id' = patient_resources.content->'noteCreated'->>'id'
    where patient_resources.content->'patient'->>'id' = '.....' **/

    // Selecting all requests
    const requests = await resourceService.queryResourcesByType(
      "Request",
      req.agencyId,
      pathFilters,
      fields,
    );

    const allNoteIds = [];

    requests.forEach((r) => {
      const request = r as unknown as Request;
      if (request.noteCreated) {
        allNoteIds.push(request.noteCreated.id);
      }
    });

    // Selecting all notes by ID
    pathFilters = { id: allNoteIds };

    const notes = await resourceService.queryResourcesByType(
      "Note",
      req.agencyId,
      pathFilters,
      undefined,
    );

    const resolvedNotes = [];

    notes.forEach((n) => {
      const note = { ...n };
      delete note.id;
      delete note.schemaId;
      delete note.agencyId;
      delete note.patientId;
      delete note.authorId;
      delete note.resourceType;
      delete note.agencyName;

      const foundRequest = (requests as unknown as Request[]).find(
        (r) => r.noteCreated?.id === n.id,
      );

      if (foundRequest && foundRequest.changes)
        note.changes = foundRequest.changes;

      if (foundRequest && foundRequest.madeBy && foundRequest.madeBy.name)
        note.madeBy = foundRequest.madeBy.name;

      resolvedNotes.push(note);
    });

    const patient = await resourceService.readResource(patientId);
    const patientChart = requestActivities.minifyPatient(
      patient as unknown as Patient,
    );

    const summary = await generatePatientSummaryForIDG(
      patientChart,
      resolvedNotes,
    );

    res.json(summary);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/resources/Note/:id/lastSummary
 * Gets the summary of the last note for the provided note schema ID
 */
router.get("/Note/:id/lastSummary", async (req, res, next) => {
  try {
    const { id: schemaId } = req.params;
    const { patientId } = req.query;

    if (!patientId || !schemaId) {
      return res
        .status(400)
        .json({ error: "Patient ID and Schema ID are required" });
    }

    const pathFilters: Record<string, unknown> = {
      "patient.id": patientId,
      noteSchemaId: schemaId,
      status: "completed",
    };

    const lastCompletedRequests = await resourceService.queryResourcesByType(
      "Request",
      req.agencyId,
      pathFilters,
      undefined,
      1,
    );

    if (!lastCompletedRequests || lastCompletedRequests.length === 0)
      return res.status(404).json({ error: "No completed requests found." });

    const createdNoteId = (lastCompletedRequests[0] as unknown as Request)
      .noteCreated?.id;
    if (!createdNoteId)
      return res.status(404).json({ error: "No completed note found." });

    const lastNote = await resourceService.readResource(createdNoteId);

    const summary = await generateLastNoteSummary(lastNote);

    res.json(summary);
  } catch (error) {
    next(error);
  }
});

export default router;
