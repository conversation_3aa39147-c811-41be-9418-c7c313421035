import express from 'express';
import { schemaService } from '../services/schema-service';
import { getSchemaResolverService } from '../services/schema-resolver-service';
import { validateRequest } from './middleware/validation';
import { NotFoundError } from './middleware/error-handler';

const router = express.Router();

/**
 * GET /api/schemas
 * List all schemas for an agency
 * Optional query parameter: baseSchemaId - filter schemas by base schema ID
 */
router.get('/', async (req, res, next) => {
  try {
    console.log('req.query', req.query);
    // Get the agency ID from the request (set by auth middleware)
    const agencyId = req.agencyId;
    
    if (!agencyId) {
      return res.status(400).json({ error: 'Agency ID is required' });
    }
    
    // Check if baseSchemaId query parameter is provided
    const baseSchemaId = req.query.baseSchemaId as string | undefined;
    
    if (baseSchemaId) {
      const baseSchema = (await schemaService.listBaseSchemas()).find(schema => schema.name === baseSchemaId);
      if (!baseSchema) {
        return res.status(404).json({ error: `Base schema with Name ${baseSchemaId} not found` });
      }
      // Filter schemas by base schema ID
      const schemas = await schemaService.listSchemasByBase(agencyId, baseSchema.id);
      return res.json(schemas);
    }
    
    // If no baseSchemaId is provided, return all schemas
    const schemas = await schemaService.listSchemas(agencyId);
    res.json(schemas);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/schemas/base
 * List all base schemas
 */
router.get('/base', async (req, res, next) => {
  try {
    const schemas = await schemaService.listBaseSchemas();
    res.json(schemas);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/schemas/:id
 * Get a schema by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const schema = await schemaService.getSchema(req.params.id);
    
    if (!schema) {
      throw new NotFoundError(`Schema with ID ${req.params.id} not found`);
    }
    
    res.json(schema);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/schemas/name/:name/version/:version
 * Get a schema by name, version, and agency ID
 */
router.get('/name/:name/version/:version', async (req, res, next) => {
  try {
    const agencyId = req.agencyId;
    
    if (!agencyId) {
      return res.status(400).json({ error: 'Agency ID is required' });
    }
    
    const schema = await schemaService.getSchemaByName(
      req.params.name,
      req.params.version,
      agencyId
    );
    
    if (!schema) {
      throw new NotFoundError(`Schema with name ${req.params.name} and version ${req.params.version} not found`);
    }
    
    res.json(schema);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/schemas
 * Create a new schema
 */
router.post('/', validateRequest('createSchema'), async (req, res, next) => {
  try {
    const { name, version, agencyId, schema, baseSchemaId } = req.body;
    console.log('req.body', req.body);
    let agencyIdToUse = agencyId;
    if (!agencyId) {
      agencyIdToUse = req.agencyId;
    }
    const newSchema = await schemaService.createSchema(
      name,
      version,
      agencyIdToUse,
      schema,
      baseSchemaId
    );
    
    res.status(201).json(newSchema);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/schemas/:id
 * Update a schema
 */
router.put('/:id', validateRequest('updateSchema'), async (req, res, next) => {
  try {
    const { version, schema } = req.body;
    
    const updatedSchema = await schemaService.updateSchema(
      req.params.id,
      version,
      schema
    );
    
    if (!updatedSchema) {
      throw new NotFoundError(`Schema with ID ${req.params.id} not found`);
    }
    
    res.json(updatedSchema);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/schemas/:id/resolved
 * Get a resolved schema by ID (resolves $refs, $allOf, $anyOf)
 */
router.get('/:id/resolved', async (req, res, next) => {
  try {
    // Create a new instance of the schema resolver service for this request
    const schemaResolver = getSchemaResolverService();
    const resolvedSchema = await schemaResolver.resolveSchema(req.params.id);
    
    if (!resolvedSchema) {
      throw new NotFoundError(`Schema with ID ${req.params.id} not found`);
    }
    
    res.json(resolvedSchema);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/schemas/validate
 * Validate data against a schema
 */
router.post('/validate', validateRequest('validateAgainstSchema'), async (req, res, next) => {
  try {
    const { schemaId, data } = req.body;
    
    const result = await schemaService.validateAgainstSchema(schemaId, data);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

export default router;
