import express from 'express';
import { uiSchemaService } from '../services/ui-schema-service';
import { validateRequest } from './middleware/validation';
import { NotFoundError } from './middleware/error-handler';
import { schemaService } from '../services/schema-service';

const router = express.Router();

/**
 * GET /api/ui-schemas/schema/:schemaId
 * List all UI schemas for a schema
 */
router.get('/schema/:schemaId', async (req, res, next) => {
  try {
    const schema = await schemaService.getSchema(req.params.schemaId);
    if (!schema) {
      throw new NotFoundError(`Schema with ID ${req.params.schemaId} not found`);
    }
    const uiSchemas = [];
    uiSchemas.push(...(await uiSchemaService.listUISchemasBySchema(req.params.schemaId)));
    if (schema.baseSchemaId) {
      uiSchemas.push(...(await uiSchemaService.listUISchemasBySchema(schema.baseSchemaId)));
    }
    res.json(uiSchemas);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/ui-schemas/:id
 * Get a UI schema by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const uiSchema = await uiSchemaService.getUISchema(req.params.id);
    
    if (!uiSchema) {
      throw new NotFoundError(`UI Schema with ID ${req.params.id} not found`);
    }
    
    res.json(uiSchema);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/ui-schemas
 * Create a new UI schema
 */
router.post('/', validateRequest('createUISchema'), async (req, res, next) => {
  try {
    const { name, description, schemaId, content } = req.body;
    
    const newUISchema = await uiSchemaService.createUISchema(
      name,
      description || '',
      schemaId,
      content
    );
    
    res.status(201).json(newUISchema);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/ui-schemas/:id
 * Update a UI schema
 */
router.put('/:id', validateRequest('updateUISchema'), async (req, res, next) => {
  try {
    const { name, description, content } = req.body;
    
    const updatedUISchema = await uiSchemaService.updateUISchema(
      req.params.id,
      name,
      description || '',
      content
    );
    
    if (!updatedUISchema) {
      throw new NotFoundError(`UI Schema with ID ${req.params.id} not found`);
    }
    
    res.json(updatedUISchema);
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/ui-schemas/:id
 * Delete a UI schema
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const deleted = await uiSchemaService.deleteUISchema(req.params.id);
    
    if (!deleted) {
      throw new NotFoundError(`UI Schema with ID ${req.params.id} not found`);
    }
    
    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

export default router;
