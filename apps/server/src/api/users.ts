import express from 'express';
import { userService } from '../services/user-service';
import { validateRequest } from './middleware/validation';
import { NotFoundError } from './middleware/error-handler';

const router = express.Router();

/**
 * GET /api/users/me
 * Get the currently authenticated user
 */
router.get('/me', async (req, res, next) => {
  try {
    if (!req.stytchMemberId) {
      return res.status(401).json({ error: 'Not authenticated, or no Stytch member ID' });
    }
    
    // Try to find the user by Stytch ID
    const user = await userService.getUserByStytchId(req.stytchMemberId);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/stytch/:stytchId
 * Get a user by Stytch ID
 */
router.get('/stytch/:stytchId', async (req, res, next) => {
  try {
    const user = await userService.getUserByStytchId(req.params.stytchId);
    
    if (!user) {
      throw new NotFoundError(`User with Stytch ID ${req.params.stytchId} not found`);
    }
    
    res.json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/email/:email
 * Get a user by email
 */
router.get('/email/:email', async (req, res, next) => {
  try {
    const user = await userService.getUserByEmail(req.params.email);
    
    if (!user) {
      throw new NotFoundError(`User with email ${req.params.email} not found`);
    }
    
    res.json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/role/:role
 * Query users by role and agency ID
 */
router.get('/role/:role', async (req, res, next) => {
  try {
    const agencyId = req.agencyId;
    
    if (!agencyId) {
      return res.status(400).json({ error: 'Agency ID is required' });
    }
    
    const users = await userService.queryUsersByType(
      req.params.role,
      agencyId
    );
    
    res.json(users);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/:id
 * Get a user by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const user = await userService.readUser(req.params.id);
    
    if (!user) {
      throw new NotFoundError(`User with ID ${req.params.id} not found`);
    }
    
    res.json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/users
 * Create a new user
 */
router.post('/', validateRequest('createUser'), async (req, res, next) => {
  try {
    const { schemaId, agencyId, stytchId, ...userData } = req.body;

    const newUser = await userService.createUser(
      userData,
      schemaId,
      agencyId,
      stytchId
    );
    
    res.status(201).json(newUser);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/users/:id
 * Update a user
 */
router.put('/:id', validateRequest('updateUser'), async (req, res, next) => {
  try {
    const { schemaId, stytchId, ...userData } = req.body;
    
    const updatedUser = await userService.updateUser(
      req.params.id,
      userData,
      schemaId,
      stytchId
    );
    
    if (!updatedUser) {
      throw new NotFoundError(`User with ID ${req.params.id} not found`);
    }
    
    res.json(updatedUser);
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/users/:id
 * Delete a user
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const success = await userService.deleteUser(req.params.id);
    
    if (!success) {
      throw new NotFoundError(`User with ID ${req.params.id} not found`);
    }
    
    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

export default router;
