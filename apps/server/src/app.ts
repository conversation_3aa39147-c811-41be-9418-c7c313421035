import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import { config } from "./config";
import apiRoutes from "./api";
import adminRoutes from "./api/admin";
import { initializeResourceEventSystem } from "./services/resource-events";
import { initializeServer } from "./init";
import { Server } from "http";

// Load environment variables from .env file

const run = async () => {
  await initializeServer({ migrate: true, configureStytch: true });

  // Create Express app
  const app = express();
  const port = config.port;

  // Middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(
    cors({
      origin: function (origin, callback) {
        // Allow all origins for development
        callback(null, true);

        // // Allow requests with no origin (like mobile apps or curl requests)
        // if (!origin) return callback(null, true);

        // if (config.cors.allowedOrigins.indexOf(origin) !== -1) {
        //   callback(null, origin);
        // } else {
        //   callback(null, config.cors.allowedOrigins[0]); // Allow the first origin as a fallback
        // }
      },
      credentials: true,
    }),
  );
  app.use(cookieParser());

  // API routes
  app.use("/api/admin", adminRoutes); // Mount admin routes first
  app.use("/api", apiRoutes);

  // Health check endpoint
  app.get("/health", (req, res) => {
    res.json({ status: "ok" });
  });

  // Initialize resource event system
  initializeResourceEventSystem();

  // Start Express server and store reference
  const server: Server = app.listen(port, () => {
    console.log(`Server listening on port ${port}`);
  });

  // Handle server errors
  server.on("error", (error: any) => {
    if (error.code === "EADDRINUSE") {
      console.log(`Port ${port} is already in use. Retrying in 1 second...`);
      setTimeout(() => {
        server.close();
        server.listen(port);
      }, 1000);
    } else {
      console.error("Server error:", error);
    }
  });

  // Graceful shutdown handling
  const gracefulShutdown = (signal: string) => {
    console.log(`\nReceived ${signal}. Gracefully shutting down...`);

    server.close((err) => {
      if (err) {
        console.error("Error during server shutdown:", err);
        process.exit(1);
      }

      console.log("Server closed successfully");
      process.exit(0);
    });

    // Force close server after 10 seconds
    setTimeout(() => {
      console.error(
        "Could not close connections in time, forcefully shutting down",
      );
      process.exit(1);
    }, 10000);
  };

  // Listen for termination signals
  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));

  // Handle uncaught exceptions
  process.on("uncaughtException", (error) => {
    console.error("Uncaught Exception:", error);
    gracefulShutdown("uncaughtException");
  });

  process.on("unhandledRejection", (reason, promise) => {
    console.error("Unhandled Rejection at:", promise, "reason:", reason);
    gracefulShutdown("unhandledRejection");
  });
};

run();
