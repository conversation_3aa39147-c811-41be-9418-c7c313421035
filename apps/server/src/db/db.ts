import { Pool } from "pg";
import { config } from "../config";
import { runMigrations } from "./migrations/runMigrations";

let pool: Pool;

const appPool = async (migrate = true) => {
  // Create a connection pool
  pool = new Pool(config.db);

  // Test the connection
  pool.on("connect", () => {
    console.log("Connected to PostgreSQL database");
  });

  pool.on("error", (err) => {
    console.error("Unexpected error on idle client", err);
    process.exit(-1);
  });

  // Run database migrations
  if (migrate) {
    try {
      await runMigrations(pool);
    } catch (error) {
      console.error("Failed to run migrations:", error);
      throw error;
    }
  }

  return pool;
};

export default appPool;
