import { Pool } from 'pg';
import { 
  SchemaRepository, 
  ResourceRepository, 
  UserRepository,
  AgencyRepository,
  UISchemaRepository
} from './interfaces';
import { PostgresSchemaRepository } from './postgres/schema-repository';
import { PostgresResourceRepository } from './postgres/resource-repository';
import { PostgresUserRepository } from './postgres/user-repository';
import { PostgresAgencyRepository } from './postgres/agency-repository';
import { PostgresUISchemaRepository } from './postgres/ui-schema-repository';
import { InMemorySchemaRepository } from './memory/schema-repository';
import { InMemoryResourceRepository } from './memory/resource-repository';
import { InMemoryUserRepository } from './memory/user-repository';
import { InMemoryAgencyRepository } from './memory/agency-repository';
import { MemoryUISchemaRepository } from './memory/ui-schema-repository';

export enum RepositoryType {
  POSTGRES = 'postgres',
  MEMORY = 'memory'
}

export class RepositoryFactory {
  private static instance: RepositoryFactory;
  private type: RepositoryType;
  private pool?: Pool;
  
  private schemaRepository?: SchemaRepository;
  private resourceRepository?: ResourceRepository;
  private userRepository?: UserRepository;
  private agencyRepository?: AgencyRepository;
  private uiSchemaRepository?: UISchemaRepository;
  
  private constructor(type: RepositoryType, pool?: Pool) {
    this.type = type;
    this.pool = pool;
  }
  
  public static getInstance(type: RepositoryType = RepositoryType.POSTGRES, pool?: Pool): RepositoryFactory {
    if (!RepositoryFactory.instance) {
      RepositoryFactory.instance = new RepositoryFactory(type, pool);
    }
    return RepositoryFactory.instance;
  }
  
  public getSchemaRepository(): SchemaRepository {
    if (!this.schemaRepository) {
      if (this.type === RepositoryType.POSTGRES) {
        if (!this.pool) {
          throw new Error('Pool is required for Postgres repositories');
        }
        this.schemaRepository = new PostgresSchemaRepository(this.pool);
      } else if (this.type === RepositoryType.MEMORY) {
        this.schemaRepository = new InMemorySchemaRepository();
      }
    }
    return this.schemaRepository;
  }
  
  public getResourceRepository(): ResourceRepository {
    if (!this.resourceRepository) {
      if (this.type === RepositoryType.POSTGRES) {
        if (!this.pool) {
          throw new Error('Pool is required for Postgres repositories');
        }
        this.resourceRepository = new PostgresResourceRepository(this.pool);
      } else if (this.type === RepositoryType.MEMORY) {
        this.resourceRepository = new InMemoryResourceRepository();
      }
    }
    return this.resourceRepository;
  }
  
  public getUserRepository(): UserRepository {
    if (!this.userRepository) {
      if (this.type === RepositoryType.POSTGRES) {
        if (!this.pool) {
          throw new Error('Pool is required for Postgres repositories');
        }
        this.userRepository = new PostgresUserRepository(this.pool);
      } else if (this.type === RepositoryType.MEMORY) {
        this.userRepository = new InMemoryUserRepository();
      }
    }
    return this.userRepository;
  }
  
  public getAgencyRepository(): AgencyRepository {
    if (!this.agencyRepository) {
      if (this.type === RepositoryType.POSTGRES) {
        if (!this.pool) {
          throw new Error('Pool is required for Postgres repositories');
        }
        this.agencyRepository = new PostgresAgencyRepository(this.pool);
      } else if (this.type === RepositoryType.MEMORY) {
        this.agencyRepository = new InMemoryAgencyRepository();
      }
    }
    return this.agencyRepository;
  }
  
  public getUISchemaRepository(): UISchemaRepository {
    if (!this.uiSchemaRepository) {
      if (this.type === RepositoryType.POSTGRES) {
        if (!this.pool) {
          throw new Error('Pool is required for Postgres repositories');
        }
        this.uiSchemaRepository = new PostgresUISchemaRepository(this.pool);
      } else if (this.type === RepositoryType.MEMORY) {
        this.uiSchemaRepository = new MemoryUISchemaRepository();
      }
    }
    return this.uiSchemaRepository;
  }
  
  public setType(type: RepositoryType): void {
    this.type = type;
    this.schemaRepository = undefined;
    this.resourceRepository = undefined;
    this.userRepository = undefined;
    this.agencyRepository = undefined;
    this.uiSchemaRepository = undefined;
  }
}
