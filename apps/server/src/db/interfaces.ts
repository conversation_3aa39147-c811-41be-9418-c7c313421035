import { BaseResource } from "../services/resource-events";

// Schema metadata
export interface SchemaMetadata {
  id: string;
  name: string;
  version: string;
  agencyId: string;
  baseSchemaId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// JSON Schema type (simplified)
export interface JSONSchema {
  $schema?: string;
  $id?: string;
  title?: string;
  description?: string;
  type?: string | string[];
  properties?: Record<string, JSONSchema>;
  required?: string[];
  $ref?: string;
  allOf?: JSONSchema[];
  anyOf?: JSONSchema[];
  oneOf?: JSONSchema[];
  [key: string]: unknown;
}

// UI Schema type (for customizing display)
export interface UISchemaContent {
  [key: string]: unknown;
}

// UI Schema metadata
export interface UISchemaMetadata {
  id: string;
  name: string;
  description?: string;
  schemaId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Complete UI Schema with metadata and content
export interface UISchema extends UISchemaMetadata {
  content: UISchemaContent;
}

// Complete schema with metadata and JSON Schema
export interface Schema extends SchemaMetadata {
  schema: JSONSchema;
  uiSchemas?: UISchema[];
}

// Resource fields that are required for all resources
export interface ResourceFields {
  id?: string;
  resourceType: string;
  schemaId: string;
  agencyId: string;
  createdAt: string;
  updatedAt: string;
}

// Repository interfaces
export interface SchemaRepository {
  createSchema(
    name: string,
    version: string,
    agencyId: string,
    schema: JSONSchema,
    baseSchemaId?: string,
  ): Promise<Schema>;
  updateSchema(
    id: string,
    version: string,
    schema: JSONSchema,
  ): Promise<Schema | null>;
  getSchema(id: string): Promise<Schema | null>;
  getSchemaByName(
    name: string,
    version: string,
    agencyId: string,
  ): Promise<Schema | null>;
  listSchemas(agencyId: string): Promise<Schema[]>;
  listAllSchemas(): Promise<Schema[]>;
  listBaseSchemas(): Promise<Schema[]>;
}

export interface UISchemaRepository {
  createUISchema(
    name: string,
    description: string,
    schemaId: string,
    content: UISchemaContent,
  ): Promise<UISchema>;
  updateUISchema(
    id: string,
    name: string,
    description: string,
    content: UISchemaContent,
  ): Promise<UISchema | null>;
  getUISchema(id: string): Promise<UISchema | null>;
  listUISchemasBySchema(schemaId: string): Promise<UISchema[]>;
  deleteUISchema(id: string): Promise<boolean>;
}

export interface ResourceRepository {
  createResource<T extends BaseResource>(
    resource: T,
    schemaId: string,
    agencyId: string,
  ): Promise<T>;
  readResource<T extends BaseResource>(id: string): Promise<T | null>;
  updateResource<T extends BaseResource>(
    id: string,
    resource: T,
    schemaId: string,
  ): Promise<T | null>;
  deleteResource(id: string): Promise<boolean>;
  queryResourcesByType<T extends Record<string, unknown>>(
    resourceType: string,
    agencyId: string,
    jsonPathFilters?: Record<string, unknown>,
    fields?: string[],
    limit?: number,
  ): Promise<T[]>;
}

export interface UserRepository {
  createUser<T extends Record<string, unknown> & ResourceFields>(
    user: T,
    schemaId: string,
    agencyId: string,
    stytchId?: string,
  ): Promise<T>;
  readUser<T extends Record<string, unknown>>(id: string): Promise<T | null>;
  updateUser<T extends Record<string, unknown> & ResourceFields>(
    id: string,
    user: T,
    schemaId: string,
    stytchId?: string,
  ): Promise<T | null>;
  deleteUser(id: string): Promise<boolean>;
  queryUsersByType<T extends Record<string, unknown>>(
    role: string,
    agencyId: string,
  ): Promise<T[]>;
  getUserByEmail<T extends Record<string, unknown>>(
    email: string,
  ): Promise<T | null>;
  getUserByStytchId<T extends Record<string, unknown>>(
    stytchId: string,
  ): Promise<T | null>;
}

export interface AgencyRepository {
  createAgency(
    name: string,
    stytchOrganizationId?: string,
  ): Promise<{ id: string; name: string; stytchOrganizationId?: string }>;
  getAgency(id: string): Promise<{
    id: string;
    name: string;
    stytchOrganizationId?: string;
  } | null>;
  listAgencies(): Promise<
    { id: string; name: string; stytchOrganizationId?: string }[]
  >;
  updateAgency(
    id: string,
    name: string,
    stytchOrganizationId?: string,
  ): Promise<{
    id: string;
    name: string;
    stytchOrganizationId?: string;
  } | null>;
  deleteAgency(id: string): Promise<boolean>;
}
