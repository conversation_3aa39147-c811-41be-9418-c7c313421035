import { uuidv7 } from 'uuidv7';
import { AgencyRepository } from '../interfaces';

interface Agency {
  id: string;
  name: string;
  stytchOrganizationId?: string;
}

export class InMemoryAgencyRepository implements AgencyRepository {
  private agencies: Agency[] = [];

  async createAgency(name: string, stytchOrganizationId?: string): Promise<{ id: string; name: string; stytchOrganizationId?: string }> {
    const id = uuidv7();  
    const agency = { id, name, stytchOrganizationId };

    this.agencies.push(agency);

    return agency;
  }

  async getAgency(id: string): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const agency = this.agencies.find(a => a.id === id);
    return agency || null;
  }

  async listAgencies(): Promise<{ id: string; name: string; stytchOrganizationId?: string }[]> {
    return [...this.agencies].sort((a, b) => a.name.localeCompare(b.name));
  }

  async updateAgency(id: string, name: string, stytchOrganizationId?: string): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const index = this.agencies.findIndex(a => a.id === id);

    if (index === -1) {
      return null;
    }

    // Preserve existing stytchOrganizationId if not provided
    const existingStytchOrgId = this.agencies[index].stytchOrganizationId;
    const updatedStytchOrgId = stytchOrganizationId !== undefined ? stytchOrganizationId : existingStytchOrgId;

    const updatedAgency = { id, name, stytchOrganizationId: updatedStytchOrgId };
    this.agencies[index] = updatedAgency;

    return updatedAgency;
  }

  async deleteAgency(id: string): Promise<boolean> {
    const initialLength = this.agencies.length;
    this.agencies = this.agencies.filter(a => a.id !== id);
    return initialLength > this.agencies.length;
  }
}
