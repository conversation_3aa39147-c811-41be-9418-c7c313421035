import { uuidv7 } from "uuidv7";
import { ResourceRepository, ResourceFields } from "../interfaces";
import { BaseResource } from "../../services/resource-events";

interface ResourceRecord {
  id: string;
  resourceType: string;
  schemaId: string;
  agencyId: string;
  content: Record<string, unknown>;
}

export class InMemoryResourceRepository implements ResourceRepository {
  private resources: ResourceRecord[] = [];

  async createResource<T extends Record<string, unknown> & ResourceFields>(
    resource: T,
    schemaId: string,
    agencyId: string,
  ): Promise<T> {
    // Ensure the resource has an ID
    if (!resource.id) {
      resource.id = uuidv7();
    }

    this.resources.push({
      id: resource.id,
      resourceType: resource.resourceType,
      schemaId,
      agencyId,
      content: resource,
    });

    return resource;
  }

  async readResource<T extends Record<string, unknown>>(
    id: string,
  ): Promise<T | null> {
    const record = this.resources.find((r) => r.id === id);
    return record ? (record.content as T) : null;
  }

  async updateResource<T extends BaseResource>(
    id: string,
    resource: T,
    schemaId: string,
  ): Promise<T | null> {
    const index = this.resources.findIndex((r) => r.id === id);

    if (index === -1) {
      return null;
    }

    const agencyId = this.resources[index].agencyId;

    this.resources[index] = {
      id,
      resourceType: resource.resourceType,
      schemaId,
      agencyId,
      content: resource,
    };

    return resource;
  }

  async deleteResource(id: string): Promise<boolean> {
    const initialLength = this.resources.length;
    this.resources = this.resources.filter((r) => r.id !== id);
    return initialLength > this.resources.length;
  }

  async queryResourcesByType<T extends Record<string, unknown>>(
    resourceType: string,
    agencyId: string,
  ): Promise<T[]> {
    return this.resources
      .filter((r) => r.resourceType === resourceType && r.agencyId === agencyId)
      .map((r) => r.content as T);
  }
}
