import { uuidv7 } from 'uuidv7';
import { Schema, JSONSchema, SchemaRepository } from '../interfaces';

export class InMemorySchemaRepository implements SchemaRepository {
  private schemas: Schema[] = [];

  async createSchema(name: string, version: string, agencyId: string, schema: JSONSchema, baseSchemaId?: string): Promise<Schema> {
    const id = uuidv7();
    const now = new Date();

    const newSchema: Schema = {
      id,
      name,
      version,
      agencyId,
      baseSchemaId,
      schema,
      createdAt: now,
      updatedAt: now
    };

    this.schemas.push(newSchema);

    return newSchema;
  }

  async updateSchema(id: string, version: string, schema: JSONSchema): Promise<Schema | null> {
    const index = this.schemas.findIndex(s => s.id === id);

    if (index === -1) {
      return null;
    }

    const updatedSchema = {
      ...this.schemas[index],
      version,
      schema,
      updatedAt: new Date()
    };

    this.schemas[index] = updatedSchema;

    return updatedSchema;
  }

  async getSchema(id: string): Promise<Schema | null> {
    const schema = this.schemas.find(s => s.id === id);
    return schema || null;
  }

  async getSchemaByName(name: string, version: string, agencyId: string): Promise<Schema | null> {
    const schema = this.schemas.find(
      s => s.name === name && s.version === version && s.agencyId === agencyId
    );
    return schema || null;
  }

  async listSchemas(agencyId: string): Promise<Schema[]> {
    return this.schemas
      .filter(s => s.agencyId === agencyId)
      .sort((a, b) => {
        if (a.name !== b.name) {
          return a.name.localeCompare(b.name);
        }
        return b.version.localeCompare(a.version); // Descending version order
      });
  }

  async listBaseSchemas(): Promise<Schema[]> {
    return this.schemas
      .filter(s => !s.baseSchemaId)
      .sort((a, b) => {
        if (a.name !== b.name) {
          return a.name.localeCompare(b.name);
        }
        return b.version.localeCompare(a.version); // Descending version order
      });
  }

  async listAllSchemas(): Promise<Schema[]> {
    return this.schemas
      .sort((a, b) => {
        if (a.name !== b.name) {
          return a.name.localeCompare(b.name);
        }
        return b.version.localeCompare(a.version); // Descending version order
      });
  }
}
