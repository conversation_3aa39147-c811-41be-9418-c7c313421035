import { uuidv7 } from 'uuidv7';
import { UISchema, UISchemaContent, UISchemaRepository } from '../interfaces';

export class MemoryUISchemaRepository implements UISchemaRepository {
  private uiSchemas: UISchema[] = [];

  async createUISchema(name: string, description: string, schemaId: string, content: UISchemaContent): Promise<UISchema> {
    const id = uuidv7();
    const now = new Date();

    const uiSchema: UISchema = {
      id,
      name,
      description,
      schemaId,
      content,
      createdAt: now,
      updatedAt: now
    };

    this.uiSchemas.push(uiSchema);

    return uiSchema;
  }

  async updateUISchema(id: string, name: string, description: string, content: UISchemaContent): Promise<UISchema | null> {
    const index = this.uiSchemas.findIndex(uiSchema => uiSchema.id === id);

    if (index === -1) {
      return null;
    }

    const uiSchema = this.uiSchemas[index];
    const now = new Date();

    const updatedUISchema: UISchema = {
      ...uiSchema,
      name,
      description,
      content,
      updatedAt: now
    };

    this.uiSchemas[index] = updatedUISchema;

    return updatedUISchema;
  }

  async getUISchema(id: string): Promise<UISchema | null> {
    const uiSchema = this.uiSchemas.find(uiSchema => uiSchema.id === id);

    if (!uiSchema) {
      return null;
    }

    return { ...uiSchema };
  }

  async listUISchemasBySchema(schemaId: string): Promise<UISchema[]> {
    return this.uiSchemas
      .filter(uiSchema => uiSchema.schemaId === schemaId)
      .map(uiSchema => ({ ...uiSchema }))
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  async deleteUISchema(id: string): Promise<boolean> {
    const index = this.uiSchemas.findIndex(uiSchema => uiSchema.id === id);

    if (index === -1) {
      return false;
    }

    this.uiSchemas.splice(index, 1);

    return true;
  }
}
