import { uuidv7 } from 'uuidv7';
import { UserRepository, ResourceFields } from '../interfaces';

interface UserRecord {
  id: string;
  resourceType: string;
  schemaId: string;
  agencyId: string;
  stytchId?: string;
  content: Record<string, unknown>;
}

export class InMemoryUserRepository implements UserRepository {
  private users: UserRecord[] = [];

  async createUser<T extends Record<string, unknown> & ResourceFields>(user: T, schemaId: string, agencyId: string, stytchId?: string): Promise<T> {
    // Ensure the user has an ID
    if (!user.id) {
      user.id = uuidv7();
    }

    // Ensure agencyId is included in the content
    const contentWithAgencyId = {
      ...user,
      agencyId
    };

    this.users.push({
      id: user.id,
      resourceType: user.resourceType,
      schemaId,
      agencyId,
      stytchId,
      content: contentWithAgencyId
    });

    return user;
  }

  async readUser<T extends Record<string, unknown>>(id: string): Promise<T | null> {
    const record = this.users.find(u => u.id === id);
    return record ? record.content as T : null;
  }

  async updateUser<T extends Record<string, unknown> & ResourceFields>(id: string, user: T, schemaId: string, stytchId?: string): Promise<T | null> {
    const index = this.users.findIndex(u => u.id === id);

    if (index === -1) {
      return null;
    }

    const agencyId = this.users[index].agencyId;
    // Preserve existing stytchId if not provided
    const existingStytchId = this.users[index].stytchId;
    const updatedStytchId = stytchId !== undefined ? stytchId : existingStytchId;

    // Ensure agencyId is included in the content
    const contentWithAgencyId = {
      ...user,
      agencyId
    };

    this.users[index] = {
      id,
      resourceType: user.resourceType,
      schemaId,
      agencyId,
      stytchId: updatedStytchId,
      content: contentWithAgencyId
    };

    return user;
  }

  async deleteUser(id: string): Promise<boolean> {
    const initialLength = this.users.length;
    this.users = this.users.filter(u => u.id !== id);
    return initialLength > this.users.length;
  }

  async queryUsersByType<T extends Record<string, unknown>>(role: string, agencyId: string): Promise<T[]> {
    // If role is 'all', return all users for the agency
    if (role === 'all') {
      return this.users
        .filter(u => u.agencyId === agencyId)
        .map(u => u.content as T);
    }

    // Otherwise, filter by role in the content JSON
    return this.users
      .filter(u => {
        const content = u.content as Record<string, unknown>;
        return content.role === role && u.agencyId === agencyId;
      })
      .map(u => u.content as T);
  }

  async getUserByEmail<T extends Record<string, unknown>>(email: string): Promise<T | null> {
    const record = this.users.find(u => {
      const content = u.content as Record<string, unknown>;
      return content.email === email;
    });

    return record ? record.content as T : null;
  }

  async getUserByStytchId<T extends Record<string, unknown>>(stytchId: string): Promise<T | null> {
    const record = this.users.find(u => u.stytchId === stytchId);
    return record ? record.content as T : null;
  }
}
