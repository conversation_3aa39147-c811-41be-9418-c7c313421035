--
-- PostgreSQL database dump
--

-- Dumped from database version 16.6
-- Dumped by pg_dump version 16.0

-- Started on 2025-04-29 11:20:50 EDT

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 4362 (class 1262 OID 16456)
-- Name: hospice_os; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE hospice_os WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.UTF-8';


ALTER DATABASE hospice_os OWNER TO postgres;

\connect hospice_os

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 2 (class 3079 OID 16457)
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- TOC entry 4363 (class 0 OID 0)
-- Dependencies: 2
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


SET default_tablespace = '';

SET default_table_access_method = heap;


--
-- Name: migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.migrations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    applied_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT migrations_name_check CHECK (((name)::text <> ''::text))
);


ALTER TABLE public.migrations OWNER TO postgres;

--
-- TOC entry 216 (class 1259 OID 16468)
-- Name: agencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.agencies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    stytch_organization_id character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT agencies_name_check CHECK (((name)::text <> ''::text))
);


ALTER TABLE public.agencies OWNER TO postgres;

--
-- TOC entry 217 (class 1259 OID 16477)
-- Name: resources; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.resources (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    resource_type character varying(255) NOT NULL,
    schema_id uuid NOT NULL,
    agency_id uuid NOT NULL,
    content jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    internal_status character varying(255),
    CONSTRAINT resources_resource_type_check CHECK (((resource_type)::text <> ''::text))
);


ALTER TABLE public.resources OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 16486)
-- Name: schemas; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.schemas (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    version character varying(50) NOT NULL,
    agency_id uuid NOT NULL,
    base_schema_id uuid,
    schema jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT schemas_name_check CHECK (((name)::text <> ''::text))
);


ALTER TABLE public.schemas OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 16495)
-- Name: ui_schemas; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ui_schemas (
    id uuid NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    schema_id uuid NOT NULL,
    content jsonb NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.ui_schemas OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 16500)
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    resource_type character varying(255) NOT NULL,
    schema_id uuid NOT NULL,
    agency_id uuid NOT NULL,
    stytch_id character varying(255),
    content jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT users_resource_type_check CHECK (((resource_type)::text <> ''::text))
);


ALTER TABLE public.users OWNER TO postgres;

--
-- TOC entry 4352 (class 0 OID 16468)
-- Dependencies: 216
-- Data for Name: agencies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.agencies (id, name, stytch_organization_id, created_at, updated_at) FROM stdin;
857d2613-f37f-4c83-8e7e-cb7c906754f3	System	organization-test-c34549c7-c885-48bb-85a1-0e6428cda6ef	2025-04-18 14:35:44.626031+00	2025-04-18 14:50:01.329+00
bd6e787b-b665-4f7b-9b32-1df1797620cd	Tallio Test	organization-test-197fc644-a18f-45bb-aa60-f584d6e1dc01	2025-04-19 16:25:13.343+00	2025-04-19 16:25:13.343+00
\.


--
-- TOC entry 4353 (class 0 OID 16477)
-- Dependencies: 217
-- Data for Name: resources; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.resources (id, resource_type, schema_id, agency_id, content, created_at, updated_at, internal_status) FROM stdin;
a64ac039-764d-4534-8238-563c3f375100	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "a64ac039-764d-4534-8238-563c3f375100", "tags": ["visit_note", "demographic_update"], "status": "pending_review", "content": "Patient is reporting pain in the left knee, possibly due to prior injury.", "patient": {"id": "85f15dc9-3572-4fbd-9753-753b29f4b3d3", "resourceType": "Patient"}, "summary": "visit note and update to the patient's address", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "createdAt": "2022-05-01T12:34:56Z", "responses": [{"sentBy": "tallio internal", "content": "Acknowledged. Please proceed with tests."}, {"sentBy": "end user", "content": "Noted. Patient has been informed."}], "noteCreated": {"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}, "noteTemplate": {"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}, "resourceType": "Request", "fileAttachments": ["report_1.pdf", "image_2.jpg"], "audioAttachments": ["audio_note_1.mp3", "audio_note_2.wav"]}	2025-04-21 15:11:32.898596+00	2025-04-21 15:11:32.898596+00	\N
aea28b4a-b37e-4a0f-8f19-33ce8cce93ee	Note	d483d5f0-627f-4459-a386-f8c78aa77f88	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "title": "Routine Visit", "status": "pending_review", "summary": "Routine check-up with wound dressings and medication administration", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "authorId": "f2a1e3b7-6e8e-43b5-a8a6-5b4e5b5ec7b2", "noteType": "NursingVisit", "createdAt": "2022-04-27T10:00:00Z", "patientId": "a1b2c3d4-e5f6-7g8h-9i10-j11k12l13m14", "updatedAt": "2022-04-27T11:00:00Z", "woundCare": "Dressings changed, no sign of infection", "vitalSigns": {"heartRate": 78, "temperature": 36.5, "bloodPressure": "120/80", "respiratoryRate": 16}, "resourceType": "Note", "skinAssessment": "No signs of new pressure sores", "educationProvided": "Informed patient about medication side effects and wound care", "medicationsAdministered": [{"dose": "500mg", "medicationName": "Paracetamol", "timeAdministered": "2022-04-27T10:30:00Z"}, {"dose": "10mg", "medicationName": "Lisinoprils", "timeAdministered": "2022-04-27T10:35:00Z"}]}	2025-04-19 18:15:38.838803+00	2025-04-19 18:15:38.838803+00	\N
94ffdebd-1ebc-4ce6-b0f6-6dce1c826526	PlanOfCare	ac4a9a07-a690-4eaa-b650-8b42658503e4	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "94ffdebd-1ebc-4ce6-b0f6-6dce1c826526", "status": "pending_review", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "goalsOfCare": "Manage symptoms and improve quality of life", "resourceType": "PlanOfCare", "interventions": "Administer nitroglycerin for chest pain, monitor vital signs, and provide oxygen therapy.", "chiefComplaint": "Severe chest pain", "servicesNeeded": ["Cardiology Consultation", "Dietary Consultation"], "visitFrequency": "Twice a week", "equipmentNeeded": ["Wheelchair", "Oxygen Concentrator"], "symptomsPresent": {"pain": true, "dyspnea": true, "fatigue": true, "painLevel": 8}, "functionalStatus": {"percentage": 60, "description": "Able to perform most activities of daily living, but needs assistance with heavy lifting and strenuous activities"}, "primaryCaregiver": "John Doe", "pastMedicalHistory": ["Hypertension", "Type 2 Diabetes"], "historyOfPresentIllness": "Patient experienced sudden severe chest pain while at rest, with onset 3 hours prior to presentation. No history of similar episodes."}	2025-04-20 19:05:14.769715+00	2025-04-20 19:05:14.769715+00	\N
24f2f272-03a4-460c-bb40-381a0c3f1689	Patient	6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "24f2f272-03a4-460c-bb40-381a0c3f1689", "status": "Active", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "Ross", "schemaId": "6cb5c6e8-22c2-4df5-beb4-5cf2509e2673", "firstName": "Bob", "resourceType": "Patient", "admissionDate": "2025-04-25"}	2025-04-25 16:14:00.009534+00	2025-04-25 16:14:00.009534+00	\N
1cf92a48-fd73-4882-82ed-58ae290a1a8e	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "1cf92a48-fd73-4882-82ed-58ae290a1a8e", "tags": ["new_patient"], "patient": {"id": "24f2f272-03a4-460c-bb40-381a0c3f1689", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-25T16:14:01.469Z", "resourceType": "Request", "fileAttachments": ["uploads/24f2f272-03a4-460c-bb40-381a0c3f1689/644e62b7-b5b9-427b-a5cb-08f200803e8d-Tallio W9.pdf"]}	2025-04-25 16:14:01.532481+00	2025-04-25 16:14:01.532481+00	\N
56bf3a00-ae6f-4de6-b4a1-fbcf5e9ad852	Patient	6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "56bf3a00-ae6f-4de6-b4a1-fbcf5e9ad852", "status": "Active", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "Billybob", "schemaId": "6cb5c6e8-22c2-4df5-beb4-5cf2509e2673", "firstName": "Billy", "resourceType": "Patient", "admissionDate": "2025-04-25"}	2025-04-25 16:20:33.077742+00	2025-04-25 16:20:33.077742+00	\N
1213bc33-1bd1-41a9-b72f-d0a64ae1a96d	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "1213bc33-1bd1-41a9-b72f-d0a64ae1a96d", "tags": ["new_patient"], "patient": {"id": "56bf3a00-ae6f-4de6-b4a1-fbcf5e9ad852", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-25T16:20:33.437Z", "resourceType": "Request", "fileAttachments": []}	2025-04-25 16:20:33.48972+00	2025-04-25 16:20:33.48972+00	\N
1291b7aa-520c-476d-b0b6-d1b9abb09929	Patient	6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "1291b7aa-520c-476d-b0b6-d1b9abb09929", "status": "Active", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "Jobs", "schemaId": "6cb5c6e8-22c2-4df5-beb4-5cf2509e2673", "firstName": "Steve", "resourceType": "Patient", "admissionDate": "2025-04-25"}	2025-04-25 16:23:32.260952+00	2025-04-25 16:23:32.260952+00	\N
6647f031-9336-4bc6-b7e6-79744d4d6a01	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "6647f031-9336-4bc6-b7e6-79744d4d6a01", "tags": ["new_patient"], "patient": {"id": "1291b7aa-520c-476d-b0b6-d1b9abb09929", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-25T16:23:32.642Z", "resourceType": "Request", "fileAttachments": []}	2025-04-25 16:23:32.696839+00	2025-04-25 16:23:32.696839+00	\N
70d22ff3-cb37-45e5-9887-08c01be6eaf7	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "87f15dc9-3572-4fbd-9753-753b29f4b3d3", "user": {"id": "0cc674da-0472-4bc5-96bb-6d594e045c03", "resourceType": "User"}, "email": "<EMAIL>", "gender": "Female", "orders": [], "status": "pending_review", "address": "123 Main Street, Anytown, Anywhere, 12345", "nextIDG": "2025-04-29T15:00", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "Doe", "allergies": ["Nuts", "Shellfish"], "firstName": "Jane", "insurance": [{"number": "INS-123456", "provider": "Insurance Co."}], "planOfCare": {"id": "94ffdebd-1ebc-4ce6-b0f6-6dce1c826526", "resourceType": "PlanOfCare"}, "requestRef": {"id": "a64ac039-764d-4534-8238-563c3f375100", "resourceType": "Request"}, "visitNotes": [{"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}, {"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}], "dateOfBirth": "1990-01-01", "medications": [{"name": "Drug A", "route": "Oral", "dosage": "500mg", "frequency": "Once daily", "indication": "Condition B", "resourceType": "Medication"}], "phoneNumber": "+1-************", "resourceType": "Patient", "admissionDate": "2022-01-01", "agencyActions": [{"title": "Review Patient Admission Data", "content": "", "priority": "high", "completed": false, "relatedTo": {"id": "", "resourceType": null}, "actionType": "base"}, {"title": "Missing data", "content": "", "priority": "high", "completed": false, "actionType": "more_data", "requestRef": {"id": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "resourceType": "Request"}}], "benefitPeriod": "1st", "timeLineItems": [{"tags": ["Medications"], "title": "Pain medication change", "madeBy": {"date": "2025-04-23T00:00", "name": "Mikel"}, "summary": "Small increase of med A and B", "approvedBy": {"date": "2025-04-23T00:00", "name": "Mikel"}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Medications", "changes": [{"field": "Med A", "newValue": "20 mg a day", "oldValue": "10 mg a day"}, {"field": "Med B", "newValue": "100 IU Intramuscular EOD", "oldValue": "50 IU Intramuscular BIW"}]}]}, {"tags": ["Demographics"], "title": "Name misspelling", "madeBy": {"date": "2025-04-24T00:00", "name": "Mikel"}, "summary": "Demographics: Name misspelling ", "approvedBy": {"date": "2025-04-24T12:00", "name": "Mikel"}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Demographics", "changes": [{"field": "First Name", "newValue": "Daniel", "oldValue": "Daniell"}]}]}], "primaryDiagnosis": "Condition A", "complianceTimeline": {"timeLineItems": [{"started": "2025-04-14T00:15", "blockers": [], "completed": "2025-04-24T00:15", "description": "Certification of Terminal Illness", "displayName": "CTI"}, {"started": "2025-04-24T00:45", "blockers": [], "completed": "2025-04-24T00:45", "description": "", "displayName": "NOE"}, {"started": "", "blockers": [], "completed": "", "description": "", "displayName": "Admission Visit"}, {"started": "", "blockers": [], "completed": "", "description": "2025-04-29", "displayName": "First IDG"}]}, "secondaryDiagnoses": ["Condition B", "Condition C"], "medicalRecordNumber": "MRN-123456", "emergencyContactName": "Jane Doe", "emergencyContactPhone": "******-555-5556"}	2025-04-28 14:14:20.302343+00	2025-04-28 14:14:20.302343+00	\N
c59a8972-5d85-4d65-ac45-8cfcf7d4770d	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "c59a8972-5d85-4d65-ac45-8cfcf7d4770d", "tags": ["file_upload"], "patient": {"id": "87f15dc9-3572-4fbd-9753-753b29f4b3d3", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-28T14:23:19.448Z", "resourceType": "Request", "parentRequest": {"id": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "resourceType": "Request"}, "fileAttachments": ["uploads/87f15dc9-3572-4fbd-9753-753b29f4b3d3/1afce6c8-2203-4313-829f-a709b8a92d21-TinyMCE Document.pdf"]}	2025-04-28 14:23:19.502214+00	2025-04-28 14:23:19.502214+00	\N
27832fc9-f108-40e6-8b9c-4539c226adc9	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "27832fc9-f108-40e6-8b9c-4539c226adc9", "tags": ["file_upload"], "patient": {"id": "87f15dc9-3572-4fbd-9753-753b29f4b3d3", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-28T14:32:34.404Z", "resourceType": "Request", "parentRequest": {"id": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "resourceType": "Request"}, "fileAttachments": ["uploads/87f15dc9-3572-4fbd-9753-753b29f4b3d3/6a2f07b4-92b5-4f77-9a85-3eccbaa1a092-TinyMCE Document.pdf"]}	2025-04-28 14:32:34.459591+00	2025-04-28 14:32:34.459591+00	\N
85f15dc9-3572-4fbd-9753-753b29f4b3d3	Patient	6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "85f15dc9-3572-4fbd-9753-753b29f4b3d3", "user": {"id": "0cc674da-0472-4bc5-96bb-6d594e045c03", "resourceType": "User"}, "email": "<EMAIL>", "gender": "Male", "orders": [], "status": "pending_review", "address": "123 Main Street, Anytown, Anywhere, 12345", "nextIDG": "2025-04-29T15:00", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "Doe", "allergies": ["Nuts", "Shellfish"], "firstName": "John", "insurance": [{"number": "INS-123456", "provider": "Insurance Co."}], "planOfCare": {"id": "94ffdebd-1ebc-4ce6-b0f6-6dce1c826526", "resourceType": "PlanOfCare"}, "requestRef": {"id": "a64ac039-764d-4534-8238-563c3f375100", "resourceType": "Request"}, "visitNotes": [{"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}, {"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}], "dateOfBirth": "1990-01-01", "medications": [{"name": "Drug A", "route": "Oral", "dosage": "500mg", "frequency": "Once daily", "indication": "Condition B", "resourceType": "Medication"}], "phoneNumber": "+1-************", "resourceType": "Patient", "admissionDate": "2022-01-01", "agencyActions": [{"title": "Review Patient Admission Data", "content": "", "priority": "high", "completed": false, "relatedTo": {"id": "", "resourceType": null}, "actionType": "base"}, {"title": "Review and Submit Order", "content": "", "priority": "high", "completed": false, "relatedTo": {"id": "", "resourceType": null}, "actionType": "base"}], "benefitPeriod": "2nd", "timeLineItems": [{"tags": ["Medications"], "title": "Pain medication change", "madeBy": {"date": "2025-04-23T00:00", "name": "Mikel"}, "summary": "Small increase of med A and B", "approvedBy": {"date": "2025-04-23T00:00", "name": "Mikel"}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Medications", "changes": [{"field": "Med A", "newValue": "20 mg a day", "oldValue": "10 mg a day"}, {"field": "Med B", "newValue": "100 IU Intramuscular EOD", "oldValue": "50 IU Intramuscular BIW"}]}]}, {"tags": ["Demographics"], "title": "Name misspelling", "madeBy": {"date": "2025-04-24T00:00", "name": "Mikel"}, "summary": "Demographics: Name misspelling ", "approvedBy": {"date": "2025-04-24T12:00", "name": "Mikel"}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Demographics", "changes": [{"field": "First Name", "newValue": "Daniel", "oldValue": "Daniell"}]}]}, {"tags": ["Visit Notes"], "title": "Routine Visit", "madeBy": {"date": "2025-04-23T00:00", "name": "Mikel", "user": {"id": "", "resourceType": ""}}, "summary": "Routine check-up with wound dressings and medication administration", "approvedBy": {"date": "2025-04-23T12:00", "name": "Mikel", "user": {"id": "", "resourceType": ""}}, "requestRef": {"id": "a64ac039-764d-4534-8238-563c3f375100", "resourceType": "Request"}, "versionHistorySections": []}], "primaryDiagnosis": "Condition A", "complianceTimeline": {"timeLineItems": [{"started": "2025-04-14T00:15", "blockers": [], "completed": "2025-04-24T00:15", "description": "Certification of Terminal Illness", "displayName": "CTI"}, {"started": "2025-04-24T00:45", "blockers": [], "completed": "2025-04-24T00:45", "description": "", "displayName": "NOE"}, {"started": "2025-04-25T09:09", "blockers": [], "completed": "2025-04-25T10:00", "description": "", "displayName": "Admission Visit"}, {"started": "2025-04-25T22:10", "blockers": [], "completed": "2025-04-25T23:00", "description": "", "displayName": "Last IDG"}, {"started": "", "blockers": [], "completed": "", "description": "", "displayName": "Next IDG"}]}, "secondaryDiagnoses": ["Condition B", "Condition C"], "medicalRecordNumber": "MRN-123456", "emergencyContactName": "Jane Doe", "emergencyContactPhone": "******-555-5556"}	2025-04-19 23:57:06.643002+00	2025-04-19 23:57:06.643002+00	\N
9245b676-191c-40d8-aa93-03641e313fb6	Branch	8ef3b25f-3890-4b82-9680-d19b7f8d9a7a	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "9245b676-191c-40d8-aa93-03641e313fb6", "name": "Branch A", "status": "pending_review", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "8ef3b25f-3890-4b82-9680-d19b7f8d9a7a", "idgInterval": "2 weeks", "lastIdgDate": "2025-04-17T10:00", "nextIdgDate": "2025-05-01T10:00", "resourceType": "Branch"}	2025-04-28 18:17:52.873779+00	2025-04-28 18:17:52.873779+00	\N
87f15dc9-3572-4fbd-9753-753b29f4b3d3	Patient	6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "87f15dc9-3572-4fbd-9753-753b29f4b3d3", "user": {"id": "0cc674da-0472-4bc5-96bb-6d594e045c03", "resourceType": "User"}, "email": "<EMAIL>", "branch": {"id": "9245b676-191c-40d8-aa93-03641e313fb6", "resourceType": "Branch"}, "gender": "Female", "orders": [], "status": "pending_review", "address": "123 Main Street, Anytown, Anywhere, 12345", "nextIDG": "2025-04-29T15:00", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "Doe", "allergies": ["Nuts", "Shellfish"], "firstName": "Jane", "insurance": [{"number": "INS-123456", "provider": "Insurance Co."}], "planOfCare": {"id": "94ffdebd-1ebc-4ce6-b0f6-6dce1c826526", "resourceType": "PlanOfCare"}, "requestRef": {"id": "a64ac039-764d-4534-8238-563c3f375100", "resourceType": "Request"}, "visitNotes": [{"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}, {"id": "aea28b4a-b37e-4a0f-8f19-33ce8cce93ee", "resourceType": "Note"}], "dateOfBirth": "1990-01-01", "medications": [{"name": "Drug A", "route": "Oral", "dosage": "500mg", "frequency": "Once daily", "indication": "Condition B", "resourceType": "Medication"}], "phoneNumber": "+1-************", "resourceType": "Patient", "admissionDate": "2022-01-01", "agencyActions": [{"title": "Review Patient Admission Data", "content": "", "priority": "high", "completed": true, "relatedTo": {"id": "", "resourceType": null}, "actionType": "base"}], "benefitPeriod": "1st", "timeLineItems": [{"tags": ["Medications"], "title": "Pain medication change", "madeBy": {"date": "2025-04-23T00:00", "name": "Mikel"}, "summary": "Small increase of med A and B", "approvedBy": {"date": "2025-04-23T00:00", "name": "Mikel"}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Medications", "changes": [{"field": "Med A", "newValue": "20 mg a day", "oldValue": "10 mg a day"}, {"field": "Med B", "newValue": "100 IU Intramuscular EOD", "oldValue": "50 IU Intramuscular BIW"}]}]}, {"tags": ["Demographics"], "title": "Name misspelling", "madeBy": {"date": "2025-04-24T00:00", "name": "Mikel"}, "summary": "Demographics: Name misspelling ", "approvedBy": {"date": "2025-04-24T12:00", "name": "Mikel"}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Demographics", "changes": [{"field": "First Name", "newValue": "Daniel", "oldValue": "Daniell"}]}]}], "primaryDiagnosis": "Condition A", "complianceTimeline": {"timeLineItems": [{"started": "2025-04-14T00:15", "blockers": [], "completed": "2025-04-24T00:15", "description": "Certification of Terminal Illness", "displayName": "CTI"}, {"started": "2025-04-24T00:45", "blockers": [], "completed": "2025-04-24T00:45", "description": "", "displayName": "NOE"}, {"started": "", "blockers": [], "completed": "", "description": "", "displayName": "Admission Visit"}, {"started": "", "blockers": [], "completed": "", "description": "2025-04-29", "displayName": "First IDG"}]}, "secondaryDiagnoses": ["Condition B", "Condition C"], "medicalRecordNumber": "MRN-123456", "emergencyContactName": "Jane Doe", "emergencyContactPhone": "******-555-5556"}	2025-04-19 23:57:06.643002+00	2025-04-19 23:57:06.643002+00	\N
2b9e0a77-b416-4899-b461-c70b13c1745b	PlanOfCare	ac4a9a07-a690-4eaa-b650-8b42658503e4	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "2b9e0a77-b416-4899-b461-c70b13c1745b", "status": "pending_review", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "goalsOfCare": "Increase mobility", "resourceType": "PlanOfCare", "interventions": "", "chiefComplaint": "CHF", "servicesNeeded": [], "visitFrequency": "2x weekly", "equipmentNeeded": [], "symptomsPresent": {"pain": false, "dyspnea": false, "fatigue": false, "painLevel": null}, "functionalStatus": {"percentage": 50, "description": "Full assist needed"}, "primaryCaregiver": "John Cena", "pastMedicalHistory": ["Diabetes"], "historyOfPresentIllness": "CHF"}	2025-04-28 14:43:09.13533+00	2025-04-28 14:43:09.13533+00	\N
68de285e-c8a7-446c-8b83-e46911ee589f	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "68de285e-c8a7-446c-8b83-e46911ee589f", "tags": ["file_upload"], "patient": {"id": "24f2f272-03a4-460c-bb40-381a0c3f1689", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-28T21:38:27.439Z", "resourceType": "Request", "fileAttachments": ["uploads/24f2f272-03a4-460c-bb40-381a0c3f1689/89d9599a-1c83-4090-ae0e-6e5e83e48cba-HospiceOS-screens.pdf"]}	2025-04-28 21:38:27.486558+00	2025-04-28 21:38:27.486558+00	\N
a081f841-daf8-435d-82c7-bf685a2b4013	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "a081f841-daf8-435d-82c7-bf685a2b4013", "tags": ["file_upload"], "patient": {"id": "b1d374be-ffad-41c2-86fc-91efd4d8f598", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-28T23:27:41.516Z", "resourceType": "Request", "fileAttachments": ["uploads/b1d374be-ffad-41c2-86fc-91efd4d8f598/4ae9c81b-7f75-4864-b894-2bf666b97a4a-HospiceOS-screens.pdf"]}	2025-04-28 23:27:41.633608+00	2025-04-28 23:27:41.633608+00	\N
6868ae98-e532-4b2d-8f29-e39b30c440fa	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "6868ae98-e532-4b2d-8f29-e39b30c440fa", "tags": ["file_upload"], "patient": {"id": "b1d374be-ffad-41c2-86fc-91efd4d8f598", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-29T13:50:47.623Z", "resourceType": "Request", "fileAttachments": ["uploads/b1d374be-ffad-41c2-86fc-91efd4d8f598/4467a61f-7760-4a04-8f8f-9385ebb05060-HospiceOS-screens.pdf"]}	2025-04-29 13:50:47.693488+00	2025-04-29 13:50:47.693488+00	\N
b1d374be-ffad-41c2-86fc-91efd4d8f598	Patient	6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "b1d374be-ffad-41c2-86fc-91efd4d8f598", "user": {"id": "1cc674da-0472-4bc5-96bb-6d594e045c25", "resourceType": "User"}, "gender": "Male", "status": "Active", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "lastName": "John", "allergies": ["Sulfa"], "firstName": "Jimmy", "insurance": [{"number": "565656655", "provider": "Medicare"}], "planOfCare": {"id": "2b9e0a77-b416-4899-b461-c70b13c1745b", "resourceType": "PlanOfCare"}, "visitNotes": [{"id": "", "resourceType": null}], "dateOfBirth": "09/10/1940", "medications": [{"name": "Lisinopril", "route": "Orally", "dosage": "500mg", "frequency": "Twice daily", "indication": "", "resourceType": null}], "resourceType": "Patient", "admissionDate": "2025-04-25", "agencyActions": [{"title": "Test Action", "content": "Test action content", "priority": "high", "completed": false, "actionType": "more_data", "requestRef": {"id": "", "resourceType": ""}}, {"title": "Review Admission Note", "content": "Review completed admission note for John Doe", "priority": "medium", "completed": true, "actionType": "approval", "requestRef": {"id": "", "resourceType": ""}}], "benefitPeriod": "1st", "timeLineItems": [{"tags": ["Processing"], "title": "Patient Admitted", "madeBy": {"date": "2025-04-28T10:45", "name": "Bob Ross", "user": {"id": "", "resourceType": ""}}, "status": "processing", "summary": "Patient Admitted", "approvedBy": {"date": "2025-04-27T09:45", "name": "Steve Ballmer", "user": {"id": "", "resourceType": ""}}, "requestRef": {"id": "", "resourceType": ""}, "versionHistorySections": [{"name": "Medication Change", "changes": [{"field": "Medication", "newValue": "Lisinopril 5mg", "oldValue": "Lisinopril 10mg"}]}]}, {"tags": ["Processing"], "title": "Pending document review (title)", "madeBy": {"date": "2025-04-29T13:58:11.313Z", "name": "John Cena"}, "started": "", "summary": "", "completed": "", "approvedBy": {"date": "", "name": ""}, "requestRef": {"id": "514c2aff-7ad0-4ebf-9c9b-f2653ea0b15d", "resourceType": "Request"}, "description": "Pending document review (description)", "displayName": "", "versionHistorySections": []}], "primaryDiagnosis": "CHF", "complianceTimeline": {"timeLineItems": [{"started": "", "blockers": [], "completed": "", "description": "", "displayName": "NOE"}]}, "medicalRecordNumber": "12345", "emergencyContactName": "Johnny Appleseed"}	2025-04-25 16:25:07.049495+00	2025-04-25 16:25:07.049495+00	\N
4042af07-63ec-468c-93b4-260ce0804cf1	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "4042af07-63ec-468c-93b4-260ce0804cf1", "tags": ["file_upload"], "patient": {"id": "b1d374be-ffad-41c2-86fc-91efd4d8f598", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-29T13:52:36.936Z", "resourceType": "Request", "fileAttachments": ["uploads/b1d374be-ffad-41c2-86fc-91efd4d8f598/4da80403-39aa-40b2-adf2-7bc565246660-HospiceOS-screens.pdf"]}	2025-04-29 13:52:37.008756+00	2025-04-29 13:52:37.008756+00	\N
8fc6b16a-0517-4f75-9420-96dbd53e685a	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "8fc6b16a-0517-4f75-9420-96dbd53e685a", "tags": ["file_upload"], "patient": {"id": "b1d374be-ffad-41c2-86fc-91efd4d8f598", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-29T13:55:57.295Z", "resourceType": "Request", "fileAttachments": ["uploads/b1d374be-ffad-41c2-86fc-91efd4d8f598/83652434-81e4-4ac5-a2c7-98caad5c7ea1-HospiceOS-screens.pdf"]}	2025-04-29 13:55:57.369711+00	2025-04-29 13:55:57.369711+00	\N
514c2aff-7ad0-4ebf-9c9b-f2653ea0b15d	Request	fb016bdd-1d9e-489a-b05e-08bd7fcb188b	bd6e787b-b665-4f7b-9b32-1df1797620cd	{"id": "514c2aff-7ad0-4ebf-9c9b-f2653ea0b15d", "tags": ["file_upload"], "patient": {"id": "b1d374be-ffad-41c2-86fc-91efd4d8f598", "resourceType": "Patient"}, "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "schemaId": "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", "createdAt": "2025-04-29T13:58:10.969Z", "resourceType": "Request", "fileAttachments": ["uploads/b1d374be-ffad-41c2-86fc-91efd4d8f598/70f27359-6309-4e02-958d-da96d5f62c53-HospiceOS-screens.pdf"]}	2025-04-29 13:58:11.044493+00	2025-04-29 13:58:11.044493+00	\N
\.


--
-- TOC entry 4354 (class 0 OID 16486)
-- Dependencies: 218
-- Data for Name: schemas; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.schemas (id, name, version, agency_id, base_schema_id, schema, created_at, updated_at) FROM stdin;
73f3953a-4d14-459e-b63b-145bef1fe1fe	UserBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/UserBase", "type": "object", "title": "UserBase", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["id", "resourceType", "email", "firstName", "lastName", "role", "agencyId"], "properties": {"id": {"type": "string", "format": "uuid"}, "role": {"enum": ["Admin", "Nurse", "MSW", "Chaplain", "Physician"], "type": "string"}, "email": {"type": "string", "format": "email"}, "agencyId": {"type": "string", "format": "uuid"}, "lastName": {"type": "string"}, "stytchId": {"type": "string"}, "firstName": {"type": "string"}, "resourceType": {"type": "string", "const": "User"}}}	2025-04-18 14:35:50.058+00	2025-04-18 14:35:50.058+00
372353e5-e971-4366-bb9e-1948c924b74c	OrderBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/OrderBase", "type": "object", "title": "OrderBase", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["id", "resourceType", "patientId", "authorId", "orderType", "title", "content", "createdAt"], "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "summary": {"type": "string"}, "authorId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "orderType": {"type": "string"}, "patientId": {"type": "string", "format": "uuid"}, "updatedAt": {"type": "string", "format": "date-time"}, "resourceType": {"type": "string", "const": "Order"}}}	2025-04-19 03:17:59.815+00	2025-04-19 17:33:41.712+00
b30b1e44-c7e2-2b5c-90cd-49dfda514e50	MedicationBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/MedicationBase", "type": "object", "title": "Medication", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["name", "dosage", "timing"], "properties": {"name": {"type": "string"}, "route": {"type": "string"}, "dosage": {"type": "string", "description": "Dosage amount and unit (e.g., '500mg', '1 tablet')"}, "frequency": {"type": "string", "description": "Timing instructions (e.g., 'Once daily', 'Every 8 hours')"}, "indication": {"type": "string"}, "resourceType": {"const": "Medication"}}, "additionalProperties": false}	2025-04-18 19:04:54.318924+00	2025-04-23 01:00:55.123+00
a30b1e44-c7e2-4b5c-90cd-49dfda584e50	NoteBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/NoteBase", "type": "object", "title": "NoteBase", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["id", "resourceType", "patientId", "authorId", "noteType", "title", "createdAt"], "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "summary": {"type": "string"}, "authorId": {"type": "string", "format": "uuid"}, "noteType": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "patientId": {"type": "string", "format": "uuid"}, "updatedAt": {"type": "string", "format": "date-time"}, "resourceType": {"type": "string", "const": "Note"}}}	2025-04-18 14:35:50.061+00	2025-04-19 17:34:20.119+00
d483d5f0-627f-4459-a386-f8c78aa77f88	NursingVisitNote	1.0.0	bd6e787b-b665-4f7b-9b32-1df1797620cd	a30b1e44-c7e2-4b5c-90cd-49dfda584e50	{"$id": "https://hospice-os.org/schemas/NursingVisitNote", "type": "object", "allOf": [{"$ref": "a30b1e44-c7e2-4b5c-90cd-49dfda584e50"}, {"required": ["vitalSigns"], "properties": {"noteType": {"const": "NursingVisit"}, "woundCare": {"type": "string"}, "vitalSigns": {"type": "object", "required": ["bloodPressure", "heartRate", "respiratoryRate", "temperature"], "properties": {"heartRate": {"type": "integer"}, "temperature": {"type": "number"}, "bloodPressure": {"type": "string"}, "respiratoryRate": {"type": "integer"}}}, "skinAssessment": {"type": "string"}, "educationProvided": {"type": "string"}, "medicationsAdministered": {"type": "array", "items": {"type": "object", "required": ["medicationName", "dose", "timeAdministered"], "properties": {"dose": {"type": "string"}, "medicationName": {"type": "string"}, "timeAdministered": {"type": "string", "format": "date-time"}}}}}}], "title": "NursingVisitNote", "$schema": "http://json-schema.org/draft-07/schema#"}	2025-04-19 16:57:29.876+00	2025-04-22 22:44:47.686+00
ac4a9a07-a690-4eaa-b650-8b42658503e4	PlanOfCareBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"type": "object", "title": "Clinical Patient Assessment Schema", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["resourceType", "chiefComplaint", "historyOfPresentIllness", "pastMedicalHistory", "functionalStatus", "symptomsPresent"], "properties": {"goalsOfCare": {"type": "string", "description": "Patient's specified care goals or preferences"}, "resourceType": {"type": "string", "const": "PlanOfCare"}, "interventions": {"type": "string", "description": "Details about interventions planned or administered"}, "chiefComplaint": {"type": "string", "description": "Primary reason patient is seeking care"}, "servicesNeeded": {"type": "array", "items": {"type": "string"}, "description": "Services required by the patient"}, "visitFrequency": {"type": "string", "description": "Recommended frequency of visits for patient"}, "equipmentNeeded": {"type": "array", "items": {"type": "string"}, "description": "Equipment necessary for patient care"}, "symptomsPresent": {"type": "object", "required": ["dyspnea", "fatigue", "pain"], "properties": {"pain": {"type": "boolean"}, "dyspnea": {"type": "boolean"}, "fatigue": {"type": "boolean"}, "painLevel": {"type": "integer", "maximum": 10, "minimum": 0}}}, "functionalStatus": {"type": "object", "required": ["percentage", "description"], "properties": {"percentage": {"type": "integer", "maximum": 100, "minimum": 0, "description": "Functional status as a percentage"}, "description": {"type": "string", "description": "Brief description of patient's functional abilities"}}}, "primaryCaregiver": {"type": "string"}, "pastMedicalHistory": {"type": "array", "items": {"type": "string"}, "description": "List of patient's past medical diagnoses"}, "historyOfPresentIllness": {"type": "string", "description": "Detailed account of the development and progress of the current illness"}}}	2025-04-18 22:11:08.353+00	2025-04-28 16:49:43.203+00
5a471325-0e72-4e58-8a3d-6672df25facf	Social Worker Visit Note	1.0	bd6e787b-b665-4f7b-9b32-1df1797620cd	a30b1e44-c7e2-4b5c-90cd-49dfda584e50	{"$id": "https://hospice-os.org/schemas/SocialWorkVisitNote", "type": "object", "allOf": [{"$ref": "https://hospice-os.org/schemas/NoteBase"}, {"required": ["psychosocialAssessment"], "properties": {"noteType": {"const": "SocialWorkVisit"}, "financialConcerns": {"type": "string"}, "resourcesProvided": {"type": "array", "items": {"type": "string"}}, "caregiverSupportPlan": {"type": "string"}, "psychosocialAssessment": {"type": "string"}}}], "title": "Social Worker Visit Note", "$schema": "http://json-schema.org/draft-07/schema#"}	2025-04-19 16:49:55.671+00	2025-04-19 16:49:55.671+00
0bb09975-062c-4b93-802a-5049fea5c6fc	test	1.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	e9044eaa-3e1b-4dfa-b863-4cd0aa9a1a9b	{"type": "object", "allOf": [{"$ref": "cb223201-8923-427f-af00-00a27221f5c1"}], "properties": {"test": {"type": "string"}}}	2025-04-22 22:46:15.121+00	2025-04-23 14:21:40.799+00
cb223201-8923-427f-af00-00a27221f5c1	IDGMeetingBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"type": "object", "properties": {"patients": {"type": "array", "items": {"type": "object", "properties": {"newOrder": {"type": "object", "items": {"type": "object"}, "properties": {"order": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Order"}}}, "schemaId": {"type": "string", "format": "uuid", "properties": {"resourceType": {"const": "Order"}}}}}, "patientRef": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Patient"}}}, "concludingNote": {"type": "string"}}}}, "timeEnded": {"type": "string", "format": "date-time"}, "timeStarted": {"type": "string", "format": "date-time"}, "concludingNote": {"type": "string"}}}	2025-04-19 03:05:37.771+00	2025-04-24 20:29:54.916+00
fc3b5899-9f33-4632-bb5f-876566c19aa9	TimelineItem	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"type": "object", "required": ["madeBy", "title", "summary"], "properties": {"tags": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string"}, "madeBy": {"type": "object", "required": ["name", "date"], "properties": {"date": {"type": "string", "format": "date-time"}, "name": {"type": "string", "description": "name of the person who made the change"}, "user": {"type": "object", "required": ["id", "resourceType"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "User"}}}}, "description": "who made the Change?"}, "status": {"enum": ["submitted", "waiting_on_more_data", "processing", "waiting_on_approvals"], "type": "string"}, "summary": {"type": "string"}, "approvedBy": {"type": "object", "required": ["name", "date"], "properties": {"date": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "user": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "User", "format": ""}}}}, "description": "which authorized person approved the change? blank if no approval needed"}, "requestRef": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Request"}}, "description": "Timeline items are made from Requests. This request can have children"}, "versionHistorySections": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "the section name"}, "changes": {"type": "array", "items": {"$ref": "#/definitions/VersionChange"}}}, "description": ""}}}, "definitions": {"VersionChange": {"type": "object", "required": ["field", "oldValue", "newValue"], "properties": {"field": {"type": "string", "description": "the field name"}, "newValue": {"type": "string", "description": "what the value was changed to"}, "oldValue": {"type": "string", "description": "the previous value, can be nothing if it didn't exist before"}}}}}	2025-04-22 17:09:02.986+00	2025-04-28 12:25:57.875+00
e9044eaa-3e1b-4dfa-b863-4cd0aa9a1a9b	ActionBase	1.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/ActionBase", "type": "object", "title": "Action", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["title"], "properties": {"title": {"type": "string", "description": "The title of the action"}, "content": {"type": "string", "description": "The main text content of the action"}, "priority": {"enum": ["high", "medium", "low"], "type": "string"}, "completed": {"type": "boolean"}, "actionType": {"enum": ["more_data", "approval", "reminder"], "type": "string"}, "requestRef": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Request"}}}}}	2025-04-21 19:50:30.913+00	2025-04-28 13:56:44.435+00
fb016bdd-1d9e-489a-b05e-08bd7fcb188b	Request	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/Request", "type": "object", "title": "Request", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["id", "resourceType"], "properties": {"id": {"type": "string", "format": "uuid"}, "tags": {"type": "array", "items": {"enum": ["visit_note", "order", "poc_update", "demographic_update"], "type": "string"}}, "content": {"type": "string", "description": "The main text content of the request"}, "patient": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"const": "Patient"}}}, "summary": {"type": "string", "description": "an ai summary of the request"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp for when the request was created"}, "responses": {"type": "array", "items": {"type": "object", "properties": {"sentBy": {"type": "string", "description": "tallio internal or end user"}, "content": {"type": "string"}}}}, "noteCreated": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Note"}}, "description": "The ref to the filled note generated using the included context and note schema (Template)"}, "noteSchemaId": {"type": "string", "format": "uuid", "properties": {}, "description": "the schema Id of a Schema that inherits from NoteBase"}, "orderCreated": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Order"}}, "description": "The ref to the filled order generated using the included context and order schema (Template)"}, "resourceType": {"type": "string", "const": "Request"}, "orderSchemaId": {"type": "string", "format": "uuid", "properties": {}, "description": "the schema Id of a Schema that inherits from OrderBase"}, "parentRequest": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Request"}}}, "fileAttachments": {"type": "array", "items": {"type": "string"}}, "audioAttachments": {"type": "array", "items": {"type": "string"}, "description": "An array of audio attachment objects"}}}	2025-04-21 14:58:25.651+00	2025-04-25 19:19:05.63+00
8ef3b25f-3890-4b82-9680-d19b7f8d9a7a	BranchBase	1.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "idgInterval": {"type": "string", "description": "1 week, 2 weeks, 10 days, 13 days, etc."}, "lastIdgDate": {"type": "string", "format": "date-time"}, "nextIdgDate": {"type": "string", "format": "date-time"}, "resourceType": {"type": "string", "const": "Branch"}}}	2025-04-28 17:04:05.054+00	2025-04-28 18:16:33.035+00
b34fe81f-44d0-487f-81b1-0e3fe3097e4f	Workflow	1.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://serverlessworkflow.io/schemas/1.0.0/workflow.json", "type": "object", "$defs": {"task": {"oneOf": [{"$ref": "#/$defs/callTask"}, {"$ref": "#/$defs/doTask"}, {"$ref": "#/$defs/forkTask"}, {"$ref": "#/$defs/emitTask"}, {"$ref": "#/$defs/forTask"}, {"$ref": "#/$defs/listenTask"}, {"$ref": "#/$defs/raiseTask"}, {"$ref": "#/$defs/runTask"}, {"$ref": "#/$defs/setTask"}, {"$ref": "#/$defs/switchTask"}, {"$ref": "#/$defs/tryTask"}, {"$ref": "#/$defs/waitTask"}], "title": "Task", "description": "A discrete unit of work that contributes to achieving the overall objectives defined by the workflow.", "unevaluatedProperties": false}, "error": {"type": "object", "title": "Error", "required": ["type", "status"], "properties": {"type": {"oneOf": [{"$ref": "#/$defs/uriTemplate", "title": "LiteralErrorType", "description": "The literal error type."}, {"$ref": "#/$defs/runtimeExpression", "title": "ExpressionErrorType", "description": "An expression based error type."}], "title": "ErrorType", "description": "A URI reference that identifies the error type."}, "title": {"type": "string", "title": "ErrorTitle", "description": "A short, human-readable summary of the error."}, "detail": {"type": "string", "title": "ErrorDetails", "description": "A human-readable explanation specific to this occurrence of the error."}, "status": {"type": "integer", "title": "ErrorStatus", "description": "The status code generated by the origin for this occurrence of the error."}, "instance": {"oneOf": [{"type": "string", "title": "LiteralErrorInstance", "format": "json-pointer", "description": "The literal error instance."}, {"$ref": "#/$defs/runtimeExpression", "title": "ExpressionErrorInstance", "description": "An expression based error instance."}], "title": "ErrorInstance", "description": "A JSON Pointer used to reference the component the error originates from."}}, "description": "Represents an error.", "unevaluatedProperties": false}, "input": {"type": "object", "title": "Input", "properties": {"from": {"oneOf": [{"type": "string"}, {"type": "object"}], "title": "InputFrom", "description": "A runtime expression, if any, used to mutate and/or filter the input of the workflow or task."}, "schema": {"$ref": "#/$defs/schema", "title": "InputSchema", "description": "The schema used to describe and validate the input of the workflow or task."}}, "description": "Configures the input of a workflow or task.", "unevaluatedProperties": false}, "doTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "DoTask", "required": ["do"], "properties": {"do": {"$ref": "#/$defs/taskList", "title": "DoTaskConfiguration", "description": "The configuration of the tasks to perform sequentially."}}, "description": "Allows to execute a list of tasks in sequence.", "unevaluatedProperties": false}, "export": {"type": "object", "title": "Export", "properties": {"as": {"oneOf": [{"type": "string"}, {"type": "object"}], "title": "ExportAs", "description": "A runtime expression, if any, used to export the output data to the context."}, "schema": {"$ref": "#/$defs/schema", "title": "ExportSchema", "description": "The schema used to describe and validate the workflow context."}}, "description": "Set the content of the context. .", "unevaluatedProperties": false}, "output": {"type": "object", "title": "Output", "properties": {"as": {"oneOf": [{"type": "string"}, {"type": "object"}], "title": "OutputAs", "description": "A runtime expression, if any, used to mutate and/or filter the output of the workflow or task."}, "schema": {"$ref": "#/$defs/schema", "title": "OutputSchema", "description": "The schema used to describe and validate the output of the workflow or task."}}, "description": "Configures the output of a workflow or task.", "unevaluatedProperties": false}, "schema": {"type": "object", "oneOf": [{"title": "SchemaInline", "required": ["document"], "properties": {"document": {"description": "The schema's inline definition."}}}, {"title": "SchemaExternal", "required": ["resource"], "properties": {"resource": {"$ref": "#/$defs/externalResource", "title": "SchemaExternalResource", "description": "The schema's external resource."}}}], "title": "Schema", "properties": {"format": {"type": "string", "title": "SchemaFormat", "default": "json", "description": "The schema's format. Defaults to 'json'. The (optional) version of the format can be set using `{format}:{version}`."}}, "description": "Represents the definition of a schema.", "unevaluatedProperties": false}, "catalog": {"type": "object", "title": "Catalog", "required": ["endpoint"], "properties": {"endpoint": {"$ref": "#/$defs/endpoint", "title": "CatalogEndpoint", "description": "The root URL where the catalog is hosted."}}, "description": "The definition of a resource catalog.", "unevaluatedProperties": false}, "forTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "ForTask", "required": ["for", "do"], "properties": {"do": {"$ref": "#/$defs/taskList", "title": "ForTaskDo"}, "for": {"type": "object", "title": "ForTaskConfiguration", "required": ["in"], "properties": {"at": {"type": "string", "title": "ForAt", "default": "index", "description": "The name of the variable used to store the index of the current item being enumerated."}, "in": {"type": "string", "title": "ForIn", "description": "A runtime expression used to get the collection to enumerate."}, "each": {"type": "string", "title": "ForEach", "default": "item", "description": "The name of the variable used to store the current item being enumerated."}}, "description": "The definition of the loop that iterates over a range of values.", "unevaluatedProperties": false}, "while": {"type": "string", "title": "While", "description": "A runtime expression that represents the condition, if any, that must be met for the iteration to continue."}}, "description": "Allows workflows to iterate over a collection of items, executing a defined set of subtasks for each item in the collection. This task type is instrumental in handling scenarios such as batch processing, data transformation, and repetitive operations across datasets.", "unevaluatedProperties": false}, "runTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "RunTask", "required": ["run"], "properties": {"run": {"type": "object", "oneOf": [{"title": "RunContainer", "required": ["container"], "properties": {"container": {"type": "object", "title": "Container", "required": ["image"], "properties": {"name": {"type": "string", "title": "ContainerName", "description": "A runtime expression, if any, used to give specific name to the container."}, "image": {"type": "string", "title": "ContainerImage", "description": "The name of the container image to run."}, "ports": {"type": "object", "title": "ContainerPorts", "description": "The container's port mappings, if any."}, "command": {"type": "string", "title": "ContainerCommand", "description": "The command, if any, to execute on the container."}, "volumes": {"type": "object", "title": "ContainerVolumes", "description": "The container's volume mappings, if any."}, "lifetime": {"$ref": "#/$defs/containerLifetime", "title": "ContainerLifetime", "description": "An object, if any, used to configure the container's lifetime"}, "environment": {"type": "object", "title": "ContainerEnvironment", "description": "A key/value mapping of the environment variables, if any, to use when running the configured process."}}, "description": "The configuration of the container to run.", "unevaluatedProperties": false}}, "description": "Enables the execution of external processes encapsulated within a containerized environment."}, {"title": "RunScript", "required": ["script"], "properties": {"script": {"type": "object", "oneOf": [{"type": "object", "title": "InlineScript", "required": ["code"], "properties": {"code": {"type": "string", "title": "InlineScriptCode"}}, "description": "The script's code."}, {"type": "object", "title": "ExternalScript", "required": ["source"], "properties": {"source": {"$ref": "#/$defs/externalResource", "title": "ExternalScriptResource"}}, "description": "The script's resource."}], "title": "Script", "required": ["language"], "properties": {"language": {"type": "string", "title": "ScriptLanguage", "description": "The language of the script to run."}, "arguments": {"type": "object", "title": "ScriptArguments", "description": "A key/value mapping of the arguments, if any, to use when running the configured script.", "additionalProperties": true}, "environment": {"type": "object", "title": "ScriptEnvironment", "description": "A key/value mapping of the environment variables, if any, to use when running the configured script process.", "additionalProperties": true}}, "description": "The configuration of the script to run.", "unevaluatedProperties": false}}, "description": "Enables the execution of custom scripts or code within a workflow, empowering workflows to perform specialized logic, data processing, or integration tasks by executing user-defined scripts written in various programming languages."}, {"title": "RunShell", "required": ["shell"], "properties": {"shell": {"type": "object", "title": "Shell", "required": ["command"], "properties": {"command": {"type": "string", "title": "ShellCommand", "description": "The shell command to run."}, "arguments": {"type": "object", "title": "ShellArguments", "description": "A list of the arguments of the shell command to run.", "additionalProperties": true}, "environment": {"type": "object", "title": "ShellEnvironment", "description": "A key/value mapping of the environment variables, if any, to use when running the configured process.", "additionalProperties": true}}, "description": "The configuration of the shell command to run.", "unevaluatedProperties": false}}, "description": "Enables the execution of shell commands within a workflow, enabling workflows to interact with the underlying operating system and perform system-level operations, such as file manipulation, environment configuration, or system administration tasks."}, {"title": "RunWorkflow", "required": ["workflow"], "properties": {"workflow": {"type": "object", "title": "SubflowConfiguration", "required": ["namespace", "name", "version"], "properties": {"name": {"type": "string", "title": "SubflowName", "description": "The name of the workflow to run."}, "input": {"type": "object", "title": "SubflowInput", "description": "The data, if any, to pass as input to the workflow to execute. The value should be validated against the target workflow's input schema, if specified.", "additionalProperties": true}, "version": {"type": "string", "title": "SubflowVersion", "default": "latest", "description": "The version of the workflow to run. Defaults to latest."}, "namespace": {"type": "string", "title": "SubflowNamespace", "description": "The namespace the workflow to run belongs to."}}, "description": "The configuration of the workflow to run.", "unevaluatedProperties": false}}, "description": "Enables the invocation and execution of nested workflows within a parent workflow, facilitating modularization, reusability, and abstraction of complex logic or business processes by encapsulating them into standalone workflow units."}], "title": "RunTaskConfiguration", "properties": {"await": {"type": "boolean", "title": "AwaitProcessCompletion", "default": true, "description": "Whether to await the process completion before continuing."}, "return": {"enum": ["stdout", "stderr", "code", "all", "none"], "type": "string", "title": "ProcessReturnType", "default": "stdout", "description": "Configures the output of the process."}}, "description": "The configuration of the process to execute.", "unevaluatedProperties": false}}, "description": "Provides the capability to execute external containers, shell commands, scripts, or workflows.", "unevaluatedProperties": false}, "setTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "SetTask", "required": ["set"], "properties": {"set": {"type": "object", "title": "SetTaskConfiguration", "description": "The data to set.", "minProperties": 1, "additionalProperties": true}}, "description": "A task used to set data.", "unevaluatedProperties": false}, "timeout": {"type": "object", "title": "Timeout", "required": ["after"], "properties": {"after": {"$ref": "#/$defs/duration", "title": "TimeoutAfter", "description": "The duration after which to timeout."}}, "description": "The definition of a timeout.", "unevaluatedProperties": false}, "tryTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "TryTask", "required": ["try", "catch"], "properties": {"try": {"$ref": "#/$defs/taskList", "title": "TryTaskConfiguration", "description": "The task(s) to perform."}, "catch": {"type": "object", "title": "TryTaskCatch", "properties": {"as": {"type": "string", "title": "CatchAs", "description": "The name of the runtime expression variable to save the error as. Defaults to 'error'."}, "do": {"$ref": "#/$defs/taskList", "title": "TryTaskCatchDo", "description": "The definition of the task(s) to run when catching an error."}, "when": {"type": "string", "title": "CatchWhen", "description": "A runtime expression used to determine whether to catch the filtered error."}, "retry": {"oneOf": [{"$ref": "#/$defs/retryPolicy", "title": "RetryPolicyDefinition", "description": "The retry policy to use, if any, when catching errors."}, {"type": "string", "title": "RetryPolicyReference", "description": "The name of the retry policy to use, if any, when catching errors."}]}, "errors": {"type": "object", "title": "CatchErrors", "properties": {"with": {"$ref": "#/$defs/errorFilter"}}, "description": "static error filter"}, "exceptWhen": {"type": "string", "title": "CatchExceptWhen", "description": "A runtime expression used to determine whether not to catch the filtered error."}}, "description": "The object used to define the errors to catch.", "unevaluatedProperties": false}}, "description": "Serves as a mechanism within workflows to handle errors gracefully, potentially retrying failed tasks before proceeding with alternate ones.", "unevaluatedProperties": false}, "callTask": {"oneOf": [{"$ref": "#/$defs/taskBase", "type": "object", "title": "CallAsyncAPI", "required": ["call", "with"], "properties": {"call": {"type": "string", "const": "asyncapi"}, "with": {"type": "object", "oneOf": [{"required": ["document", "operation", "message"]}, {"required": ["document", "operation", "subscription"]}, {"required": ["document", "channel", "message"]}, {"required": ["document", "channel", "subscription"]}], "title": "AsyncApiArguments", "properties": {"server": {"$ref": "#/$defs/asyncApiServer", "title": "AsyncAPIServer", "description": "An object used to configure to the server to call the specified AsyncAPI operation on."}, "channel": {"type": "string", "title": "With", "description": "The name of the channel on which to perform the operation. Used only in case the referenced document uses AsyncAPI v2.6.0."}, "message": {"$ref": "#/$defs/asyncApiOutboundMessage", "title": "AsyncApiMessage", "description": "An object used to configure the message to publish using the target operation."}, "document": {"$ref": "#/$defs/externalResource", "title": "AsyncAPIDocument", "description": "The document that defines the AsyncAPI operation to call."}, "protocol": {"enum": ["amqp", "amqp1", "anypointmq", "googlepubsub", "http", "ibmmq", "jms", "kafka", "mercure", "mqtt", "mqtt5", "nats", "pulsar", "redis", "sns", "solace", "sqs", "stomp", "ws"], "type": "string", "title": "AsyncApiProtocol", "description": "The protocol to use to select the target server."}, "operation": {"type": "string", "title": "AsyncAPIOperation", "description": "A reference to the AsyncAPI operation to call."}, "subscription": {"$ref": "#/$defs/asyncApiSubscription", "title": "AsyncApiSubscription", "description": "An object used to configure the subscription to messages consumed using the target operation."}, "authentication": {"$ref": "#/$defs/referenceableAuthenticationPolicy", "title": "AsyncAPIAuthentication", "description": "The authentication policy, if any, to use when calling the AsyncAPI operation."}}, "description": "The Async API call arguments.", "unevaluatedProperties": false}}, "description": "Defines the AsyncAPI call to perform.", "unevaluatedProperties": false}, {"$ref": "#/$defs/taskBase", "type": "object", "title": "CallGRPC", "required": ["call", "with"], "properties": {"call": {"type": "string", "const": "grpc"}, "with": {"type": "object", "title": "GRPCArguments", "required": ["proto", "service", "method"], "properties": {"proto": {"$ref": "#/$defs/externalResource", "title": "WithGRPCProto", "description": "The proto resource that describes the GRPC service to call."}, "method": {"type": "string", "title": "WithGRPCMethod", "description": "The name of the method to call on the defined GRPC service."}, "service": {"type": "object", "title": "WithGRPCService", "required": ["name", "host"], "properties": {"host": {"type": "string", "title": "WithGRPCServiceHost", "pattern": "^[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$", "description": "The hostname of the GRPC service to call."}, "name": {"type": "string", "title": "WithGRPCServiceName", "description": "The name of the GRPC service to call."}, "port": {"type": "integer", "title": "WithGRPCServicePost", "maximum": 65535, "minimum": 0, "description": "The port number of the GRPC service to call."}, "authentication": {"$ref": "#/$defs/referenceableAuthenticationPolicy", "title": "WithGRPCServiceAuthentication", "description": "The endpoint's authentication policy, if any."}}, "unevaluatedProperties": false}, "arguments": {"type": "object", "title": "WithGRPCArguments", "description": "The arguments, if any, to call the method with.", "additionalProperties": true}}, "description": "The GRPC call arguments.", "unevaluatedProperties": false}}, "description": "Defines the GRPC call to perform.", "unevaluatedProperties": false}, {"$ref": "#/$defs/taskBase", "type": "object", "title": "CallHTTP", "required": ["call", "with"], "properties": {"call": {"type": "string", "const": "http"}, "with": {"type": "object", "title": "HTTPArguments", "required": ["method", "endpoint"], "properties": {"body": {"title": "HTTPBody", "description": "The body, if any, of the HTTP request to perform."}, "query": {"type": "object", "title": "HTTPQuery", "description": "A name/value mapping of the query parameters, if any, of the HTTP request to perform.", "additionalProperties": true}, "method": {"type": "string", "title": "HTTPMethod", "description": "The HTTP method of the HTTP request to perform."}, "output": {"enum": ["raw", "content", "response"], "type": "string", "title": "HTTPOutput", "description": "The http call output format. Defaults to 'content'."}, "headers": {"type": "object", "title": "HTTPHeaders", "description": "A name/value mapping of the headers, if any, of the HTTP request to perform."}, "endpoint": {"$ref": "#/$defs/endpoint", "title": "HTTPEndpoint", "description": "The HTTP endpoint to send the request to."}, "redirect": {"type": "boolean", "title": "HttpRedirect", "description": "Specifies whether redirection status codes (`300–399`) should be treated as errors."}}, "description": "The HTTP call arguments.", "unevaluatedProperties": false}}, "description": "Defines the HTTP call to perform.", "unevaluatedProperties": false}, {"$ref": "#/$defs/taskBase", "type": "object", "title": "CallOpenAPI", "required": ["call", "with"], "properties": {"call": {"type": "string", "const": "openapi"}, "with": {"type": "object", "title": "OpenAPIArguments", "required": ["document", "operationId"], "properties": {"output": {"enum": ["raw", "content", "response"], "type": "string", "title": "WithOpenAPIOutput", "description": "The http call output format. Defaults to 'content'."}, "document": {"$ref": "#/$defs/externalResource", "title": "WithOpenAPIDocument", "description": "The document that defines the OpenAPI operation to call."}, "redirect": {"type": "boolean", "title": "HttpRedirect", "description": "Specifies whether redirection status codes (`300–399`) should be treated as errors."}, "parameters": {"type": "object", "title": "WithOpenAPIParameters", "description": "A name/value mapping of the parameters of the OpenAPI operation to call.", "additionalProperties": true}, "operationId": {"type": "string", "title": "WithOpenAPIOperation", "description": "The id of the OpenAPI operation to call."}, "authentication": {"$ref": "#/$defs/referenceableAuthenticationPolicy", "title": "WithOpenAPIAuthentication", "description": "The authentication policy, if any, to use when calling the OpenAPI operation."}}, "description": "The OpenAPI call arguments.", "unevaluatedProperties": false}}, "description": "Defines the OpenAPI call to perform.", "unevaluatedProperties": false}, {"$ref": "#/$defs/taskBase", "type": "object", "title": "CallFunction", "required": ["call"], "properties": {"call": {"not": {"enum": ["asyncapi", "grpc", "http", "openapi"]}, "type": "string", "description": "The name of the function to call."}, "with": {"type": "object", "title": "FunctionArguments", "description": "A name/value mapping of the parameters, if any, to call the function with.", "additionalProperties": true}}, "description": "Defines the function call to perform.", "unevaluatedProperties": false}], "title": "CallTask", "description": "Defines the call to perform."}, "duration": {"oneOf": [{"type": "object", "title": "DurationInline", "properties": {"days": {"type": "integer", "title": "DurationDays", "description": "Number of days, if any."}, "hours": {"type": "integer", "title": "DurationHours", "description": "Number of days, if any."}, "minutes": {"type": "integer", "title": "DurationMinutes", "description": "Number of minutes, if any."}, "seconds": {"type": "integer", "title": "DurationSeconds", "description": "Number of seconds, if any."}, "milliseconds": {"type": "integer", "title": "DurationMilliseconds", "description": "Number of milliseconds, if any."}}, "description": "The inline definition of a duration.", "minProperties": 1, "unevaluatedProperties": false}, {"type": "string", "title": "DurationExpression", "pattern": "^P(?!$)(\\\\d+(?:\\\\.\\\\d+)?Y)?(\\\\d+(?:\\\\.\\\\d+)?M)?(\\\\d+(?:\\\\.\\\\d+)?W)?(\\\\d+(?:\\\\.\\\\d+)?D)?(T(?=\\\\d)(\\\\d+(?:\\\\.\\\\d+)?H)?(\\\\d+(?:\\\\.\\\\d+)?M)?(\\\\d+(?:\\\\.\\\\d+)?S)?)?$", "description": "The ISO 8601 expression of a duration."}]}, "emitTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "EmitTask", "required": ["emit"], "properties": {"emit": {"type": "object", "title": "EmitTaskConfiguration", "required": ["event"], "properties": {"event": {"type": "object", "title": "EmitEventDefinition", "properties": {"with": {"$ref": "#/$defs/eventProperties", "title": "EmitEventWith", "required": ["source", "type"], "description": "Defines the properties of event to emit."}}, "description": "The definition of the event to emit.", "additionalProperties": true}}, "description": "The configuration of an event's emission.", "unevaluatedProperties": false}}, "description": "Allows workflows to publish events to event brokers or messaging systems, facilitating communication and coordination between different components and services.", "unevaluatedProperties": false}, "endpoint": {"oneOf": [{"$ref": "#/$defs/runtimeExpression"}, {"$ref": "#/$defs/uriTemplate"}, {"type": "object", "title": "EndpointConfiguration", "required": ["uri"], "properties": {"uri": {"oneOf": [{"$ref": "#/$defs/uriTemplate", "title": "LiteralEndpointURI", "description": "The literal endpoint's URI."}, {"$ref": "#/$defs/runtimeExpression", "title": "ExpressionEndpointURI", "description": "An expression based endpoint's URI."}], "title": "EndpointUri", "description": "The endpoint's URI."}, "authentication": {"$ref": "#/$defs/referenceableAuthenticationPolicy", "title": "EndpointAuthentication", "description": "The authentication policy to use."}}, "unevaluatedProperties": false}], "title": "Endpoint", "description": "Represents an endpoint."}, "forkTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "ForkTask", "required": ["fork"], "properties": {"fork": {"type": "object", "title": "ForkTaskConfiguration", "required": ["branches"], "properties": {"compete": {"type": "boolean", "title": "ForkCompete", "default": false, "description": "Indicates whether or not the concurrent tasks are racing against each other, with a single possible winner, which sets the composite task's output."}, "branches": {"$ref": "#/$defs/taskList", "title": "ForkBranches"}}, "description": "The configuration of the branches to perform concurrently.", "unevaluatedProperties": false}}, "description": "Allows workflows to execute multiple tasks concurrently and optionally race them against each other, with a single possible winner, which sets the task's output.", "unevaluatedProperties": false}, "taskBase": {"type": "object", "title": "TaskBase", "properties": {"if": {"type": "string", "title": "TaskBaseIf", "description": "A runtime expression, if any, used to determine whether or not the task should be run."}, "then": {"$ref": "#/$defs/flowDirective", "title": "TaskBaseThen", "description": "The flow directive to be performed upon completion of the task."}, "input": {"$ref": "#/$defs/input", "title": "TaskBaseInput", "description": "Configure the task's input."}, "export": {"$ref": "#/$defs/export", "title": "TaskBaseExport", "description": "Export task output to context."}, "output": {"$ref": "#/$defs/output", "title": "TaskBaseOutput", "description": "Configure the task's output."}, "timeout": {"oneOf": [{"$ref": "#/$defs/timeout", "title": "TaskTimeoutDefinition", "description": "The task's timeout configuration, if any."}, {"type": "string", "title": "TaskTimeoutReference", "description": "The name of the task's timeout, if any."}], "title": "TaskTimeout"}, "metadata": {"type": "object", "title": "TaskMetadata", "description": "Holds additional information about the task.", "additionalProperties": true}}, "description": "An object inherited by all tasks."}, "taskList": {"type": "array", "items": {"type": "object", "title": "TaskItem", "maxProperties": 1, "minProperties": 1, "additionalProperties": {"$ref": "#/$defs/task"}}, "title": "TaskList", "description": "List of named tasks to perform."}, "waitTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "WaitTask", "required": ["wait"], "properties": {"wait": {"$ref": "#/$defs/duration", "title": "WaitTaskConfiguration", "description": "The amount of time to wait."}}, "description": "Allows workflows to pause or delay their execution for a specified period of time.", "unevaluatedProperties": false}, "extension": {"type": "object", "title": "Extension", "required": ["extend"], "properties": {"when": {"type": "string", "title": "ExtensionCondition", "description": "A runtime expression, if any, used to determine whether or not the extension should apply in the specified context."}, "after": {"$ref": "#/$defs/taskList", "title": "ExtensionDoAfter", "description": "The task(s) to execute after the extended task, if any."}, "before": {"$ref": "#/$defs/taskList", "title": "ExtensionDoBefore", "description": "The task(s) to execute before the extended task, if any."}, "extend": {"enum": ["call", "composite", "emit", "for", "listen", "raise", "run", "set", "switch", "try", "wait", "all"], "type": "string", "title": "ExtensionTarget", "description": "The type of task to extend."}}, "description": "The definition of an extension.", "unevaluatedProperties": false}, "raiseTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "RaiseTask", "required": ["raise"], "properties": {"raise": {"type": "object", "title": "RaiseTaskConfiguration", "required": ["error"], "properties": {"error": {"oneOf": [{"$ref": "#/$defs/error", "title": "RaiseErrorDefinition", "description": "Defines the error to raise."}, {"type": "string", "title": "RaiseErrorReference", "description": "The name of the error to raise"}], "title": "RaiseTaskError"}}, "description": "The definition of the error to raise.", "unevaluatedProperties": false}}, "description": "Intentionally triggers and propagates errors.", "unevaluatedProperties": false}, "listenTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "ListenTask", "required": ["listen"], "properties": {"listen": {"type": "object", "title": "ListenTaskConfiguration", "required": ["to"], "properties": {"to": {"$ref": "#/$defs/eventConsumptionStrategy", "title": "ListenTo", "description": "Defines the event(s) to listen to."}, "read": {"enum": ["data", "envelope", "raw"], "type": "string", "title": "ListenAndReadAs", "default": "data", "description": "Specifies how events are read during the listen operation."}}, "description": "The configuration of the listener to use.", "unevaluatedProperties": false}, "foreach": {"$ref": "#/$defs/subscriptionIterator", "title": "ListenIterator", "description": "Configures the iterator, if any, for processing consumed event(s)."}}, "description": "Provides a mechanism for workflows to await and react to external events, enabling event-driven behavior within workflow systems.", "unevaluatedProperties": false}, "switchTask": {"$ref": "#/$defs/taskBase", "type": "object", "title": "SwitchTask", "required": ["switch"], "properties": {"switch": {"type": "array", "items": {"type": "object", "title": "SwitchItem", "maxProperties": 1, "minProperties": 1, "additionalProperties": {"type": "object", "title": "SwitchCase", "required": ["then"], "properties": {"then": {"$ref": "#/$defs/flowDirective", "title": "SwitchCaseOutcome", "description": "The flow directive to execute when the case matches."}, "when": {"type": "string", "title": "SwitchCaseCondition", "description": "A runtime expression used to determine whether or not the case matches."}}, "description": "The definition of a case within a switch task, defining a condition and corresponding tasks to execute if the condition is met.", "unevaluatedProperties": false}}, "title": "SwitchTaskConfiguration", "minItems": 1, "description": "The definition of the switch to use."}}, "description": "Enables conditional branching within workflows, allowing them to dynamically select different paths based on specified conditions or criteria.", "unevaluatedProperties": false}, "errorFilter": {"type": "object", "title": "ErrorFilter", "properties": {"type": {"type": "string", "description": "if present, means this value should be used for filtering"}, "title": {"type": "string", "description": "if present, means this value should be used for filtering"}, "status": {"type": "integer", "description": "if present, means this value should be used for filtering"}, "details": {"type": "string", "description": "if present, means this value should be used for filtering"}, "instance": {"type": "string", "description": "if present, means this value should be used for filtering"}}, "description": "Error filtering base on static values. For error filtering on dynamic values, use catch.when property", "minProperties": 1}, "eventFilter": {"type": "object", "title": "EventFilter", "required": ["with"], "properties": {"with": {"$ref": "#/$defs/eventProperties", "title": "WithEvent", "description": "An event filter is a mechanism used to selectively process or handle events based on predefined criteria, such as event type, source, or specific attributes.", "minProperties": 1}, "correlate": {"type": "object", "title": "EventFilterCorrelate", "description": "A correlation is a link between events and data, established by mapping event attributes to specific data attributes, allowing for coordinated processing or handling based on event characteristics.", "additionalProperties": {"type": "object", "required": ["from"], "properties": {"from": {"type": "string", "title": "CorrelateFrom", "description": "A runtime expression used to extract the correlation value from the filtered event."}, "expect": {"type": "string", "title": "CorrelateExpect", "description": "A constant or a runtime expression, if any, used to determine whether or not the extracted correlation value matches expectations. If not set, the first extracted value will be used as the correlation's expectation."}}}}}, "description": "An event filter is a mechanism used to selectively process or handle events based on predefined criteria, such as event type, source, or specific attributes.", "unevaluatedProperties": false}, "oauth2Token": {"type": "object", "title": "OAuth2TokenDefinition", "required": ["token", "type"], "properties": {"type": {"type": "string", "title": "OAuth2TokenType", "description": "The type of the security token to use."}, "token": {"type": "string", "title": "OAuth2Token", "description": "The security token to use."}}, "description": "Represents an OAuth2 token.", "unevaluatedProperties": false}, "retryPolicy": {"type": "object", "title": "RetryPolicy", "properties": {"when": {"type": "string", "title": "RetryWhen", "description": "A runtime expression, if any, used to determine whether or not to retry running the task, in a given context."}, "delay": {"$ref": "#/$defs/duration", "title": "RetryDelay", "description": "The duration to wait between retry attempts."}, "limit": {"type": "object", "title": "RetryLimit", "properties": {"attempt": {"type": "object", "title": "RetryLimitAttempt", "properties": {"count": {"type": "integer", "title": "RetryLimitAttemptCount", "description": "The maximum amount of retry attempts, if any."}, "duration": {"$ref": "#/$defs/duration", "title": "RetryLimitAttemptDuration", "description": "The maximum duration for each retry attempt."}}, "unevaluatedProperties": false}, "duration": {"$ref": "#/$defs/duration", "title": "RetryLimitDuration", "description": "The duration limit, if any, for all retry attempts."}}, "description": "The retry limit, if any.", "unevaluatedProperties": false}, "jitter": {"type": "object", "title": "RetryPolicyJitter", "required": ["from", "to"], "properties": {"to": {"$ref": "#/$defs/duration", "title": "RetryPolicyJitterTo", "description": "The maximum duration of the jitter range."}, "from": {"$ref": "#/$defs/duration", "title": "RetryPolicyJitterFrom", "description": "The minimum duration of the jitter range."}}, "description": "The parameters, if any, that control the randomness or variability of the delay between retry attempts.", "unevaluatedProperties": false}, "backoff": {"type": "object", "oneOf": [{"title": "ConstantBackoff", "required": ["constant"], "properties": {"constant": {"type": "object", "description": "The definition of the constant backoff to use, if any."}}}, {"title": "ExponentialBackOff", "required": ["exponential"], "properties": {"exponential": {"type": "object", "description": "The definition of the exponential backoff to use, if any."}}}, {"title": "LinearBackoff", "required": ["linear"], "properties": {"linear": {"type": "object", "description": "The definition of the linear backoff to use, if any."}}}], "title": "RetryBackoff", "description": "The retry duration backoff.", "unevaluatedProperties": false}, "exceptWhen": {"type": "string", "title": "RetryExcepWhen", "description": "A runtime expression used to determine whether or not to retry running the task, in a given context."}}, "description": "Defines a retry policy.", "unevaluatedProperties": false}, "uriTemplate": {"anyOf": [{"type": "string", "title": "LiteralUriTemplate", "format": "uri-template", "pattern": "^[A-Za-z][A-Za-z0-9+\\\\-.]*://.*"}, {"type": "string", "title": "LiteralUri", "format": "uri", "pattern": "^[A-Za-z][A-Za-z0-9+\\\\-.]*://.*"}], "title": "UriTemplate"}, "flowDirective": {"anyOf": [{"enum": ["continue", "exit", "end"], "type": "string", "title": "FlowDirectiveEnum", "default": "continue"}, {"type": "string"}], "title": "FlowDirective", "description": "Represents different transition options for a workflow."}, "processResult": {"type": "object", "title": "ProcessResult", "required": ["code", "stdout", "stderr"], "properties": {"code": {"type": "integer", "title": "ProcessExitCode", "description": "The process's exit code."}, "stderr": {"type": "string", "title": "ProcessStandardError", "description": "The content of the process's STDERR."}, "stdout": {"type": "string", "title": "ProcessStandardOutput", "description": "The content of the process's STDOUT."}}, "description": "The object returned by a run task when its return type has been set 'all'.", "unevaluatedProperties": false}, "asyncApiServer": {"type": "object", "title": "AsyncApiServer", "required": ["name"], "properties": {"name": {"type": "string", "title": "AsyncApiServerName", "description": "The target server's name."}, "variables": {"type": "object", "title": "AsyncApiServerVariables", "description": "The target server's variables, if any."}}, "description": "Configures the target server of an AsyncAPI operation.", "unevaluatedProperties": false}, "eventProperties": {"type": "object", "title": "EventProperties", "properties": {"id": {"type": "string", "title": "EventId", "description": "The event's unique identifier."}, "data": {"anyOf": [{"$ref": "#/$defs/runtimeExpression"}, {}], "title": "EventData", "description": "The event's payload data"}, "time": {"oneOf": [{"type": "string", "title": "LiteralTime", "format": "date-time"}, {"$ref": "#/$defs/runtimeExpression"}], "title": "EventTime", "description": "When the event occured."}, "type": {"type": "string", "title": "EventType", "description": "This attribute contains a value describing the type of event related to the originating occurrence."}, "source": {"oneOf": [{"$ref": "#/$defs/uriTemplate"}, {"$ref": "#/$defs/runtimeExpression"}], "title": "EventSource", "description": "Identifies the context in which an event happened."}, "subject": {"type": "string", "title": "EventSubject", "description": "The subject of the event."}, "dataschema": {"oneOf": [{"$ref": "#/$defs/uriTemplate", "title": "LiteralDataSchema", "description": "The literal event data schema."}, {"$ref": "#/$defs/runtimeExpression", "title": "ExpressionDataSchema", "description": "An expression based event data schema."}], "title": "EventDataschema", "description": "The schema describing the event format."}, "datacontenttype": {"type": "string", "title": "EventDataContentType", "description": "Content type of data value. This attribute enables data to carry any type of content, whereby format and encoding might differ from that of the chosen event format."}}, "description": "Describes the properties of an event.", "additionalProperties": true}, "externalResource": {"type": "object", "title": "ExternalResource", "required": ["endpoint"], "properties": {"name": {"type": "string", "title": "ExternalResourceName", "description": "The name of the external resource, if any."}, "endpoint": {"$ref": "#/$defs/endpoint", "title": "ExternalResourceEndpoint", "description": "The endpoint of the external resource."}}, "description": "Represents an external resource.", "unevaluatedProperties": false}, "containerLifetime": {"if": {"properties": {"cleanup": {"const": "eventually"}}}, "else": {"not": {"required": ["after"]}}, "then": {"required": ["after"]}, "type": "object", "title": "ContainerLifetime", "required": ["cleanup"], "properties": {"after": {"$ref": "#/$defs/duration", "title": "ContainerLifetimeDuration", "description": "The duration after which to cleanup the container, in case the cleanup policy has been set to 'eventually'"}, "cleanup": {"enum": ["always", "never", "eventually"], "type": "string", "title": "ContainerCleanupPolicy", "default": "never", "description": "The container cleanup policy to use"}}, "description": "The configuration of a container's lifetime", "unevaluatedProperties": false}, "runtimeExpression": {"type": "string", "title": "RuntimeExpression", "pattern": "^\\\\s*\\\\$\\\\{.+\\\\}\\\\s*$", "description": "A runtime expression."}, "asyncApiSubscription": {"type": "object", "title": "AsyncApiSubscription", "required": ["consume"], "properties": {"filter": {"$ref": "#/$defs/runtimeExpression", "title": "AsyncApiSubscriptionCorrelation", "description": "A runtime expression, if any, used to filter consumed messages."}, "consume": {"$ref": "#/$defs/asyncApiMessageConsumptionPolicy", "title": "AsyncApiMessageConsumptionPolicy", "description": "An object used to configure the subscription's message consumption policy."}, "foreach": {"$ref": "#/$defs/subscriptionIterator", "title": "AsyncApiSubscriptionIterator", "description": "Configures the iterator, if any, for processing consumed messages(s)."}}, "description": "An object used to configure the subscription to messages consumed using the target operation.", "unevaluatedProperties": false}, "authenticationPolicy": {"type": "object", "oneOf": [{"title": "BasicAuthenticationPolicy", "required": ["basic"], "properties": {"basic": {"type": "object", "oneOf": [{"title": "BasicAuthenticationProperties", "required": ["username", "password"], "properties": {"password": {"type": "string", "description": "The password to use."}, "username": {"type": "string", "description": "The username to use."}}, "description": "Inline configuration of the basic authentication policy."}, {"$ref": "#/$defs/secretBasedAuthenticationPolicy", "title": "BasicAuthenticationPolicySecret", "description": "Secret based configuration of the basic authentication policy."}], "title": "BasicAuthenticationPolicyConfiguration", "description": "The configuration of the basic authentication policy.", "unevaluatedProperties": false}}, "description": "Use basic authentication."}, {"title": "BearerAuthenticationPolicy", "required": ["bearer"], "properties": {"bearer": {"type": "object", "oneOf": [{"title": "BearerAuthenticationProperties", "required": ["token"], "properties": {"token": {"type": "string", "description": "The bearer token to use."}}, "description": "Inline configuration of the bearer authentication policy."}, {"$ref": "#/$defs/secretBasedAuthenticationPolicy", "title": "BearerAuthenticationPolicySecret", "description": "Secret based configuration of the bearer authentication policy."}], "title": "BearerAuthenticationPolicyConfiguration", "description": "The configuration of the bearer authentication policy.", "unevaluatedProperties": false}}, "description": "Use bearer authentication."}, {"title": "DigestAuthenticationPolicy", "required": ["digest"], "properties": {"digest": {"type": "object", "oneOf": [{"title": "DigestAuthenticationProperties", "required": ["username", "password"], "properties": {"password": {"type": "string", "description": "The password to use."}, "username": {"type": "string", "description": "The username to use."}}, "description": "Inline configuration of the digest authentication policy."}, {"$ref": "#/$defs/secretBasedAuthenticationPolicy", "title": "DigestAuthenticationPolicySecret", "description": "Secret based configuration of the digest authentication policy."}], "title": "DigestAuthenticationPolicyConfiguration", "description": "The configuration of the digest authentication policy.", "unevaluatedProperties": false}}, "description": "Use digest authentication."}, {"title": "OAuth2AuthenticationPolicy", "required": ["oauth2"], "properties": {"oauth2": {"type": "object", "oneOf": [{"type": "object", "allOf": [{"$ref": "#/$defs/oauth2AuthenticationProperties"}, {"type": "object", "properties": {"endpoints": {"type": "object", "title": "OAuth2AuthenticationPropertiesEndpoints", "properties": {"token": {"type": "string", "title": "OAuth2TokenEndpoint", "format": "uri-template", "default": "/oauth2/token", "description": "The relative path to the token endpoint. Defaults to `/oauth2/token`."}, "revocation": {"type": "string", "title": "OAuth2RevocationEndpoint", "format": "uri-template", "default": "/oauth2/revoke", "description": "The relative path to the revocation endpoint. Defaults to `/oauth2/revoke`."}, "introspection": {"type": "string", "title": "OAuth2IntrospectionEndpoint", "format": "uri-template", "default": "/oauth2/introspect", "description": "The relative path to the introspection endpoint. Defaults to `/oauth2/introspect`."}}, "description": "The endpoint configurations for OAuth2."}}}], "title": "OAuth2ConnectAuthenticationProperties", "description": "The inline configuration of the OAuth2 authentication policy.", "unevaluatedProperties": false}, {"$ref": "#/$defs/secretBasedAuthenticationPolicy", "title": "OAuth2AuthenticationPolicySecret", "description": "Secret based configuration of the OAuth2 authentication policy."}], "title": "OAuth2AuthenticationPolicyConfiguration", "description": "The configuration of the OAuth2 authentication policy.", "unevaluatedProperties": false}}, "description": "Use OAuth2 authentication."}, {"title": "OpenIdConnectAuthenticationPolicy", "required": ["oidc"], "properties": {"oidc": {"type": "object", "oneOf": [{"$ref": "#/$defs/oauth2AuthenticationProperties", "title": "OpenIdConnectAuthenticationProperties", "description": "The inline configuration of the OpenIdConnect authentication policy.", "unevaluatedProperties": false}, {"$ref": "#/$defs/secretBasedAuthenticationPolicy", "title": "OpenIdConnectAuthenticationPolicySecret", "description": "Secret based configuration of the OpenIdConnect authentication policy."}], "title": "OpenIdConnectAuthenticationPolicyConfiguration", "description": "The configuration of the OpenIdConnect authentication policy.", "unevaluatedProperties": false}}, "description": "Use OpenIdConnect authentication."}], "title": "AuthenticationPolicy", "description": "Defines an authentication policy."}, "subscriptionIterator": {"type": "object", "title": "SubscriptionIterator", "properties": {"at": {"type": "string", "title": "SubscriptionIteratorIndex", "default": "index", "description": "The name of the variable used to store the index of the current item being enumerated."}, "do": {"$ref": "#/$defs/taskList", "title": "SubscriptionIteratorTasks", "description": "The tasks to perform for each consumed item."}, "item": {"type": "string", "title": "SubscriptionIteratorItem", "default": "item", "description": "The name of the variable used to store the current item being enumerated."}, "export": {"$ref": "#/$defs/export", "title": "SubscriptionIteratorExport", "description": "An object, if any, used to customize the content of the workflow context."}, "output": {"$ref": "#/$defs/output", "title": "SubscriptionIteratorOutput", "description": "An object, if any, used to customize the item's output and to document its schema."}}, "description": "Configures the iteration over each item (event or message) consumed by a subscription.", "unevaluatedProperties": false}, "asyncApiInboundMessage": {"type": "object", "allOf": [{"$ref": "#/$defs/asyncApiOutboundMessage"}], "title": "AsyncApiInboundMessage", "properties": {"correlationId": {"type": "string", "title": "AsyncApiMessageCorrelationId", "description": "The message's correlation id, if any."}}, "description": "Represents a message counsumed by an AsyncAPI subscription."}, "asyncApiOutboundMessage": {"type": "object", "title": "AsyncApiOutboundMessage", "properties": {"headers": {"type": "object", "title": "AsyncApiMessageHeaders", "description": "The message's headers, if any.", "additionalProperties": true}, "payload": {"type": "object", "title": "AsyncApiMessagePayload", "description": "The message's payload, if any.", "additionalProperties": true}}, "description": "An object used to configure the message to publish using the target operation.", "unevaluatedProperties": false}, "eventConsumptionStrategy": {"type": "object", "oneOf": [{"title": "AllEventConsumptionStrategy", "required": ["all"], "properties": {"all": {"type": "array", "items": {"$ref": "#/$defs/eventFilter"}, "title": "AllEventConsumptionStrategyConfiguration", "description": "A list containing all the events that must be consumed."}}}, {"title": "AnyEventConsumptionStrategy", "required": ["any"], "properties": {"any": {"type": "array", "items": {"$ref": "#/$defs/eventFilter"}, "title": "AnyEventConsumptionStrategyConfiguration", "description": "A list containing any of the events to consume."}, "until": {"oneOf": [{"type": "string", "title": "AnyEventUntilCondition", "description": "A runtime expression condition evaluated after consuming an event and which determines whether or not to continue listening."}, {"allOf": [{"$ref": "#/$defs/eventConsumptionStrategy", "description": "The strategy that defines the event(s) to consume to stop listening."}, {"properties": {"until": false}}], "title": "AnyEventUntilConsumed"}]}}}, {"title": "OneEventConsumptionStrategy", "required": ["one"], "properties": {"one": {"$ref": "#/$defs/eventFilter", "title": "OneEventConsumptionStrategyConfiguration", "description": "The single event to consume."}}}], "title": "EventConsumptionStrategy", "description": "Describe the event consumption strategy to adopt.", "unevaluatedProperties": false}, "oauth2AuthenticationProperties": {"type": "object", "title": "OAuth2AutenthicationData", "properties": {"actor": {"$ref": "#/$defs/oauth2Token", "title": "OAuth2AutenthicationDataActor", "description": "The security token that represents the identity of the acting party."}, "grant": {"enum": ["authorization_code", "client_credentials", "password", "refresh_token", "urn:ietf:params:oauth:grant-type:token-exchange"], "type": "string", "title": "OAuth2AutenthicationDataGrant", "description": "The grant type to use."}, "client": {"type": "object", "title": "OAuth2AutenthicationDataClient", "properties": {"id": {"type": "string", "title": "ClientId", "description": "The client id to use."}, "secret": {"type": "string", "title": "ClientSecret", "description": "The client secret to use, if any."}, "assertion": {"type": "string", "title": "ClientAssertion", "description": "A JWT containing a signed assertion with your application credentials."}, "authentication": {"enum": ["client_secret_basic", "client_secret_post", "client_secret_jwt", "private_key_jwt", "none"], "type": "string", "title": "ClientAuthentication", "default": "client_secret_post", "description": "The authentication method to use to authenticate the client."}}, "description": "The definition of an OAuth2 client.", "unevaluatedProperties": false}, "scopes": {"type": "array", "items": {"type": "string"}, "title": "OAuth2AutenthicationDataScopes", "description": "The scopes, if any, to request the token for."}, "issuers": {"type": "array", "items": {"type": "string"}, "title": "OAuth2Issuers", "description": "A list that contains that contains valid issuers that will be used to check against the issuer of generated tokens."}, "request": {"type": "object", "title": "OAuth2TokenRequest", "properties": {"encoding": {"enum": ["application/x-www-form-urlencoded", "application/json"], "type": "string", "title": "Oauth2TokenRequestEncoding", "default": "application/x-www-form-urlencoded"}}, "description": "The configuration of an OAuth2 token request"}, "subject": {"$ref": "#/$defs/oauth2Token", "title": "OAuth2AutenthicationDataSubject", "description": "The security token that represents the identity of the party on behalf of whom the request is being made."}, "password": {"type": "string", "title": "OAuth2AutenthicationDataPassword", "description": "The password to use. Used only if the grant type is Password."}, "username": {"type": "string", "title": "OAuth2AutenthicationDataUsername", "description": "The username to use. Used only if the grant type is Password."}, "audiences": {"type": "array", "items": {"type": "string"}, "title": "OAuth2AutenthicationDataAudiences", "description": "The audiences, if any, to request the token for."}, "authority": {"$ref": "#/$defs/uriTemplate", "title": "OAuth2AutenthicationDataAuthority", "description": "The URI that references the OAuth2 authority to use."}}, "description": "Inline configuration of the OAuth2 authentication policy."}, "secretBasedAuthenticationPolicy": {"type": "object", "title": "SecretBasedAuthenticationPolicy", "required": ["use"], "properties": {"use": {"type": "string", "title": "SecretBasedAuthenticationPolicyName", "minLength": 1, "description": "The name of the authentication policy to use."}}, "description": "Represents an authentication policy based on secrets.", "unevaluatedProperties": false}, "asyncApiMessageConsumptionPolicy": {"type": "object", "oneOf": [{"title": "AsyncApiMessageConsumptionPolicyAmount", "required": ["amount"], "properties": {"amount": {"type": "integer", "description": "The amount of (filtered) messages to consume before disposing of the subscription."}}}, {"title": "AsyncApiMessageConsumptionPolicyWhile", "required": ["while"], "properties": {"while": {"$ref": "#/$defs/runtimeExpression", "description": "A runtime expression evaluated after each consumed (filtered) message to decide if message consumption should continue."}}}, {"title": "AsyncApiMessageConsumptionPolicyUntil", "required": ["until"], "properties": {"until": {"$ref": "#/$defs/runtimeExpression", "description": "A runtime expression evaluated before each consumed (filtered) message to decide if message consumption should continue."}}}], "title": "AsyncApiMessageConsumptionPolicy", "properties": {"for": {"$ref": "#/$defs/duration", "title": "AsyncApiMessageConsumptionPolicyFor", "description": "Specifies the time period over which messages will be consumed."}}, "description": "An object used to configure a subscription's message consumption policy.", "unevaluatedProperties": false}, "referenceableAuthenticationPolicy": {"type": "object", "oneOf": [{"title": "AuthenticationPolicyReference", "required": ["use"], "properties": {"use": {"type": "string", "title": "ReferenceableAuthenticationPolicyName", "minLength": 1, "description": "The name of the authentication policy to use."}}, "description": "The reference of the authentication policy to use."}, {"$ref": "#/$defs/authenticationPolicy"}], "title": "ReferenceableAuthenticationPolicy", "description": "Represents a referenceable authentication policy.", "unevaluatedProperties": false}}, "$schema": "https://json-schema.org/draft/2020-12/schema", "required": ["document", "do"], "properties": {"do": {"$ref": "#/$defs/taskList", "title": "Do", "description": "Defines the task(s) the workflow must perform."}, "use": {"type": "object", "title": "Use", "properties": {"errors": {"type": "object", "title": "UseErrors", "description": "The workflow's reusable errors.", "additionalProperties": {"$ref": "#/$defs/error"}}, "retries": {"type": "object", "title": "UseRetries", "description": "The workflow's reusable retry policies.", "additionalProperties": {"$ref": "#/$defs/retryPolicy"}}, "secrets": {"type": "array", "items": {"type": "string", "description": "The workflow's secrets."}, "title": "UseSecrets", "description": "The workflow's reusable secrets."}, "catalogs": {"type": "object", "title": "UseCatalogs", "description": "The workflow's reusable catalogs.", "additionalProperties": {"$ref": "#/$defs/catalog"}}, "timeouts": {"type": "object", "title": "UseTimeouts", "description": "The workflow's reusable timeouts.", "additionalProperties": {"$ref": "#/$defs/timeout"}}, "functions": {"type": "object", "title": "UseFunctions", "description": "The workflow's reusable functions.", "additionalProperties": {"$ref": "#/$defs/task"}}, "extensions": {"type": "array", "items": {"type": "object", "title": "ExtensionItem", "maxProperties": 1, "minProperties": 1, "additionalProperties": {"$ref": "#/$defs/extension"}}, "title": "UseExtensions", "description": "The workflow's extensions."}, "authentications": {"type": "object", "title": "UseAuthentications", "description": "The workflow's reusable authentication policies.", "additionalProperties": {"$ref": "#/$defs/authenticationPolicy"}}}, "description": "Defines the workflow's reusable components.", "unevaluatedProperties": false}, "input": {"$ref": "#/$defs/input", "title": "Input", "description": "Configures the workflow's input."}, "output": {"$ref": "#/$defs/output", "title": "Output", "description": "Configures the workflow's output."}, "timeout": {"oneOf": [{"$ref": "#/$defs/timeout", "title": "TimeoutDefinition", "description": "The workflow's timeout configuration, if any."}, {"type": "string", "title": "TimeoutReference", "description": "The name of the workflow's timeout, if any."}], "title": "DoTimeout"}, "document": {"type": "object", "title": "Document", "required": ["dsl", "namespace", "name", "version"], "properties": {"dsl": {"type": "string", "title": "WorkflowDSL", "pattern": "^(0|[1-9]\\\\d*)\\\\.(0|[1-9]\\\\d*)\\\\.(0|[1-9]\\\\d*)(?:-((?:0|[1-9]\\\\d*|\\\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\\\.(?:0|[1-9]\\\\d*|\\\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\\\+([0-9a-zA-Z-]+(?:\\\\.[0-9a-zA-Z-]+)*))?$", "description": "The version of the DSL used by the workflow."}, "name": {"type": "string", "title": "WorkflowName", "pattern": "^[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$", "description": "The workflow's name."}, "tags": {"type": "object", "title": "WorkflowTags", "description": "A key/value mapping of the workflow's tags, if any.", "additionalProperties": true}, "title": {"type": "string", "title": "WorkflowTitle", "description": "The workflow's title."}, "summary": {"type": "string", "title": "WorkflowSummary", "description": "The workflow's Markdown summary."}, "version": {"type": "string", "title": "WorkflowVersion", "pattern": "^(0|[1-9]\\\\d*)\\\\.(0|[1-9]\\\\d*)\\\\.(0|[1-9]\\\\d*)(?:-((?:0|[1-9]\\\\d*|\\\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\\\.(?:0|[1-9]\\\\d*|\\\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\\\+([0-9a-zA-Z-]+(?:\\\\.[0-9a-zA-Z-]+)*))?$", "description": "The workflow's semantic version."}, "metadata": {"type": "object", "title": "WorkflowMetadata", "description": "Holds additional information about the workflow.", "additionalProperties": true}, "namespace": {"type": "string", "title": "WorkflowNamespace", "pattern": "^[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$", "description": "The workflow's namespace."}}, "description": "Documents the workflow.", "unevaluatedProperties": false}, "schedule": {"type": "object", "title": "Schedule", "properties": {"on": {"$ref": "#/$defs/eventConsumptionStrategy", "title": "ScheduleOn", "description": "Specifies the events that trigger the workflow execution."}, "cron": {"type": "string", "title": "ScheduleCron", "description": "Specifies the schedule using a cron expression, e.g., '0 0 * * *' for daily at midnight."}, "after": {"$ref": "#/$defs/duration", "title": "ScheduleAfter", "description": "Specifies a delay duration that the workflow must wait before starting again after it completes."}, "every": {"$ref": "#/$defs/duration", "title": "ScheduleEvery", "description": "Specifies the duration of the interval at which the workflow should be executed."}}, "description": "Schedules the workflow.", "unevaluatedProperties": false}}, "description": "Serverless Workflow DSL - Workflow Schema."}	2025-04-26 12:24:43.81+00	2025-04-26 12:24:43.81+00
6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	PatientBase	1.0.0	857d2613-f37f-4c83-8e7e-cb7c906754f3	\N	{"$id": "https://hospice-os.org/schemas/PatientBase", "type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["resourceType", "id", "firstName", "lastName", "dateOfBirth"], "properties": {"id": {"type": "string"}, "email": {"type": "string", "format": "email", "required": ["test"], "properties": {"test": {"type": "object", "required": ["fffff"], "properties": {"fffff": {"type": "string"}}}}}, "branch": {"type": "object", "required": ["resourceType", "id"], "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"type": "string", "const": "Branch"}}}, "gender": {"type": "string"}, "orders": {"type": "array", "items": {"$ref": "#/definitions/OrderReference"}}, "status": {"enum": ["Active", "Discharged", "Deceased"], "type": "string", "default": "Active"}, "address": {"type": "string"}, "nextIDG": {"type": "string", "format": "date-time"}, "lastName": {"type": "string"}, "allergies": {"type": "array", "items": {"type": "string"}}, "firstName": {"type": "string"}, "insurance": {"type": "array", "items": {"$ref": "#/definitions/Insurance"}}, "planOfCare": {"$ref": "#/definitions/PlanOfCareReference"}, "visitNotes": {"type": "array", "items": {"$ref": "#/definitions/NoteReference"}}, "dateOfBirth": {"type": "string"}, "medications": {"type": "array", "items": {"$ref": "b30b1e44-c7e2-2b5c-90cd-49dfda514e50"}}, "phoneNumber": {"type": "string"}, "resourceType": {"type": "string", "const": "Patient"}, "admissionDate": {"type": "string"}, "agencyActions": {"type": "array", "items": {"$ref": "e9044eaa-3e1b-4dfa-b863-4cd0aa9a1a9b"}}, "benefitPeriod": {"type": "string", "default": 1}, "timeLineItems": {"type": "array", "items": {"$ref": "fc3b5899-9f33-4632-bb5f-876566c19aa9"}}, "primaryDiagnosis": {"type": "string"}, "complianceTimeline": {"type": "object", "properties": {"timeLineItems": {"type": "array", "items": {"type": "object", "properties": {"started": {"type": "string", "format": "date-time"}, "blockers": {"type": "array", "items": {"type": "string"}, "description": "things that are blocking this from being completed"}, "completed": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "displayName": {"type": "string"}}}}}, "description": ""}, "secondaryDiagnoses": {"type": "array", "items": {"type": "string"}}, "medicalRecordNumber": {"type": "string"}, "emergencyContactName": {"type": "string"}, "emergencyContactPhone": {"type": "string"}}, "definitions": {"Insurance": {"type": "object", "required": ["provider", "number"], "properties": {"number": {"type": "string"}, "provider": {"type": "string"}}}, "NoteReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"const": "Note"}}}, "OrderReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"const": "Order"}}}, "PlanOfCareReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "resourceType": {"const": "PlanOfCare"}}}}}	2025-04-18 14:35:50.054+00	2025-04-28 17:24:04.618+00
\.


--
-- TOC entry 4355 (class 0 OID 16495)
-- Dependencies: 219
-- Data for Name: ui_schemas; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ui_schemas (id, name, description, schema_id, content, created_at, updated_at) FROM stdin;
aec6d817-64ab-4978-a395-804d910e6499	patient-chart		6cb5c6e8-22c2-4df5-beb4-5cf2509e2673	{"id": {"ui:widget": "hidden"}, "agencyId": {"ui:widget": "hidden"}, "schemaId": {"ui:widget": "hidden"}, "ui:order": ["admissionDate", "firstName", "lastName", "primaryDiagnosis", "secondaryDiagnosis", "email", "phoneNumber", "dateOfBirth", "gender", "address", "status", "medicalRecordNumber", "emergencyContactName", "emergencyContactPhone", "allergies", "insurance", "planOfCare", "visitNotes", "orders"], "planOfCare": {"ui:widget": "resourceDisplay", "ui:options": {"lookupBy": "id", "hideFields": ["id", "resourceType", "schemaId", "agencyId", "status"], "resourceType": "PlanOfCare"}}, "visitNotes": {"ui:widget": "resourceDisplay", "ui:options": {"lookupBy": "id", "resourceType": "Note", "displayFields": ["title", "summary"]}}, "medications": {"ui:widget": "table", "ui:options": {"columns": ["name", "route", "dosage", "frequency", "indication"]}}, "resourceType": {"ui:widget": "hidden"}, "agencyActions": {"ui:widget": "hidden"}, "timeLineItems": {"ui:widget": "hidden"}}	2025-04-19 02:44:30.671+00	2025-04-24 14:47:13.151+00
106dbb71-6e83-4e3c-b69d-0358a614ca41	showNoteOrder		fb016bdd-1d9e-489a-b05e-08bd7fcb188b	{"noteCreated": {"ui:widget": "resourceDisplay", "ui:options": {"lookupBy": "id", "titleField": "title", "collapsible": false, "hideFields": ["id", "resourceType", "agencyId", "authorId", "updatedAt", "schemaId", "title", "status"], "resourceType": "Note"}}, "orderCreated": {"ui:widget": "resourceDisplay", "ui:options": {"lookupBy": "id", "titleField": "title", "collapsible": false, "hideFields": ["id", "resourceType", "agencyId", "authorId", "updatedAt", "schemaId", "title", "status"], "resourceType": "Note"}}, "displayFields": ["noteCreated", "orderCreated"]}	2025-04-25 02:22:56.494+00	2025-04-25 02:56:26.287+00
\.


--
-- TOC entry 4356 (class 0 OID 16500)
-- Dependencies: 220
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, resource_type, schema_id, agency_id, stytch_id, content, created_at, updated_at) FROM stdin;
0cc674da-0472-4bc5-96bb-6d594e045c03	User	73f3953a-4d14-459e-b63b-145bef1fe1fe	857d2613-f37f-4c83-8e7e-cb7c906754f3	member-test-4586a23b-6fc2-4d18-9592-276a5fb953cf	{"id": "0cc674da-0472-4bc5-96bb-6d594e045c03", "role": "Admin", "email": "<EMAIL>", "agencyId": "857d2613-f37f-4c83-8e7e-cb7c906754f3", "resourceType": "User"}	2025-04-18 16:21:17.577721+00	2025-04-18 16:21:17.577721+00
dc46f069-c262-42d0-b0bc-7bbded82f57d	User	73f3953a-4d14-459e-b63b-145bef1fe1fe	bd6e787b-b665-4f7b-9b32-1df1797620cd	member-test-bfc9e13f-f472-498d-8aeb-23f2d9d3603f	{"id": "dc46f069-c262-42d0-b0bc-7bbded82f57d", "role": "Admin", "email": "<EMAIL>", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "resourceType": "User"}	2025-04-27 00:30:38.086245+00	2025-04-27 00:30:38.086245+00
15086012-3228-43b0-b238-76f4d05892db	User	73f3953a-4d14-459e-b63b-145bef1fe1fe	bd6e787b-b665-4f7b-9b32-1df1797620cd	member-test-f86368dd-a5f0-43ef-92d0-1c98b9a5d5b5	{"id": "15086012-3228-43b0-b238-76f4d05892db", "name": "Steve Jobs", "role": "Admin", "email": "<EMAIL>", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "resourceType": "User"}	2025-04-27 00:34:24.413587+00	2025-04-27 00:34:24.413587+00
669906c9-ba40-4438-a92d-335008665a27	User	73f3953a-4d14-459e-b63b-145bef1fe1fe	bd6e787b-b665-4f7b-9b32-1df1797620cd	member-test-a1dc91ef-8c3a-415c-b726-51cfbfa5d08a	{"id": "669906c9-ba40-4438-a92d-335008665a27", "name": "HospiceOS Test User", "role": "Nurse", "email": "<EMAIL>", "agencyId": "bd6e787b-b665-4f7b-9b32-1df1797620cd", "resourceType": "User"}	2025-04-28 14:57:46.017855+00	2025-04-28 14:57:46.017855+00
1cc674da-0472-4bc5-96bb-6d594e045c25	User	73f3953a-4d14-459e-b63b-145bef1fe1fe	857d2613-f37f-4c83-8e7e-cb7c906754f3	member-test-75cb8526-baca-4cf8-87db-739ac9464ed9	{"id": "1cc674da-0472-4bc5-96bb-6d594e045c25", "role": "Admin", "email": "<EMAIL>", "agencyId": "857d2613-f37f-4c83-8e7e-cb7c906754f3", "resourceType": "User"}	2025-04-18 16:21:17.577721+00	2025-04-18 16:21:17.577721+00
\.


--
-- TOC entry 4183 (class 2606 OID 16510)
-- Name: agencies agencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.agencies
    ADD CONSTRAINT agencies_pkey PRIMARY KEY (id);


--
-- TOC entry 4187 (class 2606 OID 16512)
-- Name: resources resources_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.resources
    ADD CONSTRAINT resources_pkey PRIMARY KEY (id);


--
-- TOC entry 4191 (class 2606 OID 16514)
-- Name: schemas schemas_name_version_agency_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schemas
    ADD CONSTRAINT schemas_name_version_agency_id_key UNIQUE (name, version, agency_id);


--
-- TOC entry 4193 (class 2606 OID 16516)
-- Name: schemas schemas_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schemas
    ADD CONSTRAINT schemas_pkey PRIMARY KEY (id);


--
-- TOC entry 4197 (class 2606 OID 16518)
-- Name: ui_schemas ui_schemas_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ui_schemas
    ADD CONSTRAINT ui_schemas_pkey PRIMARY KEY (id);


--
-- TOC entry 4202 (class 2606 OID 16520)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 4184 (class 1259 OID 16521)
-- Name: idx_resources_agency_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_resources_agency_id ON public.resources USING btree (agency_id);


--
-- TOC entry 4185 (class 1259 OID 16522)
-- Name: idx_resources_resource_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_resources_resource_type ON public.resources USING btree (resource_type);


--
-- TOC entry 4188 (class 1259 OID 16523)
-- Name: idx_schemas_agency_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schemas_agency_id ON public.schemas USING btree (agency_id);


--
-- TOC entry 4189 (class 1259 OID 16524)
-- Name: idx_schemas_name_version; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schemas_name_version ON public.schemas USING btree (name, version);


--
-- TOC entry 4194 (class 1259 OID 16525)
-- Name: idx_ui_schemas_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ui_schemas_name ON public.ui_schemas USING btree (name);


--
-- TOC entry 4195 (class 1259 OID 16526)
-- Name: idx_ui_schemas_schema_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ui_schemas_schema_id ON public.ui_schemas USING btree (schema_id);


--
-- TOC entry 4198 (class 1259 OID 16527)
-- Name: idx_users_agency_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_agency_id ON public.users USING btree (agency_id);


--
-- TOC entry 4199 (class 1259 OID 16528)
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_email ON public.users USING btree (((content ->> 'email'::text)));


--
-- TOC entry 4200 (class 1259 OID 16529)
-- Name: idx_users_resource_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_resource_type ON public.users USING btree (resource_type);


--
-- TOC entry 4203 (class 2606 OID 16530)
-- Name: resources resources_agency_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.resources
    ADD CONSTRAINT resources_agency_id_fkey FOREIGN KEY (agency_id) REFERENCES public.agencies(id);


--
-- TOC entry 4204 (class 2606 OID 16535)
-- Name: resources resources_schema_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.resources
    ADD CONSTRAINT resources_schema_id_fkey FOREIGN KEY (schema_id) REFERENCES public.schemas(id);


--
-- TOC entry 4205 (class 2606 OID 16540)
-- Name: schemas schemas_agency_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schemas
    ADD CONSTRAINT schemas_agency_id_fkey FOREIGN KEY (agency_id) REFERENCES public.agencies(id);


--
-- TOC entry 4206 (class 2606 OID 16545)
-- Name: ui_schemas ui_schemas_schema_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ui_schemas
    ADD CONSTRAINT ui_schemas_schema_id_fkey FOREIGN KEY (schema_id) REFERENCES public.schemas(id) ON DELETE CASCADE;


--
-- TOC entry 4207 (class 2606 OID 16550)
-- Name: users users_agency_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_agency_id_fkey FOREIGN KEY (agency_id) REFERENCES public.agencies(id);


--
-- TOC entry 4208 (class 2606 OID 16555)
-- Name: users users_schema_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_schema_id_fkey FOREIGN KEY (schema_id) REFERENCES public.schemas(id);


-- Completed on 2025-04-29 11:20:54 EDT

--
-- PostgreSQL database dump complete
--

