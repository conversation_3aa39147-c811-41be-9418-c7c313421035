import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);

/**
 * Runs all migrations that haven't been applied yet
 */
export async function runMigrations(pool: Pool): Promise<void> {
  console.log('Checking for pending migrations...');
  
  // Ensure migrations table exists
  await ensureMigrationsTable(pool);
  
  // Get list of applied migrations
  const appliedMigrations = await getAppliedMigrations(pool);
  
  // Get all migration files
  const migrationsDir = path.join(__dirname);
  const files = await readdir(migrationsDir);
  const migrationFiles = files
    .filter(file => file.endsWith('.sql'))
    .sort(); // Ensure alphabetical order (e.g., 001_, 002_, etc.)
  
  // Run migrations that haven't been applied
  for (const migrationFile of migrationFiles) {
    if (!appliedMigrations.includes(migrationFile)) {
      console.log(`Applying migration: ${migrationFile}`);
      try {
        const filePath = path.join(migrationsDir, migrationFile);
        const sql = await readFile(filePath, 'utf8');
        
        // Start a transaction for the migration
        const client = await pool.connect();
        try {
          await client.query('BEGIN');
          
          // Execute the migration
          await client.query(sql);
          
          // Record the migration
          await client.query(
            'INSERT INTO public.migrations (name) VALUES ($1)',
            [migrationFile]
          );
          
          await client.query('COMMIT');
          console.log(`Migration applied successfully: ${migrationFile}`);
        } catch (error) {
          await client.query('ROLLBACK');
          console.error(`Error applying migration ${migrationFile}:`, error);
          throw error; // Re-throw to stop migration process
        } finally {
          client.release();
        }
      } catch (error) {
        console.error(`Failed to apply migration ${migrationFile}. Stopping migration process.`);
        throw error;
      }
    } else {
      console.log(`Migration already applied: ${migrationFile}`);
    }
  }
  
  console.log('All migrations applied successfully.');
}

/**
 * Ensures the migrations table exists
 */
async function ensureMigrationsTable(pool: Pool): Promise<void> {
  try {
    // Check if migrations table exists
    const { rows } = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'migrations'
      );
    `);
    
    const tableExists = rows[0].exists;
    
    // If migrations table doesn't exist, create it
    // This should normally not happen as it's part of initial schema
    if (!tableExists) {
      console.log('Migrations table does not exist. Creating it...');
      await pool.query(`
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      `);
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS public.migrations (
          id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
          name character varying(255) NOT NULL,
          applied_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT migrations_name_check CHECK (((name)::text <> ''::text))
        );
      `);
      console.log('Migrations table created.');
    }
  } catch (error) {
    console.error('Error ensuring migrations table:', error);
    throw error;
  }
}

/**
 * Gets list of migrations that have already been applied
 */
async function getAppliedMigrations(pool: Pool): Promise<string[]> {
  try {
    const { rows } = await pool.query('SELECT name FROM public.migrations ORDER BY applied_at ASC');
    return rows.map(row => row.name);
  } catch (error) {
    console.error('Error fetching applied migrations:', error);
    throw error;
  }
} 