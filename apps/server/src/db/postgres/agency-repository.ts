import { Pool } from 'pg';
import { uuidv7 } from 'uuidv7';
import { AgencyRepository } from '../interfaces';

export class PostgresAgencyRepository implements AgencyRepository {
  constructor(private pool: Pool) { }

  async createAgency(name: string, stytchOrganizationId?: string): Promise<{ id: string; name: string; stytchOrganizationId?: string }> {
    const id = uuidv7();
    const now = new Date();

    const query = `
      INSERT INTO agencies (id, name, stytch_organization_id, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $4)
      RETURNING id, name, stytch_organization_id as "stytchOrganizationId"
    `;

    const result = await this.pool.query(query, [id, name, stytchOrganizationId, now]);

    return result.rows[0];
  }

  async getAgency(id: string): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const query = `
      SELECT id, name, stytch_organization_id as "stytchOrganizationId"
      FROM agencies
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];
  }

  async listAgencies(): Promise<{ id: string; name: string; stytchOrganizationId?: string }[]> {
    const query = `
      SELECT id, name, stytch_organization_id as "stytchOrganizationId"
      FROM agencies
      ORDER BY name
    `;

    const result = await this.pool.query(query);

    return result.rows;
  }

  async updateAgency(id: string, name: string, stytchOrganizationId?: string): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const now = new Date();

    // First, check if stytchOrganizationId is provided
    if (stytchOrganizationId !== undefined) {
      const query = `
        UPDATE agencies
        SET name = $1, stytch_organization_id = $2, updated_at = $3
        WHERE id = $4
        RETURNING id, name, stytch_organization_id as "stytchOrganizationId"
      `;

      const result = await this.pool.query(query, [name, stytchOrganizationId, now, id]);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } else {
      // If stytchOrganizationId is not provided, don't update it
      const query = `
        UPDATE agencies
        SET name = $1, updated_at = $2
        WHERE id = $3
        RETURNING id, name, stytch_organization_id as "stytchOrganizationId"
      `;

      const result = await this.pool.query(query, [name, now, id]);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    }
  }

  async deleteAgency(id: string): Promise<boolean> {
    const query = `
      DELETE FROM agencies
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }
}
