import { Pool } from "pg";
import { uuidv7 } from "uuidv7";
import { ResourceRepository } from "../interfaces";
import { BaseResource } from "../../services/resource-events";

// Define a type for resources that includes required fields
interface ResourceFields {
  id?: string;
  resourceType: string;
  schemaId: string;
  agencyId: string;
  createdAt: string;
  updatedAt: string;
}

export class PostgresResourceRepository implements ResourceRepository {
  constructor(private pool: Pool) {}

  async createResource<T extends Record<string, unknown> & ResourceFields>(
    resource: T,
    schemaId: string,
    agencyId: string,
  ): Promise<T> {
    // Ensure the resource has an ID
    if (!resource.id) {
      resource.id = uuidv7();
    }

    resource.schemaId = schemaId;
    resource.agencyId = agencyId;

    const query = `
      INSERT INTO resources (id, resource_type, schema_id, agency_id, content,
        created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id
    `;

    await this.pool.query(query, [
      resource.id,
      resource.resourceType,
      schemaId,
      agencyId,
      JSON.stringify(resource),
      resource.createdAt || new Date().toISOString(),
      resource.updatedAt || new Date().toISOString(),
    ]);

    return resource;
  }

  async readResource<T extends Record<string, unknown> & ResourceFields>(
    id: string,
  ): Promise<T | null> {
    const query = `
      SELECT id, resource_type, schema_id, agency_id, created_at, updated_at, content
      FROM resources
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    const resource = result.rows[0].content as T;
    resource.schemaId = result.rows[0].schema_id;
    resource.agencyId = result.rows[0].agency_id;
    resource.createdAt = result.rows[0].created_at;
    resource.updatedAt = result.rows[0].updated_at;
    return resource;
  }

  async updateResource<T extends BaseResource>(
    id: string,
    resource: T,
    schemaId: string,
  ): Promise<T | null> {
    // Ensure the resource has the correct ID
    if (resource.id !== id) {
      throw new Error("Resource ID must match the ID in the request");
    }

    const query = `
      UPDATE resources
      SET content = $1, schema_id = $2, updated_at = $3
      WHERE id = $4
      RETURNING id
    `;

    const result = await this.pool.query(query, [
      JSON.stringify(resource),
      schemaId,
      resource.updatedAt,
      id,
    ]);

    if (result.rows.length === 0) {
      return null;
    }

    return resource;
  }

  async deleteResource(id: string): Promise<boolean> {
    const query = `
      DELETE FROM resources
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async queryResourcesByType<T extends Record<string, unknown>>(
    resourceType: string,
    agencyId?: string,
    jsonPathFilters?: Record<string, unknown>,
    fields?: string[],
    limit?: number,
  ): Promise<T[]> {
    // Determine what to select based on fields parameter
    let selectClause = "r.content";

    // If fields are specified, construct a JSON object with only those fields
    if (fields && fields.length > 0) {
      // Always include required fields
      const requiredFields = ["id", "resourceType", "schemaId", "agencyId"];
      const allFields = [...new Set([...requiredFields, ...fields])];

      // Build a JSON object with only the specified fields
      selectClause = "jsonb_build_object(";
      allFields.forEach((field, index) => {
        // Add comma if not the first field
        if (index > 0) {
          selectClause += ", ";
        }

        // Add the field name and its value from the content
        selectClause += `'${field}', r.content->'${field}'`;
      });
      selectClause += ")";
    }

    let fromClause = "resources";
    if (resourceType === "User") {
      fromClause = "users";
    }

    let query = `
      SELECT ${selectClause} as content, a.name as agency_name
      FROM ${fromClause} r
      JOIN agencies a on a.id = r.agency_id
    `;

    const params: any[] = [];
    let paramIndex = 1;
    let whereClauseAdded = false;

    // Add agency filter
    if (agencyId) {
      query += `
        WHERE r.agency_id = $${paramIndex}
      `;
      params.push(agencyId);
      paramIndex++;
      whereClauseAdded = true;
    }

    // Add resource type filter
    if (resourceType !== "all") {
      if (whereClauseAdded) {
        query += `
          AND r.resource_type = $${paramIndex}
        `;
      } else {
        query += `
          WHERE r.resource_type = $${paramIndex}
        `;
        whereClauseAdded = true;
      }
      params.push(resourceType);
      paramIndex++;
    }

    // Add JSONB path filters
    if (jsonPathFilters && Object.keys(jsonPathFilters).length > 0) {
      for (const [path, value] of Object.entries(jsonPathFilters)) {
        // Split path on dots to handle nested fields
        const pathParts = path.split(".");

        // Build the JSONB path expression
        let jsonbPath = "r.content";
        for (let i = 0; i < pathParts.length - 1; i++) {
          jsonbPath += `->'${pathParts[i]}'`;
        }
        // Add the final ->> operator for the last part
        jsonbPath += `->>'${pathParts[pathParts.length - 1]}'`;

        if (Array.isArray(value)) {
          // Use = ANY($param) for array values
          if (whereClauseAdded) {
            query += `
              AND ${jsonbPath} = ANY($${paramIndex}::text[])
            `;
          } else {
            query += `
              WHERE ${jsonbPath} = ANY($${paramIndex}::text[])
            `;
            whereClauseAdded = true;
          }
          params.push((value as any[]).map(String));
        } else {
          // Use = $param for single values
          if (whereClauseAdded) {
            query += `
              AND ${jsonbPath} = $${paramIndex}
            `;
          } else {
            query += `
              WHERE ${jsonbPath} = $${paramIndex}
            `;
            whereClauseAdded = true;
          }
          params.push(value as string);
        }
        paramIndex++;
      }
    }

    query += " ORDER BY r.created_at DESC";

    // Add limit if specified
    if (limit) {
      query += ` LIMIT ${limit}`;
    }

    const result = await this.pool.query(query, params);

    return result.rows.map(
      (row) => ({ ...row.content, agencyName: row.agency_name }) as T,
    );
  }
}
