import { Pool } from 'pg';
import { uuidv7 } from 'uuidv7';
import { Schema, JSONSchema, SchemaRepository } from '../interfaces';
const SYSTEM_ACCOUNT_ID = '857d2613-f37f-4c83-8e7e-cb7c906754f3';


export class PostgresSchemaRepository implements SchemaRepository {
  constructor(private pool: Pool) { }

  async createSchema(name: string, version: string, agencyId: string, schema: JSONSchema, baseSchemaId?: string): Promise<Schema> {
    const id = uuidv7();
    const now = new Date();

    const query = `
      INSERT INTO schemas (id, name, version, agency_id, base_schema_id, schema, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
      RETURNING id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
    `;

    const result = await this.pool.query(query, [
      id,
      name,
      version,
      agencyId,
      baseSchemaId || null,
      JSON.stringify(schema),
      now
    ]);

    return result.rows[0];
  }

  async updateSchema(id: string, version: string, schema: JSONSchema): Promise<Schema | null> {
    const now = new Date();

    const query = `
      UPDATE schemas
      SET version = $1, schema = $2, updated_at = $3
      WHERE id = $4
      RETURNING id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
    `;

    const result = await this.pool.query(query, [
      version,
      JSON.stringify(schema),
      now,
      id
    ]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];
  }

  async getSchema(id: string): Promise<Schema | null> {

    const query = `
      SELECT id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
      FROM schemas
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];
  }

  async getSchemaByName(name: string, version: string, agencyId: string): Promise<Schema | null> {
    const query = `
      SELECT id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
      FROM schemas
      WHERE name = $1 AND version = $2 AND agency_id = $3
    `;

    const result = await this.pool.query(query, [
      name,
      version,
      agencyId
    ]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];
  }

  async listSchemas(agencyId: string): Promise<Schema[]> {
    const query = `
      SELECT id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
      FROM schemas
      WHERE (agency_id = $1 OR agency_id = $2)
      ORDER BY name, version DESC
    `;

    const result = await this.pool.query(query, [agencyId, SYSTEM_ACCOUNT_ID]);

    return result.rows;
  }

  async listAllSchemas(): Promise<Schema[]> {
    const query = `
      SELECT id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
      FROM schemas
      ORDER BY name, version DESC
    `;

    const result = await this.pool.query(query);

    return result.rows;
  }

  async listBaseSchemas(): Promise<Schema[]> {
    const query = `
      SELECT id, name, version, agency_id as "agencyId", base_schema_id as "baseSchemaId", schema, created_at as "createdAt", updated_at as "updatedAt"
      FROM schemas
      WHERE base_schema_id IS NULL
      ORDER BY name, version DESC
    `;

    const result = await this.pool.query(query);

    return result.rows;
  }
}
