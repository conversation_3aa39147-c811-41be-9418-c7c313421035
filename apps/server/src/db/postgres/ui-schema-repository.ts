import { Pool } from 'pg';
import { uuidv7 } from 'uuidv7';
import { UISchema, UISchemaContent, UISchemaRepository } from '../interfaces';

export class PostgresUISchemaRepository implements UISchemaRepository {
  constructor(private pool: Pool) { }

  async createUISchema(name: string, description: string, schemaId: string, content: UISchemaContent): Promise<UISchema> {
    const id = uuidv7();
    const now = new Date();

    const query = `
      INSERT INTO ui_schemas (id, name, description, schema_id, content, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $6)
      RETURNING id, name, description, schema_id as "schemaId", content, created_at as "createdAt", updated_at as "updatedAt"
    `;

    const result = await this.pool.query(query, [
      id,
      name,
      description,
      schemaId,
      JSON.stringify(content),
      now
    ]);

    return result.rows[0];
  }

  async updateUISchema(id: string, name: string, description: string, content: UISchemaContent): Promise<UISchema | null> {
    const now = new Date();

    const query = `
      UPDATE ui_schemas
      SET name = $1, description = $2, content = $3, updated_at = $4
      WHERE id = $5
      RETURNING id, name, description, schema_id as "schemaId", content, created_at as "createdAt", updated_at as "updatedAt"
    `;

    const result = await this.pool.query(query, [
      name,
      description,
      JSON.stringify(content),
      now,
      id
    ]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];
  }

  async getUISchema(id: string): Promise<UISchema | null> {
    const query = `
      SELECT id, name, description, schema_id as "schemaId", content, created_at as "createdAt", updated_at as "updatedAt"
      FROM ui_schemas
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];
  }

  async listUISchemasBySchema(schemaId: string): Promise<UISchema[]> {
    const query = `
      SELECT id, name, description, schema_id as "schemaId", content, created_at as "createdAt", updated_at as "updatedAt"
      FROM ui_schemas
      WHERE schema_id = $1
      ORDER BY name
    `;

    const result = await this.pool.query(query, [schemaId]);

    return result.rows;
  }

  async deleteUISchema(id: string): Promise<boolean> {
    const query = `
      DELETE FROM ui_schemas
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }
}
