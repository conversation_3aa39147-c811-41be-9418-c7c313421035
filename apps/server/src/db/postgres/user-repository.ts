import { Pool } from 'pg';
import { uuidv7 } from 'uuidv7';
import { UserRepository, ResourceFields } from '../interfaces';

export class PostgresUserRepository implements UserRepository {
  constructor(private pool: Pool) { }

  async createUser<T extends Record<string, unknown> & ResourceFields>(user: T, schemaId: string, agencyId: string, stytchId?: string): Promise<T> {
    // Ensure the user has an ID
    if (!user.id) {
      user.id = uuidv7();
    }

    const query = `
      INSERT INTO users (id, resource_type, schema_id, agency_id, stytch_id, content)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id
    `;

    // Ensure agencyId is included in the content
    const contentWithAgencyId = {
      ...user,
      agencyId
    };

    await this.pool.query(query, [
      user.id,
      user.resourceType,
      schemaId,
      agencyId,
      stytchId,
      JSON.stringify(contentWithAgencyId)
    ]);

    return user;
  }

  async readUser<T extends Record<string, unknown>>(id: string): Promise<T | null> {
    const query = `
      SELECT content
      FROM users
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0].content as T;
  }

  async updateUser<T extends Record<string, unknown> & ResourceFields>(id: string, user: T, schemaId: string, stytchId?: string): Promise<T | null> {
    // Ensure the user has the correct ID
    if (user.id !== id) {
      throw new Error("User ID must match the ID in the request");
    }

    // If stytchId is provided, update it
    if (stytchId !== undefined) {
      const query = `
        UPDATE users
        SET content = $1, schema_id = $2, stytch_id = $3
        WHERE id = $4
        RETURNING id
      `;

      // Ensure agencyId is included in the content
      const contentWithAgencyId = {
        ...user,
        agencyId: user.agencyId || (await this.getAgencyIdForUser(id))
      };

      const result = await this.pool.query(query, [
        JSON.stringify(contentWithAgencyId),
        schemaId,
        stytchId,
        id
      ]);

      if (result.rows.length === 0) {
        return null;
      }
    } else {
      // Otherwise, just update content and schema_id
      const query = `
        UPDATE users
        SET content = $1, schema_id = $2
        WHERE id = $3
        RETURNING id
      `;

      // Ensure agencyId is included in the content
      const contentWithAgencyId = {
        ...user,
        agencyId: user.agencyId || (await this.getAgencyIdForUser(id))
      };

      const result = await this.pool.query(query, [
        JSON.stringify(contentWithAgencyId),
        schemaId,
        id
      ]);

      if (result.rows.length === 0) {
        return null;
      }
    }

    return user;
  }

  async deleteUser(id: string): Promise<boolean> {
    const query = `
      DELETE FROM users
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async queryUsersByType<T extends Record<string, unknown>>(role: string, agencyId: string): Promise<T[]> {
    // If role is 'all', return all users for the agency
    let queryParams = []

    if (agencyId !== "all")
      queryParams.push(agencyId)

    if (role === 'all') {
      let query = `
        SELECT u.content, a.name as agency_name
        FROM users u
        JOIN agencies a on a.id = u.agency_id
      `;

      if (agencyId !== "all") {
        query += `WHERE u.agency_id = $1`
      }

      query += ` ORDER BY a.name`

      const result = await this.pool.query(query, queryParams);

      return result.rows.map(row => ({ ...row.content, agencyName: row.agency_name } as T));
    }

    queryParams.push(role)

    // Otherwise, filter by role in the content JSON
    let query = `
      SELECT u.content, a.name as agency_name
      FROM users u
      JOIN agencies a on a.id = u.agency_id
      WHERE u.content->>'role' = $1
    `;

    if (agencyId !== "all") {
      query += ` AND u.agency_id = $2`
    }

    query += ` ORDER BY a.name`

    const result = await this.pool.query(query, queryParams);

    return result.rows.map(row => ({ ...row.content, agencyName: row.agency_name } as T));
  }

  async getUserByEmail<T extends Record<string, unknown>>(email: string): Promise<T | null> {
    const query = `
      SELECT content
      FROM users
      WHERE content->>'email' = $1
    `;

    const result = await this.pool.query(query, [email]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0].content as T;
  }

  // Helper method to get the agencyId for a user
  private async getAgencyIdForUser(userId: string): Promise<string> {
    const query = `
      SELECT agency_id
      FROM users
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [userId]);

    if (result.rows.length === 0) {
      throw new Error(`User with ID ${userId} not found`);
    }

    return result.rows[0].agency_id;
  }

  async getUserByStytchId<T extends Record<string, unknown>>(stytchId: string): Promise<T | null> {
    const query = `
      SELECT content
      FROM users
      WHERE stytch_id = $1
    `;

    const result = await this.pool.query(query, [stytchId]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0].content as T;
  }
}
