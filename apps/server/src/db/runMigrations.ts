import { Pool } from 'pg';
import { config, initializeConfig } from '../config';
import { runMigrations } from './migrations/runMigrations';
import dotenv from 'dotenv';
import path from 'path';

async function main() {
  console.log('Starting database migration process...');
  if (process.env.NODE_ENV !== 'production') {
    dotenv.config({ path: path.resolve(__dirname, '../../.env') });
  }
  // Initialize config
  await initializeConfig();

  // Create database connection
  const pool = new Pool(config.db);
  
  try {
    // Run migrations
    await runMigrations(pool);
    console.log('Migration process completed successfully.');
  } catch (error) {
    console.error('Migration process failed:', error);
    process.exit(1);
  } finally {
    // Close pool
    await pool.end();
  }
}

// Run the migration script
main().catch(err => {
  console.error('Unexpected error:', err);
  process.exit(1);
}); 