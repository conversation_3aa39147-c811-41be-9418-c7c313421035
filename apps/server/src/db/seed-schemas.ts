
import { Pool } from 'pg';
import { config } from '../config';
import { RepositoryFactory, RepositoryType } from './factory';

// Base schemas
export const PatientBaseSchema = {
  $schema: "http://json-schema.org/draft-07/schema#",
  $id: "https://hospice-os.org/schemas/PatientBase",
  title: "PatientBase",
  type: "object",
  properties: {
    id: {
      type: "string",
      format: "uuid"
    },
    resourceType: {
      type: "string",
      const: "Patient"
    },
    firstName: {
      type: "string"
    },
    lastName: {
      type: "string"
    },
    dateOfBirth: {
      type: "string",
      format: "date"
    },
    gender: {
      type: "string"
    },
    medicalRecordNumber: {
      type: "string"
    },
    status: {
      type: "string",
      enum: ["Active", "Discharged", "Deceased"],
      default: "Active"
    }
  },
  required: ["id", "resourceType", "firstName", "lastName", "dateOfBirth", "gender", "medicalRecordNumber"]
};

export const UserBaseSchema = {
  $schema: "http://json-schema.org/draft-07/schema#",
  $id: "https://hospice-os.org/schemas/UserBase",
  title: "UserBase",
  type: "object",
  properties: {
    id: {
      type: "string",
      format: "uuid"
    },
    resourceType: {
      type: "string",
      const: "User"
    },
    email: {
      type: "string",
      format: "email"
    },
    firstName: {
      type: "string"
    },
    lastName: {
      type: "string"
    },
    role: {
      type: "string",
      enum: ["Admin", "Nurse", "MSW", "Chaplain", "Physician"]
    },
    agencyId: {
      type: "string",
      format: "uuid"
    },
    stytchId: {
      type: "string"
    }
  },
  required: ["id", "resourceType", "email", "firstName", "lastName", "role", "agencyId"]
};

export const NoteBaseSchema = {
  $schema: "http://json-schema.org/draft-07/schema#",
  $id: "https://hospice-os.org/schemas/NoteBase",
  title: "NoteBase",
  type: "object",
  properties: {
    id: {
      type: "string",
      format: "uuid"
    },
    resourceType: {
      type: "string",
      const: "Note"
    },
    patientId: {
      type: "string",
      format: "uuid"
    },
    authorId: {
      type: "string",
      format: "uuid"
    },
    noteType: {
      type: "string"
    },
    title: {
      type: "string"
    },
    content: {
      type: "object"
    },
    createdAt: {
      type: "string",
      format: "date-time"
    },
    updatedAt: {
      type: "string",
      format: "date-time"
    }
  },
  required: ["id", "resourceType", "patientId", "authorId", "noteType", "title", "content", "createdAt"]
};

// Base schemas to seed
const baseSchemas = [
  { name: 'PatientBase', schema: PatientBaseSchema },
  { name: 'UserBase', schema: UserBaseSchema },
  { name: 'NoteBase', schema: NoteBaseSchema }
];

async function seedBaseSchemas() {
  const pool = new Pool(config.db);
  
  try {
    // Initialize the repository factory
    RepositoryFactory.getInstance(RepositoryType.POSTGRES, pool);
    
    // Get the system agency
    const query = `
      SELECT id FROM agencies WHERE name = 'System' LIMIT 1
    `;
    
    const result = await pool.query(query);
    
    if (result.rows.length === 0) {
      throw new Error('System agency not found. Please run the database initialization script first.');
    }
    
    const systemAgencyId = result.rows[0].id;
    console.log(`Found System Agency ID: ${systemAgencyId}`);
    
    // Get the schema repository
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    
    // Seed base schemas
    for (const baseSchema of baseSchemas) {
      // Check if schema already exists
      const existingSchema = await schemaRepository.getSchemaByName(
        baseSchema.name,
        '1.0.0',
        systemAgencyId
      );
      
      if (existingSchema) {
        console.log(`Schema ${baseSchema.name} already exists, skipping...`);
        continue;
      }
      
      // Create the schema
      const schema = await schemaRepository.createSchema(
        baseSchema.name,
        '1.0.0',
        systemAgencyId,
        baseSchema.schema
      );
      
      console.log(`Seeded base schema: ${baseSchema.name} with ID: ${schema.id}`);
    }
    
    console.log('Base schemas seeded successfully');
  } catch (error) {
    console.error('Error seeding base schemas:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  seedBaseSchemas()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Base schema seeding failed:', error);
      process.exit(1);
    });
}

export { seedBaseSchemas };