import { RepositoryFactory, RepositoryType } from './factory';
import { 
  PatientBaseSchema, 
  UserBaseSchema, 
  NoteBaseSchema 
} from './seed-schemas';

/**
 * Sets up a test environment with in-memory repositories and seeded base schemas.
 * @returns An object containing the repository factory and agency IDs.
 */
export async function setupTestEnvironment() {
  // Use in-memory repositories
  const factory = RepositoryFactory.getInstance(RepositoryType.MEMORY);
  
  // Create a test agency
  const agencyRepository = factory.getAgencyRepository();
  const systemAgency = await agencyRepository.createAgency('System');
  const testAgency = await agencyRepository.createAgency('Test Agency');
  
  // Seed base schemas
  const schemaRepository = factory.getSchemaRepository();
  
  await schemaRepository.createSchema(
    'PatientBase',
    '1.0.0',
    systemAgency.id,
    PatientBaseSchema
  );
  
  await schemaRepository.createSchema(
    'UserBase',
    '1.0.0',
    systemAgency.id,
    UserBaseSchema
  );
  
  await schemaRepository.createSchema(
    'NoteBase',
    '1.0.0',
    systemAgency.id,
    NoteBaseSchema
  );
  
  return {
    factory,
    systemAgency,
    testAgency
  };
}
