import { RepositoryFactory } from "./db/factory";
import dotenv from "dotenv";
import path from "path";
import { config, initializeConfig } from "./config";
import appPool from "./db/db";
import { RepositoryType } from "./db/factory";
import initializeS3Client from "./s3";
import { initializeStytchClient } from "./services/stytch-service";

export const initializeServer = async ({
  configureStytch = true,
  migrate = true,
}: {
  configureStytch?: boolean;
  migrate?: boolean;
} = {}) => {
  if (process.env.NODE_ENV !== "production") {
    dotenv.config({ path: path.resolve(__dirname, "../.env") });
  }
  await initializeConfig();
  await initializeS3Client();
  if (configureStytch) {
    await initializeStytchClient();
  }
  // Log environment variables for debugging

  // Determine repository type from environment variable
  const useInMemoryRepositories = config.useInMemoryRepositories === "true";
  const repositoryType = useInMemoryRepositories
    ? RepositoryType.MEMORY
    : RepositoryType.POSTGRES;

  // Initialize the repository factory
  if (repositoryType === RepositoryType.POSTGRES) {
    const pool = await appPool(migrate);
    // Initialize the database connection for PostgreSQL
    RepositoryFactory.getInstance(repositoryType, pool);

    // // Log connection status
    // pool.on('connect', () => {
    //   console.log('Connected to PostgreSQL database');
    // });

    pool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
      process.exit(-1);
    });
  } else {
    // Initialize in-memory repositories
    RepositoryFactory.getInstance(repositoryType);
    console.log("Using in-memory repositories");
  }

  // Log environment variables for debugging
  console.log("Environment Variables:", {
    STYTCH_PROJECT_ID: config.stytch.projectId ? "Set" : "Not Set",
    STYTCH_SECRET: config.stytch.secret ? "Set" : "Not Set",
    STYTCH_ENV: config.stytch.env || "Not Set",
    DB_HOST: config.db.host || "Not Set",
    DB_PORT: config.db.port || "Not Set",
    DB_USER: config.db.user || "Not Set",
    DB_PASSWORD: config.db.password ? "Set" : "Not Set",
    DB_DATABASE: config.db.database || "Not Set",
    CORS_ALLOWED_ORIGINS: config.cors.allowedOrigins?.join(",") || "Not Set",
  });
};
