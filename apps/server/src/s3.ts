import { S3Client } from "@aws-sdk/client-s3";


export let s3Client: S3Client;

const initializeS3Client = async () => {
  console.log('Initializing S3 client');
  if (process.env.AWS_REGION && process.env.NODE_ENV !== 'development') {
    s3Client = new S3Client({ 
      region: process.env.AWS_REGION,
    });
  } else if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
    s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });
  }

  return s3Client;
}

export default initializeS3Client;