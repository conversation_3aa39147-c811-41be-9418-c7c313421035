/* eslint-disable @typescript-eslint/no-explicit-any */
// import { describe, it, expect, beforeEach, vi, MockedFunction } from "vitest";

// // Import the functions we want to test
// // Note: These are internal functions, so we'll need to export them for testing
// // or test them through the public API

// describe("LLM Service ID-based Changeset Generation", () => {
//   beforeEach(() => {
//     vi.clearAllMocks();
//   });
//   describe("ID Generation and Serialization", () => {
//     it("should add IDs to objects in arrays that don't have them", async () => {
//       const patientChart = {
//         medications: [
//           { name: "Morphine", dosage: "5mg" },
//           { name: "Tylenol", dosage: "500mg", id: "existing-id" },
//         ],
//         planOfCare: {
//           domains: {
//             physical: {
//               goals: [
//                 { description: "Pain management" },
//                 { description: "Mobility improvement" },
//               ],
//             },
//           },
//         },
//       };

//       // Mock the parse response
//       mockParse.mockResolvedValue({
//         output_parsed: { changes: [] },
//       });

//       // Call generateChangeset which will internally serialize the chart
//       await generateChangeset("No changes", patientChart, {});

//       // Verify the serialized chart was used
//       const callArgs = mockParse.mock.calls[0][0];
//       const userMessage = callArgs.input.find(
//         (msg: any) => msg.role === "user",
//       );

//       // Check that the chart was serialized with IDs
//       expect(userMessage.content).toContain("medicationsById");
//       expect(userMessage.content).toContain("goalsById");
//       expect(userMessage.content).toContain("existing-id");
//     });

//     it("should convert arrays to ID-keyed objects", async () => {
//       const chartWithIds = {
//         medications: [
//           { id: "med-123", name: "Morphine", dosage: "5mg" },
//           { id: "med-456", name: "Tylenol", dosage: "500mg" },
//         ],
//         planOfCare: {
//           domains: {
//             physical: {
//               goals: [{ id: "goal-789", description: "Pain management" }],
//             },
//           },
//         },
//       };

//       const expectedSerialized = {
//         medicationsById: {
//           "med-123": { id: "med-123", name: "Morphine", dosage: "5mg" },
//           "med-456": { id: "med-456", name: "Tylenol", dosage: "500mg" },
//         },
//         planOfCare: {
//           domains: {
//             physical: {
//               goalsById: {
//                 "goal-789": { id: "goal-789", description: "Pain management" },
//               },
//             },
//           },
//         },
//       };

//       // Mock the parse response
//       mockParse.mockResolvedValue({
//         output_parsed: { changes: [] },
//       });

//       // Call generateChangeset which will internally serialize the chart
//       await generateChangeset("No changes", chartWithIds, {});

//       // Verify the serialized chart format was used
//       const callArgs = mockParse.mock.calls[0][0];
//       const userMessage = callArgs.input.find(
//         (msg: any) => msg.role === "user",
//       );

//       // Check for the serialized format
//       expect(userMessage.content).toContain('"medicationsById":{"med-123"');
//       expect(userMessage.content).toContain('"med-456"');
//       expect(userMessage.content).toContain('"goalsById":{"goal-789"');
//     });

//     it("should translate ID-keyed paths back to numeric indices", async () => {
//       const changeset = {
//         changes: [
//           {
//             op: "replace" as const,
//             path: "/medicationsById/med-123/dosage",
//             value: "10mg",
//             oldValue: "5mg",
//             reason: "Increased dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       const originalChart = {
//         medications: [
//           { id: "med-123", name: "Morphine", dosage: "5mg" },
//           { id: "med-456", name: "Tylenol", dosage: "500mg" },
//         ],
//       };

//       const expectedTranslated = {
//         changes: [
//           {
//             op: "replace" as const,
//             path: "/medications/0/dosage",
//             value: "10mg",
//             oldValue: "5mg",
//             reason: "Increased dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       // Mock the parse response to return a changeset with ID-keyed paths
//       mockParse.mockResolvedValue({
//         output_parsed: changeset,
//       });

//       const result = await generateChangeset(
//         "Increase Morphine to 10mg",
//         originalChart,
//         {},
//       );

//       // Verify the path was translated from ID-keyed to numeric index
//       expect(result.changeset.changes[0].path).toBe("/medications/0/dosage");
//     });
//   });

//   describe("Integration Tests", () => {
//     it("should handle nested array structures", async () => {
//       const complexChart = {
//         medications: [{ name: "Morphine", dosage: "5mg" }],
//         planOfCare: {
//           domains: {
//             physical: {
//               goals: [{ description: "Pain management" }],
//               interventions: [
//                 { description: "Medication administration", assignedTo: "RN" },
//               ],
//             },
//             spiritual: {
//               goals: [{ description: "Spiritual comfort" }],
//             },
//           },
//         },
//         orders: [{ type: "DME", description: "Wheelchair" }],
//       };

//       // Mock the parse response
//       mockParse.mockResolvedValue({
//         output_parsed: { changes: [] },
//       });

//       await generateChangeset("No changes", complexChart, {});

//       // Verify nested arrays were serialized
//       const callArgs = mockParse.mock.calls[0][0];
//       const userMessage = callArgs.input.find(
//         (msg: any) => msg.role === "user",
//       );

//       // Check that nested arrays were converted to ID-keyed objects
//       expect(userMessage.content).toContain("medicationsById");
//       expect(userMessage.content).toContain("goalsById");
//       expect(userMessage.content).toContain("interventionsById");
//       expect(userMessage.content).toContain("ordersById");
//     });

//     it("should preserve arrays that don't contain objects with IDs", async () => {
//       const chart = {
//         medications: [{ name: "Morphine", dosage: "5mg" }],
//         allergies: ["Penicillin", "Shellfish"], // Array of strings
//         diagnoses: ["Cancer", "Diabetes"], // Array of strings
//       };

//       // Mock the parse response
//       mockParse.mockResolvedValue({
//         output_parsed: { changes: [] },
//       });

//       await generateChangeset("No changes", chart, {});

//       const callArgs = mockParse.mock.calls[0][0];
//       const userMessage = callArgs.input.find(
//         (msg: any) => msg.role === "user",
//       );

//       // Check that object arrays were converted but string arrays were preserved
//       expect(userMessage.content).toContain("medicationsById");
//       expect(userMessage.content).toContain("allergies");
//       expect(userMessage.content).toContain("Penicillin");
//       expect(userMessage.content).toContain("diagnoses");
//       expect(userMessage.content).toContain("Cancer");
//       // Should not have allergyById or diagnosesById
//       expect(userMessage.content).not.toContain("allergiesById");
//       expect(userMessage.content).not.toContain("diagnosesById");
//     });
//   });

//   describe("Changeset Validation", () => {
//     it("should fix invalid operations on new array item paths", async () => {
//       const invalidChangeset = {
//         changes: [
//           {
//             op: "replace" as const, // Invalid: should be "add" for "/-" path
//             path: "/medications/-",
//             value: { name: "Tylenol", dosage: "500mg" },
//             oldValue: null,
//             reason: "Added Tylenol",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       mockParse.mockResolvedValue({
//         output_parsed: invalidChangeset,
//       });

//       const result = await generateChangeset(
//         "Add Tylenol",
//         { medications: [] },
//         {},
//       );

//       // Verify the operation was corrected to "add"
//       expect(result.changeset.changes[0].op).toBe("add");
//       expect(result.changeset.changes[0].path).toBe("/medications/-");
//     });

//     it("should allow multiple add operations to the same array path", async () => {
//       const changesetWithMultipleAdds = {
//         changes: [
//           {
//             op: "add" as const,
//             path: "/symptoms/-",
//             value: { name: "Nausea", severity: "Mild" },
//             oldValue: null,
//             reason: "Added symptom: Nausea",
//             name: "symptoms",
//             fieldName: "symptoms",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//           {
//             op: "add" as const,
//             path: "/symptoms/-",
//             value: { name: "Vomiting", severity: "Moderate" },
//             oldValue: null,
//             reason: "Added symptom: Vomiting",
//             name: "symptoms",
//             fieldName: "symptoms",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       mockParse.mockResolvedValue({
//         output_parsed: changesetWithMultipleAdds,
//       });

//       const result = await generateChangeset(
//         "Patient has nausea and vomiting",
//         { symptoms: [] },
//         {},
//       );

//       // Both add operations should be preserved
//       expect(result.changeset.changes).toHaveLength(2);
//       expect(result.changeset.changes[0].op).toBe("add");
//       expect(result.changeset.changes[1].op).toBe("add");
//       expect(result.changeset.changes[0].path).toBe("/symptoms/-");
//       expect(result.changeset.changes[1].path).toBe("/symptoms/-");
//     });

//     it("should remove duplicate non-add operations with the same path", async () => {
//       const changesetWithDuplicates = {
//         changes: [
//           {
//             op: "replace" as const,
//             path: "/medications/0/dosage",
//             value: "5mg",
//             oldValue: "10mg",
//             reason: "First change",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//           {
//             op: "replace" as const,
//             path: "/medications/0/dosage",
//             value: "15mg",
//             oldValue: "10mg",
//             reason: "Second change",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       mockParse.mockResolvedValue({
//         output_parsed: changesetWithDuplicates,
//       });

//       const result = await generateChangeset(
//         "Change dosage",
//         { medications: [{ id: "med-123", name: "Morphine", dosage: "10mg" }] },
//         {},
//       );

//       // Only the last occurrence should be kept
//       expect(result.changeset.changes).toHaveLength(1);
//       expect(result.changeset.changes[0].value).toBe("15mg");
//       expect(result.changeset.changes[0].reason).toBe("Second change");
//     });

//     it("should consolidate split new item patterns into one complete change", async () => {
//       const splitNewItemChangeset = {
//         changes: [
//           {
//             op: "add" as const,
//             path: "/medications/-",
//             value: { name: "Tylenol" }, // Partial object
//             oldValue: null,
//             reason: "Added new medication",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//           {
//             op: "add" as const, // LLM trying to add individual fields
//             path: "/medications/0/dosage",
//             value: "400mg",
//             oldValue: null,
//             reason: "Set dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//           {
//             op: "add" as const,
//             path: "/medications/0/frequency",
//             value: "twice daily",
//             oldValue: null,
//             reason: "Set frequency",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//           {
//             op: "add" as const,
//             path: "/medications/0/route",
//             value: "oral",
//             oldValue: null,
//             reason: "Set route",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       mockParse.mockResolvedValue({
//         output_parsed: splitNewItemChangeset,
//       });

//       const result = await generateChangeset(
//         "Add Tylenol 400mg twice daily orally",
//         { medications: [] },
//         {},
//       );

//       // Should be consolidated into one change
//       expect(result.changeset.changes).toHaveLength(1);
//       expect(result.changeset.changes[0].op).toBe("add");
//       expect(result.changeset.changes[0].path).toBe("/medications/-");

//       // Should contain all the fields in one complete object
//       const consolidatedValue = result.changeset.changes[0].value;
//       expect(consolidatedValue).toEqual({
//         name: "Tylenol",
//         dosage: "400mg",
//         frequency: "twice daily",
//         route: "oral",
//       });

//       // Should have combined reason
//       expect(result.changeset.changes[0].reason).toBe(
//         "Added new medication; Set dosage; Set frequency; Set route",
//       );
//     });
//   });
// });

// import {
//   generateChangeset,
//   judgeChangeset,
//   Changeset,
//   ChangesetJudgement,
//   processSpelledOutWords,
// } from "../llm-service";
// import * as openaiMock from "openai";

// vi.mock("openai", () => {
//   const mockParse = vi.fn();
//   const mockCreate = vi.fn();
//   const mockClient = {
//     responses: {
//       parse: mockParse,
//       create: mockCreate,
//     },
//   };
//   return {
//     default: vi.fn().mockImplementation(() => mockClient),
//     OpenAI: vi.fn().mockImplementation(() => mockClient),
//     __mockParse: mockParse,
//   };
// });

// // Mock 'node-fetch'
// global.fetch = vi.fn() as MockedFunction<typeof fetch>;

// // Access the mock before describe block
// const mockParse = (openaiMock as any).__mockParse;

// describe("llm-service", () => {
//   beforeEach(() => {
//     vi.clearAllMocks();
//   });

//   describe("processSpelledOutWords", () => {
//     it("should convert spelled-out names to proper case", () => {
//       const testCases = [
//         { input: "John R Y K E S is here.", expected: "John Rykes is here." },
//         { input: "J O H N", expected: "John" },
//         { input: "M A R Y", expected: "Mary" },
//         { input: "S M I T H", expected: "Smith" },
//       ];

//       testCases.forEach(({ input, expected }) => {
//         const result = processSpelledOutWords(`Patient name is ${input}.`);
//         expect(result).toBe(`Patient name is ${expected}.`);
//       });
//     });

//     it("should preserve known acronyms in uppercase", () => {
//       const testCases = [
//         { input: "E M S", expected: "EMS" },
//         { input: "I C U", expected: "ICU" },
//         { input: "C P R", expected: "CPR" },
//         { input: "D N R", expected: "DNR" },
//         { input: "M R S A", expected: "MRSA" },
//       ];

//       testCases.forEach(({ input, expected }) => {
//         const result = processSpelledOutWords(`Call ${input} immediately.`);
//         expect(result).toBe(`Call ${expected} immediately.`);
//       });
//     });

//     it("should handle words spelled with dashes", () => {
//       const testCases = [
//         { input: "R-Y-K-E-S", expected: "Rykes" },
//         { input: "E-M-S", expected: "EMS" },
//         { input: "J-O-H-N", expected: "John" },
//       ];

//       testCases.forEach(({ input, expected }) => {
//         const result = processSpelledOutWords(`The ${input} service.`);
//         expect(result).toBe(`The ${expected} service.`);
//       });
//     });

//     it("should handle mixed spaces and dashes", () => {
//       const testCases = [
//         { input: "R Y-K-E S", expected: "Rykes" },
//         { input: "E-M S", expected: "EMS" },
//       ];

//       testCases.forEach(({ input, expected }) => {
//         const result = processSpelledOutWords(`Contact ${input} now.`);
//         expect(result).toBe(`Contact ${expected} now.`);
//       });
//     });

//     it("should not modify non-spelled-out text", () => {
//       const text = "The patient is in room A and needs medication B.";
//       const result = processSpelledOutWords(text);
//       expect(result).toBe(text);
//     });

//     it("should handle multiple spelled-out words in one text", () => {
//       const input = "Patient R Y K E S was taken by E M S to the I C U.";
//       const expected = "Patient Rykes was taken by EMS to the ICU.";
//       const result = processSpelledOutWords(input);
//       expect(result).toBe(expected);
//     });

//     it("should preserve word boundaries", () => {
//       const input = "Give I V medication, not I M.";
//       const expected = "Give IV medication, not IM.";
//       const result = processSpelledOutWords(input);
//       expect(result).toBe(expected);
//     });

//     it("should handle double spaces in input", () => {
//       const testCases = [
//         {
//           input: "Patient  R  Y  K  E  S  was  brought  by  E  M  S.",
//           expected: "Patient Rykes was brought by EMS.",
//         },
//         {
//           input: "Give   I   V   medication.",
//           expected: "Give IV medication.",
//         },
//         {
//           input: "Patient    J O H N    needs    E M S.",
//           expected: "Patient John needs EMS.",
//         },
//       ];

//       testCases.forEach(({ input, expected }) => {
//         const result = processSpelledOutWords(input);
//         expect(result).toBe(expected);
//       });
//     });

//     it("should handle partial spelled-out names after regular text", () => {
//       const testCases = [
//         {
//           input: "Russell R Y KSE is a 94 year old",
//           expected: "Russell Rykse is a 94 year old",
//         },
//         {
//           input: "The patient's name is John D u e M L E R",
//           expected: "The patient's name is John Duemler",
//         },
//         {
//           input: "John S M I T H called",
//           expected: "John Smith called",
//         },
//         {
//           input: "Mary J O N E S arrived",
//           expected: "Mary Jones arrived",
//         },
//         {
//           input: "Dr. S M I T H examined the patient",
//           expected: "Dr. Smith examined the patient",
//         },
//       ];

//       testCases.forEach(({ input, expected }) => {
//         const result = processSpelledOutWords(input);
//         expect(result).toBe(expected);
//       });
//     });
//   });

//   describe("generateChangeset", () => {
//     it("should generate a changeset based on transcription and patient chart", async () => {
//       const transcription =
//         "The patient's medication for Morphine needs to be increased to 10mg. Also, add a new supply: gloves, quantity 1 box.";
//       const patientChart = {
//         medications: [
//           {
//             name: "Morphine",
//             dosage: "5mg",
//             route: "PO",
//             frequency: "q4h",
//             timing: "PRN",
//             covered: true,
//             resourceType: "Medication",
//           },
//         ],
//         supplies: [],
//       };
//       const expectedChangeset: Changeset = {
//         changes: [
//           {
//             op: "replace",
//             path: "/medications/0/dosage",
//             value: "10mg",
//             oldValue: "5mg",
//             reason: "Increased Morphine dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//           {
//             op: "add",
//             path: "/supplies/-",
//             value: { name: "gloves", quantity: 1, notes: "1 box" },
//             oldValue: null,
//             reason: "Added new supply: gloves",
//             name: "supplies",
//             fieldName: "supplies",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       // Now mockParse refers to mockParseGlobal which is used by the mocked OpenAI instances
//       mockParse.mockResolvedValue({ output_parsed: expectedChangeset });

//       const result = await generateChangeset(transcription, patientChart, {});

//       expect(mockParse).toHaveBeenCalledTimes(1);
//       expect(mockParse).toHaveBeenCalledWith(
//         expect.objectContaining({
//           model: "gpt-4.1",
//           input: expect.arrayContaining([
//             expect.objectContaining({ role: "system" }),
//             expect.objectContaining({
//               role: "user",
//               content: expect.any(String),
//             }),
//           ]),
//           text: expect.objectContaining({
//             format: expect.objectContaining({ type: "json_schema" }),
//           }),
//         }),
//       );

//       // The system now serializes the chart before processing (adding IDs and converting to ById format)
//       // So we need to check the content contains the serialized version
//       const lastCall = mockParse.mock.calls[0][0];
//       const userMessage = lastCall.input.find(
//         (msg: any) => msg.role === "user",
//       );
//       expect(userMessage.content).toContain("medicationsById");
//       expect(userMessage.content).toContain(transcription);

//       expect(result.changeset).toEqual(expectedChangeset);
//       expect(result.messages).toBeDefined();
//     });

//     it("should handle feedback and generate a new changeset", async () => {
//       const transcription = "Initial visit note.";
//       const patientChart = { problems: [{ description: "Pain" }] };
//       const initialChangesetResponse: Changeset = {
//         changes: [
//           {
//             op: "replace",
//             path: "/problems/0/description",
//             value: "Severe Pain",
//             oldValue: "Pain",
//             reason: "Updated problem description",
//             name: "problems",
//             fieldName: "problems",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };
//       const feedback =
//         "The problem description should be 'Chronic Severe Pain'.";
//       const expectedFinalChangeset: Changeset = {
//         changes: [
//           {
//             op: "replace",
//             path: "/problems/0/description",
//             value: "Chronic Severe Pain",
//             oldValue: "Pain",
//             reason: "Updated problem description based on feedback",
//             name: "problems",
//             fieldName: "problems",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       mockParse
//         .mockResolvedValueOnce({ output_parsed: initialChangesetResponse })
//         .mockResolvedValueOnce({ output_parsed: expectedFinalChangeset });

//       const initialResult = await generateChangeset(
//         transcription,
//         patientChart,
//         {},
//       );
//       const previousMessages = [...initialResult.messages];
//       const finalResult = await generateChangeset(
//         "",
//         patientChart,
//         {},
//         feedback,
//         previousMessages,
//       );

//       expect(mockParse).toHaveBeenCalledTimes(2);
//       expect(mockParse).toHaveBeenCalledTimes(2);
//       const lastCall = mockParse.mock.calls[1][0];
//       const userMessages = lastCall.input.filter(
//         (msg: any) => msg.role === "user",
//       );
//       const feedbackMessage = userMessages[userMessages.length - 1];
//       expect(feedbackMessage.content).toContain(feedback);
//       expect(finalResult.changeset).toEqual(expectedFinalChangeset);
//     });

//     it("should handle additional transcription and regenerate changeset", async () => {
//       const initialTranscription = "Patient reports nausea.";
//       const patientChart = { symptoms: [] };
//       const initialChangesetResponse: Changeset = {
//         changes: [
//           {
//             op: "add",
//             path: "/symptoms/-",
//             value: { name: "Nausea", severity: "Mild" },
//             oldValue: null,
//             reason: "Added symptom: Nausea",
//             name: "symptoms",
//             fieldName: "symptoms",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };
//       const additionalTranscription = "Also experiencing vomiting.";
//       const expectedFinalChangeset: Changeset = {
//         changes: [
//           {
//             op: "add",
//             path: "/symptoms/-",
//             value: { name: "Nausea", severity: "Mild" },
//             oldValue: null,
//             reason: "Added symptom: Nausea",
//             name: "symptoms",
//             fieldName: "symptoms",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//           {
//             op: "add",
//             path: "/symptoms/-",
//             value: { name: "Vomiting", severity: "Moderate" },
//             oldValue: null,
//             reason: "Added symptom: Vomiting",
//             name: "symptoms",
//             fieldName: "symptoms",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };

//       mockParse
//         .mockResolvedValueOnce({ output_parsed: initialChangesetResponse })
//         .mockResolvedValueOnce({ output_parsed: expectedFinalChangeset });

//       const initialResult = await generateChangeset(
//         initialTranscription,
//         patientChart,
//         {},
//       );
//       const previousMessages2 = [...initialResult.messages];
//       const finalResult = await generateChangeset(
//         additionalTranscription,
//         patientChart,
//         {},
//         undefined,
//         previousMessages2,
//       );

//       expect(mockParse).toHaveBeenCalledTimes(2);
//       expect(mockParse).toHaveBeenCalledTimes(2);
//       const lastCall = mockParse.mock.calls[1][0];
//       const userMessages = lastCall.input.filter(
//         (msg: any) => msg.role === "user",
//       );
//       const lastUserMessage = userMessages[userMessages.length - 1];
//       expect(lastUserMessage.content).toContain(additionalTranscription);
//       expect(lastUserMessage.content).toContain("<visitTranscript>");
//       expect(finalResult.changeset).toEqual(expectedFinalChangeset);
//     });
//   });

//   describe("judgeChangeset", () => {
//     it("should return a judgement on the changeset", async () => {
//       const changeset: Changeset = {
//         changes: [
//           {
//             op: "replace",
//             path: "/medications/0/dosage",
//             value: "10mg",
//             oldValue: "5mg",
//             reason: "Increased Morphine dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//         ],
//       };
//       const patientChart = {
//         medications: [
//           {
//             name: "Morphine",
//             dosage: "5mg",
//             route: "PO",
//             frequency: "q4h",
//             timing: "PRN",
//             covered: true,
//             resourceType: "Medication",
//           },
//         ],
//       };
//       const transcript =
//         "The patient's Morphine dosage was increased to 10mg. The patient also mentioned needing new diabetic socks.";
//       const expectedJudgement: ChangesetJudgement = {
//         changes: [
//           { description: "Missing change: Add new DME for diabetic socks." },
//         ],
//         timestamp: "2025-06-23 10:30:00",
//       };

//       mockParse.mockResolvedValue({ output_parsed: expectedJudgement });

//       const result = await judgeChangeset(
//         changeset,
//         patientChart,
//         transcript,
//         {},
//       ); // TODO: replace {} with patientChartSchema

//       expect(mockParse).toHaveBeenCalledTimes(1);
//       expect(mockParse).toHaveBeenCalledWith(
//         expect.objectContaining({
//           model: "o4-mini",
//           input: expect.arrayContaining([
//             expect.objectContaining({ role: "system" }),
//             expect.objectContaining({
//               role: "user",
//               content: expect.any(String),
//             }),
//           ]),
//           text: expect.objectContaining({
//             format: expect.objectContaining({ type: "json_schema" }),
//           }),
//         }),
//       );

//       // Verify the content contains what we expect
//       const lastCall = mockParse.mock.calls[0][0];
//       const userMessage = lastCall.input.find(
//         (msg: any) => msg.role === "user",
//       );
//       expect(userMessage.content).toContain(JSON.stringify(patientChart));
//       expect(userMessage.content).toContain(transcript);
//       expect(userMessage.content).toContain(JSON.stringify(changeset));
//       expect(result.judgement).toEqual(expectedJudgement);
//       expect(result.messages).toBeDefined();
//     });

//     it("should handle follow-up judgement with previous messages", async () => {
//       const initialChangeset: Changeset = {
//         changes: [
//           {
//             op: "replace",
//             path: "/medications/0/dosage",
//             value: "10mg",
//             oldValue: "5mg",
//             reason: "Increased Morphine dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//         ],
//       };
//       const patientChart = {
//         medications: [{ name: "Morphine", dosage: "5mg" }],
//       };
//       const transcript = "Morphine to 10mg. Also needs pads.";
//       const initialJudgementResponse: ChangesetJudgement = {
//         changes: [{ description: "Missing supply: pads." }],
//         timestamp: "2025-06-23 10:30:00",
//       };

//       const updatedChangeset: Changeset = {
//         changes: [
//           {
//             op: "replace",
//             path: "/medications/0/dosage",
//             value: "10mg",
//             oldValue: "5mg",
//             reason: "Increased Morphine dosage",
//             name: "medications",
//             fieldName: "medications",
//             readBackRequired: true,
//             readBackPerformed: false,
//           },
//           {
//             op: "add",
//             path: "/supplies/-",
//             value: { name: "pads", quantity: 1 },
//             oldValue: null,
//             reason: "Added pads",
//             name: "supplies",
//             fieldName: "supplies",
//             readBackRequired: false,
//             readBackPerformed: false,
//           },
//         ],
//       };
//       const expectedFinalJudgement: ChangesetJudgement = {
//         changes: [],
//         timestamp: "2025-06-23 10:30:00",
//       };

//       mockParse
//         .mockResolvedValueOnce({ output_parsed: initialJudgementResponse })
//         .mockResolvedValueOnce({ output_parsed: expectedFinalJudgement });

//       const initialResult = await judgeChangeset(
//         initialChangeset,
//         patientChart,
//         transcript,
//         {},
//       );
//       const prevJudgeMessages = [...initialResult.messages];
//       const finalResult = await judgeChangeset(
//         updatedChangeset,
//         patientChart,
//         "",
//         {},
//         prevJudgeMessages,
//       );

//       expect(mockParse).toHaveBeenCalledTimes(2);
//       expect(finalResult.judgement).toEqual(expectedFinalJudgement);
//     });
//   });
// });
