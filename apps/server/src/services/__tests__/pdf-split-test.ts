// import * as path from 'path';
// import { splitPdfIntoImages } from '../pdf-service';

// /**
//  * This is a simple test for demonstrating the PDF splitting functionality.
//  * To run this test, you need to:
//  * 1. Have GraphicsMagick or ImageMagick installed on your system
//  * 2. Have a sample PDF file to test with
//  * 
//  * Example usage:
//  * ```
//  * // Place a sample.pdf in the tests directory
//  * // Run with:
//  * ts-node pdf-split-test.ts
//  * ```
//  */
// async function runTest() {
//   try {
//     // Path to a sample PDF file - replace with actual path
//     const testPdfPath = path.join(__dirname, 'sample.pdf');
//     const pdfUrl = `file://${testPdfPath}`;
    
//     // Output directory for the images
//     const outputDir = path.join(__dirname, 'output-images');
    
//     console.log('Starting PDF to Image conversion...');
//     console.log('PDF URL:', pdfUrl);
//     console.log('Output directory:', outputDir);
    
//     // Convert PDF to images
//     const imageFiles = await splitPdfIntoImages(pdfUrl, outputDir);
    
//     console.log('Conversion complete!');
//     console.log('Generated image files:');
//     imageFiles.forEach(file => console.log(` - ${file}`));
    
//   } catch (error) {
//     console.error('Test failed with error:', error);
//   }
// }

// // Uncomment to run the test
// // runTest();

// export default runTest; 