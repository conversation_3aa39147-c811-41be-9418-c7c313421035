import { describe, it, expect, vi, beforeEach } from 'vitest';
import { SchemaResolverService } from '../schema-resolver-service';
import { RepositoryFactory } from '../../db/factory';
import { JSONSchema } from '../../db/interfaces';

// Mock the repository factory
vi.mock('../../db/factory', () => {
  const mockSchemaRepository = {
    getSchema: vi.fn(),
    listSchemas: vi.fn(),
  };
  
  const mockAgencyRepository = {
    listAgencies: vi.fn(),
  };
  
  return {
    RepositoryType: { MEMORY: 'memory' },
    RepositoryFactory: {
      getInstance: vi.fn().mockReturnValue({
        getSchemaRepository: vi.fn().mockReturnValue(mockSchemaRepository),
        getAgencyRepository: vi.fn().mockReturnValue(mockAgencyRepository),
      }),
    },
  };
});

describe('SchemaResolverService', () => {
  let schemaResolverService: SchemaResolverService;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockSchemaRepository: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockAgencyRepository: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    schemaResolverService = new SchemaResolverService();
    mockSchemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    mockAgencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
  });
  
  describe('resolveSchema with nested definitions', () => {
    it('should resolve references to definitions in nested schemas', async () => {
      // Define the timeline schema with a VersionChange definition
      const timelineSchema: JSONSchema = {
        type: "object",
        properties: {
          requestRef: {
            type: "object",
            required: [
              "resourceType",
              "id"
            ],
            properties: {
              id: {
                type: "string",
                format: "uuid"
              },
              resourceType: {
                type: "string",
                const: "Request"
              }
            }
          },
          versionHistorySections: {
            type: "array",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  description: "the section name"
                },
                changes: {
                  type: "array",
                  items: {
                    $ref: "#/definitions/VersionChange"
                  }
                }
              },
              description: ""
            }
          }
        },
        definitions: {
          VersionChange: {
            type: "object",
            required: [
              "field",
              "oldValue",
              "newValue"
            ],
            properties: {
              field: {
                type: "string",
                description: "the field name"
              },
              newValue: {
                type: "string",
                description: "what the value was changed to"
              },
              oldValue: {
                type: "string",
                description: "the previous value, can be nothing if it didn't exist before"
              }
            }
          }
        }
      };
      
      // Define the patient schema that references the timeline schema
      const patientSchema: JSONSchema = {
        $id: "https://hospice-os.org/schemas/PatientBase",
        type: "object",
        $schema: "http://json-schema.org/draft-07/schema#",
        required: [
          "resourceType",
          "id",
          "firstName",
          "lastName",
          "dateOfBirth",
          "gender"
        ],
        properties: {
          id: {
            type: "string"
          },
          gender: {
            type: "string"
          },
          timeLineItems: {
            type: "array",
            items: {
              $ref: "fc3b5899-9f33-4632-bb5f-876566c19aa9"
            }
          },
          firstName: {
            type: "string"
          },
          lastName: {
            type: "string"
          },
          dateOfBirth: {
            type: "string"
          },
          resourceType: {
            type: "string",
            const: "Patient"
          }
        },
        definitions: {
          Insurance: {
            type: "object",
            required: [
              "provider",
              "number"
            ],
            properties: {
              number: {
                type: "string"
              },
              provider: {
                type: "string"
              }
            }
          }
        }
      };
      
      // Setup mocks
      mockSchemaRepository.getSchema.mockImplementation((id: string) => {
        if (id === 'patient-schema-id') {
          return { schema: patientSchema };
        } else if (id === 'fc3b5899-9f33-4632-bb5f-876566c19aa9') {
          return { schema: timelineSchema };
        }
        return null;
      });
      
      mockAgencyRepository.listAgencies.mockResolvedValue([{ id: 'agency1' }]);
      mockSchemaRepository.listSchemas.mockResolvedValue([
        { schema: patientSchema },
        { schema: timelineSchema }
      ]);
      
      // Resolve the patient schema
      const resolvedSchema = await schemaResolverService.resolveSchema('patient-schema-id');
      
      // Verify the schema was resolved correctly
      expect(resolvedSchema).toBeTruthy();
      expect(resolvedSchema?.properties?.timeLineItems?.items).toBeTruthy();
      
      // The timeLineItems.items should no longer have a $ref
      const timeLineItemsItems = resolvedSchema?.properties?.timeLineItems?.items as JSONSchema;
      expect(timeLineItemsItems.$ref).toBeUndefined();
      
      // The versionHistorySections.items.properties.changes.items should have the VersionChange properties
      const versionHistorySections = timeLineItemsItems.properties?.versionHistorySections as JSONSchema;
      const versionHistorySectionsItems = versionHistorySections.items as JSONSchema;
      const changes = versionHistorySectionsItems.properties?.changes as JSONSchema;
      const changesItems = changes.items as JSONSchema;
      expect(changesItems.$ref).toBeUndefined();
      expect(changesItems.properties?.field).toBeTruthy();
      expect(changesItems.properties?.newValue).toBeTruthy();
      expect(changesItems.properties?.oldValue).toBeTruthy();
      expect(changesItems.required).toContain('field');
      expect(changesItems.required).toContain('newValue');
      expect(changesItems.required).toContain('oldValue');
    });
  });
});
