import { RepositoryFactory } from '../db/factory';

export class AgencyService {
  async createAgency(
    name: string,
    stytchOrganizationId?: string
  ): Promise<{ id: string; name: string; stytchOrganizationId?: string }> {
    const agencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
    return await agencyRepository.createAgency(name, stytchOrganizationId);
  }

  async getAgency(
    id: string
  ): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const agencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
    return await agencyRepository.getAgency(id);
  }

  async listAgencies(): Promise<{ id: string; name: string; stytchOrganizationId?: string }[]> {
    const agencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
    return await agencyRepository.listAgencies();
  }

  async updateAgency(
    id: string,
    name: string,
    stytchOrganizationId?: string
  ): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const agencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
    return await agencyRepository.updateAgency(id, name, stytchOrganizationId);
  }

  async deleteAgency(
    id: string
  ): Promise<boolean> {
    const agencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
    return await agencyRepository.deleteAgency(id);
  }
  
  async getAgencyByStytchOrganizationId(
    stytchOrganizationId: string
  ): Promise<{ id: string; name: string; stytchOrganizationId?: string } | null> {
    const agencyRepository = RepositoryFactory.getInstance().getAgencyRepository();
    
    // Since we don't have a direct method in the repository, we'll list all agencies and filter
    const agencies = await agencyRepository.listAgencies();
    return agencies.find(agency => agency.stytchOrganizationId === stytchOrganizationId) || null;
  }
}

// Export a singleton instance
export const agencyService = new AgencyService();
