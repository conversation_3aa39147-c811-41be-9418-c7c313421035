/* eslint-disable @typescript-eslint/no-explicit-any */
import OpenAI from "openai";
import { AssemblyAI } from "assemblyai";
import countPages from "page-count";
import { pipeline } from "stream/promises";
import fetch from "node-fetch";
import fs from "fs";
import {
  EasyInputMessage,
  ResponseInput,
} from "openai/resources/responses/responses.mjs";
import { ChangesetItem, Patient, Request } from "@hospice-os/apptypes";
import { v4 as uuidv4 } from "uuid";
import { PatientChart } from "../temporal/request/requestActivities";

// TODO
export const openaiClient = new OpenAI({
  apiKey: "***************************************************",
});
const assemblyAiClient = new AssemblyAI({
  apiKey: "********************************",
});

/**
 * Process spelled-out words in transcription text
 * Handles patterns like "R Y K E S" -> "Rykes" and "E M S" -> "EMS"
 */
export function processSpelledOutWords(text: string): string {
  // Remove double spaces to normalize the text before processing
  const normalizedText = text.replace(/\s+/g, " ");
  // Common medical and general acronyms to keep uppercase
  const acronymDictionary = new Set([
    // Medical acronyms
    "EMS",
    "ICU",
    "CPR",
    "DNR",
    "CHF",
    "UTI",
    "CVA",
    "TIA",
    "IVF",
    "CHG",
    "COPD",
    "GERD",
    "MRSA",
    "PICC",
    "CPAP",
    "BIPAP",
    "NPO",
    "PRN",
    "BID",
    "TID",
    "QID",
    "QHS",
    "QOD",
    "STAT",
    "DVT",
    "AFib",
    "ALS",
    "MS",
    "PT",
    "OT",
    "ST",
    "RT",
    "MD",
    "DO",
    "PA",
    "NP",
    "RN",
    "LPN",
    "CNA",
    "MSW",
    "IV",
    "IM",
    "PO",
    "SQ",
    "SC",
    "SL",
    "PR",
    "NG",
    "GT",
    "JT",
    "PEG",
    "BP",
    "HR",
    "RR",
    "O2",
    "SpO2",
    "EKG",
    "ECG",
    "MRI",
    "CT",
    "PET",
    "CBC",
    "BMP",
    "CMP",
    "LFT",
    "TSH",
    "HbA1c",
    "INR",
    "PTT",
    "ABG",
    "UA",
    "C&S",
    "BUN",
    "GFR",
    "ALT",
    "AST",
    "ALP",
    "GGT",
    "WBC",
    "RBC",
    "Hgb",
    "Hct",
    "PLT",
    "Na",
    "K",
    "Cl",
    "CO2",
    "Ca",
    "Mg",
    "Phos",
    "ED",
    "ER",
    "OR",
    "PACU",
    "SNF",
    "ALF",
    "LTC",
    "DME",
    "HME",
    "DMEPOS",
    // General acronyms
    "USA",
    "UK",
    "UN",
    "EU",
    "CEO",
    "CFO",
    "CTO",
    "COO",
    "VP",
    "SVP",
    "EVP",
    "LLC",
    "INC",
    "LTD",
    "PC",
    "PA",
    "PLLC",
    "LLP",
    "LP",
    "DBA",
    "AM",
    "PM",
    "EST",
    "CST",
    "MST",
    "PST",
    "EDT",
    "CDT",
    "MDT",
    "PDT",
    "ATM",
    "GPS",
    "USB",
    "PDF",
    "FAQ",
    "URL",
    "API",
    "SQL",
    "XML",
    "JSON",
    "ASAP",
    "FYI",
    "BTW",
    "LOL",
    "OMG",
    "TMI",
    "BRB",
    "TBD",
    "TBA",
    "ETA",
    "DOB",
    "SSN",
    "VIN",
    "PIN",
    "ATM",
    "ID",
    "IT",
    "HR",
    "PR",
    "QA",
  ]);

  // Two-pass approach for better accuracy
  // First pass: Handle patterns with dashes (more specific)
  const dashPattern = /\b[A-Za-z](?:-[A-Za-z])+\b/g;
  const intermediateText = normalizedText.replace(dashPattern, (match) => {
    const combined = match.replace(/-/g, "");

    // Check if the original match was all uppercase (potential acronym)
    const isAllUppercase =
      match.replace(/-/g, "").toUpperCase() === combined.toUpperCase();

    // If it's 2-4 letters and all uppercase, check if it's a known acronym
    if (combined.length >= 2 && combined.length <= 4 && isAllUppercase) {
      const upperCombined = combined.toUpperCase();
      if (acronymDictionary.has(upperCombined)) {
        return upperCombined; // Keep as uppercase acronym
      }
    }

    // Otherwise, treat as a proper name (capitalize first letter only)
    return combined.charAt(0).toUpperCase() + combined.slice(1).toLowerCase();
  });

  // Second pass: Handle space-separated patterns
  // More restrictive pattern to avoid matching things like "s name"
  // Updated pattern to handle cases like "R Y KSE" but not "R Y K E S is"
  const spacePattern = /\b[A-Za-z](?:\s[A-Za-z])+(?:[A-Z]+(?![a-z]))?\b/g;

  // Process with a custom replacer that preserves spacing
  let result = intermediateText;
  let offset = 0;

  const matches = [...intermediateText.matchAll(spacePattern)];

  for (const match of matches) {
    const matchText = match[0];
    const matchIndex = match.index! + offset;

    // Check context to avoid matching patterns like "'s name"
    if (matchIndex > 0 && result[matchIndex - 1] === "'") {
      continue; // Don't process if preceded by apostrophe
    }

    // Check if this looks like a spelled-out word (at least 2 single letters with spaces)
    const parts = matchText.split(/\s+/);
    const singleLetterCount = parts.filter((p) => p.length === 1).length;

    if (singleLetterCount < 2) {
      continue; // Not enough single letters, probably not a spelled-out word
    }

    const combined = matchText.replace(/\s/g, "");

    // Check if the original match was all uppercase (potential acronym)
    const isAllUppercase =
      matchText.replace(/\s/g, "").toUpperCase() === combined.toUpperCase();

    // If it's 2-4 letters and all uppercase, check if it's a known acronym
    let replacement;
    if (combined.length >= 2 && combined.length <= 4 && isAllUppercase) {
      const upperCombined = combined.toUpperCase();
      if (acronymDictionary.has(upperCombined)) {
        replacement = upperCombined; // Keep as uppercase acronym
      } else {
        replacement =
          combined.charAt(0).toUpperCase() + combined.slice(1).toLowerCase();
      }
    } else {
      // Otherwise, treat as a proper name (capitalize first letter only)
      replacement =
        combined.charAt(0).toUpperCase() + combined.slice(1).toLowerCase();
    }

    // Replace in the result string
    result =
      result.substring(0, matchIndex) +
      replacement +
      result.substring(matchIndex + matchText.length);

    // Update offset for subsequent matches
    offset += replacement.length - matchText.length;
  }

  return result;
}

export const getTranscription = async (audioUrl: string) => {
  console.log("audioUrl: ", audioUrl);

  // TODO: Move customSpelling & wordBoost to db long term & have ability to edit via admin
  const customSpelling = [
    {
      to: "NOMNC",
      from: ["NonMC"],
    },
    {
      to: "glargine",
      from: ["glardine"],
    },
    {
      to: "Etexilate",
      from: ["Extectalate"],
    },
    {
      to: "SANTYL",
      from: ["Santal"],
    },
    {
      to: "Medigyne",
      from: ["Medregyne"],
    },
    {
      to: "AmLactin",
      from: ["amylactin"],
    },
    {
      to: "portacath",
      from: ["porter Cath"],
    },
    {
      to: "Exufiber",
      from: ["Exofiber"],
    },
    {
      to: "Novolin",
      from: ["Novalin"],
    },
    {
      to: "Januvia",
      from: ["Genuvia"],
    },
    {
      to: "Prevena",
      from: ["Pravina"],
    },
    {
      to: "cefepime",
      from: ["cephapem"],
    },
    {
      to: "ceftriaxone",
      from: ["cephriaxone"],
    },
    {
      to: "lactulose",
      from: ["lactalose"],
    },
    {
      to: "lorazepam",
      from: ["larazepam"],
    },
    {
      to: "protonix",
      from: ["protonics"],
    },
    {
      to: "Zosyn",
      from: ["zosin"],
    },
    {
      to: "Chlorthalidone",
      from: ["Chlorothalidone"],
    },
    {
      to: "fibracol",
      from: ["fiber call"],
    },
    {
      to: "zejula",
      from: ["zjula"],
    },
    {
      to: "keppra",
      from: ["Kepra"],
    },
    {
      to: "Allopurinol",
      from: ["Alipurinol"],
    },
    {
      to: "tolterodine",
      from: ["tilteridine"],
    },
    {
      to: "Tegaderm",
      from: ["Tegoderm"],
    },
    {
      to: "Nadolol",
      from: ["natalol"],
    },
    {
      to: "Farxiga",
      from: ["farcega"],
    },
    {
      to: "dilaudid",
      from: ["Dilauded"],
    },
    {
      to: "lovenox",
      from: ["lovinox"],
    },
    {
      to: "stayfix",
      from: ["stapix"],
    },
    {
      to: "voshe",
      from: ["vosh"],
    },
    {
      to: "Cefdinir",
      from: ["cephanir"],
    },
    {
      to: "perfenazine",
      from: ["perphenazine"],
    },
    {
      to: "sorbact",
      from: ["sorebacked"],
    },
    {
      to: "circaids",
      from: ["Circades"],
    },
    {
      to: "circaid",
      from: ["circade"],
    },
    {
      to: "Monurol",
      from: ["Monerol"],
    },
    {
      to: "Januvia",
      from: ["Genuvia"],
    },
    {
      to: "PCP",
      from: ["primary care physician"],
    },
    {
      to: "WNL",
      from: ["within normal limits"],
    },
    {
      to: "x3",
      from: ["times three"],
    },
    {
      to: "x4",
      from: ["times four"],
    },
    {
      to: "miconazole",
      from: ["myconazole"],
    },
    {
      to: "mirtazapine",
      from: ["Martazapine"],
    },
    {
      to: "Fioricet",
      from: ["Fioraset"],
    },
    {
      to: "Sitagliptin",
      from: ["Citiglyptin"],
    },
    {
      to: "donepezil",
      from: ["Donopazil"],
    },
    {
      to: "prazosin",
      from: ["Presocin"],
    },
    {
      to: "duloxetine",
      from: ["Deloxetine"],
    },
    {
      to: "Lotrisone",
      from: ["Lotrasone"],
    },
    {
      to: "loratadine",
      from: ["Laratidine"],
    },
    {
      to: "glipizide",
      from: ["Glypizide"],
    },
    {
      to: "latanoprost",
      from: ["Latanaprost"],
    },
    {
      to: "Bacitracin",
      from: ["bassetrasin"],
    },
    {
      to: "parotitis",
      from: ["perititis"],
    },
    {
      to: "dulcolax",
      from: ["Docalax"],
    },
    {
      to: "voltaren",
      from: ["Voltarin"],
    },
    {
      to: "calciphylaxis",
      from: ["Calcifylaxis"],
    },
    {
      to: "tubigrips",
      from: ["tubi grips"],
    },
    {
      to: "Opticell",
      from: ["Optocell"],
    },
    {
      to: "ecchymosis",
      from: ["echomosis"],
    },
    {
      to: "furosemide",
      from: ["ferocimide"],
    },
    {
      to: "levothyroxine",
      from: ["Levothyroxin"],
    },
    {
      to: "dapagliflozin",
      from: ["dapafilglozin"],
    },
    {
      to: "citalopram",
      from: ["cetalopram"],
    },
    {
      to: "Arformoterol",
      from: ["r for motorol"],
    },
    {
      to: "Budesonide",
      from: ["Budesinide"],
    },
    {
      to: "Lisinopril",
      from: ["lysinopril"],
    },
    {
      to: "GI/GU",
      from: ["gigu"],
    },
    {
      to: "xeroform",
      from: ["zero form"],
    },
    {
      to: "POC",
      from: ["plan of care"],
    },
    {
      to: "cephalexin",
      from: ["cephalaxin"],
    },
    {
      to: "periwound",
      from: ["peri wound"],
    },
    {
      to: "olanzapine",
      from: ["alonzapine"],
    },
    {
      to: "serosanguineous",
      from: ["serosanguinous"],
    },
    {
      to: "serosanguineous",
      from: ["serosanguinous"],
    },
    {
      to: "kerlix",
      from: ["curlax"],
    },
    {
      to: "kerlix",
      from: ["curlex"],
    },
    {
      to: "kerlix",
      from: ["curlx"],
    },
    {
      to: "levofloxacin",
      from: ["level levofloxacin"],
    },
    {
      to: "Heywood",
      from: ["Haywood"],
    },
    {
      to: "Leominster",
      from: ["Lemenster"],
    },
    {
      to: "ileal",
      from: ["ilial"],
    },
    {
      to: "Cubii",
      from: ["qb"],
    },
    {
      to: "PleurX",
      from: ["plerex"],
    },
    {
      to: "viactiv",
      from: ["viactive"],
    },
    {
      to: "maalox",
      from: ["malox"],
    },
    {
      to: "bactroban",
      from: ["backtraban"],
    },
    {
      to: "mounjaro",
      from: ["Manjaro"],
    },
    {
      to: "gentamicin",
      from: ["gentomycin"],
    },
    {
      to: "cefazolin",
      from: ["cephazolin"],
    },
    {
      to: "cavilon",
      from: ["cavalon"],
    },
    {
      to: "calmoseptine",
      from: ["kelmoseptine"],
    },
    {
      to: "aquacel",
      from: ["aquacell"],
    },
    {
      to: "xarelto",
      from: ["Zarelto"],
    },
  ];

  const wordBoost = [
    "CCVNA",
    "ICC",
    "ascites",
    "serosanguineous",
    "NOMNC",
    "olanzapine",
    "levofloxacin",
    "ileal",
    "eliquis",
    "PleurX",
    "Losartan",
    "metoprolol",
    "kerlix",
    "Rhonchi",
    "recert",
    "zofran",
    "Homecare HomeBase",
    "popliteal",
    "coban two",
    "thirty second sit to stand",
    "duloxetine",
    "levemir",
    "novolog",
    "naproxen",
    "sertraline",
    "empagliflozin",
    "flagyl",
    "sigmoidectomy",
    "AEROBIKA",
    "sertraline",
    "brimonidine",
    "mycitracin",
    "bactrim",
    "MOLST",
    "Lisinopril",
    "calmoseptine",
    "Tresiba",
    "triamcinolone",
    "hydrofera Blue",
    "ecchymosis",
    "fluoxetine",
    "entresto",
    "phytonadione",
    "tresiba",
    "trelegy",
    "therex",
    "Amlodipine",
    "Albuterol",
    "Acetaminophen",
    "Morphine",
    "Lorazepam",
    "Atropine",
    "Haloperidol",
    "Senna",
  ];

  // Slam-1 still in beta, doesn't support word_boost or custom_spelling - had an issue where some of the injected words got leaked into the output
  // const transcript = await assemblyAiClient.transcripts.transcribe({
  //   audio: audioUrl,
  //   speech_model: "slam-1",
  //   custom_spelling: customSpelling,
  //   word_boost: wordBoost,
  //   prompt: `Transcribe the audio. Pay very close attention to words that are spelled out letter by letter and attempt to concatenate them into a single word.
  //   Do not transcribe into individual letters when a word is being spelled out.\n\n
  //   The following words are important and should be transcribed as-is: ${JSON.stringify(wordBoost)}\n\n
  //   The following words are often mis-transcribed and should be corrected: ${customSpelling.map((item) => item.from + " -> " + item.to).join("\n")}`,
  // });

  const transcript = await assemblyAiClient.transcripts.transcribe({
    audio: audioUrl,
    custom_spelling: customSpelling,
    word_boost: wordBoost,
  });

  // Process spelled-out words in the transcription
  const processedText = processSpelledOutWords(transcript.text || "");

  return processedText;
};

export const uploadFileToOpenAI = async (presignedUrl: string) => {
  const fileName = presignedUrl.split("?")[0].split("/").pop();
  const res = await fetch(presignedUrl);
  await pipeline(res.body, fs.createWriteStream(`/tmp/${fileName}`));

  // now open a new ReadStream (never consumed yet)
  const stream = fs.createReadStream(`/tmp/${fileName}`);

  let openAIFile: OpenAI.Files.FileObject;
  try {
    openAIFile = await openaiClient.files.create({
      file: stream,
      purpose: "user_data",
    });
    console.log("openAIFile: ", openAIFile);
  } catch (error) {
    console.error("Failed to create file", error);
    throw error;
  }

  // buffer the file
  const fileBuffer = fs.readFileSync(`/tmp/${fileName}`);
  const pageCount = await countPages(fileBuffer, "pdf");
  return { openAIFile, pageCount };
};

export const getMarkdownFromPDF = async (
  openAIFile: OpenAI.Files.FileObject,
  pageStart: number,
  pageEnd: number,
) => {
  const messages = [
    {
      role: "system",
      content: [
        {
          type: "input_text",
          text: `You are a helpful assistant that converts PDF documents into clean, well-formatted Markdown.
          - If the pages do not have readable text then OCR them. Do not include any text in your response that is not part of the PDF. 
          - If the page is blank, just return an empty string.
          - If you are asked to convert a range of pages that don't exist, just return the markdown for the existing pages.
          - If you are asked to convert a single page that doesn't exist, just return an empty string.
          - You must pay special attention to the checkboxes that are in the PDF. 
          - Be very strict about the page numbers. Each new page should have an H1 header with the page number at the top and a horizontal line at the end of the page.
          Please double check the checkboxes one by one to make sure they are correctly converted to markdown.
          `,
        },
      ],
    },
    {
      role: "user",
      content: [
        {
          type: "input_file",
          file_id: openAIFile.id,
        },
        {
          type: "input_text",
          text: `Please convert pages ${pageStart} to ${pageEnd} of the PDF to Markdown page by page. Please double check the checkboxes one by one to make sure they are correct.`,
        },
      ],
    },
  ] as any;

  console.log(
    `🔔 Converting PDF to Markdown… pages ${pageStart} to ${pageEnd}`,
  );
  try {
    const resp = await openaiClient.responses.create(
      {
        model: "o4-mini",
        input: messages,
        reasoning: {
          effort: "low",
        },
        max_output_tokens: 200000,
        text: {
          format: {
            type: "text",
          },
        },
      },
      {
        timeout: 60 * 1000 * 10,
      },
    );
    if (resp.error) {
      console.error(
        `Failed to convert PDF to Markdown pages ${pageStart} to ${pageEnd}`,
        resp.error,
      );
      throw new Error(resp.error.message);
    }
    console.log(
      `✅ Conversion of PDF to Markdown complete pages ${pageStart} to ${pageEnd}`,
    );
    console.log("resp: ", resp);
    return resp.output_text;
  } catch (error) {
    console.error(
      `Failed to convert PDF to Markdown pages ${pageStart} to ${pageEnd}`,
      error,
    );
    throw error;
  }
};

export const extractPageMarkdown = async (
  presignedUrl: string,
  text: string,
) => {
  const res = await fetch(presignedUrl);
  const base64Image = await res.arrayBuffer();
  const base64ImageString = Buffer.from(base64Image).toString("base64");

  const messages: ResponseInput = [
    {
      role: "system",
      content: `You are a helpful assistant that converts images from a PDF into clean, well-formatted Markdown.
      - you are given the text from tesseract.js. Use it to help you convert the image to markdown. Use it specifically for double checking identifiers, ssn, payor ids, etc and spellings.
      - pay special attention to the checkboxes.
      - if the page is blank, just return an empty string.
      - do not include any text that is not part of the image.
      - do not hallucinate any text.
      `,
    },
    {
      role: "user",
      content: [
        {
          type: "input_image",
          image_url: `data:image/jpeg;base64,${base64ImageString}`,
          detail: "high",
        },
        {
          type: "input_text",
          text: `<tesseractText>
          ${text}
          </tesseractText>`,
        },
        {
          type: "input_text",
          text: `Please convert the image to Markdown.`,
        },
      ],
    },
  ];

  const completion = await openaiClient.responses.create({
    model: "o4-mini",
    input: messages,
    reasoning: {
      effort: "medium",
    },
  });

  return completion.output_text;
};

export const fillInitialPatientChart = async (
  transcription: string,
  schema: any,
  currentPatientChart: Patient,
  messageHistory?: ResponseInput,
  additionalInstructions?: string,
) => {
  const messages: ResponseInput =
    messageHistory ||
    ([
      {
        role: "system",
        content: `
        You are a highly trained hospice scribe with extreme attention to detail.
        Your job is to fill in the initial patient chart with the data from the transcription.
        The transcription is provided in the user message.
        The schema is a JSON schema that defines the patient chart.
        The current patient chart is provided in the system message.
        Fill in the missing fields in the current patient chart. Including adding additional entries to arrays.
        Do not remove any existing entries from the current patient chart.
        Do not make up any information. Only use the information provided in the transcription.
        Return the full and completed patient chart.
        <currentPatientChart>
        ${JSON.stringify(currentPatientChart)}
        </currentPatientChart>
        `,
      },
      {
        role: "user",
        content: transcription,
      },
    ] as ResponseInput);

  if (additionalInstructions) {
    messages.push({
      role: "user",
      content: additionalInstructions,
    });
  }

  const completion = await openaiClient.responses.parse({
    model: "o3",
    max_output_tokens: 100000,
    input: messages,
    reasoning: {
      effort: "high",
    },
    text: {
      format: {
        name: "PatientChart",
        schema: schema,
        type: "json_schema",
        strict: true,
      },
    },
  });

  const jsonResult = completion.output_parsed;
  return jsonResult as any;
};

export const fillSchemaFromTranscription = async (
  transcription: string,
  schema: any,
  additionalInstructions?: any,
) => {
  const messages: ResponseInput = [
    {
      role: "system",
      content: `
        You are a highly trained hospice scribe with extreme attention to detail. Your job is to convert raw hospice visit audio transcripts into fully populated and valid JSON objects.

        ## Requirements
        1. Include every field defined in the JSON schema.
        2. For fields with no transcript data, set the value to an empty string ("") if the field expects a string, or "null" otherwise.
        3. Only populate fields grounded by actual transcript content—don't hallucinate.
        4. Fix any typos, grammar, or punctuation issues—do not change meaning.
        5. Review your work, and double check your output. Go through everything one more time and make sure you didn't miss anything.


        ## Specifics
        - This task is critical—be as thorough and accurate as possible.
        - Omitting any transcript detail is unacceptable.
        - Your precise output directly impacts patient care.

        ## Context
        Our service provides transcription to hospice agencies. Your audited, corrected, and formatted notes enable clinicians to document accurately and efficiently.

        The audio transcript will be provided in the user message. Parse it and emit the JSON object directly, following all requirements.
        `,
    },
    {
      role: "user",
      content: transcription,
    },
    ...((additionalInstructions
      ? [
          {
            role: "user",
            content: `
        Here is more audio from the same visit, use it to regenerate the note:
        <visitTranscript>
        ${additionalInstructions}
        </visitTranscript>`,
          },
        ]
      : []) as any),
  ];

  const completion = await openaiClient.responses.parse({
    model: "gpt-4.1",
    input: messages,
    text: {
      format: {
        name: "VisitNoteSchema",
        schema: schema,
        type: "json_schema",
        strict: false,
      },
    },
    temperature: 0,
  });

  const jsonResult = completion.output_parsed;
  return jsonResult as any;
};

// ****NOTE*****: Definitions CANNOT reference other definitions or OpenAI will throw an invalid schema error
const changesetSchema = {
  type: "object",
  properties: {
    changes: {
      additionalProperties: false,
      type: "array",
      items: {
        additionalProperties: false,
        type: "object",
        required: [
          "op",
          "path",
          "value",
          "reason",
          "name",
          "readBackRequired",
          "readBackPerformed",
        ],
        properties: {
          op: {
            type: "string",
            enum: ["replace", "add", "remove"],
            description: "The operation to perform on the path",
          },
          path: {
            type: "string",
            description:
              "JSON pointer path to the object/field that is changing in RFC 6901 format. Example: /medications/0/dosage",
          },
          value: {
            anyOf: [
              {
                additionalProperties: false,
                type: "object",
                required: ["goals", "problem", "interventions"],
                properties: {
                  goals: {
                    type: "array",
                    items: {
                      $ref: "#/definitions/Goal",
                    },
                  },
                  problem: {
                    type: "string",
                  },
                  interventions: {
                    type: "array",
                    items: {
                      $ref: "#/definitions/Intervention",
                    },
                  },
                },
              },
              { $ref: "#/definitions/Medication" },
              { $ref: "#/definitions/DMEOrSupplies" },
              { $ref: "#/definitions/Goal" },
              { $ref: "#/definitions/Intervention" },
              { $ref: "#/definitions/VisitFrequency" },
              { $ref: "#/definitions/Diagnosis" },
              { $ref: "#/definitions/AttendingPhysician" },
              { $ref: "#/definitions/LevelOfCare" },
              { type: "string" },
              { type: "boolean" },
              { type: "number" },
              { type: "null" },
              {
                type: "string",
                enum: [
                  "Army",
                  "Navy",
                  "Air Force",
                  "Marine Corps",
                  "Coast Guard",
                  "Other",
                  "None",
                ],
                description: "The veteran status of the patient",
              },
              {
                type: "string",
                enum: [
                  "Living Will",
                  "DNR",
                  "Healthcare Proxy",
                  "Power of Attorney",
                  "POLST",
                ],
                description: "The advanced directive the patient has",
              },
            ],
            description: "The new value to set at the path.",
          },
          reason: {
            type: "string",
            description:
              "A concise description of the reason for this change. Do not repeat information found in other fields. Do not say `the transcript`. Do not include field names.",
          },
          name: {
            type: "string",
            description: "The name or title of the item being changed.",
          },
          readBackRequired: {
            type: "boolean",
            description:
              "True if this is adding or stopping a medication, or a medication change for dosage, frequency, strength, start/end date, or if this is a DME change related to oxygen. False otherwise.",
          },
          readBackPerformed: {
            type: "boolean",
            description:
              "True if the visit transcript explicitly states a read back or teach back was performed for this change. False otherwise.",
          },
        },
      },
    },
  },
  definitions: {
    Medication: {
      description:
        "A medication is a drug that is used to treat a medical condition.",
      type: "object",
      title: "Medication",
      additionalProperties: false,
      required: [
        "name",
        "route",
        "frequency",
        "dosage",
        "startDate",
        "covered",
        "form",
        "notes",
        "status",
        "endDate",
        "highRisk",
        "strength",
        "indication",
        "providedBy",
        "finalRuleAddendumRequired",
        "relatedToTerminalDiagnosis",
      ],
      properties: {
        form: {
          type: ["string", "null"],
          description:
            "Dosage form (e.g., 'oral tablet', 'liquid', 'patch', 'injection').",
        },
        name: {
          type: "string",
          description:
            "Full name of the medication (e.g., 'Levothyroxine Sodium').",
        },
        notes: {
          type: ["string", "null"],
          description:
            "Any additional notes about the medication not covered by the other fields. Leave empty if not applicable.",
        },
        route: {
          type: ["string", "null"],
          description:
            "Route of administration (e.g., 'oral', 'sublingual', 'IV', 'SC', 'topical').",
        },
        dosage: {
          type: ["string", "null"],
          description:
            "Total dosage amount to be taken at each administration (e.g., '1 tablet', '2 mL', '500 mg').",
        },
        status: {
          type: ["string", "null"],
          enum: ["Active", "Discontinued"],
        },
        covered: {
          type: "boolean",
          description:
            "Whether or not this medication is covered under the hospice benefit (Final Rule compliance). If not specifically state, set to null",
        },
        endDate: {
          type: ["string", "null"],
          description:
            "Date when this medication was discontinued. Use MM/DD/YYYY format.",
        },
        highRisk: {
          type: ["boolean", "null"],
          description:
            "Whether this is considered a high-risk medication (e.g., narcotics, antipsychotics). If not specifically state, set to null",
        },
        frequency: {
          type: ["string", "null"],
          description:
            "Frequency of administration (e.g., 'Once daily', 'Every 8 hours').",
        },
        startDate: {
          type: ["string", "null"],
          description:
            "Date when this medication was started. Use MM/DD/YYYY format.",
        },
        indication: {
          type: ["string", "null"],
          description:
            "Reason for prescribing the medication (e.g., 'hypothyroidism', 'pain management').",
        },
        providedBy: {
          type: ["string", "null"],
          description:
            "Who provided this medication information (e.g., 'caregiver', 'facility MAR', 'patient').",
        },
        finalRuleAddendumRequired: {
          type: ["boolean", "null"],
          description:
            "Indicates if the family was notified that the med is unrelated and not covered",
        },
        relatedToTerminalDiagnosis: {
          type: "string",
          enum: ["Yes", "No", "Unknown"],
        },
      },
    },
    DMEOrSupplies: {
      additionalProperties: false,
      description:
        "A DME or supply is a durable medical equipment or supply item that is used to help the patient achieve a goal.",
      type: "object",
      required: ["name", "notes", "quantity"],
      properties: {
        name: {
          type: ["string", "null"],
        },
        notes: {
          type: ["string", "null"],
        },
        quantity: {
          type: ["integer", "null"],
        },
      },
    },
    Goal: {
      additionalProperties: false,
      description: "A goal is a desired outcome for the patient.",
      type: "object",
      required: ["description", "completeBy"],
      properties: {
        completeBy: {
          type: ["string", "null"],
        },
        description: {
          type: ["string", "null"],
        },
      },
    },
    VisitFrequency: {
      additionalProperties: false,
      description: "A visit frequency is a scheduled visit to the patient.",
      type: "object",
      required: ["startDate", "endDate", "frequency", "discipline"],
      properties: {
        startDate: {
          type: ["string", "null"],
          description:
            "Date when this visit frequency starts. Use MM/DD/YYYY format.",
        },
        endDate: {
          type: ["string", "null"],
          description:
            "Date when this visit frequency ends. Use MM/DD/YYYY format.",
        },
        frequency: {
          type: ["string", "null"],
        },
        discipline: {
          description: "The discipline that is providing the visit frequency.",
          type: ["string", "null"],
          enum: [
            "Nursing",
            "Social Work",
            "Chaplain",
            "Aide",
            "Volunteer",
            "Other",
          ],
        },
      },
    },
    Intervention: {
      additionalProperties: false,
      description:
        "An intervention is a planned activity (e.g. education, therapy, etc. not a medication or DME) to help the patient achieve a goal related to their plan of care.",
      type: "object",
      required: ["description", "assignedTo"],
      properties: {
        assignedTo: {
          type: ["string", "null"],
        },
        description: {
          type: ["string", "null"],
        },
      },
    },
    Diagnosis: {
      type: "object",
      additionalProperties: false,
      required: ["code", "description"],
      properties: {
        code: {
          type: ["string", "null"],
          description: "ICD-10 code, or unspecified",
        },
        description: {
          type: ["string", "null"],
          description:
            "Description of the diagnosis, e.g: Congestive Heart Failure (CHF)",
        },
      },
    },
    AttendingPhysician: {
      additionalProperties: false,
      type: "object",
      description:
        "The attending physician is the physician who is responsible for the patient's care if they have elected NOT to use the hospice agencies physician/medical director.",
      required: [
        "fax",
        "address",
        "protocol",
        "credentials",
        "firstName",
        "lastName",
        "npi",
        "phone",
        "practiceName",
      ],
      properties: {
        fax: {
          type: ["string", "null"],
        },
        npi: {
          type: ["string", "null"],
        },
        phone: {
          type: ["string", "null"],
        },
        address: {
          type: "object",
          required: ["city", "state", "street", "zipCode"],
          properties: {
            city: {
              type: ["string", "null"],
            },
            state: {
              type: ["string", "null"],
            },
            street: {
              type: ["string", "null"],
            },
            zipCode: {
              type: ["number", "null"],
            },
            county: {
              type: ["string", "null"],
            },
          },
        },
        lastName: {
          type: ["string", "null"],
        },
        protocol: {
          type: ["string", "null"],
        },
        firstName: {
          type: ["string", "null"],
        },
        credentials: {
          enum: ["MD", "DO", "RN", "APRN", "Other"],
          type: "string",
        },
        practiceName: {
          type: ["string", "null"],
        },
      },
    },
    ReferralDetails: {
      type: "object",
      additionalProperties: false,
      required: [
        "dateAndTime",
        "psychosocialNeeds",
        "referringProvider",
        "referralSourceType",
        "referralSourceName",
        "currentPhysicalNeeds",
        "historyOfTreatmentToDate",
        "additionalInformationOfInterest",
      ],
      properties: {
        dateAndTime: {
          type: ["string", "null"],
          format: "date-time",
        },
        psychosocialNeeds: {
          type: ["string", "null"],
        },
        referringProvider: {
          type: ["object", "null"],
          additionalProperties: false,
          required: ["firstName", "lastName", "npi", "credentials"],
          properties: {
            npi: {
              type: ["string", "null"],
            },
            lastName: {
              type: ["string", "null"],
            },
            firstName: {
              type: ["string", "null"],
            },
            credentials: {
              enum: ["MD", "DO", "RN", "APRN", "Other"],
              type: ["string", "null"],
            },
          },
        },
        referralSourceType: {
          enum: [
            "Physician/Doctor",
            "Hospital",
            "Skilled Nursing Facility (SNF)",
            "Home Health Agency",
            "Assisted Living Facility",
            "Nursing Home",
            "Geriatric Care Manager",
            "Area Agency on Aging (AAA)",
            "Family",
            "Social Worker",
            "Hospital Discharge Planner",
            "Other Hospice Agency",
            "State Medicaid Waiver Program",
            "Medical Equipment Company",
            "Pharmacy",
            "Support Group",
            "Adult Day Center",
            "Other",
          ],
          type: ["string", "null"],
        },
        referralSourceName: {
          type: ["string", "null"],
        },
        currentPhysicalNeeds: {
          type: ["string", "null"],
        },
        historyOfTreatmentToDate: {
          type: ["string", "null"],
        },
        additionalInformationOfInterest: {
          type: ["string", "null"],
        },
      },
    },
    Insurance: {
      type: "object",
      additionalProperties: false,
      required: [
        "notes",
        "planName",
        "planType",
        "memberId",
        "groupNumber",
        "effectiveDate",
        "expirationDate",
        "priorAuthNumber",
        "policyHolderName",
        "policyHolderRelationship",
      ],
      properties: {
        notes: {
          type: ["string", "null"],
          description: "Any additional notes",
        },
        memberId: {
          type: ["string", "null"],
          description: "Subscriber or member ID",
        },
        planName: {
          type: ["string", "null"],
          description:
            "Name of the insurance company and plan. Example: Humana - Gold Plus",
        },
        planType: {
          enum: [
            "Medicare",
            "Medicaid",
            "Medicare Part D",
            "Private",
            "VA",
            "Pharmacy",
            "Other",
          ],
          type: ["string", "null"],
          description: "Category of this plan",
        },
        groupNumber: {
          type: ["string", "null"],
          description: "Group number (if applicable)",
        },
        effectiveDate: {
          type: ["string", "null"],
          format: "date",
          description: "Coverage start date. Use MM/DD/YYYY format.",
        },
        expirationDate: {
          type: ["string", "null"],
          format: "date",
          description: "Coverage end date. Use MM/DD/YYYY format.",
        },
        priorAuthNumber: {
          type: ["string", "null"],
          description: "Prior authorization number, if required",
        },
        policyHolderName: {
          type: ["string", "null"],
          description: "Name of the subscriber/policy holder",
        },
        policyHolderRelationship: {
          enum: ["Self", "Spouse", "Child", "Other"],
          type: ["string", "null"],
          description:
            "Relation of subscriber to patient, if the policy holder is not the patient",
        },
      },
    },
    CaregiverAndProxy: {
      type: "object",
      additionalProperties: false,
      required: [
        "proxyName",
        "proxyAddress",
        "relationship",
        "primaryCaregiver",
        "proxyPrimaryPhoneNumber",
        "proxySecondaryPhoneNumber",
        "healthcareProxyPOA",
      ],
      properties: {
        proxyName: {
          type: ["string", "null"],
        },
        proxyAddress: {
          type: "object",
          required: ["city", "state", "street", "zipCode"],
          properties: {
            city: {
              type: ["string", "null"],
            },
            state: {
              type: ["string", "null"],
            },
            street: {
              type: ["string", "null"],
            },
            zipCode: {
              type: ["number", "null"],
            },
            county: {
              type: ["string", "null"],
            },
          },
        },
        relationship: {
          type: ["string", "null"],
        },
        primaryCaregiver: {
          type: ["string", "null"],
        },
        healthcareProxyPOA: {
          type: ["boolean", "null"],
        },
        proxyPrimaryPhoneNumber: {
          type: ["string", "null"],
        },
        proxySecondaryPhoneNumber: {
          type: ["string", "null"],
        },
      },
    },
    LevelOfCare: {
      type: "object",
      additionalProperties: false,
      required: ["levelOfCare", "startDate"],
      properties: {
        levelOfCare: {
          type: "string",
          enum: [
            "Routine Home Care",
            "Continuous Home Care",
            "Inpatient Respite Care",
            "General Inpatient Care",
          ],
          description: "The hospice level of care for this episode.",
        },
        startDate: {
          type: "string",
          format: "date-time",
          description: "When this level-of-care episode began.",
        },
        endDate: {
          type: "string",
          format: "date-time",
          description: "When this level-of-care episode ended (if applicable).",
        },
      },
    },
    // Allergy: {
    //   additionalProperties: false,
    //   type: "object",
    //   required: ["allergy", "reaction"],
    //   properties: {
    //     allergy: {
    //       type: ["string", "null"],
    //       description: "What the patient is allergic to (e.g Sulfa)",
    //     },
    //     reaction: {
    //       type: ["string", "null"],
    //       description:
    //         "The reaction and severity of the allergy. (e.g hives, rash).",
    //     },
    //   },
    // },
  },
  required: ["changes"],
  additionalProperties: false,
};

export type Changeset = {
  changes: ChangesetItem[];
};

/**
 * Generate a unique ID for objects that can appear in lists
 * @param prefix - The prefix for the ID (e.g., 'med', 'goal', 'intervention')
 * @returns A unique ID string
 */
const generateObjectId = (prefix: string): string => {
  return `${prefix}-${uuidv4().substring(0, 8)}`;
};

/**
 * Generate appropriate prefix for ID based on array property name
 * @param arrayKey - The property name of the array
 * @returns The prefix to use for ID generation
 */
const getIdPrefix = (arrayKey: string): string => {
  const prefixMap: Record<string, string> = {
    medications: "med",
    goals: "goal",
    interventions: "intervention",
    supplies: "supply",
    orders: "order",
    problems: "problem",
    diagnoses: "diagnosis",
    allergies: "allergy",
    visits: "visit",
    notes: "note",
    assessments: "assessment",
    actions: "action",
    timeLineItems: "timeline",
    benefitPeriods: "benefit",
  };

  return prefixMap[arrayKey] || arrayKey;
};

/**
 * Recursively add IDs to objects in arrays that don't already have them
 * @param obj - The object to process
 * @param path - Current path for tracking (used internally)
 * @returns The object with IDs added
 */
const addIdsToArrayObjects = (obj: any, path: string = ""): any => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    // Process array items
    return obj.map((item: any) => {
      if (
        typeof item === "object" &&
        item !== null &&
        (!item.id || item.id.length === 0)
      ) {
        // Extract array name from path for prefix generation
        const pathParts = path.split(".");
        const arrayKey = pathParts[pathParts.length - 1] || "items";
        const prefix = getIdPrefix(arrayKey);

        // Add ID and recursively process the item
        return addIdsToArrayObjects(
          { ...item, id: generateObjectId(prefix) },
          path,
        );
      }
      return addIdsToArrayObjects(item, path);
    });
  }

  // Process object properties
  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const currentPath = path ? `${path}.${key}` : key;
    result[key] = addIdsToArrayObjects(value, currentPath);
  }

  return result;
};

/**
 * Recursively convert arrays containing objects to ID-keyed objects
 * @param obj - The object to process
 * @returns The object with arrays converted to ID-keyed objects
 */
const convertArraysToIdKeyedObjects = (obj: any): any => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    // For arrays, recursively process each item but don't convert the array itself
    // (conversion happens at the parent object level)
    return obj.map((item: any) => convertArraysToIdKeyedObjects(item));
  }

  // Process object properties
  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      // Check if this array contains objects with IDs
      const hasObjectsWithIds = value.some(
        (item: any) => typeof item === "object" && item !== null && item.id,
      );

      if (hasObjectsWithIds) {
        // Convert array to ID-keyed object
        const idKeyedObject: any = {};
        value.forEach((item: any) => {
          if (typeof item === "object" && item !== null && item.id) {
            // Recursively process the item
            idKeyedObject[item.id] = convertArraysToIdKeyedObjects(item);
          }
        });

        // Create the "ById" version and remove the original array
        const newKey = `${key}ById`;
        result[newKey] = idKeyedObject;
        // Don't add the original array to result
      } else {
        // Keep arrays that don't contain objects with IDs (e.g., arrays of strings)
        result[key] = convertArraysToIdKeyedObjects(value);
      }
    } else {
      // Recursively process non-array values
      result[key] = convertArraysToIdKeyedObjects(value);
    }
  }

  return result;
};

/**
 * Serialize patient chart for LLM processing by adding IDs and converting arrays to ID-keyed objects
 * @param patientChart - The original patient chart
 * @param existingChartWithIds - Optional existing chart with IDs to maintain consistency
 * @returns The serialized chart with ID-keyed objects and the chart with IDs
 */
export const serializeChartForLLM = (patientChart: any): [any, any] => {
  // If we have an existing chart with IDs, merge new data while preserving existing IDs
  const chartWithIds = addIdsToArrayObjects(patientChart);

  // Then convert arrays to ID-keyed objects (recursive)
  const serializedChart = convertArraysToIdKeyedObjects(chartWithIds);

  return [serializedChart, chartWithIds];
};

/**
 * Translate ID-keyed paths back to numeric indices for changeset processing
 * @param changeset - The changeset with ID-keyed paths
 * @param originalPatientChart - The original patient chart with arrays
 * @returns The changeset with numeric index paths
 */
const translateChangesetPaths = (
  changeset: Changeset,
  originalPatientChart: any,
): Changeset => {
  console.log(
    "translateChangesetPaths - input changeset:",
    JSON.stringify(changeset, null, 2),
  );
  console.log(
    "translateChangesetPaths - originalPatientChart:",
    JSON.stringify(originalPatientChart, null, 2),
  );

  const translatedChanges = changeset.changes.map((change: ChangesetItem) => {
    let translatedPath = change.path;
    console.log(`translateChangesetPaths - processing path: ${translatedPath}`);

    // Check if this is an ID-keyed path (contains "ById")
    if (translatedPath.includes("ById/")) {
      console.log(
        `translateChangesetPaths - found ID-keyed path: ${translatedPath}`,
      );
      // Extract the ID-keyed portion
      const pathParts = translatedPath.split("/");
      const newPathParts: string[] = [];

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i];

        if (part.endsWith("ById") && i + 1 < pathParts.length) {
          // This is an ID-keyed object reference
          const arrayName = part.replace("ById", "");
          const itemId = pathParts[i + 1];

          // Find the index of this item in the original array
          const index = findItemIndexById(
            originalPatientChart,
            arrayName,
            itemId,
            newPathParts.slice(1),
          );

          if (index !== -1) {
            newPathParts.push(arrayName);
            newPathParts.push(index.toString());
            i++; // Skip the ID part since we've processed it
          } else {
            // If we can't find the item, it might be a new item, keep the original path structure
            newPathParts.push(part);
          }
        } else {
          newPathParts.push(part);
        }
      }

      translatedPath = newPathParts.join("/");
      console.log(`translateChangesetPaths - translated to: ${translatedPath}`);
    } else {
      console.log(
        `translateChangesetPaths - no ID-keyed pattern found, keeping original path`,
      );
    }

    return {
      ...change,
      path: translatedPath,
    };
  });

  console.log(
    "translateChangesetPaths - output changeset:",
    JSON.stringify({ changes: translatedChanges }, null, 2),
  );

  return {
    changes: translatedChanges,
  };
};

/**
 * Recursively find the index of an item by its ID in the original patient chart
 * @param chart - The original patient chart
 * @param arrayName - The name of the array to search in
 * @param itemId - The ID to search for
 * @param pathPrefix - The path prefix to navigate to the array
 * @returns The index of the item, or -1 if not found
 */
const findItemIndexById = (
  chart: any,
  arrayName: string,
  itemId: string,
  pathPrefix: string[],
): number => {
  console.log(
    `findItemIndexById - looking for ${arrayName}[id=${itemId}] with pathPrefix:`,
    pathPrefix,
  );
  let current = chart;

  // Navigate to the array using the path prefix
  for (const part of pathPrefix) {
    if (current && current[part]) {
      current = current[part];
      console.log(
        `findItemIndexById - navigated to ${part}, current:`,
        current,
      );
    } else {
      console.log(`findItemIndexById - failed to navigate to ${part}`);
      return -1;
    }
  }

  // Look for the array
  if (current && current[arrayName] && Array.isArray(current[arrayName])) {
    const array = current[arrayName];
    console.log(
      `findItemIndexById - found array ${arrayName} with ${array.length} items`,
    );
    const index = array.findIndex((item: any) => item && item.id === itemId);
    console.log(`findItemIndexById - found item at index: ${index}`);
    return index;
  }

  console.log(
    `findItemIndexById - array ${arrayName} not found in current object`,
  );
  return -1;
};

export const generateChangeset = async (
  transcription: string,
  patientChart: any,
  patientChartSchema: any,
  serializedChart: any,
  feedback?: any,
  previousMessages?: ResponseInput,
): Promise<{
  changeset: Changeset;
  messages: ResponseInput;
}> => {
  const messages: ResponseInput = previousMessages || [
    {
      role: "system",
      content: `
        # Role
        You are a hospice clinical scribe with extreme attention to detail. 
        # Instructions
        You will be given a patient's current chart in JSON format in the XML tag: <currentPatientChart>, and a transcript of the most recent visit in the XML tag: <visitTranscript>. Your job is to compare the two and specifically look for all items in the visitTranscript that change the patient's chart. You should be looking for at least the following, but this list is not comprehensive:
        - Medication changes/orders (e.g changing a medication dosage, adding or removing a medication)
        - DME (durable medical equipment) additions or changes - an item is considered DME if it can withstand repeated use, has an expected life of at least 3 years, and requires a physician's prescription.
        - Supply changes (e.g wound care supplies, bed pads, etc.) - an item is considered a supply if it is disposable, single-use, or has a short useful life, not durable (cannot withstand repeated use over years), and does not require a physician's prescription for coverage.
        - Visit frequency changes (e.g changing skilled nursing visits from 2 to 3 times per week)
        - Adding a new patient problem or goal, changing an intervention on the plan of care, etc.
        - Adding a diagnosis
        - Demographic or related chart updates
        # Rules
        - **IMPORTANT PATH FORMATTING**: For existing items in lists (medications, goals, interventions, etc.), always use the ID-keyed path format. For example:
          * To modify an existing medication: "/medicationsById/med-a12b3c4d/dosage"
          * To modify an existing goal: "/planOfCare/domains/physical/goalsById/goal-x1y2z3w4/description"
          * To modify an existing intervention: "/planOfCare/domains/physical/interventionsById/intervention-p9q8r7s6/assignedTo"
        - **NEW ITEMS**: For NEW items being added to arrays, use ONLY ONE change with path ending in "/-" and include ALL required fields in the value object:
          * CORRECT: {"op": "add", "path": "/medications/-", "value": {"name": "Tylenol", "dosage": "400mg", "route": "oral", "frequency": "twice daily", "startDate": "06/23/2025", "covered": true, "form": "tablet", "notes": "", "status": "Active", "endDate": null, "highRisk": false, "strength": "400mg", "indication": "pain relief", "providedBy": "physician", "finalRuleAddendumRequired": false, "relatedToTerminalDiagnosis": "No"}}
          * WRONG: Multiple changes with "/medications/-" then "/medications/0/strength", "/medications/0/frequency" etc.
        - **CRITICAL**: Paths ending with "/-" MUST ALWAYS use "add" operation and must include a COMPLETE object with ALL required fields
        - Never create multiple changes for a single new item - combine all fields into one complete object
        - For existing items, you can modify individual fields, but for new items, provide the complete object in one change
        - Every change must be grounded in the visitTranscript.
        - Do not miss any changes - it is critical for compliance that every change be recorded appropriately with no mistakes.
        - Medications should never be removed ('op'='remove'), you must set the status to 'Discontinued', add an end date if specified, and **always keep the existing fields like dosage, frequency, etc.**.
        - Always be as verbose as possible, and include as much detail as you can from the visitTranscript as to why the change is happening.
        - If the change is adding/stopping a medication or changing a medication dosage, frequency, strength, start/end date, or a DME change related to oxygen, set 'readBackRequired' to 'true'. Otherwise, set to 'false'.
        - Only set 'readBackPerformed' to 'true' if the visit transcript explicitly states a read back or teach back was performed with a physician/doctor/provider for the change, and 'readBackRequired' is 'true'. **Under no circumstances should you infer this**, it must be explicitly stated in the transcript, mark it as 'false' otherwise.
        - Always ensure the object being changed is valid according to the patient chart schema (<patientChartSchema>).
        # Output
        You will extract these changes and format them into a JSON object that represents a changeset for audit and compliance purposes.
        **CRITICAL**: Use ID-keyed paths for existing items (e.g., "/medicationsById/med-a12b3c4d/strength") and array paths with '/-' for new items (e.g., "/medications/-").`,
    },
    {
      role: "user",
      content: `
        <currentDate>
        MM/DD/YYYY: ${new Date().toLocaleDateString()}
        </currentDate>
        Here is the patient's current chart:
        <currentPatientChart>
        ${JSON.stringify(serializedChart)}
        </currentPatientChart>
        Here is the patient chart schema:
        <patientChartSchema>
        ${JSON.stringify(patientChartSchema)}
        </patientChartSchema>
        Here is the visit audio transcript:
        <visitTranscript>
        ${transcription}
        </visitTranscript>
        Generate your changeset now. `,
    },
  ];

  if (feedback) {
    messages.push({
      role: "user",
      content: `
        Follow up message with feedback from LLM judge/QA step:
        
        The judge identified these missing changes from the transcript that need to be added to your changeset:
        ${feedback}
        
        Please regenerate your complete changeset to include these missing items while preserving all existing changes. 
        Make sure that:
        - Paths ending with "/-" always use "add" operation with COMPLETE objects containing ALL required fields
        - Never split a new item into multiple changes - each new array item should be ONE change with a complete value object
        - No duplicate changes with the same path
        - All changes are still grounded in the original transcript`,
    });
  }

  if (previousMessages && transcription) {
    messages.push({
      role: "user",
      content: `
        <currentDate>
        MM/DD/YYYY: ${new Date().toLocaleDateString()}
        </currentDate>
        Here is more audio from the same visit, use it to regenerate your last changeset, do not miss any pre-existing changes:
        <visitTranscript>
        ${transcription}
        </visitTranscript>`,
    });
  }

  console.log("changeset messages", messages);

  const completion = await openaiClient.responses.parse({
    model: "gpt-4.1",
    input: messages,
    text: {
      format: {
        name: "ChangesetSchema",
        schema: changesetSchema,
        type: "json_schema",
        strict: false,
      },
    },
    temperature: 0,
  });

  const jsonResult = completion.output_parsed;
  messages.push({
    role: "assistant",
    content: JSON.stringify(jsonResult),
  });

  // Translate ID-keyed paths back to numeric indices
  const translatedChangeset = translateChangesetPaths(
    jsonResult as Changeset,
    patientChart,
  );
  return {
    changeset: translatedChangeset,
    messages: messages,
  };

  // Validate and fix changeset operations
  // const validatedChangeset = validateChangesetOperations(translatedChangeset);

  // return { changeset: validatedChangeset, messages: messages };
};

const judgeSchema = {
  type: "object",
  properties: {
    changes: {
      type: "array",
      description: "A list of changes made, detailing each modification.",
      items: {
        type: "object",
        properties: {
          description: {
            type: "string",
            description: "A description of the change.",
          },
        },
        required: ["description"],
        additionalProperties: false,
      },
    },
  },
  required: ["changes"],
  additionalProperties: false,
};

export type ChangesetJudgement = {
  timestamp: string;
  changes: {
    description: string;
  }[];
};

export const judgeChangeset = async (
  changeset: any,
  patientChart: any,
  transcript: string,
  patientChartSchema: any,
  previousMessages?: any,
): Promise<{
  judgement: ChangesetJudgement;
  messages: ResponseInput;
}> => {
  const messages: ResponseInput = previousMessages || [
    {
      role: "system",
      content: `
      # Role
      You are an expert hospice documentation analyst.  You will receive:

      <currentPatientChart>…</currentPatientChart>
      <visitTranscript>…</visitTranscript>
      <changeset>…</changeset>

      Your job is to **ONLY** identify *new and clinically significant* changes that:

        • Occurred in the visitTranscript  
        • Are not already captured at the *same JSON pointer path* in the changeset  
        • Fall into one of these domains: Medication orders, durable medical equipment (DME)/supply orders, visit frequency or level-of-care changes, plan of care (POC) problems/goals/interventions, education interventions, or adding a new diagnosis.

      # Rules
      1. **Do not** suggest any change that duplicates an existing path+value in the changeset (even if worded differently).  
      2. **Do not** invent or infer anything not explicitly stated in the transcript.  
      3. Only consider the five most important missing items; ignore the rest.  
      4. You must ensure the 'path' field maps correctly to the object being changed, created, or deleted in the currentPatientChart and is in RFC 6901 format.
      5. You must not use field names in your changes, just describe the change. 
      6. Verify that adding/stopping a medication or any change to a medication route, dosage, frequency, timing, strength, start/end date, or a DME change related to oxygen, has 'readBackRequired' set to 'true'.
      7. If there are no more changes found, you must return an empty array for the "changes" field.
      `,
    },
    {
      role: "user",
      content: `
      Here is the patient's current chart:
      <currentPatientChart>
      ${JSON.stringify(patientChart)}
      </currentPatientChart>
      Here is the audio transcript from the latest visit:
      Here is the patient chart schema:
      <patientChartSchema>
      ${JSON.stringify(patientChartSchema)}
      </patientChartSchema>
      <visitTranscript>
      ${transcript}
      </visitTranscript>
      And finally, here is the JSON changeset based on that visit audio transcript:
      <changeset>
      ${JSON.stringify(changeset)}
      </changeset>
      <currentDate>
      MM/DD/YYYY: ${new Date().toLocaleDateString()}
      </currentDate>
      Is anything missing in the changeset?
      `,
    },
  ];

  if (previousMessages && transcript) {
    messages.push({
      role: "user",
      content: `
      <currentDate>
      MM/DD/YYYY: ${new Date().toLocaleDateString()}
      </currentDate>
      Here is more audio from the same visit:
      <visitTranscript>
      ${transcript}
      </visitTranscript>
      `,
    });
  }

  if (previousMessages && changeset) {
    messages.push({
      role: "user",
      content: `
      <currentDate>
      MM/DD/YYYY: ${new Date().toLocaleDateString()}
      </currentDate>
      Here is the updated changeset. Is anything else missing?
      <changeset>
      ${JSON.stringify(changeset)}
      </changeset>
      <currentDate>
      MM/DD/YYYY: ${new Date().toLocaleDateString()}
      </currentDate>
      `,
    });
  }
  console.log("judge messages", messages);

  // o3 effort low
  const completion = await openaiClient.responses.parse({
    model: "o3",
    reasoning: { effort: "low" },
    input: messages,
    text: {
      format: {
        name: "judgeChangesetSchema",
        schema: judgeSchema,
        type: "json_schema",
        strict: false,
      },
    },
  });

  const jsonResult = completion.output_parsed;
  messages.push({
    role: "assistant",
    content: JSON.stringify(jsonResult),
  });
  return { judgement: jsonResult as ChangesetJudgement, messages: messages };
};

export const performQa = async (currentNote: any): Promise<string> => {
  const input: EasyInputMessage[] = [
    {
      role: "developer",
      content: `
        # Role
        You are an expert hospice auditor. 

        # Instructions
        Your job is to review documentation that a clinician is currently completing and look for things that are missing and would lead to a denial or audit. 
        The clinician is completing their documentation via voice in our medical record, and we then structure their voice recordings into a JSON object. 
        You will be provided this JSON representation of the in-progress documentation. You must make concise suggestions on where the documentation can be improved, or critical areas that are missing. 

        You must follow these rules:

        # Rules
        - Utilize the Medicare conditions of participation (CoPs), conditions of payment, HIS (Hospice Item Set), and other industry-standard compliance regulations when reviewing the documentation, but **do not** reference them in your suggestions.
        - The created date, patient ID, and clinician/user ID will be auto-populated - do not make suggestions for these.
        - Not all fields are required - do not make a suggestion for a field that is not required for compliance. 
        - Do not make suggestions for things that need to happen in the future, for example, if a written CTI (certification of terminal illness ) is marked as pending, do not make a suggestion stating to ensure it is completed.
        - All suggestions must be specific - do not give generic suggestions such as "review for completeness and accuracy". 
        - Do not include any pre or post text. Only output the list of suggestions.
        - If referencing specific fields in the JSON, you must use human-readable versions (e.g "worseTimeOfDay" should be "Worse Time of Day").
        - Pay close attention to medications and make sure all required info is obtained, including route, dosage, frequency, etc. **Medication changes require a read back or teach back from the physician**.

        # Output
        Output only a concise list in markdown that will be shown to the clinician completing the documentation. Logically organize the list into sections, but do not duplicate suggestions across sections. 
        Clinicians will use your suggestions to make improvements, so be specific and use words and terminology the clinician will understand. Do not include any text other than the list.`,
    },
    {
      role: "user",
      content: `
      <currentDate>
      MM/DD/YYYY: ${new Date().toLocaleDateString()}
      </currentDate>
      Here is the in-progress visit documentation for an ${currentNote.noteType}:
        ${JSON.stringify(currentNote)}`,
    },
  ];

  const response = await openaiClient.responses.create({
    model: "gpt-4.1",
    input,
    temperature: 0,
  });

  return response.output_text;
};

export const generateTimelineItemSummary = async (request: Request) => {
  const input: EasyInputMessage[] = [
    {
      role: "system",
      content: `You are a hospice clinical scribe with extreme attention to detail. Your job is generate a 1-2 sentence summary of the provided request.`,
    },
    {
      role: "user",
      content: `Here is the request:
      ${JSON.stringify(request)}`,
    },
  ];

  const response = await openaiClient.responses.create({
    model: "gpt-4.1",
    input,
    temperature: 0,
  });

  return response.output_text;
};

export const generateIDGPatientSummary = async (request: Request) => {
  const input: EasyInputMessage[] = [
    {
      role: "system",
      content: `You are a hospice clinical scribe with extreme attention to detail. 
      Your job is generate a 3-5 sentence summary of the updates and discussion around
      this patient's IDG meeting session. The IDG meeting is a meeting of the interdisciplinary team
      that discusses the patient's care plan and goals. The IDG meeting is typically held every 14 days.
      There may be very little discussion, or there may be a lot of discussion.
      The summary should be concise and to the point.`,
    },
    {
      role: "user",
      content: `Here is the request:
      ${JSON.stringify(request)}`,
    },
  ];

  const response = await openaiClient.responses.create({
    model: "gpt-4.1",
    input,
    temperature: 0,
  });

  return response.output_text;
};

export const generatePatientSummaryForIDG = async (
  patientChart: any,
  pastVisitNotes: any,
) => {
  const input: EasyInputMessage[] = [
    {
      role: "system",
      content: `
      You are a hospice IDG (Interdisciplinary Group) patient‐summary generator.  
      When given a patient's historical data and recent visit notes, produce a concise IDG "patient snapshot" that will be read aloud during the meeting.  
      Your summary must:

      1. Start with a brief **Patient Overview** (name, age, primary terminal diagnosis, current benefit period and next recert date).  
      2. Highlight **Key Changes Since Last IDG**:
        • New or worsened symptoms (e.g., pain, dyspnea, agitation).  
        • Functional or cognitive decline (e.g., ADL dependencies, ambulation status).  
        • Any hospitalizations, ER visits, infections, or wound developments.  
        • Medication, DME, or supply changes that occurred.  
      3. Provide **Evidence of Decline**:
        • Vital sign trends or objective measures (e.g., PPS/FAST changes, weight loss, lab values if applicable).  
        • Concrete examples from recent notes showing deterioration (e.g., "now bedbound," "speech slowed").  
      4. Address **Psychosocial & Spiritual Concerns**:
        • Any new psychosocial stressors, caregiver strain, or bereavement risk flagged by Social Work or Chaplain notes.  
        • Changes in family support, living situation, or financial/resource needs.  
      5. Assess **Hospice Eligibility**:
        • Based on the above decline, state whether the patient continues to meet Medicare hospice criteria (prognosis ≤6 months).  
        • If there is any ambiguity or emerging reason they might not qualify, call it out.  
      6. End with a brief **Plan & Recommendations**:
        • Any recommended POC adjustments (new problems/goals/interventions).  
        • Pending orders or F2F requirements (e.g., "F2F due in 4 days").  
        • Referrals or follow-up tasks to assign at this IDG.

      Use only facts drawn from the provided data.  Omit any off-topic remarks or extraneous detail.  Structure your output with clear headings and bullet points in markdown format. 
      The first section should be the "Patient Overview", do not include pre-amble or any text after your last section.`,
    },
    {
      role: "user",
      content: `Here is all the information you need to generate a patient summary for IDG:
      <patientChart>
      ${JSON.stringify(patientChart)}
      </patientChart>
      <pastVisitNotes>
      ${JSON.stringify(pastVisitNotes)}
      </pastVisitNotes>
      `,
    },
  ];

  const response = await openaiClient.responses.create({
    model: "gpt-4.1",
    input,
    temperature: 0,
  });

  return response.output_text;
};

export const generateLastNoteSummary = async (
  lastVisitJson: Record<string, any>,
) => {
  const input: EasyInputMessage[] = [
    {
      role: "system",
      content: `You are a hospice clinical visit summarizer. When given a JSON object representing a single, most-recent discipline visit (RN, MSW, Chaplain, etc.), you will produce a concise Markdown summary highlighting:

1. **Patient/Caregiver Concerns**: Any issues, questions, or complaints raised  
2. **Assessment Findings**: Key observations (vitals, symptoms, functional status, wounds)  
3. **Interventions Performed**: What actions or teaching the clinician carried out  
4. **Orders & Changes**: New or modified medications, DME, supplies, POC updates  
5. **Education & Referrals**: Topics taught or referrals made  
6. **Follow-Up & Next Steps**: Planned tasks, upcoming visits, or alerts for IDG  

**Important:** Use **only** the information present in the JSON object. Do **not** infer, embellish, or add any details that are not explicitly included in the provided JSON.
Use clear headings and bullet points.  Do **not** include the raw JSON—only the human‐readable summary. Do **not** include any pre-amble or ending message. `,
    },
    {
      role: "user",
      content: `Below is the JSON record of the most recent visit. Generate a Markdown-formatted summary under the six headings defined in the system prompt, using **only** the data in this JSON.
      <lastVisitJson>
      ${JSON.stringify(lastVisitJson)}
      </lastVisitJson>`,
    },
  ];

  const response = await openaiClient.responses.create({
    model: "gpt-4.1",
    input,
    temperature: 0,
  });

  return response.output_text;
};

export const generateModifiedChangeset = async (
  patientChart: PatientChart,
  changeset: Changeset,
  audioTranscription: string,
  serializedChart: any,
): Promise<Changeset> => {
  const input: EasyInputMessage[] = [
    {
      role: "system",
      content: `  You are a hospice clinical scribe with extreme attention to detail. Your job is to modify an existing changeset based on instructions provided in an audio transcription.

      # Instructions
      You will be given:
      1. A patient chart in JSON format
      2. An existing changeset (not yet applied to the patient chart) in JSON format
      3. An audio transcription containing instructions for modifying, updating, or correcting the changeset

      Your task is to:
      - Carefully analyze the audio transcription for any instructions to modify, add, remove, or update items in the changeset
      - Apply those modifications to create a new changeset
      - Maintain the same JSON schema structure as the original changeset
      - Ensure all changes are grounded in the audio transcription
      - Preserve any existing changeset items that are not mentioned in the audio instructions 
        - (e.g. if a changeset has 3 plan of care updates, and the audio transcription says to use different terminology for 1 plan of care update, you should end up with 3 total plan of care updates (1 modified, 2 unchanged))

      # Rules
      - Only modify items that are explicitly mentioned in the audio transcription
      - If the audio mentions removing a changeset item, remove it from the output
      - **IMPORTANT PATH FORMATTING**: For existing items in lists, always use the ID-keyed path format (e.g., "/medicationsById/med-a12b3c4d/dosage"). For NEW items being added to arrays, use '/-' at the end of the original array path (e.g., "/medications/-")
      - If the audio mentions modifying an existing change, update the relevant fields
      - Maintain all required fields for each changeset item (op, path, value, reason, name, readBackRequired, readBackPerformed)
      - Do not invent or infer changes not explicitly stated in the audio
      - Ensure 'readBackRequired' is set to true for medication changes (adding/stopping/dosage/frequency/strength/start/end date changes) or oxygen-related DME changes
      - Only set 'readBackPerformed' to true if explicitly stated in the audio transcription`,
    },
    {
      role: "user",
      content: `
      Here is the current patient chart:
      ${JSON.stringify(serializedChart)}

      Here is the current changeset (not yet applied to the patient chart):
      ${JSON.stringify(changeset)}
      
      Here is the audio transcription with modification instructions:
      ${audioTranscription}

      Please output the modified changeset following the same JSON schema structure.`,
    },
  ];

  const response = await openaiClient.responses.parse({
    model: "o3",
    reasoning: {
      effort: "high",
    },
    input,
    text: {
      format: {
        name: "ModifiedChangesetSchema",
        schema: changesetSchema,
        type: "json_schema",
        strict: false,
      },
    },
  });

  // Translate ID-keyed paths back to numeric indices
  const translatedChangeset = translateChangesetPaths(
    response.output_parsed as Changeset,
    patientChart,
  );
  return translatedChangeset;

  // Validate and fix changeset operations
  // const validatedChangeset = validateChangesetOperations(translatedChangeset);

  // return validatedChangeset;
};
