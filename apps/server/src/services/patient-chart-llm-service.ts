import { ResponseInput } from "openai/resources/responses/responses.mjs";
import { openaiClient } from "./llm-service";

const planOfCareSchema = {
  $id: "https://hospice-os.org/schemas/PlanOfCare",
  $schema: "http://json-schema.org/draft-07/schema#",
  title: "Plan of Care",
  type: "object",
  additionalProperties: false,
  required: [
    "problemsGoalsInterventions",
    "visitFrequencies",
    "educationAndCoordination",
    "reviewAndSignOff",
  ],
  properties: {
    reviewAndSignOff: {
      type: "object",
      additionalProperties: false,
      required: ["createdBy", "creationDate", "hospicePhysicianSignOffDate"],
      properties: {
        createdBy: {
          type: "string",
        },
        creationDate: {
          type: "string",
          format: "date",
        },
        hospicePhysicianSignOffDate: {
          type: "string",
          format: "date",
        },
      },
    },
    visitFrequencies: {
      type: "array",
      items: {
        type: "object",
        additionalProperties: false,
        required: ["discipline", "frequency", "startDate", "endDate"],
        properties: {
          discipline: {
            type: "string",
            enum: ["RN", "Aide", "SW", "Chaplain", "Volunteer", "Other"],
          },
          frequency: {
            type: "string",
            description:
              "The planned frequency of visits for this discipline (e.g., '2x/week')",
          },
          startDate: {
            type: "string",
            format: "date",
            description:
              "The date when visits for this discipline are scheduled to begin",
          },
          endDate: {
            type: "string",
            format: "date",
            description:
              "The date when visits for this discipline are scheduled to end",
          },
        },
      },
    },
    educationAndCoordination: {
      type: "object",
      additionalProperties: false,
      required: ["familyUnderstandsPlan", "educationTopicsProvided"],
      properties: {
        familyUnderstandsPlan: {
          type: "boolean",
        },
        educationTopicsProvided: {
          type: "array",
          description:
            "A list of education topics provided to the patient, family, and/or caregiver.",
          items: {
            type: "string",
          },
        },
      },
    },
    problemsGoalsInterventions: {
      type: "object",
      additionalProperties: false,
      required: [
        "physical",
        "spiritual",
        "functional",
        "bereavement",
        "psychosocial",
      ],
      properties: {
        physical: {
          type: "array",
          items: { $ref: "#/$defs/PGIBlock" },
        },
        spiritual: {
          type: "array",
          items: { $ref: "#/$defs/PGIBlock" },
        },
        functional: {
          type: "array",
          items: { $ref: "#/$defs/PGIBlock" },
        },
        bereavement: {
          type: "array",
          items: { $ref: "#/$defs/PGIBlock" },
        },
        psychosocial: {
          type: "array",
          items: { $ref: "#/$defs/PGIBlock" },
        },
      },
    },
  },
  $defs: {
    Goal: {
      type: "object",
      additionalProperties: false,
      required: ["description", "completeBy"],
      properties: {
        description: {
          type: "string",
        },
        completeBy: {
          type: "string",
        },
      },
    },
    Intervention: {
      type: "object",
      additionalProperties: false,
      required: ["description", "assignedTo"],
      properties: {
        description: {
          type: "string",
        },
        assignedTo: {
          type: "string",
        },
      },
    },
    PGIBlock: {
      type: "object",
      additionalProperties: false,
      required: ["problem", "goals", "interventions"],
      properties: {
        problem: {
          type: "string",
        },
        goals: {
          type: "array",
          items: { $ref: "#/$defs/Goal" },
        },
        interventions: {
          type: "array",
          items: { $ref: "#/$defs/Intervention" },
        },
      },
    },
  },
};

export const generatePlanOfCare = async (
  transcription: string,
  originalPlanOfCare?: string,
  messageHistory?: ResponseInput,
  additionalInstructions?: string,
) => {
  const messages: ResponseInput =
    messageHistory ||
    ([
      {
        role: "system",
        content: `
          You are a highly trained hospice scribe with extreme attention to detail.
          Your job is to fill in the initial plan of care with the data from the transcription.
          The transcription is provided in the user message.
          The schema is a JSON schema that defines the plan of care.
          Do not make up any information. Only use the information provided in the transcription.
          Return the full and completed plan of care.
          `,
      },
      {
        role: "user",
        content: transcription,
      },
    ] as ResponseInput);

  if (originalPlanOfCare) {
    messages.push({
      role: "user",
      content: `
        The previous version of the plan of care is provided in the user message, it may be missing some information.
        Review the plan of care and make any necessary changes, output the entire plan of care.
        <planOfCare>
        ${originalPlanOfCare}
        </planOfCare>
        `,
    });
  }

  if (additionalInstructions) {
    messages.push({
      role: "user",
      content: additionalInstructions,
    });
  }

  const completion = await openaiClient.responses.parse({
    model: "o3",
    max_output_tokens: 1000000,
    input: messages,
    reasoning: {
      effort: "high",
    },
    text: {
      format: {
        name: "PlanOfCare",
        schema: planOfCareSchema,
        type: "json_schema",
        strict: true,
      },
    },
  });

  const jsonResult = completion.output_parsed;
  return jsonResult as unknown;
};

export type Subdocument = {
  startPage: number;
  endPage: number;
  type: string;
  confidence: number;
};

export const SUBDOCUMENT_CLASSIFICATION_TYPES = [
  "face_sheet",
  "medication_profile",
  "allergy_profile",
  "visit_frequency_profile",
  "body_measurements",
  "vital_sign_measurements",
  "symptom_ratings",
  "wounds",
  "physician_order", // another name for standard order
  "initial_plan_of_care_order", // done by nurse and approved by physician
  "certification_of_terminal_illness_order", // done by physician
  "skilled_nurse_visit_note", // done by nurse
  "nursing_comprehensive_assessment_order", // done by nurse at admission
  "psychosocial_comprehensive_assessment_note", // done by social worker at admission
  "bereavement_assesment_note", // done by social worker at admission
  "chaplain_assessment_note", // done by chaplain at admission
  "psychosocial_visit_note", // done by social worker
  "care_coordination_note", // done by social worker
  "chaplain_visit_note", // done by chaplain
  "aid_visit_note", // done by an nurses assistant or LPN - this can be a volunteer as well.
  "idg_note",
  "other",
];

export const SUBDOCUMENT_CLASSIFICATION_TYPES_MAP = {
  physician_order: "01968e19-eb10-7fb2-aa6d-df331feab22a", // New Order
  initial_plan_of_care_order: "01968e19-eb10-7fb2-aa6d-df331feab22a", // New Order (Do we need to differentiate between this and the physician order?)
  certification_of_terminal_illness_order:
    "01968e21-0b56-7957-8055-d05689af43b8", // Written CTI
  nursing_comprehensive_assessment_order:
    "921f31ad-ce09-4bfb-aa82-703edc5a2643", // RN Admission Assessment
  psychosocial_comprehensive_assessment_note:
    "01968bc6-fe28-77bf-9745-4b63e19d61e3", // MSW Psychosocial
  bereavement_assesment_note: "01968bc6-fe28-77bf-9745-4b63e19d61e3", // MSW Psychosocial
  chaplain_assessment_note: "01968e16-a584-7822-8cec-2d1ec5ad76d7", // Chaplain Routine Visit
  chaplain_visit_note: "01968e16-a584-7822-8cec-2d1ec5ad76d7", // Chaplain Routine Visit
  aid_visit_note: "434b60ac-55e5-451c-a388-aae40f3048f0", // Aid visit note
  skilled_nurse_visit_note: "01968dff-1723-7a65-b9da-c8d2fc5b93df", // RN Routine Visit
  psychosocial_visit_note: "01968e15-1d42-79b6-8198-b8ea856b8adf", // MSW Routine Visit
  care_coordination_note: "019702b3-9859-7ac9-8089-212705e555ea", // Care Coordination Note
  idg_note: "477831b8-d693-4acb-9256-721bf2897bbb", // IDG Note
  face_sheet: "none",
  medication_profile: "none",
  allergy_profile: "none",
  visit_frequency_profile: "none",
  body_measurements: "none",
  vital_sign_measurements: "none",
  symptom_ratings: "none",
  wounds: "none",
  other: "none",
};
const subdocumentClassificationSchema = {
  $id: "https://hospice-os.org/schemas/SubdocumentClassification",
  $schema: "http://json-schema.org/draft-07/schema#",
  title: "Subdocument Classification",
  type: "object",
  additionalProperties: false,
  required: ["subdocuments"],
  properties: {
    subdocuments: {
      type: "array",
      items: {
        type: "object",
        additionalProperties: false,
        required: ["startPage", "endPage", "type", "confidence"],
        properties: {
          startPage: {
            type: "number",
          },
          endPage: {
            type: "number",
          },
          type: {
            type: "string",
            enum: SUBDOCUMENT_CLASSIFICATION_TYPES,
          },
          confidence: {
            type: "number",
            minimum: 0,
            maximum: 100,
          },
        },
      },
    },
  },
};

export const classifySubdocuments = async (
  transcription: string,
): Promise<
  {
    startPage: number;
    endPage: number;
    type: string;
    confidence: number;
  }[]
> => {
  const messages: ResponseInput = [
    {
      role: "system",
      content: [
        {
          type: "input_text",
          text: `Classify Each Individual Subdocument in this Patient Chart Export as:
            
            ###${SUBDOCUMENT_CLASSIFICATION_TYPES.join(", ")}.
            ###Output as a JSON Array:
            [Example]
            [
            {"startPage": #, "endPage": #, "type": "...", confidence: 0-100},
            ...
            ]
            
            ###Pay very close attention to the start and end page indices. Look for timestamps, sub-page numbers (1 of 4, 2 of 2, etc) and identifiers to properly collate pages. If the same sub-document types are back to back you must make separate entries for each one. For instance, 3 single paged orders back to back would yield an array with 3 entries. 
            2 visit notes that are 3 pages each back to back would yield an array with 2 entries###
            
            Note: Pages that don't match any of the given types can be classified as "Other".`,
        },
      ],
    },
    {
      role: "user",
      content: [
        {
          type: "input_text",
          text: transcription,
        },
      ],
    },
  ] as ResponseInput;

  const completion = await openaiClient.responses.parse({
    model: "o3",
    max_output_tokens: 1000000,
    reasoning: {
      effort: "high",
    },
    input: messages,
    text: {
      format: {
        name: "SubdocumentClassification",
        schema: subdocumentClassificationSchema,
        type: "json_schema",
        strict: true,
      },
    },
  });

  const jsonResult = completion.output_parsed;
  if (!jsonResult || !("subdocuments" in jsonResult)) {
    throw new Error("No subdocuments found");
  }
  return jsonResult["subdocuments"] as unknown as {
    startPage: number;
    endPage: number;
    type: string;
    confidence: number;
  }[];
};
