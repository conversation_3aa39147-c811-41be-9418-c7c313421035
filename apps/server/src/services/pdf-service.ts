import { readFileSync, writeFileSync, mkdirSync } from "fs";
import { join, dirname, basename, extname } from "path";
import { uploadFile } from "./storage-service";

export interface PdfToJpegOptions {
  quality?: number; // JPEG quality (1-100)
  density?: number; // DPI for rendering (default 150)
  outputDir?: string; // Local directory to save images
  uploadToS3?: boolean; // Whether to upload to S3
  s3KeyPrefix?: string; // S3 key prefix for uploaded images
  format?: "jpeg" | "png"; // Output format (default: jpeg)
}

export interface PdfPageResult {
  pageNumber: number;
  filename: string;
  s3Key?: string;
}

export interface PdfSplitResult {
  totalPages: number;
  pages: PdfPageResult[];
  outputDir?: string;
}

/**
 * Split a PDF into individual JPEG pages
 * @param pdfBuffer - PDF file as Buffer
 * @param options - Configuration options
 * @returns Promise<PdfSplitResult>
 */
export const splitPdfToJpegs = async (
  pdfBuffer: Buffer,
  options: PdfToJpegOptions = {},
): Promise<PdfSplitResult> => {
  const {
    density = 150,
    outputDir,
    uploadToS3 = false,
    s3KeyPrefix = "pdf-pages",
    format = "jpeg",
  } = options;

  try {
    console.log("Starting PDF conversion with pdf-img-convert");
    const { convert } = await import("pdf-img-convert");
    // Convert PDF to images using pdf-img-convert
    const imageBuffers = (await convert(pdfBuffer, {
      base64: false,
      scale: density / 72, // Convert DPI to scale factor (72 is default DPI)
    })) as Buffer[];

    console.log(`Converted ${imageBuffers.length} pages`);

    const pages: PdfPageResult[] = [];
    const fileExtension = format === "png" ? "png" : "jpg";

    for (let i = 0; i < imageBuffers.length; i++) {
      const pageNumber = i + 1;
      const filename = `page_${pageNumber}.${fileExtension}`;
      const imageBuffer = imageBuffers[i];

      console.log(`Processing page ${pageNumber}`, imageBuffer.length, "bytes");

      const result: PdfPageResult = {
        pageNumber,
        filename,
      };

      // Save to local directory if specified
      if (outputDir) {
        mkdirSync(outputDir, { recursive: true });
        const filepath = join(outputDir, filename);
        writeFileSync(filepath, imageBuffer);
      }

      // Upload to S3 if specified
      if (uploadToS3) {
        const s3Key = `${s3KeyPrefix}/${filename}`;
        await uploadFile(s3Key, imageBuffer);
        result.s3Key = s3Key;
      }

      pages.push(result);
    }

    return {
      totalPages: pages.length,
      pages,
      outputDir,
    };
  } catch (error: unknown) {
    console.log("error", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to split PDF: ${errorMessage}`);
  }
};

/**
 * Split a PDF file from filesystem into JPEG pages
 * @param pdfPath - Path to PDF file
 * @param options - Configuration options
 * @returns Promise<PdfSplitResult>
 */
export const splitPdfFileToJpegs = async (
  pdfPath: string,
  options: PdfToJpegOptions = {},
): Promise<PdfSplitResult> => {
  try {
    console.log("Reading PDF file:", pdfPath);
    const pdfBuffer = readFileSync(pdfPath);

    // If no output directory specified, create one next to the PDF
    if (!options.outputDir) {
      const pdfDir = dirname(pdfPath);
      const pdfName = basename(pdfPath, extname(pdfPath));
      options.outputDir = join(pdfDir, `${pdfName}_pages`);
    }

    return await splitPdfToJpegs(pdfBuffer, options);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to read PDF file: ${errorMessage}`);
  }
};

/**
 * Get a specific page from PDF as JPEG
 * @param pdfBuffer - PDF file as Buffer
 * @param pageNumber - Page number (1-based)
 * @param options - Configuration options
 * @returns Promise<Buffer> - JPEG buffer
 */
export const getPdfPageAsJpeg = async (
  pdfBuffer: Buffer,
  pageNumber: number,
  options: Omit<
    PdfToJpegOptions,
    "outputDir" | "uploadToS3" | "s3KeyPrefix"
  > = {},
): Promise<Buffer> => {
  const { density = 150 } = options;

  if (pageNumber < 1) {
    throw new Error("Page number must be 1 or greater");
  }

  try {
    const { convert } = await import("pdf-img-convert");
    // Convert all pages first (pdf-img-convert doesn't support single page extraction)
    const imageBuffers = (await convert(pdfBuffer, {
      base64: false,
      scale: density / 72,
    })) as Buffer[];

    if (pageNumber > imageBuffers.length) {
      throw new Error(
        `Page ${pageNumber} does not exist. PDF has ${imageBuffers.length} pages.`,
      );
    }

    return imageBuffers[pageNumber - 1]; // Convert to 0-based index
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to extract page ${pageNumber}: ${errorMessage}`);
  }
};

/**
 * Get PDF page count using pdf-img-convert
 * @param pdfBuffer - PDF file as Buffer
 * @returns Promise<number> - Number of pages
 */
export const getPdfPageCount = async (pdfBuffer: Buffer): Promise<number> => {
  try {
    const { convert } = await import("pdf-img-convert");
    // Convert with minimal quality for faster processing
    const imageBuffers = (await convert(pdfBuffer, {
      base64: false,
      scale: 0.1, // Very low scale for faster processing
    })) as Buffer[];

    return imageBuffers.length;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to get page count: ${errorMessage}`);
  }
};

/**
 * Batch process multiple PDFs
 * @param pdfBuffers - Array of PDF buffers with identifiers
 * @param options - Configuration options
 * @returns Promise<Record<string, PdfSplitResult>>
 */
export const batchSplitPdfs = async (
  pdfBuffers: Array<{ id: string; buffer: Buffer }>,
  options: PdfToJpegOptions = {},
): Promise<Record<string, PdfSplitResult>> => {
  const results: Record<string, PdfSplitResult> = {};

  for (const { id, buffer } of pdfBuffers) {
    try {
      const pdfOptions = {
        ...options,
        s3KeyPrefix: options.s3KeyPrefix
          ? `${options.s3KeyPrefix}/${id}`
          : `pdf-pages/${id}`,
        outputDir: options.outputDir ? join(options.outputDir, id) : undefined,
      };

      results[id] = await splitPdfToJpegs(buffer, pdfOptions);
    } catch (error: unknown) {
      results[id] = {
        totalPages: 0,
        pages: [],
        outputDir: undefined,
      };
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Failed to process PDF ${id}:`, errorMessage);
    }
  }

  return results;
};

const main = async () => {
  const pdfPath = "/Users/<USER>/Downloads/ref.pdf";
  const pdfSplitResult = await splitPdfFileToJpegs(pdfPath, {
    uploadToS3: false,
  });
  console.log(pdfSplitResult);
};

if (require.main === module) {
  main();
}
