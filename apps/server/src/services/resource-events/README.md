# Resource Event System

This module provides a flexible event system for listening to resource changes (create, update, delete) in the application. It follows the observer pattern to allow different parts of the application to react to resource changes without tight coupling.

## Overview

The resource event system consists of the following components:

1. **Event Types**: Defines the types of events that can occur (CREATED, UPDATED, DELETED).
2. **Event Dispatcher**: Manages listeners and dispatches events to them.
3. **Listeners**: Components that react to resource events.

## How It Works

1. The `ResourceService` triggers events when resources are created, updated, or deleted.
2. The `ResourceEventDispatcher` dispatches these events to registered listeners.
3. Each listener implements the `ResourceListener` interface and can react to events as needed.

## Usage

### Registering a Regular Listener

You can register listeners for specific resource types and event types:

```typescript
import { resourceEventDispatcher, ResourceEventType } from './services/resource-events';

// Create a listener
const myListener = {
  onResourceEvent: (event) => {
    console.log(`Resource ${event.resource.id} was ${event.eventType}`);
    // Handle the event
  }
};

// Register for all resource types and all event types
resourceEventDispatcher.registerListener(myListener);

// Register for a specific resource type
resourceEventDispatcher.registerListener(myListener, 'patient');

// Register for a specific resource type and event type
resourceEventDispatcher.registerListener(myListener, 'patient', ResourceEventType.CREATED);
```

### Unregistering a Regular Listener

```typescript
// Unregister from all resource types and event types
resourceEventDispatcher.unregisterListener(myListener);

// Unregister from a specific resource type
resourceEventDispatcher.unregisterListener(myListener, 'patient');

// Unregister from a specific resource type and event type
resourceEventDispatcher.unregisterListener(myListener, 'patient', ResourceEventType.CREATED);
```

### Registering a JSON Path Listener

You can register listeners for changes to specific JSON paths within resources:

```typescript
import { resourceEventDispatcher } from './services/resource-events';

// Create a JSON path listener
const myPathListener = {
  onJsonPathChanged: (event) => {
    console.log(`Path ${event.path} changed in resource ${event.resource.id}`);
    console.log(`Old value: ${JSON.stringify(event.pathOldValue)}`);
    console.log(`New value: ${JSON.stringify(event.pathNewValue)}`);
    // Handle the path change
  }
};

// Register for a specific resource type and JSON path
resourceEventDispatcher.registerJsonPathListener(
  myPathListener,
  'request',
  'agencyActions[*].completed'
);
```

### Unregistering a JSON Path Listener

```typescript
// Unregister from all resource types and paths
resourceEventDispatcher.unregisterJsonPathListener(myPathListener);

// Unregister from a specific resource type (all paths)
resourceEventDispatcher.unregisterJsonPathListener(myPathListener, 'request');

// Unregister from a specific resource type and path
resourceEventDispatcher.unregisterJsonPathListener(
  myPathListener, 
  'request', 
  'agencyActions[*].completed'
);
```

### JSON Path Syntax

The system supports a simplified JSON path syntax:

- `property` - Access a property directly
- `parent.child` - Access a nested property
- `array[0]` - Access an array element by index
- `array[*]` - Access all elements in an array (returns an array of values)
- `array[*].property` - Access a property of all elements in an array

Examples:
- `name` - The name property
- `person.address.city` - The city property in the address object in the person object
- `items[0]` - The first item in the items array
- `items[*].name` - The name property of all items in the array
- `agencyActions[*].completed` - The completed property of all agency actions

### Array Change Detection

When using wildcard paths (`[*]`), the system intelligently compares individual array elements rather than the entire array. This provides several benefits:

1. **Only Changed Elements**: Listeners are only notified with elements that actually changed, not all elements
2. **Precise Change Tracking**: You can see exactly which elements were added, removed, or modified
3. **Efficient Processing**: Listeners can focus only on the elements that changed, improving performance

For example, if you're monitoring `agencyActions[*].completed` and only one action changes from `false` to `true`:

```typescript
// Old approach might return all values:
{
  oldValue: [false, false, false],
  newValue: [true, false, false]
}

// New approach only returns changed values:
{
  oldValue: [false], // First item was false
  newValue: [true]   // First item is now true
}
```

This makes it much easier to identify and respond to specific changes, especially in large arrays.

### Creating Custom Listeners

Create a class that implements the `ResourceListener` interface:

```typescript
import { ResourceEvent, ResourceListener } from './services/resource-events';

export class MyCustomListener implements ResourceListener {
  public onResourceEvent(event: ResourceEvent): void {
    const { eventType, resourceType, resource } = event;
    
    // Handle the event based on its type and the resource type
    switch (eventType) {
      case ResourceEventType.CREATED:
        // Handle creation
        break;
      case ResourceEventType.UPDATED:
        // Handle update
        if (event.previousState) {
          // Compare with previous state
        }
        break;
      case ResourceEventType.DELETED:
        // Handle deletion
        break;
    }
  }
}
```

## Example Listeners

The system includes several example listeners:

1. **LoggingListener**: Logs all resource events to the console.
2. **NotificationListener**: Simulates sending notifications for resource events.
3. **AuditTrailListener**: Simulates creating audit trail records for resource changes.
4. **IntegrationListener**: Simulates integration with external systems.

## Testing

You can test the resource event system using the provided test file:

```bash
ts-node src/test-resource-events.ts
```

This will register example listeners and dispatch mock events to demonstrate the system's functionality.

## Extending the System

To extend the system for your own needs:

1. Create custom listeners for specific use cases.
2. Register them in the `register-listeners.ts` file.
3. Ensure the resource event system is initialized when the application starts.

## Architecture

The resource event system follows these design principles:

- **Loose Coupling**: Listeners are not tightly coupled to the resource service.
- **Single Responsibility**: Each listener has a specific responsibility.
- **Open/Closed Principle**: The system is open for extension but closed for modification.
- **Dependency Inversion**: High-level modules don't depend on low-level modules.
