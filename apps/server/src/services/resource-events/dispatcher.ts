import { ResourceEvent, ResourceEventType, ResourceListener, JsonPathListener, BaseResource } from './types';
import { pathValueChanged } from './json-path-utils';

/**
 * Class responsible for managing resource event listeners and dispatching events
 */
export class ResourceEventDispatcher {
  private static instance: ResourceEventDispatcher;
  
  // Map of resource types to event types to listeners
  private listeners: Map<string, Map<ResourceEventType, Set<ResourceListener>>>;
  
  // Map for global listeners that listen to all resource types
  private globalListeners: Map<ResourceEventType, Set<ResourceListener>>;
  
  // Map of resource types to JSON path listeners
  // Each resource type has a map of JSON paths to listeners
  private jsonPathListeners: Map<string, Map<string, Set<JsonPathListener>>>;
  
  private constructor() {
    this.listeners = new Map();
    this.globalListeners = new Map();
    this.jsonPathListeners = new Map();
    
    // Initialize maps for all event types
    Object.values(ResourceEventType).forEach(eventType => {
      this.globalListeners.set(eventType, new Set());
    });
  }
  
  /**
   * Get the singleton instance of the ResourceEventDispatcher
   */
  public static getInstance(): ResourceEventDispatcher {
    if (!ResourceEventDispatcher.instance) {
      ResourceEventDispatcher.instance = new ResourceEventDispatcher();
    }
    return ResourceEventDispatcher.instance;
  }
  
  /**
   * Register a listener for a specific resource type and event type
   * @param listener The listener to register
   * @param resourceType The resource type to listen for, or undefined for all resource types
   * @param eventType The event type to listen for, or undefined for all event types
   */
  public registerListener(
    listener: ResourceListener,
    resourceType?: string,
    eventType?: ResourceEventType
  ): void {
    if (!resourceType) {
      // Register as a global listener
      if (!eventType) {
        // Register for all event types
        Object.values(ResourceEventType).forEach(type => {
          this.globalListeners.get(type)?.add(listener);
        });
      } else {
        // Register for a specific event type
        this.globalListeners.get(eventType)?.add(listener);
      }
      return;
    }
    
    // Register for a specific resource type
    if (!this.listeners.has(resourceType)) {
      this.listeners.set(resourceType, new Map());
      
      // Initialize maps for all event types
      Object.values(ResourceEventType).forEach(type => {
        this.listeners.get(resourceType)?.set(type, new Set());
      });
    }
    
    if (!eventType) {
      // Register for all event types
      Object.values(ResourceEventType).forEach(type => {
        this.listeners.get(resourceType)?.get(type)?.add(listener);
      });
    } else {
      // Register for a specific event type
      this.listeners.get(resourceType)?.get(eventType)?.add(listener);
    }
  }
  
  /**
   * Unregister a listener
   * @param listener The listener to unregister
   * @param resourceType The resource type, or undefined for all resource types
   * @param eventType The event type, or undefined for all event types
   */
  public unregisterListener(
    listener: ResourceListener,
    resourceType?: string,
    eventType?: ResourceEventType
  ): void {
    if (!resourceType) {
      // Unregister from global listeners
      if (!eventType) {
        // Unregister from all event types
        Object.values(ResourceEventType).forEach(type => {
          this.globalListeners.get(type)?.delete(listener);
        });
      } else {
        // Unregister from a specific event type
        this.globalListeners.get(eventType)?.delete(listener);
      }
      return;
    }
    
    // Unregister from a specific resource type
    if (!this.listeners.has(resourceType)) {
      return;
    }
    
    if (!eventType) {
      // Unregister from all event types
      Object.values(ResourceEventType).forEach(type => {
        this.listeners.get(resourceType)?.get(type)?.delete(listener);
      });
    } else {
      // Unregister from a specific event type
      this.listeners.get(resourceType)?.get(eventType)?.delete(listener);
    }
  }
  
  /**
   * Register a listener for changes to a specific JSON path in a resource
   * 
   * @param listener The JSON path listener to register
   * @param resourceType The resource type to listen for
   * @param jsonPath The JSON path to monitor for changes
   */
  public registerJsonPathListener(
    listener: JsonPathListener,
    resourceType: string,
    jsonPath: string
  ): void {
    // Initialize the maps if they don't exist
    if (!this.jsonPathListeners.has(resourceType)) {
      this.jsonPathListeners.set(resourceType, new Map());
    }
    
    const resourcePaths = this.jsonPathListeners.get(resourceType)!;
    
    if (!resourcePaths.has(jsonPath)) {
      resourcePaths.set(jsonPath, new Set());
    }
    
    // Add the listener
    resourcePaths.get(jsonPath)!.add(listener);
  }
  
  /**
   * Unregister a JSON path listener
   * 
   * @param listener The JSON path listener to unregister
   * @param resourceType The resource type, or undefined for all resource types
   * @param jsonPath The JSON path, or undefined for all JSON paths
   */
  public unregisterJsonPathListener(
    listener: JsonPathListener,
    resourceType?: string,
    jsonPath?: string
  ): void {
    if (!resourceType) {
      // Unregister from all resource types
      for (const [, pathMap] of this.jsonPathListeners.entries()) {
        if (!jsonPath) {
          // Unregister from all paths
          for (const [, listeners] of pathMap.entries()) {
            listeners.delete(listener);
          }
        } else {
          // Unregister from specific path
          const listeners = pathMap.get(jsonPath);
          if (listeners) {
            listeners.delete(listener);
          }
        }
      }
      return;
    }
    
    // Get the path map for this resource type
    const pathMap = this.jsonPathListeners.get(resourceType);
    if (!pathMap) {
      return;
    }
    
    if (!jsonPath) {
      // Unregister from all paths for this resource type
      for (const [, listeners] of pathMap.entries()) {
        listeners.delete(listener);
      }
    } else {
      // Unregister from specific path
      const listeners = pathMap.get(jsonPath);
      if (listeners) {
        listeners.delete(listener);
      }
    }
  }
  
  /**
   * Check if a resource has any JSON path listeners
   * 
   * @param resourceType The resource type to check
   * @returns True if the resource type has any JSON path listeners
   */
  private hasJsonPathListeners(resourceType: string): boolean {
    const pathMap = this.jsonPathListeners.get(resourceType);
    return pathMap !== undefined && pathMap.size > 0;
  }
  
  /**
   * Check for JSON path changes and dispatch events
   * 
   * @param resource The current resource
   * @param previousState The previous state of the resource
   */
  private async dispatchJsonPathEvents(
    resource: BaseResource,
    previousState: BaseResource | undefined
  ): Promise<void> {
    if (!previousState || !resource) {
      return;
    }
    
    const resourceType = resource.resourceType;
    
    // Check if this resource type has any JSON path listeners
    if (!this.hasJsonPathListeners(resourceType)) {
      return;
    }
    
    // Get the path map for this resource type
    const pathMap = this.jsonPathListeners.get(resourceType)!;
    
    // Check each path for changes
    for (const [path, listeners] of pathMap.entries()) {
      // Skip if there are no listeners
      if (listeners.size === 0) {
        continue;
      }
      
      // Check if the path has changed
      const { changed, oldValue, newValue } = pathValueChanged(
        previousState,
        resource,
        path
      );
      
      // Skip if the path hasn't changed
      if (!changed) {
        continue;
      }
      
      // Create a PATH_CHANGED event
      const event: ResourceEvent = {
        eventType: ResourceEventType.PATH_CHANGED,
        resourceType,
        resource,
        previousState,
        timestamp: new Date(),
        path,
        pathOldValue: oldValue,
        pathNewValue: newValue
      };
      
      // Dispatch to regular listeners (resourceType + PATH_CHANGED)
      await this.dispatchEvent(event);
      
      // Dispatch to JSON path listeners
      const promises = Array.from(listeners).map(listener => {
        try {
          const result = listener.onJsonPathChanged(event);
          return result instanceof Promise ? result : Promise.resolve();
        } catch (error) {
          console.error(`Error in JSON path listener: ${error}`);
          return Promise.resolve();
        }
      });
      
      // Wait for all listeners to complete
      await Promise.all(promises);
    }
  }
  
  /**
   * Dispatch an event to all registered listeners
   * @param event The event to dispatch
   */
  public async dispatchEvent(event: ResourceEvent): Promise<void> {
    const { eventType, resourceType } = event;
    
    // Get listeners for this resource type and event type
    const typeListeners = this.listeners.get(resourceType)?.get(eventType) || new Set();
    
    // Get global listeners for this event type
    const globalTypeListeners = this.globalListeners.get(eventType) || new Set();
    
    // Combine listeners
    const allListeners = [...typeListeners, ...globalTypeListeners];
    
    // Dispatch event to all listeners
    const promises = allListeners.map(listener => {
      try {
        const result = listener.onResourceEvent(event);
        return result instanceof Promise ? result : Promise.resolve();
      } catch (error) {
        console.error(`Error in resource listener: ${error}`);
        return Promise.resolve();
      }
    });
    
    // Wait for all listeners to complete
    await Promise.all(promises);
    
    // Check for JSON path changes for UPDATED events
    if (eventType === ResourceEventType.UPDATED && event.previousState) {
      await this.dispatchJsonPathEvents(event.resource, event.previousState);
    }
  }
}

// Export a singleton instance
export const resourceEventDispatcher = ResourceEventDispatcher.getInstance();
