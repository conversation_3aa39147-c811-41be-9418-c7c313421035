import { ResourceEvent, ResourceEventType, ResourceListener } from './types';

/**
 * Example listener that logs all resource events to the console
 */
export class LoggingListener implements ResourceListener {
  public onResourceEvent(event: ResourceEvent): void {
    const { eventType, resourceType, resource, timestamp } = event;
    
    console.log(`[${timestamp.toISOString()}] ${eventType} event for ${resourceType} resource:`, {
      id: resource.id,
      resourceType: resource.resourceType,
      schemaId: resource.schemaId,
      // Additional logging as needed
    });
    
    if (event.previousState && eventType === ResourceEventType.UPDATED) {
      console.log('Previous state:', event.previousState);
      console.log('New state:', resource);
    }
  }
}

/**
 * Example listener that simulates sending notifications for resource events
 */
export class NotificationListener implements ResourceListener {
  private notificationTargets: Map<string, string[]>;
  
  constructor() {
    // In a real implementation, this would likely come from a database or configuration
    this.notificationTargets = new Map([
      ['patient', ['<EMAIL>', '<EMAIL>']],
      ['visit', ['<EMAIL>', '<EMAIL>']],
      // Add more notification mappings as needed
    ]);
  }
  
  public async onResourceEvent(event: ResourceEvent): Promise<void> {
    const { eventType, resourceType, resource } = event;
    
    // Get notification targets for this resource type
    const targets = this.notificationTargets.get(resourceType) || [];
    
    if (targets.length === 0) {
      return; // No targets for this resource type
    }
    
    // Build notification message
    let message = '';
    switch (eventType) {
      case ResourceEventType.CREATED:
        message = `New ${resourceType} created: ID ${resource.id}`;
        break;
      case ResourceEventType.UPDATED:
        message = `${resourceType} updated: ID ${resource.id}`;
        break;
      case ResourceEventType.DELETED:
        message = `${resourceType} deleted: ID ${resource.id}`;
        break;
    }
    
    // In a real implementation, we would send actual notifications
    // For now, we'll just log that we would send notifications
    console.log(`Would send notification: "${message}" to:`, targets);
    
    // Simulate an async operation
    await new Promise(resolve => setTimeout(resolve, 10));
  }
}

/**
 * Example listener that simulates creating audit trail records for resource changes
 */
export class AuditTrailListener implements ResourceListener {
  public async onResourceEvent(event: ResourceEvent): Promise<void> {
    const { eventType, resourceType, resource, previousState, timestamp } = event;
    
    // In a real implementation, this would save to a database
    // For now, we'll just log the audit trail record
    const auditRecord = {
      timestamp,
      action: eventType,
      resourceType,
      resourceId: resource.id,
      changes: eventType === ResourceEventType.UPDATED ? this.calculateChanges(previousState, resource) : undefined,
      // Additional audit fields like user ID would be included in a real implementation
    };
    
    console.log('Audit trail record created:', auditRecord);
    
    // Simulate an async operation
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  private calculateChanges(
    previous: Record<string, unknown>,
    current: Record<string, unknown>
  ): Record<string, { previous: unknown; current: unknown }> {
    if (!previous) return {};
    
    const changes: Record<string, { previous: unknown; current: unknown }> = {};
    
    // Compare all keys in both objects
    const allKeys = new Set([...Object.keys(previous), ...Object.keys(current)]);
    
    for (const key of allKeys) {
      // Skip internal fields or fields we don't want to audit
      if (key === 'id' || key === 'resourceType' || key === 'schemaId') {
        continue;
      }
      
      const previousValue = previous[key];
      const currentValue = current[key];
      
      // Check if the value has changed
      if (JSON.stringify(previousValue) !== JSON.stringify(currentValue)) {
        changes[key] = {
          previous: previousValue,
          current: currentValue,
        };
      }
    }
    
    return changes;
  }
}

/**
 * Example listener that simulates integration with external systems
 */
export class IntegrationListener implements ResourceListener {
  private supportedResourceTypes: Set<string>;
  
  constructor() {
    // In a real implementation, this would come from configuration
    this.supportedResourceTypes = new Set(['patient', 'visit', 'note']);
  }
  
  public async onResourceEvent(event: ResourceEvent): Promise<void> {
    const { eventType, resourceType, resource } = event;
    
    // Check if this resource type is supported for integration
    if (!this.supportedResourceTypes.has(resourceType)) {
      return;
    }
    
    // In a real implementation, this would integrate with an external system
    console.log(`Integrating ${resourceType} ${eventType.toLowerCase()} event with external systems:`, {
      id: resource.id,
      action: eventType,
      // Additional data as needed
    });
    
    // Simulate an async operation
    await new Promise(resolve => setTimeout(resolve, 50));
  }
}
