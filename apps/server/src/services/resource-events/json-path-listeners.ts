import { ResourceEvent, JsonPathListener } from './types';

/**
 * Example listener that monitors changes to a specific JSON path
 * This listener demonstrates how to listen for changes to a specific field
 * within an array of objects, like the "completed" field in agencyActions
 */
export class ActionCompletedListener implements JsonPathListener {
  /**
   * Handle a JSON path change event
   * This method is called when the JSON path being monitored changes
   * 
   * @param event The resource event with path information
   */
  public async onJsonPathChanged(event: ResourceEvent): Promise<void> {
    const { resource, path, pathOldValue, pathNewValue } = event;
    
    console.log(`JSON path changed in ${resource.resourceType} ${resource.id}:`);
    console.log(`  Path: ${path}`);
    console.log(`  Old value: ${JSON.stringify(pathOldValue)}`);
    console.log(`  New value: ${JSON.stringify(pathNewValue)}`);
    
    // For an array of actions with a "completed" field, we might want to
    // perform different operations based on whether the action was completed or not
    
    // When using wildcard paths for arrays, we now receive only the changed values
    if (Array.isArray(pathNewValue)) {
      // With our improved array comparison, we only get the changed values
      // Count newly completed and uncompleted actions
      let newlyCompletedCount = 0;
      let newlyUncompletedCount = 0;
      
      // Check each changed value
      for (let i = 0; i < pathNewValue.length; i++) {
        const oldVal = Array.isArray(pathOldValue) && i < pathOldValue.length 
          ? pathOldValue[i] 
          : undefined;
        const newVal = pathNewValue[i];
        
        let message = `Action for patient ${resource.id} was completed`;
        if (newVal === true && oldVal !== true) {
          newlyCompletedCount++;
          message = `Action for patient ${resource.id} was just completed`;
          console.log(`  Action was just completed`);
        } else if (newVal === false && oldVal === true) {
          newlyUncompletedCount++;
          message = `Action for patient ${resource.id} was marked incomplete`;
          console.log('  Action was marked incomplete');
        }

        if (resource.agencyId !== 'bd6e787b-b665-4f7b-9b32-1df1797620cd') {
          try {
            await fetch('*******************************************************************************', {
              method: 'POST',
              body: JSON.stringify({ text: `<!channel> ${message}` }),
              headers: {
                'Content-Type': 'application/json',
              },
            });
          } catch (error) {
            console.error("Error sending slack webhook: ", error)
          }
        }
      }
      
      console.log(`  ${newlyCompletedCount} actions newly completed`);
      console.log(`  ${newlyUncompletedCount} actions newly uncompleted`);
      
      // Take different actions based on completion status
      if (newlyCompletedCount > 0) {
        // In a real implementation, we might want to:
        // - Send notifications
        // - Update related resources
        // - Trigger workflows
        console.log('  Would trigger notifications for newly completed actions');
      }
    }
    // If the path is a single value (like a specific index in an array)
    else {
      const isCompleted = pathNewValue === true;
      const wasCompleted = pathOldValue === true;
      
      if (isCompleted && !wasCompleted) {
        console.log('  Action was just completed');
        // In a real implementation, you might want to:
        // - Send a notification
        // - Update related resources
        console.log(`  Would send notification: Action for ${resource.id} was completed`);
      } else if (!isCompleted && wasCompleted) {
        console.log('  Action was marked incomplete');
        console.log(`  Would send notification: Action for ${resource.id} was marked incomplete`);
      }
    }
  }
}

/**
 * Example listener that monitors all actions in a request for status changes
 * This shows how to listen for any change to the "agencyActions" array
 */
export class RequestActionsListener implements JsonPathListener {
  public async onJsonPathChanged(event: ResourceEvent): Promise<void> {
    const { resource, pathNewValue } = event;
    
    console.log(`Actions changed in request ${resource.id}`);
    
    // In a real implementation, we might:
    // - Update a dashboard with the latest action status
    // - Recalculate metrics based on action completion
    // - Check if all actions are completed to move the request to the next stage
    
    // Example of checking if all actions are completed
    if (Array.isArray(pathNewValue)) {
      const allCompleted = pathNewValue.every(value => value === true);
      
      if (allCompleted) {
        console.log('  All actions are now completed for this request');
        // Proceed to next stage in workflow
      } else {
        const completedCount = pathNewValue.filter(value => value === true).length;
        const totalCount = pathNewValue.length;
        
        console.log(`  Progress: ${completedCount}/${totalCount} actions completed`);
      }
    }
  }
}

/**
 * Create hardcoded path listeners for common use cases
 * @returns An array of hardcoded path listeners
 */
export function createHardcodedJsonPathListeners(): { listener: JsonPathListener; resourceType: string; path: string }[] {
  return [
    {
      listener: new ActionCompletedListener(),
      resourceType: 'Patient',
      path: 'agencyActions[*].completed'
    }
  ];
}
