/**
 * Utilities for working with JSON paths in resources
 */

/**
 * Extract a value from an object using a JSON path string
 * Supports basic path navigation with dot notation and array indexing
 * 
 * Examples:
 * - "name" => obj.name
 * - "person.name" => obj.person.name
 * - "items[0]" => obj.items[0]
 * - "items[0].name" => obj.items[0].name
 * - "items[*].name" => Returns an array of all items' names
 * - "items[*].status.completed" => Returns an array of all items' status.completed values
 * 
 * @param obj The object to extract from
 * @param path The JSON path as a string
 * @returns The extracted value or undefined if the path doesn't exist
 */
export function getValueAtPath(obj: Record<string, unknown>, path: string): unknown {
  if (!obj || !path) {
    return undefined;
  }
  
  // Handle wildcard array access
  if (path.includes('[*]')) {
    const [arrayPath, ...restPaths] = path.split('[*].');
    
    // Get the array
    const array = getValueAtPath(obj, arrayPath) as unknown[];
    
    // If it's not an array or it's empty, return undefined
    if (!Array.isArray(array) || array.length === 0) {
      return undefined;
    }
    
    // If there's no rest path, return the entire array
    if (restPaths.length === 0) {
      return array;
    }
    
    // Extract the values from each array item
    const restPath = restPaths.join('');
    return array.map(item => {
      if (typeof item !== 'object' || item === null) {
        return undefined;
      }
      return getValueAtPath(item as Record<string, unknown>, restPath);
    });
  }
  
  // Handle regular paths with dot notation and array indexing
  const parts = path.split('.');
  let current: unknown = obj;
  
  for (const part of parts) {
    if (current === undefined || current === null) {
      return undefined;
    }
    
    // Handle array indexing
    if (part.includes('[') && part.includes(']')) {
      const [propName, indexStr] = part.split('[');
      const index = parseInt(indexStr.replace(']', ''), 10);
      
      if (propName === '') {
        // Direct array access like [0]
        if (Array.isArray(current) && index >= 0 && index < current.length) {
          current = current[index];
        } else {
          return undefined;
        }
      } else {
        // Property access with array like items[0]
        const propValue = (current as Record<string, unknown>)[propName];
        
        if (Array.isArray(propValue) && index >= 0 && index < propValue.length) {
          current = propValue[index];
        } else {
          return undefined;
        }
      }
    } else {
      // Regular property access
      current = (current as Record<string, unknown>)[part];
    }
  }
  
  return current;
}

/**
 * Compare two values to determine if they are different
 * Uses JSON.stringify for deep comparison
 * 
 * @param oldValue The old value
 * @param newValue The new value
 * @returns True if the values are different, false if they are the same
 */
export function valuesAreDifferent(oldValue: unknown, newValue: unknown): boolean {
  // Handle undefined and null cases
  if (oldValue === undefined && newValue === undefined) {
    return false;
  }
  if (oldValue === null && newValue === null) {
    return false;
  }
  if ((oldValue === undefined || oldValue === null) && (newValue !== undefined && newValue !== null)) {
    return true;
  }
  if ((oldValue !== undefined && oldValue !== null) && (newValue === undefined || newValue === null)) {
    return true;
  }
  
  // Use JSON.stringify for deep comparison
  return JSON.stringify(oldValue) !== JSON.stringify(newValue);
}

/**
 * Check if a value at a specific JSON path has changed between two objects
 * Handles array wildcards by comparing individual elements
 * 
 * @param oldObj The old object
 * @param newObj The new object
 * @param path The JSON path to check
 * @returns An object with the result and the old and new values
 */
export function pathValueChanged(
  oldObj: Record<string, unknown> | undefined | null, 
  newObj: Record<string, unknown> | undefined | null,
  path: string
): { changed: boolean; oldValue: unknown; newValue: unknown } {
  if (!oldObj && !newObj) {
    return { changed: false, oldValue: undefined, newValue: undefined };
  }
  
  if (!oldObj && newObj) {
    const newValue = getValueAtPath(newObj, path);
    return { changed: true, oldValue: undefined, newValue };
  }
  
  if (oldObj && !newObj) {
    const oldValue = getValueAtPath(oldObj, path);
    return { changed: true, oldValue, newValue: undefined };
  }
  
  const oldValue = oldObj ? getValueAtPath(oldObj, path) : undefined;
  const newValue = newObj ? getValueAtPath(newObj, path) : undefined;
  
  // Special handling for array wildcard paths
  if (path.includes('[*]') && Array.isArray(oldValue) && Array.isArray(newValue)) {
    // For array wildcards, we need to compare individual elements
    const changedValues: { oldValue: unknown; newValue: unknown }[] = [];
    const maxLength = Math.max(oldValue.length, newValue.length);
    
    for (let i = 0; i < maxLength; i++) {
      const oldItem = i < oldValue.length ? oldValue[i] : undefined;
      const newItem = i < newValue.length ? newValue[i] : undefined;
      
      if (valuesAreDifferent(oldItem, newItem)) {
        changedValues.push({ oldValue: oldItem, newValue: newItem });
      }
    }
    
    // If any elements changed, return only the changed values
    if (changedValues.length > 0) {
      return {
        changed: true,
        oldValue: changedValues.map(item => item.oldValue),
        newValue: changedValues.map(item => item.newValue)
      };
    }
    
    // No changes in array elements
    return { changed: false, oldValue, newValue };
  }
  
  // Regular non-array comparison
  return {
    changed: valuesAreDifferent(oldValue, newValue),
    oldValue,
    newValue
  };
}
