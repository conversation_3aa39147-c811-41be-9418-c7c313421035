import { resourceEventDispatcher } from "./dispatcher";
import { createHardcodedJsonPathListeners } from "./json-path-listeners";
import { ResourceEvent, ResourceEventType } from "./types";
import { s3Client } from "../../s3";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { config } from "../../config";
import { Request } from "@hospice-os/apptypes";
import {
  getWorkflowId,
  startPatientRequestWorkflow,
} from "../../temporal/client";
// import { ResourceEventType } from './types';

const backupPatient = async (event: ResourceEvent) => {
  const key = `patients/${event.resource.id}/${new Date().toISOString()}.pdf`;
  await s3Client.send(
    new PutObjectCommand({ Bucket: config.aws.s3Bucket, Key: key }),
  );
};
/**
 * Initialize and register all resource event listeners
 */
export function registerResourceEventListeners(): void {
  // // Create listener instances
  // const loggingListener = new LoggingListener();
  // const notificationListener = new NotificationListener();
  // const auditTrailListener = new AuditTrailListener();
  // const integrationListener = new IntegrationListener();

  // // Register global logging listener for all resource types and events
  // resourceEventDispatcher.registerListener(loggingListener);

  // Register notification listener for specific resource types
  resourceEventDispatcher.registerListener(
    {
      onResourceEvent: async (event) => {
        console.log("Resource event", event);

        const flowId = getWorkflowId({
          prefix: "patientRequest",
          agencyId: event.resource.agencyId,
          entityId: event.resource.id,
        });

        const handle = await startPatientRequestWorkflow(
          flowId,
          event.resource as unknown as Request,
        );
        console.log("Workflow submitted", handle.workflowId);
      },
    },
    "Request",
    ResourceEventType.CREATED,
  );

  // Backup patient on events
  resourceEventDispatcher.registerListener(
    {
      onResourceEvent: backupPatient,
    },
    "Patient",
    ResourceEventType.CREATED,
  );

  resourceEventDispatcher.registerListener(
    {
      onResourceEvent: backupPatient,
    },
    "Patient",
    ResourceEventType.UPDATED,
  );

  resourceEventDispatcher.registerListener(
    {
      onResourceEvent: backupPatient,
    },
    "Patient",
    ResourceEventType.DELETED,
  );

  // resourceEventDispatcher.registerListener(
  //   notificationListener,
  //   'visit',
  //   ResourceEventType.CREATED
  // );

  // // Register audit trail listener for all resource types but only for UPDATED and DELETED events
  // resourceEventDispatcher.registerListener(auditTrailListener, undefined, ResourceEventType.UPDATED);
  // resourceEventDispatcher.registerListener(auditTrailListener, undefined, ResourceEventType.DELETED);

  // // Register integration listener for specific resource types and all event types
  // resourceEventDispatcher.registerListener(integrationListener, 'patient');
  // resourceEventDispatcher.registerListener(integrationListener, 'visit');
  // resourceEventDispatcher.registerListener(integrationListener, 'note');

  // Register JSON path listeners
  const jsonPathListeners = createHardcodedJsonPathListeners();
  for (const { listener, resourceType, path } of jsonPathListeners) {
    resourceEventDispatcher.registerJsonPathListener(
      listener,
      resourceType,
      path,
    );
    console.log(
      `Registered JSON path listener for ${resourceType} path: ${path}`,
    );
  }

  console.log("Resource event listeners registered successfully");
}

// Export a function to initialize all listeners
export const initializeResourceEventSystem = registerResourceEventListeners;
