import { ResourceFields } from '../../db/interfaces';

/**
 * Type representing a resource with its required fields
 */
export type BaseResource = Record<string, unknown> & ResourceFields;

/**
 * Enum defining the types of resource events
 */
export enum ResourceEventType {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  DELETED = 'DELETED',
  PATH_CHANGED = 'PATH_CHANGED'
}

/**
 * Interface for resource events
 */
export interface ResourceEvent {
  eventType: ResourceEventType;
  resourceType: string;
  resource: BaseResource;
  previousState?: BaseResource;
  timestamp: Date;
  path?: string; // JSON path that changed (for PATH_CHANGED events)
  pathOldValue?: unknown; // Old value at the JSON path
  pathNewValue?: unknown; // New value at the JSON path
}

/**
 * Interface for resource event listeners
 */
export interface ResourceListener {
  /**
   * Method called when a resource event occurs
   * @param event The resource event
   */
  onResourceEvent(event: ResourceEvent): void | Promise<void>;
}

/**
 * Interface for JSON path event listeners
 * These listeners are specifically for changes to values at a specific JSON path
 */
export interface JsonPathListener {
  /**
   * Method called when a value at a specific JSON path changes
   * @param event The resource event with path information
   */
  onJsonPathChanged(event: ResourceEvent): void | Promise<void>;
}

/**
 * Type for a JSON path listener registration
 */
export interface JsonPathListenerRegistration {
  listener: JsonPathListener;
  path: string;
}
