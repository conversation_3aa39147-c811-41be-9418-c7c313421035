import {
  NotFoundError,
  ResourceOutOfDateError,
} from "../api/middleware/error-handler";
import { RepositoryFactory } from "../db/factory";
import { ResourceFields } from "../db/interfaces";
import { uuidv7 } from "uuidv7";
import { resourceEventDispatcher } from "./resource-events/dispatcher";
import { BaseResource, ResourceEventType } from "./resource-events/types";
import dayjs from "dayjs";

export class ResourceService {
  async createResource<T extends Record<string, unknown> & ResourceFields>(
    resource: T,
    schemaId: string,
    agencyId: string,
    bypassEventDispatch: boolean = false,
  ): Promise<T> {
    const resourceRepository =
      RepositoryFactory.getInstance().getResourceRepository();
    if (!resource.id) {
      resource.id = uuidv7();
    }

    // Create the resource

    const createdResource = await resourceRepository.createResource(
      resource,
      schemaId,
      agencyId,
    );

    if (!bypassEventDispatch) {
      // Dispatch resource created event
      await resourceEventDispatcher.dispatchEvent({
        eventType: ResourceEventType.CREATED,
        resourceType: createdResource.resourceType,
        resource: createdResource,
        timestamp: new Date(),
      });
    }

    return createdResource;
  }

  async readResource<T extends Record<string, unknown> & ResourceFields>(
    id: string,
  ): Promise<T | null> {
    const resourceRepository =
      RepositoryFactory.getInstance().getResourceRepository();
    return await resourceRepository.readResource<T>(id);
  }

  async updateResource<T extends Record<string, unknown> & ResourceFields>(
    id: string,
    resource: T,
    schemaId: string,
  ): Promise<T | null> {
    const resourceRepository =
      RepositoryFactory.getInstance().getResourceRepository();

    const existingResource = await resourceRepository.readResource<T>(id);
    if (!existingResource) {
      throw new NotFoundError(`Resource with ID ${id} not found`);
    }

    // TODO: deal with out of date resources
    // if (resource.updatedAt && existingResource.updatedAt) {
    //   if (
    //     dayjs(existingResource.updatedAt).isAfter(dayjs(resource.updatedAt))
    //   ) {
    //     throw new ResourceOutOfDateError(
    //       `Resource with ID ${id} has been updated since the request was made`,
    //     );
    //   }
    // }

    // resource.updatedAt = new Date().toISOString();
    // Update the resource
    const updatedResource = await resourceRepository.updateResource(
      id,
      resource,
      schemaId,
    );

    if (updatedResource) {
      // Dispatch resource updated event
      await resourceEventDispatcher.dispatchEvent({
        eventType: ResourceEventType.UPDATED,
        resourceType: updatedResource.resourceType,
        resource: updatedResource,
        previousState: existingResource,
        timestamp: new Date(),
      });
    }

    return updatedResource;
  }

  async deleteResource(id: string): Promise<boolean> {
    const resourceRepository =
      RepositoryFactory.getInstance().getResourceRepository();

    // Get the resource before deleting it
    const existingResource = await resourceRepository.readResource(id);
    if (!existingResource) {
      return false;
    }

    // Delete the resource
    const success = await resourceRepository.deleteResource(id);

    if (success) {
      // Dispatch resource deleted event
      await resourceEventDispatcher.dispatchEvent({
        eventType: ResourceEventType.DELETED,
        resourceType: existingResource.resourceType,
        resource: existingResource,
        timestamp: new Date(),
      });
    }

    return success;
  }

  async queryResourcesByType<T extends Record<string, unknown>>(
    resourceType: string,
    agencyId?: string,
    jsonPathFilters?: Record<string, unknown>,
    fields?: string[],
    limit?: number,
  ): Promise<T[]> {
    const resourceRepository =
      RepositoryFactory.getInstance().getResourceRepository();
    return await resourceRepository.queryResourcesByType<T>(
      resourceType,
      agencyId,
      jsonPathFilters,
      fields,
      limit,
    );
  }
}

// Export a singleton instance
export const resourceService = new ResourceService();
