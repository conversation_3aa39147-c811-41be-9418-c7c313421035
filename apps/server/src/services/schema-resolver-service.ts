import { RepositoryFactory } from "../db/factory";
import { JSONSchema } from "../db/interfaces";

export class SchemaResolverService {
  /**
   * Resolves a schema by ID, resolving all $refs and composing all $allOf and $anyOf
   * @param schemaId The ID of the schema to resolve
   * @returns The resolved schema
   */
  async resolveSchema(schemaId: string): Promise<JSONSchema | null> {
    console.log(`Resolving schema with ID: ${schemaId}`);

    // Get the schema repository - this ensures we get the current instance with the correct pool
    const schemaRepository =
      RepositoryFactory.getInstance().getSchemaRepository();

    const schema = await schemaRepository.getSchema(schemaId);

    if (!schema) {
      console.error(`Schema with ID ${schemaId} not found`);
      return null;
    }

    console.log(`Schema found: ${schema.schema.$id || "no $id"}`);
    console.log(`Schema keys: ${Object.keys(schema.schema).join(", ")}`);

    if (schema.schema.definitions) {
      console.log(
        `Schema has definitions: ${Object.keys(schema.schema.definitions).join(", ")}`,
      );
    } else {
      console.log("Schema does not have definitions");
    }

    // Pass the schema as both the schema to resolve and the root schema
    // This ensures internal references can be resolved
    return this.resolveSchemaObject(schema.schema, schema.schema);
  }

  /**
   * Resolves a schema object, resolving all $refs and composing all $allOf and $anyOf
   * @param schema The schema object to resolve
   * @param rootSchema The original root schema (for resolving references)
   * @param accumulatedSchema The schema with all definitions resolved so far
   * @returns The resolved schema
   */
  private async resolveSchemaObject(
    schema: JSONSchema,
    rootSchema?: JSONSchema,
    accumulatedSchema?: JSONSchema,
  ): Promise<JSONSchema> {
    // console.log(
    //   `Resolving schema object: ${schema.title || schema.type || "unnamed"}`,
    // );

    // Create a deep copy of the schema to avoid modifying the original
    const resolvedSchema = JSON.parse(JSON.stringify(schema));

    // Store the original schema for resolving internal references
    // If rootSchema is provided, use it; otherwise, this is the root schema
    const originalSchema = rootSchema ? rootSchema : resolvedSchema;

    // If accumulatedSchema is not provided, use the original schema as the starting point
    // This is the schema with all definitions resolved so far
    const currentAccumulatedSchema = accumulatedSchema || originalSchema;

    // Resolve $ref - first try to resolve against accumulated schema, then fall back to original
    if (resolvedSchema.$ref) {
      console.log(`Found $ref in schema: ${resolvedSchema.$ref}`);
      // First try to resolve against the accumulated schema
      let referencedSchema = await this.resolveReference(
        resolvedSchema.$ref,
        currentAccumulatedSchema,
      );

      // If that doesn't work, try the original schema
      if (!referencedSchema && originalSchema !== currentAccumulatedSchema) {
        console.log(
          `Falling back to original schema for reference: ${resolvedSchema.$ref}`,
        );
        referencedSchema = await this.resolveReference(
          resolvedSchema.$ref,
          originalSchema,
        );
      }
      if (referencedSchema) {
        console.log(`Successfully resolved reference: ${resolvedSchema.$ref}`);
        // Merge the referenced schema with the current schema
        delete resolvedSchema.$ref;
        Object.assign(resolvedSchema, referencedSchema);
      } else {
        console.error(`Failed to resolve reference: ${resolvedSchema.$ref}`);
      }
    }

    // Resolve $allOf
    if (resolvedSchema.allOf && Array.isArray(resolvedSchema.allOf)) {
      const resolvedAllOf = await Promise.all(
        resolvedSchema.allOf.map((subSchema) =>
          this.resolveSchemaObject(
            subSchema,
            originalSchema,
            // Pass the currently resolving schema as the accumulated schema
            // We create a new object that combines the current accumulated schema and the resolved schema so far
            this.mergeAccumulatedSchema(
              currentAccumulatedSchema,
              resolvedSchema,
            ),
          ),
        ),
      );

      // Merge all schemas in allOf
      delete resolvedSchema.allOf;

      // Merge properties
      resolvedSchema.properties = resolvedSchema.properties || {};

      // Merge required fields
      const requiredFields = new Set<string>(resolvedSchema.required || []);

      for (const subSchema of resolvedAllOf) {
        // Merge properties
        if (subSchema.properties) {
          resolvedSchema.properties = {
            ...resolvedSchema.properties,
            ...subSchema.properties,
          };
        }

        // Merge required fields
        if (subSchema.required && Array.isArray(subSchema.required)) {
          subSchema.required.forEach((field) => requiredFields.add(field));
        }

        // Merge other fields
        for (const [key, value] of Object.entries(subSchema)) {
          if (key !== "properties" && key !== "required") {
            resolvedSchema[key] = value;
          }
        }
      }

      // Update required fields
      if (requiredFields.size > 0) {
        resolvedSchema.required = Array.from(requiredFields);
      }
    }

    // Resolve $anyOf
    if (resolvedSchema.anyOf && Array.isArray(resolvedSchema.anyOf)) {
      const resolvedAnyOf = await Promise.all(
        resolvedSchema.anyOf.map((subSchema) =>
          this.resolveSchemaObject(
            subSchema,
            originalSchema,
            this.mergeAccumulatedSchema(
              currentAccumulatedSchema,
              resolvedSchema,
            ),
          ),
        ),
      );

      // Create a union schema
      delete resolvedSchema.anyOf;

      // Merge properties from all anyOf schemas
      resolvedSchema.properties = resolvedSchema.properties || {};

      for (const subSchema of resolvedAnyOf) {
        if (subSchema.properties) {
          for (const [propName, propSchema] of Object.entries(
            subSchema.properties,
          )) {
            // If the property already exists, create a oneOf
            if (resolvedSchema.properties[propName]) {
              resolvedSchema.properties[propName] = {
                oneOf: [resolvedSchema.properties[propName], propSchema],
              };
            } else {
              resolvedSchema.properties[propName] = propSchema;
            }
          }
        }
      }
    }

    // Recursively resolve nested schemas in properties
    if (resolvedSchema.properties) {
      for (const [propName, propSchema] of Object.entries(
        resolvedSchema.properties,
      )) {
        if (typeof propSchema === "object" && propSchema !== null) {
          resolvedSchema.properties[propName] = await this.resolveSchemaObject(
            propSchema as JSONSchema,
            originalSchema,
            this.mergeAccumulatedSchema(
              currentAccumulatedSchema,
              resolvedSchema,
            ),
          );
        }
      }
    }

    // Recursively resolve nested schemas in items (for arrays)
    if (
      resolvedSchema.items &&
      typeof resolvedSchema.items === "object" &&
      resolvedSchema.items !== null
    ) {
      resolvedSchema.items = await this.resolveSchemaObject(
        resolvedSchema.items as JSONSchema,
        originalSchema,
        this.mergeAccumulatedSchema(currentAccumulatedSchema, resolvedSchema),
      );
    }

    // Recursively resolve nested schemas in definitions
    if (resolvedSchema.definitions) {
      for (const [defName, defSchema] of Object.entries(
        resolvedSchema.definitions,
      )) {
        if (typeof defSchema === "object" && defSchema !== null) {
          resolvedSchema.definitions[defName] = await this.resolveSchemaObject(
            defSchema as JSONSchema,
            originalSchema,
            this.mergeAccumulatedSchema(
              currentAccumulatedSchema,
              resolvedSchema,
            ),
          );
        }
      }
    }

    return resolvedSchema;
  }

  /**
   * Helper method to find a definition anywhere in the schema hierarchy
   * @param schema The schema object to search in
   * @param path The path segments to the definition
   * @returns The found definition or null if not found
   */
  private findDefinitionAtAnyLevel(
    schema: JSONSchema,
    path: string[],
  ): JSONSchema | null {
    // First try the direct path resolution method
    console.log(`Finding definition ${path[1]} at any level`);
    try {
      let current: Record<string, unknown> = schema;

      // Navigate through the path to find the referenced schema
      for (const segment of path) {
        if (
          typeof current !== "object" ||
          current === null ||
          !(segment in current)
        ) {
          return null;
        }

        // Get the next level
        current = current[segment] as Record<string, unknown>;
      }

      // If we found something, return it
      if (current && typeof current === "object") {
        return current as JSONSchema;
      }
    } catch (error) {
      console.log(`Direct path resolution failed: ${error}`);
    }

    // If direct path didn't work and this is a definitions reference, try to find it recursively
    if (path[0] === "definitions") {
      const definitionName = path[1]; // The name of the definition we're looking for
      return this.findNestedDefinition(schema, definitionName);
    }

    return null;
  }

  /**
   * Merges the accumulated schema with the current schema being resolved
   * This creates a new schema object that can be used for resolving references
   * @param accumulatedSchema The schema accumulated so far
   * @param currentSchema The schema currently being resolved
   * @returns A merged schema with definitions from both schemas
   */
  private mergeAccumulatedSchema(
    accumulatedSchema: JSONSchema,
    currentSchema: JSONSchema,
  ): JSONSchema {
    // Create a deep copy to avoid modifying either input schema
    const result = JSON.parse(JSON.stringify(accumulatedSchema));

    // Ensure definitions exist in the result
    if (!result.definitions) {
      result.definitions = {};
    }

    // Merge definitions from the current schema
    if (currentSchema.definitions) {
      result.definitions = {
        ...result.definitions,
        ...JSON.parse(JSON.stringify(currentSchema.definitions)),
      };
    }

    return result;
  }

  /**
   * Recursively search for a definition by name in any nested level of the schema
   * @param schema The schema to search in
   * @param definitionName The name of the definition to find
   * @returns The found definition or null if not found
   */
  private findNestedDefinition(
    schema: JSONSchema,
    definitionName: string,
  ): JSONSchema | null {
    // Base case: if this is not an object, return null
    if (typeof schema !== "object" || schema === null) {
      return null;
    }

    // Check if this object has definitions and if it contains our target
    if (
      schema.definitions &&
      typeof schema.definitions === "object" &&
      schema.definitions[definitionName]
    ) {
      return schema.definitions[definitionName] as JSONSchema;
    }

    // Recursively check all properties
    if (schema.properties) {
      for (const [, propSchema] of Object.entries(schema.properties)) {
        if (typeof propSchema === "object" && propSchema !== null) {
          const found = this.findNestedDefinition(
            propSchema as JSONSchema,
            definitionName,
          );
          if (found) return found;
        }
      }
    }

    // Recursively check items (for arrays)
    if (schema.items && typeof schema.items === "object") {
      const found = this.findNestedDefinition(
        schema.items as JSONSchema,
        definitionName,
      );
      if (found) return found;
    }

    // Recursively check allOf, anyOf, oneOf
    for (const key of ["allOf", "anyOf", "oneOf"]) {
      const schemaKey = key as "allOf" | "anyOf" | "oneOf";
      if (schema[schemaKey] && Array.isArray(schema[schemaKey])) {
        for (const subSchema of schema[schemaKey] as JSONSchema[]) {
          const found = this.findNestedDefinition(subSchema, definitionName);
          if (found) return found;
        }
      }
    }

    return null;
  }

  /**
   * Resolves a reference to a schema
   * @param ref The reference to resolve
   * @param originalSchema The original schema containing the reference (for internal references)
   * @returns The referenced schema
   */
  private async resolveReference(
    ref: string,
    originalSchema?: JSONSchema,
  ): Promise<JSONSchema | null> {
    try {
      console.log(`Resolving reference: ${ref}`);

      // Get the schema repository - this ensures we get the current instance with the correct pool
      const schemaRepository =
        RepositoryFactory.getInstance().getSchemaRepository();

      // Check if the reference is a database ID
      if (ref.startsWith("#/")) {
        console.log(`Handling internal reference: ${ref}`);

        // Local reference within the same schema
        if (!originalSchema) {
          console.error(
            "Cannot resolve internal reference without original schema",
          );
          return null;
        }

        // Parse the reference path
        const path = ref.substring(2).split("/");
        console.log(`Reference path segments: ${JSON.stringify(path)}`);

        // Try to find the reference at any level in the schema
        const referencedSchema = this.findDefinitionAtAnyLevel(
          originalSchema,
          path,
        );

        if (referencedSchema) {
          // Make a deep copy of the referenced schema to avoid modifying the original
          const clonedSchema = JSON.parse(JSON.stringify(referencedSchema));
          console.log(`Successfully resolved internal reference: ${ref}`);
          return clonedSchema as JSONSchema;
        }

        // Fall back to the old method with better error logging
        console.error(
          `Cannot resolve internal reference: ${ref}, tried standard path and nested lookup`,
        );

        if (path[0] === "definitions") {
          console.log(
            `Definition lookup failed for '${path[1]}'. Available definitions at root:`,
          );
          if (originalSchema.definitions) {
            console.log(Object.keys(originalSchema.definitions).join(", "));
          } else {
            console.log("No definitions section found at root level");
          }
        }

        return null;
      } else if (
        ref.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
        )
      ) {
        // UUID format - lookup by ID
        const schema = await schemaRepository.getSchema(ref);
        return schema ? schema.schema : null;
      } else {
        // Assume it's a content $id reference
        // We need to query all schemas and find the one with matching $id
        // This is inefficient but works for now
        // In a production system, we would add an index on schema->>'$id'

        try {
          // Get all schemas from all agencies
          const agencyRepository =
            RepositoryFactory.getInstance().getAgencyRepository();
          const allAgencies = await agencyRepository.listAgencies();

          for (const agency of allAgencies) {
            const schemas = await schemaRepository.listSchemas(agency.id);

            for (const schema of schemas) {
              if (schema.schema.$id === ref) {
                return schema.schema;
              }
            }
          }
        } catch (error) {
          console.error("Error querying agencies or schemas:", error);
        }

        return null;
      }
    } catch (error) {
      console.error("Error resolving reference:", error);
      return null;
    }
  }
}

// Create a new instance for each request
export function getSchemaResolverService(): SchemaResolverService {
  return new SchemaResolverService();
}
