import { RepositoryFactory } from '../db/factory';
import { Schema, JSONSchema } from '../db/interfaces';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

// Create AJV instance
const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// TODO: move to shared package that both frontend/server can use?
export const SCHEMA_IDS = {
  PatientBase: "6cb5c6e8-22c2-4df5-beb4-5cf2509e2673"
}

export class SchemaService {
  async createSchema(
    name: string,
    version: string,
    agencyId: string,
    schema: JSONSchema,
    baseSchemaId?: string
  ): Promise<Schema> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.createSchema(
      name,
      version,
      agencyId,
      schema,
      baseSchemaId
    );
  }

  async updateSchema(
    id: string,
    version: string,
    schema: JSONSchema
  ): Promise<Schema | null> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.updateSchema(
      id,
      version,
      schema
    );
  }

  async getSchema(id: string): Promise<Schema | null> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.getSchema(id);
  }

  async getSchemaByName(
    name: string,
    version: string,
    agencyId: string
  ): Promise<Schema | null> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.getSchemaByName(
      name,
      version,
      agencyId
    );
  }

  async listSchemas(agencyId: string): Promise<Schema[]> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.listSchemas(agencyId);
  }

  async listAllSchemas(): Promise<Schema[]> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.listAllSchemas();
  }

  async listSchemasByBase(agencyId: string, baseSchemaId: string): Promise<Schema[]> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    const schemas = await schemaRepository.listSchemas(agencyId);
    return schemas.filter(schema => schema.baseSchemaId === baseSchemaId);
  }

  async listBaseSchemas(): Promise<Schema[]> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    return await schemaRepository.listBaseSchemas();
  }

  async validateAgainstSchema(
    schemaId: string,
    data: Record<string, unknown>
  ): Promise<{ valid: boolean; errors: unknown[] | null }> {
    const schemaRepository = RepositoryFactory.getInstance().getSchemaRepository();
    const schema = await schemaRepository.getSchema(schemaId);
    
    if (!schema) {
      throw new Error(`Schema with ID ${schemaId} not found`);
    }
    
    // Compile the schema
    const validate = ajv.compile(schema.schema);
    
    // Validate the data
    const valid = validate(data);
    
    return {
      valid: !!valid,
      errors: valid ? null : validate.errors
    };
  }
}

// Export a singleton instance
export const schemaService = new SchemaService();
