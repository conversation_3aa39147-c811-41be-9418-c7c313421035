import { getSignedUrl as getSignedUrlPresigner } from '@aws-sdk/s3-request-presigner';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { s3Client } from '../s3';
import { config } from '../config';


export const getSignedUrl = async (key: string) => {
    if (!s3Client) {
        throw new Error("S3 client not initialized");
    }
    if (!config.aws.s3Bucket) {
        throw new Error("S3 bucket not initialized");
    }
    if (!key) {
        throw new Error("Key not provided");
    }
    
    const getObjectCommand = new GetObjectCommand({
        Bucket: config.aws.s3Bucket,
        Key: key
      });
  
    const signedUrl = await getSignedUrlPresigner(s3Client, getObjectCommand, { expiresIn: 600 });
    return signedUrl;
}

export const uploadFile = async (key: string, file: Buffer) => {
    if (!s3Client) {
        throw new Error("S3 client not initialized");
    }
    if (!config.aws.s3Bucket) {
        throw new Error("S3 bucket not initialized");
    }
    const putObjectCommand = new PutObjectCommand({
        Bucket: config.aws.s3Bucket,
        Key: key,
        Body: file
    });
    await s3Client.send(putObjectCommand);
}

export const createAndUploadTextFile = async (key: string, text: string) => {
    const file = Buffer.from(text);
    await uploadFile(key, file);
}