import * as stytch from 'stytch';
import { config } from '../config';

// Create a single instance of the Stytch client
let stytchClient: stytch.B2BClient;

const initializeStytchClient = async () => {
  stytchClient = new stytch.B2BClient({
    project_id: config.stytch.projectId,
    secret: config.stytch.secret,
    env: config.stytch.env === 'live'
      ? stytch.envs.live
      : stytch.envs.test
  });

  // Log Stytch configuration for debugging
  console.log('Stytch Configuration:', {
    project_id: config.stytch.projectId ? 'Set' : 'Not Set',
    secret: config.stytch.secret ? 'Set' : 'Not Set',
    env: config.stytch.env || 'Not Set'
  });
}



export { stytchClient, initializeStytchClient }; 