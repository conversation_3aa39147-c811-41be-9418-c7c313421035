import { RepositoryFactory } from '../db/factory';
import { UISchema, UISchemaContent } from '../db/interfaces';

export class UISchemaService {
  async createUISchema(
    name: string,
    description: string,
    schemaId: string,
    content: UISchemaContent
  ): Promise<UISchema> {
    const uiSchemaRepository = RepositoryFactory.getInstance().getUISchemaRepository();
    return await uiSchemaRepository.createUISchema(
      name,
      description,
      schemaId,
      content
    );
  }

  async updateUISchema(
    id: string,
    name: string,
    description: string,
    content: UISchemaContent
  ): Promise<UISchema | null> {
    const uiSchemaRepository = RepositoryFactory.getInstance().getUISchemaRepository();
    return await uiSchemaRepository.updateUISchema(
      id,
      name,
      description,
      content
    );
  }

  async getUISchema(id: string): Promise<UISchema | null> {
    const uiSchemaRepository = RepositoryFactory.getInstance().getUISchemaRepository();
    return await uiSchemaRepository.getUISchema(id);
  }

  async listUISchemasBySchema(schemaId: string): Promise<UISchema[]> {
    const uiSchemaRepository = RepositoryFactory.getInstance().getUISchemaRepository();
    return await uiSchemaRepository.listUISchemasBySchema(schemaId);
  }

  async deleteUISchema(id: string): Promise<boolean> {
    const uiSchemaRepository = RepositoryFactory.getInstance().getUISchemaRepository();
    return await uiSchemaRepository.deleteUISchema(id);
  }
}

// Export a singleton instance
export const uiSchemaService = new UISchemaService();
