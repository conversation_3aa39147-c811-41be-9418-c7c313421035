import { RepositoryFactory } from '../db/factory';
import { ResourceFields } from '../db/interfaces';

export class UserService {
  async createUser<T extends Record<string, unknown> & ResourceFields>(
    user: T,
    schemaId: string,
    agencyId: string,
    stytchId?: string
  ): Promise<T> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.createUser(user, schemaId, agencyId, stytchId);
  }

  async readUser<T extends Record<string, unknown>>(
    id: string
  ): Promise<T | null> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.readUser<T>(id);
  }

  async updateUser<T extends Record<string, unknown> & ResourceFields>(
    id: string,
    user: T,
    schemaId: string,
    stytchId?: string
  ): Promise<T | null> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.updateUser(id, user, schemaId, stytchId);
  }

  async deleteUser(
    id: string
  ): Promise<boolean> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.deleteUser(id);
  }

  async queryUsersByType<T extends Record<string, unknown>>(
    role: string,
    agencyId: string
  ): Promise<T[]> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.queryUsersByType<T>(role, agencyId);
  }

  async getUserByEmail<T extends Record<string, unknown>>(
    email: string
  ): Promise<T | null> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.getUserByEmail<T>(email);
  }

  async getUserByStytchId<T extends Record<string, unknown>>(
    stytchId: string
  ): Promise<T | null> {
    const userRepository = RepositoryFactory.getInstance().getUserRepository();
    return await userRepository.getUserByStytchId<T>(stytchId);
  }
}

// Export a singleton instance
export const userService = new UserService();
