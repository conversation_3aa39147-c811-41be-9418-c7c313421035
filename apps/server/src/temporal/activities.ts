import { ResourceService } from "../services/resource-service";
import { generalActivities } from "./general/generalActivities";
import { patientActivities } from "./patient/patientActivities";
import { requestActivities } from "./request/requestActivities";
import { pdfActivities } from "./pdf/pdfActivities";

export const createActivities = (resourceService: ResourceService) => {
  return {
    ...generalActivities(resourceService),
    ...patientActivities,
    ...requestActivities,
    ...pdfActivities,
  };
};

export type Activities = ReturnType<typeof createActivities>;
