import { Client, Connection } from "@temporalio/client";
import fs from "fs/promises";
import path from "path";
import { getCurrentStage } from "../config";
import { temporalQueueCurrent } from "./temporal";

let temporalClient: Client | null = null;

export async function getTemporalClient(): Promise<Client> {
  if (temporalClient) {
    return temporalClient;
  }

  let config = {};
  if (getCurrentStage() === "prod") {
    config = {
      address: "production.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-prod.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-prod.key"),
          ),
        },
      },
    };
  } else if (getCurrentStage() === "staging") {
    config = {
      address: "staging.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-staging.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-staging.key"),
          ),
        },
      },
    };
  } else {
    config = {
      address: "dev.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-dev.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-dev.key"),
          ),
        },
      },
    };
  }

  const connection = await Connection.connect(config);
  temporalClient = new Client({
    connection,
    namespace:
      getCurrentStage() === "prod"
        ? "production.w1anq"
        : getCurrentStage() === "staging"
          ? "staging.w1anq"
          : "dev.w1anq",
  });

  return temporalClient;
}

export async function startPatientRequestWorkflow(
  workflowId: string,
  request: unknown,
) {
  console.log("Starting patient workflow", workflowId, request);
  const client = await getTemporalClient();
  const handle = await client.workflow.start("patientRequestWorkflow", {
    taskQueue: temporalQueueCurrent,
    workflowId,
    args: [{ request }],
  });
  return handle;
}

export async function getPatientRequestWorkflowHandle(
  requestId: string,
  agencyId: string,
) {
  const client = await getTemporalClient();
  const id = getWorkflowId({
    prefix: "patientRequest",
    agencyId: agencyId,
    entityId: requestId,
  });
  return client.workflow.getHandle(id);
}

export async function startPdfImportWorkflow(
  requestId: string,
  agencyId: string,
) {
  const client = await getTemporalClient();
  const id = getWorkflowId({
    prefix: "patientImportPdf",
    agencyId: agencyId,
    entityId: requestId,
  });
  const handle = await client.workflow.start("patientImportPdfWorkflow", {
    taskQueue: temporalQueueCurrent,
    workflowId: id,
    args: [],
  });
  return handle;
}

export async function getPdfImportWorkflowHandle(
  workflowId: string,
  agencyId: string,
) {
  const client = await getTemporalClient();
  const id = getWorkflowId({
    prefix: "patientImportPdf",
    agencyId: agencyId,
    entityId: workflowId,
  });
  return client.workflow.getHandle(id);
}

export function getWorkflowId({
  prefix,
  agencyId,
  entityId,
}: {
  prefix: string;
  agencyId: string;
  entityId: string;
  reprocess?: boolean;
}) {
  if (!prefix || !agencyId || !entityId) {
    throw new Error("Prefix, agency ID, and entity ID are required");
  }

  return `${prefix}::${agencyId}::${entityId}`;
}

// Helper function to execute PDF import workflow with proper completion
export async function executePdfImportWorkflow(
  pdfUrl: string,
  requestId: string,
  agencyId: string,
  patientId: string,
  planOfCare?: any,
  patientChart?: any,
) {
  console.log("Starting PDF import workflow", { requestId, agencyId, patientId });
  
  try {
    // Start the workflow
    const handle = await startPdfImportWorkflow(requestId, agencyId);
    
    // Execute the workflow steps in sequence
    console.log("Converting PDF to markdown...");
    await handle.signal("convertPDFToMarkdown", { requestId, pdfUrl });
    
    // Optional: Generate plan of care if provided
    if (planOfCare) {
      console.log("Generating plan of care...");
      await handle.signal("generatePlanOfCare", { planOfCare });
    }
    
    // Optional: Generate patient chart if provided
    if (patientChart) {
      console.log("Generating patient chart...");
      await handle.signal("generatePatientChart", { patientChart });
    }
    
    // Classify subdocuments
    console.log("Classifying subdocuments...");
    await handle.signal("classifySubdocuments");
    
    // Process subdocuments
    console.log("Processing subdocuments...");
    await handle.signal("processSubdocuments", { agencyId, patientId });
    
    // Create resources for subdocuments
    console.log("Creating resources for subdocuments...");
    await handle.signal("createResourcesForSubdocuments", { agencyId, patientId });
    
    // Signal workflow completion
    console.log("Signaling workflow completion...");
    await handle.signal("completeWorkflow", { success: true });
    
    // Wait for the workflow to complete
    console.log("Waiting for workflow to complete...");
    await handle.result();
    
    console.log("PDF import workflow completed successfully");
    return { success: true, workflowId: handle.workflowId };
    
  } catch (error) {
    console.error("PDF import workflow failed:", error);
    
    // Try to signal failure to the workflow if it's still running
    try {
      const handle = await getPdfImportWorkflowHandle(requestId, agencyId);
      await handle.signal("completeWorkflow", { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      });
    } catch (signalError) {
      console.error("Failed to signal workflow failure:", signalError);
    }
    
    throw error;
  }
}
