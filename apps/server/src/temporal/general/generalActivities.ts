import { ResourceService } from "../../services/resource-service";
import { getSignedUrl } from "../../services/storage-service";

export const nonDeletableResources = ["Patient", "User", "Agency", "Schema"];

export type Resource = Record<string, unknown> & {
  id?: string;
  resourceType: string;
  schemaId: string;
  agencyId: string;
  createdAt: string;
  updatedAt: string;
};

export const generalActivities = (resourceService: ResourceService) => ({
  readResource: async <T>(id: string | undefined): Promise<T | undefined> => {
    if (!id) {
      return undefined;
    }

    try {
      const resource = await resourceService.readResource(id);
      return resource;
    } catch (error) {
      console.log("Error reading resource", id, error);
      return undefined;
    }
  },
  searchResourceWithPaths: async <T extends Resource>(
    resourceType: string,
    agencyId: string,
    jsonPathFilters?: Record<string, unknown>,
    fields?: string[],
    limit?: number,
  ): Promise<T[]> => {
    return await resourceService.queryResourcesByType<T>(
      resourceType,
      agencyId,
      jsonPathFilters,
      fields,
      limit,
    );
  },
  getPresignedUrl: async (key: string): Promise<string> => {
    return await getSignedUrl(key);
  },
  createResource: async (
    resource: Resource,
    skipWorkflows: boolean = false,
  ): Promise<Resource> => {
    if (resource.id) {
      throw new Error(
        "Resource ID is not allowed to be set when creating a resource",
      );
    }
    return await resourceService.createResource(
      resource,
      resource.schemaId,
      resource.agencyId,
      skipWorkflows,
    );
  },
  updateResource: async (resource: Resource): Promise<Resource> => {
    if (!resource.id) {
      throw new Error("Resource ID is required to update a resource");
    }
    return await resourceService.updateResource(
      resource.id,
      resource,
      resource.schemaId,
    );
  },
  deleteResource: async (
    resourceType: string,
    id: string,
  ): Promise<boolean> => {
    if (nonDeletableResources.includes(resourceType)) {
      throw new Error(`Resource type ${resourceType} is not deletable`);
    }
    return await resourceService.deleteResource(id);
  },
});

export type GeneralActivities = ReturnType<typeof generalActivities>;
