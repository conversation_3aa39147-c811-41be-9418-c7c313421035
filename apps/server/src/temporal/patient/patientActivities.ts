/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ChangesetItem,
  Patient,
  Request,
} from "@hospice-os/apptypes";
import { applyPatch, deepClone, Operation } from "fast-json-patch";
import {
  Changeset,
  generateIDGPatientSummary,
  generateTimelineItemSummary,
  performQa,
  serializeChartForLLM,
} from "../../services/llm-service";
import { resourceService } from "../../services/resource-service";
import { ensurePatchPaths } from "../../utils/patch-utils";
import { schemaService } from "../../services/schema-service";
import { isIDG } from "../../utils/idg";

const applyChangeset = (patient: Patient, changeset: Changeset) => {
  const operations = changeset.changes.map((change) => {
    const operation: Operation = {
      op: change.op,
      path: change.path,
      value: change.value,
    };
    return operation;
  });

  const clonedPatient = deepClone(patient);
  ensurePatchPaths(clonedPatient, operations);
  applyPatch(clonedPatient, operations);
  console.log("  previous patient", patient);
  console.log("  new patient", clonedPatient);
  return clonedPatient;
};

export const patientActivities = {
  updateRequestStatus: async (
    status: typeof request.status,
    request: Request,
  ) => {
    request.status = status;
    return await resourceService.updateResource(
      request.id,
      request as any,
      request.schemaId,
    );
  },

  checkIsIDG: async (noteSchemaId: string) => {
    return isIDG(noteSchemaId);
  },

  generateIDGSummary: async (request: Request) => {
    return await generateIDGPatientSummary(request);
  },


  generateTimelineSummary: async (request: Request) => {
    return await generateTimelineItemSummary(request);
  },

  getSchemaName: async (schemaId: string) => {
    return (await schemaService.getSchema(schemaId)).name;
  },

  applyChangesetToPatient: async (
    patient: Patient,
    changes: ChangesetItem[],
  ) => {
    return applyChangeset(patient, { changes });
  },


  performQaCheck: async (request: Request) => {
    return await performQa(request);
  },

  logRequest: async (requestId: string, message: string) => {
    console.log(`[${requestId}] ${message}`);
  },
  logRequestError: async (requestId: string, message: string) => {
    console.error(`[${requestId}] ${message}`);
  },
  
  serializeChartForLLM: async (patient: Patient) => {
    return await serializeChartForLLM(patient);
  },
};

export type PatientActivities = typeof patientActivities;
