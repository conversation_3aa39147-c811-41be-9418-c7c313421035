/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ChangesetItem,
  Request,
  User,
  Patient,
  IDGMeeting,
} from "@hospice-os/apptypes";
import {
  proxyActivities,
  setHandler,
  defineQuery,
  defineSignal,
  executeChild,
  condition,
  workflowInfo,
  WorkflowIdReusePolicy,
} from "@temporalio/workflow";
import type { patientActivities } from "./patientActivities";
import type { requestActivities } from "../request/requestActivities";
import { processRequestSubmission } from "../request/requestWorkflow";
import dayjs from "dayjs";
import { GeneralActivities } from "../general/generalActivities";

// Quick activities for DB operations
const {
  updateRequestStatus,
  checkIsIDG,
  getSchemaName,
  applyChangesetToPatient,
  logRequest,
  logRequestError,
} = proxyActivities<typeof patientActivities>({
  startToCloseTimeout: "2 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// LLM activities for AI operations
const {
  generateIDGSummary,
  generateTimelineSummary,
  performQa<PERSON>heck,
  serializeChartForLLM,
} = proxyActivities<typeof patientActivities>({
  startToCloseTimeout: "10 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// Quick activities
const { minifyPatient } = proxyActivities<typeof requestActivities>({
  startToCloseTimeout: "1 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// LLM and transcription activities
const {
  processNote,
  transcribeAudio,
  generateModifiedChangeset,
} = proxyActivities<typeof requestActivities>({
  startToCloseTimeout: "15 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

const generalActs = proxyActivities<GeneralActivities>({
  startToCloseTimeout: "2 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

export type TranscribeRequest = {
  request: Request;
};

interface PatientWorkflowState {
  startedAt?: string;
  requestId?: string;
  state?: string;
  isCompleted?: boolean;
}

// Helper function to process attachments
async function processAttachments(request: Request, fileAttachments: string[]) {
  request.status = "processing";
  await updateRequestStatus("processing", request);

  const currentWorkflowId = workflowInfo().workflowId;
  const id = `${currentWorkflowId}-processAttachments`;

  await executeChild(processRequestSubmission, {
    args: [
      {
        fileAttachments,
        requestId: request.id,
      },
    ],
    workflowIdReusePolicy: WorkflowIdReusePolicy.ALLOW_DUPLICATE,
    workflowId: id,
  });
}

export async function patientRequestWorkflow(
  req: TranscribeRequest,
): Promise<void> {
  const state: PatientWorkflowState = {
    requestId: req.request.id,
  };

  // Define query handlers
  setHandler(getStatusQuery, () => state.state);

  // Define signal handlers
  setHandler(reviewSignal, async (params) => {
    await review(params, state);
  });

  setHandler(processNoteSchemaSignal, async (params) => {
    await processNoteSchema(params, state);
  });

  setHandler(sendMoreAudioSignal, async (params) => {
    await sendMoreAudio(params);
  });

  setHandler(modifyChangesetSignal, async (params) => {
    await modifyChangeset(params);
  });

  // Main workflow execution
  await processRequest(req, state);

  // Update state to indicate waiting for review
  state.state = "waiting_for_review";

  // Wait for the workflow to be completed via the review signal
  await condition(() => state.isCompleted === true);
}

async function processRequest(
  req: TranscribeRequest,
  state: PatientWorkflowState,
) {
  const request = req.request;
  state.startedAt = dayjs().format("YYYY-MM-DD HH:mm:ss");
  request.status = "processing";

  await updateRequestStatus("processing", request);

  const isIDGFlag = await checkIsIDG(request.noteSchemaId);

  if (!request.fileAttachments || request.fileAttachments.length === 0) {
    if (isIDGFlag) {
      request.status = "completed";
      request.summary = await generateIDGSummary(request);
      await generalActs.updateResource(request);
      // Mark workflow as completed for auto-complete IDG scenario
      state.isCompleted = true;
      return;
    } else {
      logRequestError(request.id, "No attachments found");
      throw new Error("No attachments found");
    }
  }

  logRequest(
    request.id,
    `Processing ${request.fileAttachments.length} attachments`,
  );

  if (!request.noteSchemaId) {
    logRequest(
      request.id,
      "No note schema id found, waiting for one to be picked before processing attachments",
    );
    request.status = "waiting_on_note_schema";
    await updateRequestStatus("waiting_on_note_schema", request);
    return;
  }

  await processChartUpdates({
    requestId: request.id,
    state,
  });
}

async function review(
  req: {
    requestId: string;
    userId?: string;
    manualChangeset?: ChangesetItem[];
  },
  state: PatientWorkflowState,
) {
  logRequest(req.requestId, "reviewing/submitting request");
  const request = await generalActs.readResource<Request>(req.requestId);

  if (!request) {
    logRequestError(
      req.requestId,
      "Request not found when reviewing, are you sure this request is still processing?",
    );
    throw new Error("Request not found");
  }
  request.status = "processing";

  await updateRequestStatus("processing", request);

  const isIDGFlag = await checkIsIDG(request.noteSchemaId);

  logRequest(req.requestId, `request approved by: ${req.userId}`);
  const user = req.userId
    ? await generalActs.readResource<User>(req.userId)
    : undefined;
  request.status = "completed";
  request.approvedBy = {
    id: req.userId,
    resourceType: "User",
    name: `${user?.firstName} ${user?.lastName}`,
    date: new Date().toISOString(),
  };

  const patient = await generalActs.readResource<Patient>(request.patient.id);

  if (!patient) {
    throw new Error("Patient not found");
  }

  if (!isIDGFlag) {
    const timelineItemSummary = await generateTimelineSummary(request);
    request.summary = timelineItemSummary;
  }

  request.title = await getSchemaName(request.noteSchemaId);

  if (req.manualChangeset) {
    request.changes = req.manualChangeset;
  }

  const patchedPatient = await applyChangesetToPatient(
    patient,
    request.changes,
  );

  await generalActs.updateResource(patchedPatient);
  await generalActs.updateResource(request);

  if (isIDGFlag) {
    console.log("this is an idg meeting, so lets update the idg meeting");
    const idgMeeting = await generalActs.readResource<IDGMeeting>(
      request.idgMeeting.id,
    );
    if (idgMeeting) {
      idgMeeting.patients = idgMeeting.patients?.map((p) => {
        if (p.id === patient.id) {
          return {
            ...p,
            isCompleted: true,
          };
        }
        return p;
      });
      await generalActs.updateResource(idgMeeting);
    }
  }

  // Mark the workflow as completed
  state.isCompleted = true;
}

// this is a temporary function used to test a different note schema against a request
async function processNoteSchema(
  req: {
    requestId: string;
    noteSchemaId: string;
  },
  state: PatientWorkflowState,
) {
  const request = await generalActs.readResource<Request>(req.requestId);

  if (!request) {
    throw new Error("Request not found");
  }

  request.noteSchemaId = req.noteSchemaId;
  await generalActs.updateResource(request);

  return await processChartUpdates({
    requestId: req.requestId,
    state,
  });
}

async function sendMoreAudio(req: { audioUrls: string[]; requestId: string }) {
  logRequest(req.requestId, "sending more audio");
  const request = await generalActs.readResource<Request>(req.requestId);
  if (!request) {
    logRequestError(req.requestId, "Request not found when sending more audio");
    throw new Error("Request not found");
  }

  await processAttachments(request, req.audioUrls);
}

async function modifyChangeset(req: { audioUrl: string; requestId: string }) {
  logRequest(req.requestId, "modifying changeset");
  const request = await generalActs.readResource<Request>(req.requestId);
  if (!request) {
    logRequestError(req.requestId, "Request not found when sending more audio");
    throw new Error("Request not found");
  }
  request.status = "processing";
  await updateRequestStatus("processing", request);

  // Handle changeset modification
  const patient = await generalActs.readResource<Patient>(request.patient.id);
  if (!patient) {
    throw new Error("Patient not found");
  }

  const patientChart = await minifyPatient(patient);
  const transcription = await transcribeAudio(req.audioUrl);

  // Serialize chart for LLM with IDs
  const [serializedChart, chartWithIds] = await serializeChartForLLM(patient);

  // Save the patient with IDs for future use
  await generalActs.updateResource(chartWithIds);

  const modifiedChangeset = await generateModifiedChangeset(
    patientChart,
    { changes: request.changes },
    transcription.text,
    serializedChart,
  );

  request.changes = modifiedChangeset.changes;
  request.fileAttachments = [...request.fileAttachments, req.audioUrl];
  request.transcription = [...request.transcription, transcription];
  request.status = "pending_review";

  await generalActs.updateResource(request);

  // Refresh request after update
  const updatedRequest = await generalActs.readResource<Request>(request.id);
  if (!updatedRequest) {
    throw new Error("Updated request not found");
  }

  const isIDGFlag = await checkIsIDG(request.noteSchemaId);
  if (!isIDGFlag) {
    const qa = await performQaCheck(updatedRequest);
    updatedRequest.qa = qa;
  }

  const note = await processNote(
    updatedRequest.noteSchemaId,
    updatedRequest.transcription.map((t) => t.text).join("\n---\n"),
    updatedRequest.patient.id,
    updatedRequest.agencyId,
    updatedRequest.noteCreated?.id,
  );

  updatedRequest.noteCreated = {
    id: note.id,
    resourceType: "Note",
  };

  await generalActs.updateResource(updatedRequest);
}

async function processChartUpdates({
  requestId,
}: {
  requestId: string;
  state: PatientWorkflowState;
}) {
  const request = await generalActs.readResource<Request>(requestId);
  if (!request) {
    logRequestError(requestId, "Request not found when processing note schema");
    throw new Error("Request not found");
  }

  logRequest(request.id, "before processing note schema");
  await processAttachments(request, request.fileAttachments);
}

// Define queries and signals
export const getStatusQuery = defineQuery<string | undefined>("getStatus");

export const reviewSignal = defineSignal<
  [
    {
      requestId: string;
      userId: string;
      manualChangeset?: ChangesetItem[];
    },
  ]
>("review");

export const processNoteSchemaSignal = defineSignal<
  [
    {
      requestId: string;
      noteSchemaId: string;
    },
  ]
>("processNoteSchema");

export const sendMoreAudioSignal = defineSignal<
  [
    {
      audioUrls: string[];
      requestId: string;
    },
  ]
>("sendMoreAudio");

export const modifyChangesetSignal = defineSignal<
  [
    {
      audioUrl: string;
      requestId: string;
    },
  ]
>("modifyChangeset");
