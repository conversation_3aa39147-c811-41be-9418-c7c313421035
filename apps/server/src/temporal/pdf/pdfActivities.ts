/* eslint-disable @typescript-eslint/no-explicit-any */
import { Note, Patient, Request } from "@hospice-os/apptypes";
import { splitPdfToJpegs } from "../../services/pdf-service";
import { getSignedUrl } from "../../services/storage-service";
import {
  extractPageMarkdown,
  fillInitialPatientChart,
  fillSchemaFromTranscription,
} from "../../services/llm-service";
import { getSchemaResolverService } from "../../services/schema-resolver-service";
import { createWorker } from "tesseract.js";
import {
  classifySubdocuments,
  generatePlanOfCare,
  Subdocument,
  SUBDOCUMENT_CLASSIFICATION_TYPES_MAP,
} from "../../services/patient-chart-llm-service";
import { resourceService } from "../../services/resource-service";
import { uuidv7 } from "uuidv7";

export interface PageMarkdown {
  s3Key: string;
  pageNumber: number;
  markdown: string;
}

export interface SubdocumentNote {
  subdocument: Subdocument;
  note: Note | null;
}

export const pdfActivities = {
  getPdfBuffer: async (pdfUrl: string): Promise<Buffer> => {
    const pdfPresignedUrl = await getSignedUrl(pdfUrl);
    const pdfArrayBuffer = await fetch(pdfPresignedUrl).then((res) =>
      res.arrayBuffer(),
    );
    return Buffer.from(pdfArrayBuffer);
  },

  splitPdfToJpegs: async (
    pdfBuffer: Buffer,
    requestId: string,
    pdfName: string,
  ) => {
    return await splitPdfToJpegs(pdfBuffer, {
      uploadToS3: true,
      s3KeyPrefix: `uploads/${requestId}/${pdfName}/pages`,
    });
  },

  extractPageMarkdown: async (pageS3Key: string): Promise<string> => {
    const pagePresignedUrl = await getSignedUrl(pageS3Key);

    const worker = await createWorker("eng");
    const ret = await worker.recognize(
      pagePresignedUrl,
      {},
      {
        hocr: false,
      },
    );

    const markdown = await extractPageMarkdown(
      pagePresignedUrl,
      ret.data.text,
    );
    await worker.terminate();
    return markdown;
  },

  generatePlanOfCare: async (
    transcription: string,
    previousPlanOfCare?: any,
  ) => {
    return await generatePlanOfCare(
      transcription,
      previousPlanOfCare ? JSON.stringify(previousPlanOfCare) : undefined,
    );
  },

  fillInitialPatientChart: async (
    transcription: string,
    patientChartSchema: any,
    patientChart: any,
  ) => {
    return await fillInitialPatientChart(
      transcription,
      patientChartSchema,
      patientChart,
    );
  },

  getPatientChartSchema: async () => {
    const schema = await getSchemaResolverService().resolveSchema(
      "6cb5c6e8-22c2-4df5-beb4-5cf2509e2673",
    ); // PatientBase schema ID
    
    // Remove fields that shouldn't be in the initial patient chart
    delete schema["agencyActions"];
    delete schema["hospicePhysicianId"];
    
    return schema;
  },

  classifySubdocuments: async (transcription: string): Promise<Subdocument[]> => {
    return await classifySubdocuments(transcription);
  },

  hydrateNoteSchemaFromTranscription: async (
    transcription: string,
    schemaId: string,
    patientId: string,
    agencyId: string,
  ): Promise<Note> => {
    const schemaService = getSchemaResolverService();
    const resolvedSchema = await schemaService.resolveSchema(schemaId);
    if (!resolvedSchema) {
      throw new Error(`Schema ${schemaId} not found`);
    }

    resolvedSchema.properties = resolvedSchema.properties || {};
    resolvedSchema.properties.createdAt = {
      type: "string",
      format: "date-time",
      description: "Timestamp for when the note was created",
    } as any;

    resolvedSchema.required = resolvedSchema.required || [];
    if (!resolvedSchema.required.includes("createdAt")) {
      resolvedSchema.required.push("createdAt");
    }

    const jsonResult = await fillSchemaFromTranscription(transcription, resolvedSchema);
    console.log("  ↳ got json result", jsonResult);

    if (!jsonResult) {
      throw new Error("Failed to hydrate schema from transcription");
    }

    jsonResult.patientId = patientId;
    jsonResult.createdAt = jsonResult.createdAt || new Date().toISOString();
    jsonResult.updatedAt = jsonResult.createdAt;
    jsonResult.agencyId = agencyId;
    jsonResult.schemaId = schemaId;

    return jsonResult;
  },

  processSubdocument: async (
    subdocument: Subdocument,
    pages: PageMarkdown[],
    patientId: string,
    agencyId: string,
  ): Promise<SubdocumentNote | null> => {
    const schemaId = SUBDOCUMENT_CLASSIFICATION_TYPES_MAP[subdocument.type];
    if (!schemaId) {
      throw new Error(`Schema ID not found for ${subdocument.type}`);
    }
    if (schemaId === "none") {
      return null;
    }

    const subDocumentText = pages
      .slice(subdocument.startPage - 1, subdocument.endPage)
      .map((page) => `Page ${page.pageNumber}\n\n${page.markdown}`)
      .join("\n\n---\n\n");

    const note = await pdfActivities.hydrateNoteSchemaFromTranscription(
      subDocumentText,
      schemaId,
      patientId,
      agencyId,
    );

    return { subdocument, note };
  },

  createNoteResource: async (
    note: Note,
    schemaId: string,
    agencyId: string,
  ) => {
    return await resourceService.createResource(
      note as any,
      schemaId,
      agencyId,
      true, // skipWorkflows
    );
  },

  createRequestResource: async (
    request: Request,
    schemaId: string,
    agencyId: string,
  ) => {
    return await resourceService.createResource(
      request as any,
      schemaId,
      agencyId,
      true, // skipWorkflows
    );
  },

  createRequestForNote: (
    note: any,
    subdocument: any,
    patientId: string,
    agencyId: string,
    originalRequestId: string,
    pdfUrl: string,
  ): Request => {
    return {
      id: uuidv7(),
      patient: {
        id: patientId,
        resourceType: "Patient",
      },
      title: note.title,
      summary: note.summary,
      resourceType: "Request",
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
      agencyId: agencyId,
      schemaId: "fb016bdd-1d9e-489a-b05e-08bd7fcb188b",
      noteCreated: {
        id: note.id,
        resourceType: "Note",
      },
      noteSchemaId: note.schemaId,
      parentRequest: {
        id: originalRequestId,
        resourceType: "Request",
      },
      subdocument: subdocument,
      fileAttachments: [pdfUrl],
      status: "completed",
      madeBy: {
        id: "system",
        resourceType: "User",
        name: "System",
        date: note.createdAt,
      },
      approvedBy: {
        id: "system",
        resourceType: "User",
        name: "System",
        date: note.createdAt,
      },
    } as Request;
  },

  minifyPatient: (patient: Patient) => {
    const p = {
      ...patient,
      timeLineItems: undefined,
      complianceTimeline: undefined,
      agencyId: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };
    delete p.id;
    delete p.createdAt;
    delete p.updatedAt;
    delete p.agencyId;
    delete p.timeLineItems;
    delete p.complianceTimeline;

    return p;
  },
};