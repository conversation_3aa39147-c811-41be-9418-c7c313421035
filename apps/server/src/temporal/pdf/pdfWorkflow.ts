/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  proxyActivities,
  setHandler,
  defineSignal,
  condition,
} from "@temporalio/workflow";
import type { pdfActivities, PageMarkdown, SubdocumentNote } from "./pdfActivities";
import { Subdocument } from "../../services/patient-chart-llm-service";

// Quick activities for simple operations
const quickActivities = proxyActivities<typeof pdfActivities>({
  startToCloseTimeout: "2 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// Standard activities for moderate operations
const activities = proxyActivities<typeof pdfActivities>({
  startToCloseTimeout: "5 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// Long-running activities for LLM operations
const llmActivities = proxyActivities<typeof pdfActivities>({
  startToCloseTimeout: "15 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

export interface PatientImportPdfRequest {
  requestId: string;
  pdfUrl: string;
}

interface PdfWorkflowState {
  originalRequestId?: string;
  pdfUrl?: string;
  pages?: PageMarkdown[];
  transcription?: string;
  planOfCare?: any;
  patientChart?: any;
  subdocuments?: Subdocument[];
  subSubdocuments?: Subdocument[][];
  notes?: SubdocumentNote[];
  requests?: any[];
  isCompleted?: boolean;
  completedAt?: string;
  error?: string;
  completedSteps: string[];
}

export async function patientImportPdfWorkflow(): Promise<void> {
  const state: PdfWorkflowState = {
    completedSteps: [],
    isCompleted: false,
  };

  // Define signal handlers
  setHandler(convertPDFToMarkdownSignal, async (req) => {
    await convertPDFToMarkdown(req, state);
    state.completedSteps.push("pdfConverted");
  });

  setHandler(generatePlanOfCareSignal, async (req) => {
    await generatePlanOfCare(req, state);
    state.completedSteps.push("planOfCareGenerated");
  });

  setHandler(generatePatientChartSignal, async (req) => {
    await generatePatientChart(req, state);
    state.completedSteps.push("patientChartGenerated");
  });

  setHandler(classifySubdocumentsSignal, async () => {
    await classifySubdocuments(state);
    state.completedSteps.push("subdocumentsClassified");
  });

  setHandler(processSubdocumentsSignal, async (req) => {
    await processSubdocuments(req, state);
    state.completedSteps.push("subdocumentsProcessed");
  });

  setHandler(createResourcesForSubdocumentsSignal, async (req) => {
    await createResourcesForSubdocuments(req, state);
    state.completedSteps.push("resourcesCreated");
  });

  setHandler(completeWorkflowSignal, async ({ success, error }) => {
    state.isCompleted = true;
    state.completedAt = new Date().toISOString();
    if (!success) {
      state.error = error;
    }
  });

  // Wait for completion signal
  await condition(() => state.isCompleted === true);

  // Throw error if workflow failed
  if (state.error) {
    throw new Error(`PDF workflow failed: ${state.error}`);
  }
}

async function convertPDFToMarkdown(
  req: PatientImportPdfRequest,
  state: PdfWorkflowState,
) {
  state.originalRequestId = req.requestId;
  state.pdfUrl = req.pdfUrl;

  const pdfName = req.pdfUrl.split("/").pop()?.replace(".pdf", "") || "document";
  const pdfBuffer = await quickActivities.getPdfBuffer(req.pdfUrl);

  const pdfSplitResult = await activities.splitPdfToJpegs(
    pdfBuffer,
    req.requestId,
    pdfName,
  );

  const pages = pdfSplitResult.pages;

  const pageMarkdownPromises = pages.map(async (page) => {
    const markdown = await llmActivities.extractPageMarkdown(page.s3Key);
    return markdown;
  });

  const pageMarkdownArray = await Promise.all(pageMarkdownPromises);
  const pageMarkdownObjectArray: PageMarkdown[] = pages.map((page, index) => {
    return {
      s3Key: page.s3Key,
      pageNumber: page.pageNumber,
      markdown: pageMarkdownArray[index],
    };
  });

  state.pages = pageMarkdownObjectArray;
  const text = pageMarkdownObjectArray
    .map((page) => `Page ${page.pageNumber}\n\n${page.markdown}`)
    .join("\n\n---\n\n");

  state.transcription = text;
}

async function generatePlanOfCare(
  req: { planOfCare: any },
  state: PdfWorkflowState,
) {
  const transcription = state.transcription;
  if (!transcription) {
    throw new Error("Transcription not found");
  }

  const planOfCare1 = await llmActivities.generatePlanOfCare(
    transcription,
    req.planOfCare,
  );

  const planOfCareFinal = await llmActivities.generatePlanOfCare(
    transcription,
    planOfCare1,
  );

  state.planOfCare = planOfCareFinal;
  return planOfCareFinal;
}

async function generatePatientChart(
  req: { patientChart: any },
  state: PdfWorkflowState,
) {
  const transcription = state.transcription;
  if (!transcription) {
    throw new Error("Transcription not found");
  }

  const patientChartSchema = await quickActivities.getPatientChartSchema();
  const patientChart = await quickActivities.minifyPatient(req.patientChart);

  const newPatientChart = await llmActivities.fillInitialPatientChart(
    transcription,
    patientChartSchema,
    patientChart,
  );

  state.patientChart = newPatientChart;
  return newPatientChart;
}

async function classifySubdocuments(state: PdfWorkflowState) {
  const transcription = state.transcription;
  const pages = state.pages;

  if (!transcription) {
    throw new Error("Transcription not found");
  }
  if (!pages) {
    throw new Error("Pages not found");
  }

  const subdocuments = await llmActivities.classifySubdocuments(transcription);
  state.subdocuments = subdocuments;

  const subdocumentPromises = subdocuments.map(async (subdocument) => {
    const subDocumentText = pages
      .slice(subdocument.startPage - 1, subdocument.endPage)
      .map((page) => `Page ${page.pageNumber}\n\n${page.markdown}`)
      .join("\n\n---\n\n");
    
    return await llmActivities.classifySubdocuments(subDocumentText);
  });

  const subSubdocuments = await Promise.all(subdocumentPromises);

  for (const [index, subSubdocument] of subSubdocuments.entries()) {
    const subdocument = subdocuments[index];
    if (subSubdocument.length > 1) {
      console.log(
        `${subdocument.startPage}-${subdocument.endPage} changed`,
      );
    } else {
      console.log(
        `${subdocument.startPage}-${subdocument.endPage} unchanged`,
      );
    }
  }
  state.subSubdocuments = subSubdocuments;

  return subdocuments;
}

async function processSubdocuments(
  req: {
    agencyId: string;
    patientId: string;
  },
  state: PdfWorkflowState,
) {
  const pages = state.pages;
  const subSubdocuments = state.subSubdocuments;

  if (!pages) {
    throw new Error("Pages not found");
  }
  if (!subSubdocuments) {
    throw new Error("SubSubdocuments not found");
  }

  const notePromises: Promise<SubdocumentNote | null>[] = [];
  for (const subSubdocumentL1 of subSubdocuments) {
    for (const subSubdocumentL2 of subSubdocumentL1) {
      const finalSubdoc = llmActivities.processSubdocument(
        subSubdocumentL2,
        pages,
        req.patientId,
        req.agencyId,
      );
      notePromises.push(finalSubdoc);
    }
  }

  const notes = await Promise.all(notePromises);
  state.notes = notes.filter((note): note is SubdocumentNote => note !== null);

  return state.notes;
}

async function createResourcesForSubdocuments(
  req: {
    agencyId: string;
    patientId: string;
  },
  state: PdfWorkflowState,
) {
  const originalRequestId = state.originalRequestId;
  const pdfUrl = state.pdfUrl;
  const notes = state.notes;

  if (!originalRequestId) {
    throw new Error("Original request ID not found");
  }
  if (!pdfUrl) {
    throw new Error("PDF URL not found");
  }
  if (!notes) {
    throw new Error("Notes not found");
  }

  const notePromises = [];
  for (const noteData of notes) {
    if (noteData?.note) {
      const newNote = quickActivities.createNoteResource(
        noteData.note,
        noteData.note.schemaId,
        req.agencyId,
      );
      notePromises.push(newNote);
    }
  }

  const newNotes = await Promise.all(notePromises);

  const requestPromises = newNotes.map(async (note, index) => {
    const noteData = notes[index];
    const request = quickActivities.createRequestForNote(
      note,
      noteData.subdocument,
      req.patientId,
      req.agencyId,
      originalRequestId,
      pdfUrl,
    );

    return quickActivities.createRequestResource(
      request,
      "fb016bdd-1d9e-489a-b05e-08bd7fcb188b",
      req.agencyId,
    );
  });

  const requests = await Promise.all(requestPromises);
  state.requests = requests;

  return requests;
}

// Define signals
export const convertPDFToMarkdownSignal = defineSignal<[PatientImportPdfRequest]>(
  "convertPDFToMarkdown",
);

export const generatePlanOfCareSignal = defineSignal<[{ planOfCare: any }]>(
  "generatePlanOfCare",
);

export const generatePatientChartSignal = defineSignal<[{ patientChart: any }]>(
  "generatePatientChart",
);

export const classifySubdocumentsSignal = defineSignal(
  "classifySubdocuments",
);

export const processSubdocumentsSignal = defineSignal<[{
  agencyId: string;
  patientId: string;
}]>("processSubdocuments");

export const createResourcesForSubdocumentsSignal = defineSignal<[{
  agencyId: string;
  patientId: string;
}]>("createResourcesForSubdocuments");

export const completeWorkflowSignal = defineSignal<[{
  success: boolean;
  error?: string;
}]>("completeWorkflow");