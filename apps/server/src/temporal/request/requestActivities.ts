/* eslint-disable @typescript-eslint/no-explicit-any */
import { Note, Patient } from "@hospice-os/apptypes";
import {
  extractPageMarkdown,
  fillSchemaFromTranscription,
  generateChangeset,
  generateModifiedChangeset,
  getTranscription,
  judgeChangeset,
  serializeChartForLLM,
} from "../../services/llm-service";
import { resourceService } from "../../services/resource-service";
import { getSchemaResolverService } from "../../services/schema-resolver-service";
import { getSignedUrl } from "../../services/storage-service";
import { createWorker } from "tesseract.js";
import { splitPdfToJpegs } from "../../services/pdf-service";
import pointer from "json-pointer";

export type TranscribeResult = {
  urlPath: string;
  text: string;
};

export type PatientChart = Omit<
  Patient,
  | "id"
  | "resourceType"
  | "createdAt"
  | "updatedAt"
  | "timeLineItems"
  | "complianceTimeline"
  | "agencyId"
>;

export const requestActivities = {
  transcribeAudio: async (urlPath: string): Promise<TranscribeResult> => {
    console.log("▶️ transcribeAudio START", urlPath);
    const presignedURL = await getSignedUrl(urlPath);
    console.log("  ↳ got presigned url for audio", presignedURL);
    const transcription = await getTranscription(presignedURL);
    console.log("  ↳ got transcription for audio", transcription.length);

    return {
      urlPath: urlPath,
      text: transcription,
    };
  },

  transcribePDF: async (pdfUrl: string): Promise<TranscribeResult> => {
    let text = "";

    const pdfPresignedUrl = await getSignedUrl(pdfUrl);
    const pdfArrayBuffer = await fetch(pdfPresignedUrl).then((res) =>
      res.arrayBuffer(),
    );

    const pdfBuffer = Buffer.from(pdfArrayBuffer);

    const pdfSplitResult = await splitPdfToJpegs(pdfBuffer, {
      uploadToS3: true,
      s3KeyPrefix: `${pdfUrl.replace(".pdf", "")}/pages`,
    });

    const pages = pdfSplitResult.pages;

    const pageMarkdownPromises = pages.map(async (page) => {
      const pagePresignedUrl = await getSignedUrl(page.s3Key);

      const worker = await createWorker("eng");
      const ret = await worker.recognize(
        pagePresignedUrl,
        {},
        {
          hocr: false,
        },
      );

      const markdown = await extractPageMarkdown(
        pagePresignedUrl,
        ret.data.text,
      );
      await worker.terminate();

      return markdown;
    });

    const pageMarkdownArray = await Promise.all(pageMarkdownPromises);
    const pageMarkdownObjectArray = pages.map((page, index) => {
      return {
        s3Key: page.s3Key,
        pageNumber: page.pageNumber,
        markdown: pageMarkdownArray[index],
      };
    });

    text = pageMarkdownObjectArray
      .map((page) => `Page ${page.pageNumber}\n${page.markdown}`)
      .join("\n---\n");

    return {
      urlPath: pdfUrl,
      text: text,
    };
  },

  processNote: async (
    schemaId: string,
    transcription: string,
    patientId: string,
    agencyId: string,
    noteId?: string,
  ): Promise<Note> => {
    const schemaService = getSchemaResolverService();
    const resolvedSchema = await schemaService.resolveSchema(schemaId);
    const jsonResult = await fillSchemaFromTranscription(
      transcription,
      resolvedSchema,
    );
    console.log("  ↳ got json result", jsonResult);

    if (!jsonResult) {
      throw new Error("Failed to hydrate schema from transcription");
    }

    jsonResult.patientId = patientId;

    if (noteId) {
      jsonResult.id = noteId;
      jsonResult.updatedAt = new Date().toISOString();
      const updatedResource = await resourceService.updateResource(
        noteId,
        jsonResult,
        schemaId,
      );
      console.log("  ↳ updated resource", updatedResource);
      return updatedResource as any;
    } else {
      jsonResult.createdAt = new Date().toISOString();
      const newResource = await resourceService.createResource(
        jsonResult,
        schemaId,
        agencyId,
      );
      console.log("  ↳ got new resource", newResource);
      return newResource;
    }
  },

  generateChangeset: async (
    transcription: string | undefined,
    patientChart: PatientChart,
    patientChartSchema: any,
    feedback: string | null,
    previousChangesetMessages: any[],
    serializedChart?: any,
  ) => {
    return await generateChangeset(
      transcription,
      patientChart,
      patientChartSchema,
      serializedChart,
      feedback,
      previousChangesetMessages,
    );
  },

  judgeChangeset: async (
    changeset: any,
    patientChart: PatientChart,
    transcription: string | undefined,
    patientChartSchema: any,
    previousJudgeMessages: any[],
  ) => {
    return await judgeChangeset(
      changeset,
      patientChart,
      transcription,
      patientChartSchema,
      previousJudgeMessages,
    );
  },

  generateModifiedChangeset: async (
    patientChart: PatientChart,
    changeset: { changes: any[] },
    transcription: string,
    serializedChart: any,
  ) => {
    return await generateModifiedChangeset(
      patientChart,
      changeset,
      transcription,
      serializedChart,
    );
  },

  getPatientChartSchema: async () => {
    return await getSchemaResolverService().resolveSchema(
      "6cb5c6e8-22c2-4df5-beb4-5cf2509e2673",
    ); // PatientBase schema ID
  },

  addOldValueToChanges: async (changes: any[], patientChart: PatientChart) => {
    for (const change of changes) {
      if (change.op === "replace" || change.op === "remove") {
        try {
          change.oldValue = pointer.get(patientChart, change.path);
        } catch (error: any) {
          console.error(error);
          change.oldValue = null;
        }
      } else {
        change.oldValue = null;
      }
    }
    return changes;
  },

  minifyPatient: async (patient: Patient) => {
    const p = {
      ...patient,
      timeLineItems: undefined,
      complianceTimeline: undefined,
      agencyId: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };
    delete p.id;
    delete p.createdAt;
    delete p.updatedAt;
    delete p.agencyId;
    delete p.timeLineItems;
    delete p.complianceTimeline;

    const patientChart: PatientChart = p;
    return patientChart;
  },

  serializeChartForLLM: async (patient: Patient) => {
    return serializeChartForLLM(patient);
  },
};

export type RequestActivities = typeof requestActivities;
