/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Patient } from "@hospice-os/apptypes";
import { proxyActivities } from "@temporalio/workflow";
import type {
  requestActivities,
  PatientChart,
  TranscribeResult,
} from "./requestActivities";
import type { patientActivities } from "../patient/patientActivities";
import dayjs from "dayjs";

// Quick activities for simple operations
const { minifyPatient, getPatientChartSchema } = proxyActivities<typeof requestActivities>({
  startToCloseTimeout: "1 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// LLM activities for AI operations
const {
  processNote,
  transcribeAudio,
  transcribePDF,
  generateChangeset,
  judgeChangeset,
  addOldValueToChanges,
  serializeChartForLLM,
  generateModifiedChangeset,
} = proxyActivities<typeof requestActivities>({
  startToCloseTimeout: "15 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

const patientActs = proxyActivities<typeof patientActivities>({
  startToCloseTimeout: "5 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

// Create a simplified general activities interface for workflows
interface GeneralActivitiesForWorkflow {
  readResource: <T>(id: string | undefined) => Promise<T | undefined>;
  updateResource: (resource: any) => Promise<any>;
}

const generalActs = proxyActivities<GeneralActivitiesForWorkflow>({
  startToCloseTimeout: "2 minute",
  retry: {
    initialInterval: "1s",
    backoffCoefficient: 2,
  },
});

export async function processRequestSubmission(req: {
  fileAttachments: string[];
  requestId: string;
}): Promise<{ requestId: string }> {
  const request = await generalActs.readResource<Request>(req.requestId);
  if (!request) {
    throw new Error("Request not found");
  }

  const noteSchemaId = request.noteSchemaId;
  const patientId = request?.patient?.id;
  const agencyId = request?.agencyId;
  const noteId = request?.noteCreated?.id;
  const previousTranscription =
    request.transcription?.map((t) => t.text).join("\n---\n") || "";

  // Transcribe attachments
  const results = await transcribeAttachments(req.fileAttachments);

  request.transcription = [...(request?.transcription || []), ...results];

  const combinedTranscription = results
    .filter((result) => !result.urlPath.endsWith(".pdf"))
    .map((result, index) => "chunk " + (index + 1) + "\n---\n" + result.text)
    .join("\n---\n");

  // Process note
  const notePromise = processNote(
    noteSchemaId,
    previousTranscription +
      "\n---additional transcription---\n" +
      combinedTranscription,
    patientId,
    agencyId,
    noteId,
  );

  const patientChartSchema = await getPatientChartSchema();
  const patient = await generalActs.readResource<Patient>(request.patient.id);
  if (!patient) {
    throw new Error("Patient not found");
  }

  const patientChart = await minifyPatient(patient);

  // Serialize chart for LLM and add IDs to all array objects
  const [serializedChart, chartWithIds] =
    await serializeChartForLLM(patient);

  // Save the patient with IDs for future use
  await generalActs.updateResource(chartWithIds as any);

  const { requestId } = await processChangeIteration({
    transcription: combinedTranscription,
    patientChart: patientChart,
    serializedChart: serializedChart,
    patientChartSchema: patientChartSchema,
    requestId: req.requestId,
    feedback: null,
  });

  const note = await notePromise;
  const isIDGFlag = await patientActs.checkIsIDG(noteSchemaId);
  const qa = isIDGFlag ? "" : await patientActs.performQaCheck(note as any);

  const updatedRequest = await generalActs.readResource<Request>(requestId);
  if (!updatedRequest) {
    throw new Error("Updated request not found");
  }

  updatedRequest.noteCreated = {
    id: note.id,
    resourceType: "Note",
  };
  if (!isIDGFlag) {
    updatedRequest.qa = qa;
  }
  updatedRequest.status = "pending_review";
  updatedRequest.fileAttachments = [
    ...updatedRequest.fileAttachments,
    ...req.fileAttachments,
  ];
  updatedRequest.transcription = [
    ...(updatedRequest?.transcription || []),
    ...results,
  ];

  await generalActs.updateResource(updatedRequest as any);

  return { requestId: updatedRequest.id };
}

// Helper function to remove assistant responses from messages
function filterOutAssistantResponses(messages: any[]): any[] {
  if (!messages) return messages;
  return messages.filter(msg => msg.role !== 'assistant');
}

export async function processChangeIteration(req: {
  transcription?: string;
  patientChart: PatientChart;
  serializedChart: any;
  patientChartSchema: any;
  requestId: string;
  feedback?: string;
}): Promise<{
  requestId: string;
}> {
  const MAX_ITERATIONS = 5;
  let iteration = 0;
  let currentFeedback = req.feedback;
  const currentTranscription = req.transcription;
  let accumulatedChangesetMessages: any = undefined;
  let accumulatedJudgeMessages: any = undefined;

  const request = await generalActs.readResource<Request>(req.requestId);
  if (!request) {
    throw new Error("Request not found");
  }

  // Initialize with existing messages if they exist (for additional audio)
  if (request.changesetMessages) {
    accumulatedChangesetMessages = request.changesetMessages;
  }
  if (request.judgementMessages) {
    accumulatedJudgeMessages = request.judgementMessages;
  }

  while (iteration < MAX_ITERATIONS) {
    // Only pass accumulated messages on first iteration to preserve base + additional audio
    // On subsequent iterations, only pass feedback without previous messages
    const { changeset, messages: changesetMessages } =
      await generateChangeset(
        currentTranscription,
        req.patientChart,
        req.patientChartSchema,
        currentFeedback,
        iteration === 0 ? accumulatedChangesetMessages : undefined,
        req.serializedChart,
      );

    const { judgement, messages: judgementMessages } =
      await judgeChangeset(
        changeset,
        req.patientChart,
        currentTranscription,
        req.patientChartSchema,
        iteration === 0 ? accumulatedJudgeMessages : undefined,
      );

    // Store messages only from first iteration (base + additional audio, no feedback)
    if (iteration === 0) {
      // Filter out assistant responses before storing
      accumulatedChangesetMessages = filterOutAssistantResponses(changesetMessages);
      accumulatedJudgeMessages = filterOutAssistantResponses(judgementMessages);
    }

    // Update request with current iteration results
    request.changes = changeset.changes;
    request.changesetMessages = accumulatedChangesetMessages;
    request.judgementMessages = accumulatedJudgeMessages;
    await generalActs.updateResource(request as any);

    // If judge found no issues, we're done
    if (judgement.changes.length === 0) {
      // Add oldValue to final changes
      const changesWithOldValue = await addOldValueToChanges(
        changeset.changes,
        req.patientChart,
      );

      request.changes = changesWithOldValue;
      await generalActs.updateResource(request);

      return { requestId: request.id };
    }

    // Prepare for next iteration
    judgement.timestamp = dayjs().format("YYYY-MM-DD HH:mm:ss");
    currentFeedback = JSON.stringify(judgement);
    iteration++;
  }

  // If we've reached max iterations, use the last changeset
  const changesWithOldValue = await addOldValueToChanges(
    request.changes,
    req.patientChart,
  );

  request.changes = changesWithOldValue;
  await generalActs.updateResource(request);

  console.warn(
    `processChangeIteration reached MAX_ITERATIONS (${MAX_ITERATIONS}) for request ${req.requestId}`,
  );
  return { requestId: request.id };
}

async function transcribeAttachments(
  attachments: string[],
): Promise<TranscribeResult[]> {
  const audioAttachments = attachments.filter(
    (attachment) => !attachment.endsWith(".pdf"),
  );

  const resultPromises: Promise<TranscribeResult>[] = [];

  for (const audioAttachmentUrl of audioAttachments) {
    resultPromises.push(transcribeAudio(audioAttachmentUrl));
  }

  const otherAttachments = attachments.filter(
    (attachment) => !audioAttachments.includes(attachment),
  );
  for (const otherAttachmentUrl of otherAttachments) {
    resultPromises.push(transcribePDF(otherAttachmentUrl));
  }

  const results = await Promise.all(resultPromises);
  return results;
}
