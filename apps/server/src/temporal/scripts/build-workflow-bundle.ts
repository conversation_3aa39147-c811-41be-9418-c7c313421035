
import { bundleWorkflowCode } from '@temporalio/worker';
import { existsSync } from 'fs';
import { unlink, writeFile } from 'fs/promises';
import path from 'path';

async function bundle(): Promise<void> {
  // delete the workflow-bundle.js file if it exists
  const codePath = path.join(__dirname, '../workflow-bundle.js');
  if (existsSync(codePath)) {
    await unlink(codePath);
  }

  const { code } = await bundleWorkflowCode({
    workflowsPath: require.resolve('../workflows'),
  });

  await writeFile(codePath, code);
  console.log(`Bundle written to ${codePath}`);
}

bundle().catch((err) => {
  console.error(err);
  process.exit(1);
});