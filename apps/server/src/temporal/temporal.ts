import {
  Client,
  Connection,
  Workflow,
  WorkflowClient,
  WorkflowExecutionAlreadyStartedError,
  WorkflowHandleWithFirstExecutionRunId,
  WorkflowResultType,
  WorkflowStartOptions,
} from "@temporalio/client";
import fs from "fs/promises";
import { BaseResource as Resource } from "@hospice-os/apptypes";
import path from "path";

const stage = process.env.APP_STAGE || "dev";
let client: Client | undefined;

export const temporalQueueV1_0 = "main-v1.0";
export const temporalQueueCurrent = temporalQueueV1_0;

export async function initTemporalClient(): Promise<void> {
  let connectionOptions = {};
  if (stage === "prod") {
    const crt = await fs.readFile(
      path.join(__dirname, "./certificates/ca-prod.pem"),
    );
    const key = await fs.readFile(
      path.join(__dirname, "./certificates/ca-prod.key"),
    );
    connectionOptions = {
      address: "production.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt,
          key,
        },
      },
    };

    const connection = await Connection.connect(connectionOptions);

    client = new Client({
      connection,
      namespace: "production.w1anq",
    });
  } else if (stage === "staging") {
    const crt = await fs.readFile(
      path.join(__dirname, "./certificates/ca-staging.pem"),
    );
    const key = await fs.readFile(
      path.join(__dirname, "./certificates/ca-staging.key"),
    );
    connectionOptions = {
      address: "staging.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt,
          key,
        },
      },
    };

    const connection = await Connection.connect(connectionOptions);
    client = new Client({
      connection,
      namespace: "staging.w1anq",
    });
  } else {
    const crt = await fs.readFile(
      path.join(__dirname, "./certificates/ca-dev.pem"),
    );
    const key = await fs.readFile(
      path.join(__dirname, "./certificates/ca-dev.key"),
    );
    connectionOptions = {
      address: "dev.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt,
          key,
        },
      },
    };

    const connection = await Connection.connect(connectionOptions);
    client = new Client({
      connection,
      namespace: "dev.w1anq",
    });
  }
}

export function getTemporalClient(): Client {
  if (!client) {
    throw new Error("Client not initialized");
  }
  return client;
}

export function getWorkflowClient(): WorkflowClient {
  return getTemporalClient().workflow;
}

// replacement for UseExisting Conflict Policy that doesnt exist with TS sdk yet
export async function startWorkflow<T extends Workflow>(
  workflowTypeOrFunc: string | T,
  options: WorkflowStartOptions<T>,
): Promise<WorkflowHandleWithFirstExecutionRunId<T>> {
  try {
    console.log("Starting temporal workflow", workflowTypeOrFunc, options);
    return await getTemporalClient().workflow.start(
      workflowTypeOrFunc,
      options,
    );
  } catch (e) {
    if (e instanceof WorkflowExecutionAlreadyStartedError) {
      console.log(
        "Workflow currently running for id",
        e.workflowId,
        "and type",
        e.workflowType,
        "returning existing handle",
      );
      const handle = getTemporalClient().workflow.getHandle<T>(
        e.workflowId,
        e.workflowType,
      );
      const firstExecutionRunId = (await handle.describe()).runId;

      return {
        ...handle,
        firstExecutionRunId,
      };
    } else {
      console.error("Error starting workflow", JSON.stringify(e));
      throw e;
    }
  }
}

export async function executeWorkflow<T extends Workflow>(
  workflowTypeOrFunc: string | T,
  options: WorkflowStartOptions<T>,
): Promise<WorkflowResultType<T>> {
  try {
    return await getTemporalClient().workflow.execute(
      workflowTypeOrFunc,
      options,
    );
  } catch (e) {
    if (e instanceof WorkflowExecutionAlreadyStartedError) {
      console.log(
        "Workflow currently running for id",
        e.workflowId,
        "and type",
        e.workflowType,
        "returning existing handle",
      );
      return getTemporalClient()
        .workflow.getHandle<T>(e.workflowId, e.workflowType)
        .result();
    } else {
      console.error("Error executing workflow", JSON.stringify(e));
      throw e;
    }
  }
}

export async function closeTemporalClient(): Promise<void> {
  if (client) {
    try {
      await client.connection.close();
      client = undefined; // Clear the singleton reference
      console.log("Temporal client connection closed");
    } catch (error) {
      console.error("Error closing Temporal client connection:", error);
      throw error;
    }
  }
}

export type ResourceWorkflowInput<T extends Resource> = {
  resource: T & { id: string };
};
