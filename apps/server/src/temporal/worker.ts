/* eslint-disable @typescript-eslint/no-explicit-any */
import { NativeConnection, Worker } from "@temporalio/worker";
import fs from "fs/promises";
import path from "path";
import { createActivities } from "./activities";
import { temporalQueueCurrent } from "./temporal";
import { initializeServer as initializeServerInit } from "../init";
import { getCurrentStage } from "../config";
import { resourceService } from "../services/resource-service";

const initializeServer = async () => {
  await initializeServerInit({ migrate: false, configureStytch: false });
};

async function run(): Promise<void> {
  await initializeServer();

  const workflowOption = (): any =>
    getCurrentStage() === "prod" || getCurrentStage() === "staging"
      ? {
          workflowBundle: {
            codePath: require.resolve("./workflow-bundle.js"),
          },
          namespace:
            getCurrentStage() === "prod" ? "production.w1anq" : "staging.w1anq",
        }
      : {
          workflowsPath: require.resolve("./workflows"),
          namespace: "dev.w1anq",
        };

  let config = {};
  if (getCurrentStage() === "prod")
    config = {
      address: "production.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-prod.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-prod.key"),
          ),
        },
      },
    };
  else if (getCurrentStage() === "staging") {
    config = {
      address: "staging.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-staging.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-staging.key"),
          ),
        },
      },
    };
  } else {
    config = {
      address: "dev.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-dev.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-dev.key"),
          ),
        },
      },
    };
  }
  const connection = await NativeConnection.connect(config);
  const wf = workflowOption();
  if (process.env.BUILD_ID) {
    wf.bundleId = process.env.BUILD_ID;
  }
  const worker = await Worker.create({
    ...wf,
    connection: connection,
    taskQueue: temporalQueueCurrent,
    activities: createActivities(resourceService),
    // Concurrency limits for small-scale deployment
    maxConcurrentActivityTaskExecutions: 10,
    maxConcurrentWorkflowTaskExecutions: 5,
    maxConcurrentLocalActivityExecutions: 5,
    // Sticky cache size - reduces workflow replay overhead
    maxCachedWorkflows: 50,
  });

  console.log(`Temporal worker starting with concurrency limits:
    - Max concurrent activities: 10
    - Max concurrent workflows: 5
    - Max cached workflows: 50
    - Task queue: ${temporalQueueCurrent}`);

  // Graceful shutdown handling
  let isShuttingDown = false;
  
  const shutdown = async (signal: string) => {
    if (isShuttingDown) {
      console.log("Shutdown already in progress...");
      return;
    }
    
    isShuttingDown = true;
    console.log(`Received ${signal}, initiating graceful shutdown...`);
    
    try {
      // Stop accepting new work and wait for current work to complete
      await worker.shutdown();
      console.log("Worker shutdown complete");
      
      // Close the connection
      await connection.close();
      console.log("Connection closed");
      
      process.exit(0);
    } catch (err) {
      console.error("Error during shutdown:", err);
      process.exit(1);
    }
  };

  // Register shutdown handlers
  process.on("SIGTERM", () => shutdown("SIGTERM"));
  process.on("SIGINT", () => shutdown("SIGINT"));
  
  // Handle uncaught errors
  process.on("unhandledRejection", (reason, promise) => {
    console.error("Unhandled Rejection at:", promise, "reason:", reason);
    shutdown("unhandledRejection");
  });

  await worker.run();
}

run().catch((err) => {
  console.error("Worker failed to start:", err);
  process.exit(1);
});
