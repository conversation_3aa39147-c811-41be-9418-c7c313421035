import axios from 'axios';

const API_URL = 'http://localhost:3000/api';

/**
 * Test the API endpoints
 */
async function testApi() {
  try {
    // Set API key for authentication
    const headers = {
      'Content-Type': 'application/json',
      'x-api-key': 'test-api-key'
    };

    console.log('Testing API endpoints...');

    // Test health endpoint
    console.log('\nTesting health endpoint...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('Health response:', healthResponse.data);

    // Test creating an agency
    console.log('\nCreating a test agency...');
    const createAgencyResponse = await axios.post(
      `${API_URL}/agencies`,
      { name: 'Test Agency' },
      { headers }
    );
    console.log('Create agency response:', createAgencyResponse.data);
    const agencyId = createAgencyResponse.data.id;

    // Test getting the agency
    console.log('\nGetting the test agency...');
    const getAgencyResponse = await axios.get(
      `${API_URL}/agencies/${agencyId}`,
      { headers }
    );
    console.log('Get agency response:', getAgencyResponse.data);

    // Test creating a schema
    console.log('\nCreating a test schema...');
    const schemaData = {
      name: 'TestSchema',
      version: '1.0.0',
      agencyId,
      schema: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          resourceType: { type: 'string' },
          name: { type: 'string' },
          description: { type: 'string' }
        },
        required: ['id', 'resourceType', 'name']
      }
    };
    const createSchemaResponse = await axios.post(
      `${API_URL}/schemas`,
      schemaData,
      { headers }
    );
    console.log('Create schema response:', createSchemaResponse.data);
    const schemaId = createSchemaResponse.data.id;

    // Test creating a resource
    console.log('\nCreating a test resource...');
    const resourceData = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      resourceType: 'TestResource',
      name: 'Test Resource',
      description: 'This is a test resource',
      schemaId,
      agencyId
    };
    const createResourceResponse = await axios.post(
      `${API_URL}/resources`,
      resourceData,
      { headers }
    );
    console.log('Create resource response:', createResourceResponse.data);
    const resourceId = createResourceResponse.data.id;

    // Test getting the resource
    console.log('\nGetting the test resource...');
    const getResourceResponse = await axios.get(
      `${API_URL}/resources/${resourceId}`,
      { headers }
    );
    console.log('Get resource response:', getResourceResponse.data);

    // Test creating a user
    console.log('\nCreating a test user...');
    const userData = {
      id: '123e4567-e89b-12d3-a456-426614174001',
      resourceType: 'User',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'Admin',
      schemaId,
      agencyId
    };
    const createUserResponse = await axios.post(
      `${API_URL}/users`,
      userData,
      { headers }
    );
    console.log('Create user response:', createUserResponse.data);
    const userId = createUserResponse.data.id;

    // Test getting the user
    console.log('\nGetting the test user...');
    const getUserResponse = await axios.get(
      `${API_URL}/users/${userId}`,
      { headers }
    );
    console.log('Get user response:', getUserResponse.data);

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error testing API:', error.response?.data || error.message);
  }
}

// Run the tests
testApi();
