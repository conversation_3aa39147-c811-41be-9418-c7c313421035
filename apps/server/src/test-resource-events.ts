// import {
//   resourceEventDispatcher,
//   LoggingListener,
//   NotificationListener,
//   ActionCompletedListener,
// } from "./services/resource-events";

// /**
//  * This file demonstrates how to use the resource event system.
//  * Run it with: ts-node src/test-resource-events.ts
//  */

// // Mock implementation for testing purposes
// // This avoids TypeScript errors when running the actual code would require a database
// async function mockResourceEvents() {
//   console.log("Testing resource event system with mock implementation...");

//   // Register a custom listener for patient resources
//   const customListener = {
//     onResourceEvent: (event) => {
//       console.log("Custom listener received event:", {
//         type: event.eventType,
//         resourceType: event.resourceType,
//         resourceId: event.resource.id,
//         timestamp: event.timestamp,
//       });
//     },
//   };

//   // Register listeners
//   resourceEventDispatcher.registerListener(new LoggingListener());
//   resourceEventDispatcher.registerListener(new NotificationListener());
//   resourceEventDispatcher.registerListener(customListener, "patient");

//   // Register JSON path listeners
//   const actionCompletedListener = new ActionCompletedListener();
//   resourceEventDispatcher.registerJsonPathListener(
//     actionCompletedListener,
//     "request",
//     "agencyActions[*].completed",
//   );

//   console.log("Listeners registered successfully");

//   // Since we're just demonstrating the event system, we'll create mock events
//   // and dispatch them directly rather than going through the resource service

//   // Create a mock patient resource
//   const mockPatient = {
//     updatedAt: new Date().toISOString(),
//     id: "12345678-1234-1234-1234-123456789012",
//     resourceType: "patient",
//     schemaId: "00000000-0000-0000-0000-000000000001",
//     agencyId: "00000000-0000-0000-0000-000000000001",
//     firstName: "John",
//     lastName: "Doe",
//     dateOfBirth: "1980-01-01",
//     gender: "male",
//     contactInfo: {
//       email: "<EMAIL>",
//       phone: "************",
//     },
//   };

//   // Create a mock request resource with agency actions
//   const mockRequest = {
//     updatedAt: new Date().toISOString(),
//     id: "98765432-5432-5432-5432-123456789012",
//     resourceType: "request",
//     schemaId: "00000000-0000-0000-0000-000000000003",
//     agencyId: "00000000-0000-0000-0000-000000000001",
//     patientId: "12345678-1234-1234-1234-123456789012",
//     status: "pending",
//     createdAt: "2023-01-01T12:00:00Z",
//     agencyActions: [
//       {
//         id: "action1",
//         name: "Review patient information",
//         assignedTo: "user1",
//         completed: false,
//         dueDate: "2023-01-05",
//       },
//       {
//         id: "action2",
//         name: "Contact primary physician",
//         assignedTo: "user2",
//         completed: false,
//         dueDate: "2023-01-07",
//       },
//       {
//         id: "action3",
//         name: "Schedule initial assessment",
//         assignedTo: "user1",
//         completed: false,
//         dueDate: "2023-01-10",
//       },
//     ],
//   };

//   // Import the event types directly here to avoid unused import errors
//   const { ResourceEventType } = await import(
//     "./services/resource-events/types.js"
//   );

//   // Dispatch a CREATED event for the patient
//   console.log("Dispatching CREATED event for patient...");
//   await resourceEventDispatcher.dispatchEvent({
//     eventType: ResourceEventType.CREATED,
//     resourceType: mockPatient.resourceType,
//     resource: mockPatient,
//     timestamp: new Date(),
//   });

//   // Dispatch a CREATED event for the request
//   console.log("Dispatching CREATED event for request...");
//   await resourceEventDispatcher.dispatchEvent({
//     eventType: ResourceEventType.CREATED,
//     resourceType: mockRequest.resourceType,
//     resource: mockRequest,
//     timestamp: new Date(),
//   });

//   // Update the request to mark an action as completed
//   const updatedRequest = {
//     ...mockRequest,
//     agencyActions: [
//       {
//         ...mockRequest.agencyActions[0],
//         completed: true, // Mark the first action as completed
//       },
//       ...mockRequest.agencyActions.slice(1),
//     ],
//   };

//   console.log("\nUpdating request to mark first action as completed...");
//   await resourceEventDispatcher.dispatchEvent({
//     eventType: ResourceEventType.UPDATED,
//     resourceType: updatedRequest.resourceType,
//     resource: updatedRequest,
//     previousState: mockRequest,
//     timestamp: new Date(),
//   });

//   // Update the request again to mark another action as completed
//   const furtherUpdatedRequest = {
//     updatedAt: new Date().toISOString(),
//     ...updatedRequest,
//     agencyActions: [
//       updatedRequest.agencyActions[0],
//       {
//         ...updatedRequest.agencyActions[1],
//         completed: true, // Mark the second action as completed
//       },
//       updatedRequest.agencyActions[2],
//     ],
//   };

//   console.log("\nUpdating request to mark second action as completed...");
//   await resourceEventDispatcher.dispatchEvent({
//     eventType: ResourceEventType.UPDATED,
//     resourceType: furtherUpdatedRequest.resourceType,
//     resource: furtherUpdatedRequest,
//     previousState: updatedRequest,
//     timestamp: new Date(),
//   });

//   // Update the request again to mark all actions as completed
//   const finalRequest = {
//     ...furtherUpdatedRequest,
//     agencyActions: [
//       furtherUpdatedRequest.agencyActions[0],
//       furtherUpdatedRequest.agencyActions[1],
//       {
//         ...furtherUpdatedRequest.agencyActions[2],
//         completed: true, // Mark the third action as completed
//       },
//     ],
//     status: "completed", // Also update the request status
//   };

//   console.log("\nUpdating request to mark all actions as completed...");
//   await resourceEventDispatcher.dispatchEvent({
//     eventType: ResourceEventType.UPDATED,
//     resourceType: finalRequest.resourceType,
//     resource: finalRequest,
//     previousState: furtherUpdatedRequest,
//     timestamp: new Date(),
//   });

//   console.log("\nResource event test completed successfully");
// }

// // Run the test
// mockResourceEvents()
//   .then(() => {
//     console.log("Test completed");
//     process.exit(0);
//   })
//   .catch((error) => {
//     console.error("Test failed:", error);
//     process.exit(1);
//   });
