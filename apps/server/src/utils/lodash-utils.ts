import _ from "lodash";

export const omitDeepNullAndEmpty = (obj: any) => {
  if (_.isArray(obj)) {
    return obj
      .map(omitDeepNullAndEmpty)
      .filter(
        (v) =>
          v !== null &&
          v !== undefined &&
          !(typeof v === "string" && v.trim() === ""),
      );
  }
  if (_.isObject(obj) && !_.isDate(obj)) {
    const result = _.omitBy(
      _.mapValues(obj, omitDeepNullAndEmpty),
      (v) =>
        v === null || (typeof v === "string" && (v as string).trim() === ""),
    );
    // Remove empty objects
    return _.omitBy(
      result,
      (v) => _.isObject(v) && !_.isArray(v) && _.isEmpty(v),
    );
  }
  return obj;
};
