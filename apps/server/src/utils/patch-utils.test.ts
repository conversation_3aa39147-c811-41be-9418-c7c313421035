import { describe, it, expect } from 'vitest';
import { applyPatch, Operation } from 'fast-json-patch';
import { ensurePatchPaths } from './patch-utils';

interface Patient {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  medications?: { name: string }[];
}

describe('ensurePatchPaths', () => {
  it('creates intermediary paths for patch operations', () => {
    const patient: Patient = {
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: '1980-01-01'
    };

    const operations: Operation[] = [
      { op: 'add', path: '/medications/0/name', value: 'Aspirin' }
    ];

    ensurePatchPaths(patient, operations);
    const result = applyPatch(patient, operations).newDocument as Patient;

    expect(result.medications).toBeDefined();
    expect(result.medications![0].name).toBe('Aspirin');
  });
});