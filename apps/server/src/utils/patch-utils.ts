/* eslint-disable @typescript-eslint/no-explicit-any */

import { Operation } from "fast-json-patch";

export type Createable<T> = Omit<T, "id" | "createdAt" | "updatedAt">;

/**
 * Ensures that all intermediary objects or arrays exist for the given patch operations.
 * This mutates the provided document so that applying the patch will not fail with
 * `OPERATION_PATH_UNRESOLVABLE` errors when intermediate keys are missing.
 *
 * @param document The object that will be patched
 * @param operations The JSON patch operations to be applied
 */
export function ensurePatchPaths(document: any, operations: Operation[]): void {
  for (const op of operations) {
    const segments = op.path
      .split("/")
      .slice(1)
      .map((s) => s.replace(/~1/g, "/").replace(/~0/g, "~"));

    let current: any = document;
    let parent: any = null;
    let key: string | number | null = null;

    for (let i = 0; i < segments.length - 1; i++) {
      const segment = segments[i];
      const nextSegment = segments[i + 1];
      const nextIsIndex = nextSegment === "-" || /^\d+$/.test(nextSegment);
      const isIndex = segment === "-" || /^\d+$/.test(segment);

      if (isIndex) {
        const index =
          segment === "-"
            ? Array.isArray(current)
              ? current.length
              : 0
            : parseInt(segment, 10);
        if (!Array.isArray(current)) {
          if (parent && key !== null) {
            parent[key] = [];
            current = parent[key];
          } else {
            // root should never be an array index path, but guard just in case
            current = [];
          }
        }
        if (current[index] === undefined) {
          current[index] = nextIsIndex ? [] : {};
        }
        parent = current;
        key = index;
        current = current[index];
      } else {
        if (
          current[segment] === undefined ||
          current[segment] === null ||
          typeof current[segment] !== "object"
        ) {
          current[segment] = nextIsIndex ? [] : {};
        }
        parent = current;
        key = segment;
        current = current[segment];
      }
    }
  }
}
