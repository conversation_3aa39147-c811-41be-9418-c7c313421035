# Markdown Rendering System Documentation

This document explains how the markdown rendering system works in the Hospice OS application, focusing on the patient data display and the collapsible markdown components.

## Overview

The markdown rendering system converts structured patient data into a hierarchical, collapsible markdown view. It handles various data types (simple values, arrays, objects) and provides customization options through UI schemas.

## Components

### 1. PatientMarkdownView

**File**: `apps/web/src/components/PatientMarkdownView.tsx`

This component is responsible for:
- Fetching the patient schema and UI schema
- Converting patient data to markdown using the `dataToMarkdown` utility
- Rendering the markdown content using the `CollapsibleMarkdown` component

### 2. CollapsibleMarkdown

**File**: `apps/web/src/components/CollapsibleMarkdown.tsx`

This component:
- Parses markdown content into a hierarchical structure
- Renders sections with collapsible headers
- Handles nested sections and maintains proper indentation
- Respects metadata comments for controlling collapsibility and line thresholds

### 3. schemaToMarkdown Utility

**File**: `apps/web/src/utils/schemaToMarkdown.ts`

This utility:
- Converts JSON data to markdown format
- Handles different data types (objects, arrays, primitives)
- Respects UI schema options for customizing the display
- Uses schema information to determine how to render arrays

### 4. uiSchemaUtils Utility

**File**: `apps/web/src/utils/uiSchemaUtils.ts`

This utility provides helper functions for:
- Applying UI schema ordering to properties
- Determining if properties should be displayed based on conditions
- Getting widget types and options from UI schemas

## Data Flow

1. `PatientMarkdownView` receives patient data and a schema ID
2. It fetches the schema and UI schema for the patient
3. It calls `dataToMarkdown` to convert the patient data to markdown
4. The markdown is passed to `CollapsibleMarkdown` for rendering
5. `CollapsibleMarkdown` parses the markdown and renders it with collapsible sections

## Property Ordering

Properties are ordered in three groups:
1. Simple properties (strings, numbers, booleans)
2. Arrays with primitive items (e.g., allergies, secondaryDiagnoses)
3. Complex properties (objects and arrays with object-based items)

This ordering can be customized through the UI schema's `ui:order` property.

## Collapsible Sections

Sections can be made collapsible or non-collapsible using metadata comments in the markdown:

```markdown
<!-- collapsible:true -->
## Section Title

Content here...
```

## Line Thresholds

Sections can be automatically collapsed if they exceed a certain number of lines:

```markdown
<!-- lineThreshold:10 -->
## Section Title

Content here...
```

## Array Handling

Arrays are handled differently based on their content:

1. **Arrays with object-based items**: Rendered as collapsible sections with tables
2. **Arrays with primitive items**: Rendered with bold text for the name and a bullet list

The system uses the JSON schema to determine the type of array items, even for empty arrays.

## Customization

### UI Schema Options

The UI schema can include the following options for each property:

- `ui:widget`: Specifies a custom widget for rendering the property
- `ui:options`: Additional options for customizing the display
  - `headerLevel`: Controls the header size (h1-h6)
  - `collapsible`: Controls whether a section is collapsible
  - `lineThreshold`: Sets a line threshold for auto-collapsing

### Example UI Schema

```json
{
  "ui:order": ["firstName", "lastName", "allergies", "medications"],
  "medications": {
    "ui:options": {
      "headerLevel": 2,
      "collapsible": true,
      "lineThreshold": 10
    }
  },
  "allergies": {
    "ui:options": {
      "collapsible": false
    }
  }
}
```

## Implementation Details

### Parsing Markdown Sections

The `parseMarkdownSections` function in `CollapsibleMarkdown.tsx` parses the markdown content into a hierarchical structure of sections. Each section has:

- A heading
- Content
- Child sections
- Metadata (collapsible, lineThreshold)

### Rendering Sections

The `renderSections` function renders the sections recursively, respecting the hierarchy and metadata.

### Determining Array Types

The `dataToMarkdown` function uses the following approach to determine if an array contains object-based items:

1. Check the JSON schema for the array's definition
2. If not found in the schema, check the actual array content
3. Render the array accordingly (as a table for object-based items, or as a list for primitive items)

## Best Practices

1. **Use UI Schema for Customization**: Instead of hardcoding display logic, use UI schemas to customize the display.

2. **Respect Property Types**: Keep simple properties, simple arrays, and complex properties in their respective groups.

3. **Provide Clear Headings**: Use descriptive headings for sections to improve readability.

4. **Use Collapsible Sections Wisely**: Make sections collapsible only when it improves the user experience.

5. **Set Appropriate Line Thresholds**: Use line thresholds to automatically collapse long sections.

## Troubleshooting

### Property Ordering Issues

If properties are not appearing in the expected order:

1. Check the UI schema's `ui:order` property
2. Ensure that the property is included in the UI schema
3. Verify that the property exists in the patient data

### Collapsible Section Issues

If sections are not collapsing as expected:

1. Check the metadata comments in the markdown
2. Verify that the `collapsible` option is set correctly
3. Check the `lineThreshold` option if sections should auto-collapse

### Array Rendering Issues

If arrays are not rendering correctly:

1. Check the JSON schema to ensure it correctly defines the array items
2. Verify that the array data matches the schema
3. Check the UI schema for any custom rendering options
