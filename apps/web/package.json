{"name": "@hospice-os/web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --skipLibCheck && vite build", "test": "vitest run", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hospice-os/apptypes": "0.0.1", "@hospice-os/ui-components": "0.0.1", "@mantine/core": "^7.17.3", "@mantine/hooks": "^7.17.3", "@mantine/notifications": "^7.17.3", "@reduxjs/toolkit": "^2.6.1", "@stytch/react": "^19.4.3", "@stytch/vanilla-js": "^5.22.2", "@tabler/icons-react": "^3.31.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.1", "use-local-storage": "^3.0.0", "uuidv7": "^1.0.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@storybook/addon-essentials": "^9.0.0-alpha.12", "@storybook/react-vite": "^9.0.8", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "storybook": "^9.0.8", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}