import { 
  AppShell, 
  Title,
  Image,
  Anchor,
  Button,
  Menu
} from '@mantine/core';
import { useStytchMemberSession, useStytchB2BClient } from '@stytch/react/b2b';
import { Outlet, Link } from 'react-router-dom';
import './App.css';
import '@mantine/notifications/styles.css';
import tallioIcon from './assets/tallio-icon.svg';
import { UserProvider } from './features/auth/components';

function App() {
  const { session } = useStytchMemberSession();
  const stytchClient = useStytchB2BClient();

  const handleLogout = () => {
    stytchClient.session.revoke();
  };

  return (
    <UserProvider>
      <AppShell
        header={{ height: 60 }}
        padding="md"
      >
      {session && (<AppShell.Header style={{ 
        borderBottom: '1px solid #e9ecef',
        boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%',
          padding: '0 24px'
        }}>
          {/* Logo and Title */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px'
          }}>
            <Image src={tallioIcon} height={32} width={32} />
            <Title order={3} style={{ fontWeight: 600, margin: 0 }}>HospiceOS</Title>
          </div>
          
          {/* Navigation and Account */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '32px'
          }}>
            {session ? (
              <>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '24px'
                }}>
                  <Anchor component={Link} to="/patients" fw={500} style={{ 
                    textDecoration: 'none',
                    color: '#495057',
                    fontSize: '15px'
                  }}>Patients</Anchor>
                  {/* <Anchor component={Link} to="/settings" fw={500} style={{ 
                    textDecoration: 'none',
                    color: '#495057',
                    fontSize: '15px'
                  }}>Settings</Anchor> */}
                </div>
                
                <Menu position="bottom-end" shadow="md">
                  <Menu.Target>
                    <Button variant="subtle" radius="md" style={{ fontWeight: 500 }}>Account</Button>
                  </Menu.Target>
                  <Menu.Dropdown>
                    {/* <Menu.Item component={Link} to="/settings">Settings</Menu.Item> */}
                    <Menu.Divider />
                    <Menu.Item onClick={handleLogout} color="red">Logout</Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              </>
            ) : (
              <Button 
                variant="filled" 
                component={Link} 
                to="/login"
                radius="md"
                style={{ fontWeight: 500 }}
              >
                Login
              </Button>
            )}
          </div>
        </div>
      </AppShell.Header>)}

      <AppShell.Main>
        <Outlet />
      </AppShell.Main>
      </AppShell>
    </UserProvider>
  );
}

export default App;
