import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import App from './App';
import { HomePage } from './pages/Home.page';
import { SettingsPage } from './pages/Settings.page';
import { PatientChartPage } from './pages/PatientChart.page';
import { PatientsPage } from './pages/Patients.page';
import { IDGMeetingPage } from './pages/IDGMeeting.page';
import { AuthPage } from './pages/Auth.page';
import { ProtectedRoute } from './features/auth';

// Define routes
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: 'chatTest',
        element: <ProtectedRoute><HomePage /></ProtectedRoute>,
      },
      {
        path: 'patients',
        element: <ProtectedRoute><PatientsPage /></ProtectedRoute>,
      },
      {
        index: true,
        element: <ProtectedRoute><PatientChartPage /></ProtectedRoute>,
      },
      {
        path: 'patient/:id?',
        element: <ProtectedRoute><PatientChartPage /></ProtectedRoute>,
      },
      {
        path: 'idg-meeting/:id?',
        element: <ProtectedRoute><IDGMeetingPage /></ProtectedRoute>,
      },
      {
        path: 'settings',
        element: <ProtectedRoute><SettingsPage /></ProtectedRoute>,
      },
      {
        path: 'login',
        element: <AuthPage />,
      },
      {
        path: 'authenticate',
        element: <AuthPage />,
      },
    ],
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
