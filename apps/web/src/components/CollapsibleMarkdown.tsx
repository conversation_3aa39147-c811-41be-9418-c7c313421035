import React, { useState, useCallback, createElement } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Box, Collapse, Group, ActionIcon } from '@mantine/core';
import { IconChevronRight, IconChevronDown } from '@tabler/icons-react';

interface CollapsibleSectionProps {
  heading: string;
  level: number;
  children: React.ReactNode;
  collapsible: boolean;
  lineThreshold?: number;
}

function CollapsibleSection({ heading, level, children, collapsible, lineThreshold }: CollapsibleSectionProps) {
  // Determine initial open state based on:
  // - Top-level headers start expanded by default
  // - If lineThreshold is provided, check if content exceeds threshold
  const contentText = React.Children.toArray(children)
    .filter(child => typeof child === 'string')
    .join('');

  const contentLines = contentText.split('\n').length;
  const shouldCollapseByDefault = lineThreshold ? contentLines > lineThreshold : false;

  const [isOpen, setIsOpen] = useState(level === 1 && !shouldCollapseByDefault);

  const toggleOpen = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // If not collapsible, render as a regular section
  if (!collapsible) {
    return (
      <div className={`section level-${level}`}>
        {createElement(`h${level}`, { style: { margin: 0 } }, heading)}
        <Box pl={10} pt={10} className="section-content">
          {children}
        </Box>
      </div>
    );
  }

  return (
    <div className={`collapsible-section level-${level}`}>
      <Group onClick={toggleOpen} className="collapsible-header" style={{ cursor: 'pointer' }}>
        <ActionIcon size="sm" variant="transparent">
          {isOpen ? <IconChevronDown size={16} /> : <IconChevronRight size={16} />}
        </ActionIcon>
        {createElement(`h${level}`, { style: { margin: 0 } }, heading)}
      </Group>
      <Collapse in={isOpen}>
        <Box pl={20} className="collapsible-content">
          {children}
        </Box>
      </Collapse>
    </div>
  );
}

interface CollapsibleMarkdownProps {
  markdown: string;
}

export function CollapsibleMarkdown({ markdown }: CollapsibleMarkdownProps) {
  // Parse the markdown to extract sections with their headings and content
  const sections = parseMarkdownSections(markdown);

  // Render the sections recursively
  return (
    <div className="markdown-content">
      {renderSections(sections)}
    </div>
  );
}

interface MarkdownSection {
  heading: string;
  level: number;
  content: string;
  children: MarkdownSection[];
  collapsible: boolean;
  lineThreshold?: number;
}

function parseMarkdownSections(markdown: string): MarkdownSection[] {
  const lines = markdown.split('\n');
  const rootSections: MarkdownSection[] = [];

  let currentSection: MarkdownSection | null = null;
  let currentContent = '';
  let preambleContent = '';
  let foundFirstHeading = false;
  let collapsible = true; // Default to collapsible
  const objectParentStack: MarkdownSection[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Check if the line is a collapsible metadata comment
    const collapsibleMatch = line.match(/<!--\s*collapsible:(true|false)\s*-->/);
    if (collapsibleMatch) {
      collapsible = collapsibleMatch[1] === 'true';
      continue; // Skip this line
    }

    // Check if the line is a start of metadata comment
    const startOfObjectMatch = line.match(/<!--\s*start of object: ([\w-]+)\s*-->/);
    if (startOfObjectMatch) {
      if (currentSection) {
        objectParentStack.push(currentSection);
      }
      continue; // Skip this line
    }

    // Check if the line is an end of metadata comment
    const endOfObjectMatch = line.match(/<!--\s*end of object: ([\w-]+)\s*-->/);
    if (endOfObjectMatch) {
      objectParentStack.pop();
      continue; // Skip this line
    }

    // Check if the line is a line threshold metadata comment
    const lineThresholdMatch = line.match(/<!--\s*lineThreshold:(\d+)\s*-->/);
    if (lineThresholdMatch && currentSection) {
      currentSection.lineThreshold = parseInt(lineThresholdMatch[1], 10);
      continue; // Skip this line
    }

    // Check if the line is a heading
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);

    if (headingMatch) {
      foundFirstHeading = true;

      // If we were building a section, finalize it
      if (currentSection) {
        currentSection.content = currentContent.trim();
        currentContent = '';
      }

      const level = headingMatch[1].length;
      const heading = headingMatch[2];

      // Create a new section
      const newSection: MarkdownSection = {
        heading,
        level,
        content: '',
        children: [],
        collapsible
      };

      // Reset collapsible to default for next section
      collapsible = true;

      // Add the section to the appropriate parent, considering object boundaries
      const parentWrapper = objectParentStack.length > 0
        ? objectParentStack[objectParentStack.length - 1]
        : null;
      if (parentWrapper) {
        parentWrapper.children.push(newSection);
      } else if (level === 1 || rootSections.length === 0) {
        rootSections.push(newSection);
      } else {
        // Find the appropriate parent section by heading level
        const parent = findParentSection(rootSections, level);
        if (parent) {
          parent.children.push(newSection);
        } else {
          // If no parent found, add to root
          rootSections.push(newSection);
        }
      }

      currentSection = newSection;
    } else if (currentSection) {
      // Add the line to the current section's content
      currentContent += line + '\n';
    } else if (!foundFirstHeading) {
      // Content before the first heading
      preambleContent += line + '\n';
    }
  }

  // Finalize the last section
  if (currentSection) {
    currentSection.content = currentContent.trim();
  }

  // If there's content before the first heading, create a special section for it
  if (preambleContent.trim()) {
    rootSections.unshift({
      heading: 'Introduction',
      level: 1,
      content: preambleContent.trim(),
      children: [],
      collapsible: true
    });
  }

  // If no sections were found, create a default one with all content
  if (rootSections.length === 0 && (preambleContent.trim() || currentContent.trim())) {
    rootSections.push({
      heading: 'Content',
      level: 1,
      content: (preambleContent + currentContent).trim(),
      children: [],
      collapsible: true
    });
  }

  return rootSections;
}

function findParentSection(sections: MarkdownSection[], level: number): MarkdownSection | null {
  // Start from the last section and work backwards
  for (let i = sections.length - 1; i >= 0; i--) {
    const section = sections[i];

    // If this section's level is less than the target level, it's a potential parent
    if (section.level < level) {
      return section;
    }

    // Check this section's children
    const childParent = findParentSection(section.children, level);
    if (childParent) {
      return childParent;
    }
  }

  return null;
}

// Helper function to render sections with a flag to indicate if it's the root level
function renderSectionsWithLevel(sections: MarkdownSection[], isRootLevel: boolean): React.ReactNode {
  // Group sections by level to ensure consistent indentation for siblings
  const sectionsByLevel: Record<number, MarkdownSection[]> = {};

  // Group sections by their level
  sections.forEach(section => {
    if (!sectionsByLevel[section.level]) {
      sectionsByLevel[section.level] = [];
    }
    sectionsByLevel[section.level].push(section);
  });

  // Render sections level by level
  return Object.entries(sectionsByLevel).flatMap(([level, levelSections]) => {
    return levelSections.map((section, index) => {
      // Special case for the first section at the root level only
      if (isRootLevel && level === '1' && index === 0) {
        return (
          <Box ml={5} key={`root-${index}`}>
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {section.content}
            </ReactMarkdown>
            {/* Render children of the first section normally */}
            {renderSectionsWithLevel(section.children, false)}
          </Box>
        );
      }

      // Normal case for all other sections
      return (
        <CollapsibleSection
          key={`${section.level}-${index}`}
          heading={section.heading}
          level={section.level}
          collapsible={section.collapsible}
          lineThreshold={section.lineThreshold}
        >
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {section.content}
          </ReactMarkdown>
          {/* Render children with isRootLevel=false */}
          {renderSectionsWithLevel(section.children, false)}
        </CollapsibleSection>
      );
    });
  });
}

// Main render function that calls the helper with isRootLevel=true
function renderSections(sections: MarkdownSection[]): React.ReactNode {
  return renderSectionsWithLevel(sections, true);
}
