import { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@mantine/core";
import {
  IconCircleCheck,
  IconInfoCircle,
  IconAlertCircle,
} from "@tabler/icons-react";
import { ComplianceTimelineItem, Patient } from "@hospice-os/apptypes";
import { NoteModal } from "./modals/NoteModal";
import { AcceptReferralModal } from "./modals/AcceptReferralModal";
import {
  useGetRequestByIdQuery,
  useReviewRequestMutation,
} from "../store/api_extensions/requestApiExt";
import { useGetCurrentUserQuery } from "../store/api_extensions/userApiExt";
import { notifications } from "@mantine/notifications";
import { invalidateResourceTag } from "../store/api";
import { useAppDispatch } from "../store/hooks";

interface ComplianceTimelineProps {
  selectedPatient: Patient | undefined;
  onNoteModalSubmit: (templateName: string, requestId: string) => void;
}

export function ComplianceTimeline({
  selectedPatient,
  onNoteModalSubmit,
}: ComplianceTimelineProps) {
  const [isCompleteReferralModalOpen, setIsCompleteReferralModalOpen] =
    useState(false);
  const [isAcceptReferralModalOpen, setIsAcceptReferralModalOpen] =
    useState(false);
  const { data: currentUser } = useGetCurrentUserQuery();

  const [reviewRequest] = useReviewRequestMutation();
  const [timeLineItems, setTimeLineItems] = useState<ComplianceTimelineItem[]>(
    [],
  );

  const [pollForRequest, setPollForRequest] = useState(false);

  const requestId = useMemo(() => {
    if (!selectedPatient?.complianceTimeline?.timeLineItems) {
      return "";
    }

    const pendingReferralItem =
      selectedPatient.complianceTimeline.timeLineItems.find(
        (t) => t.type === "ReferralDataProcessed",
      );

    return pendingReferralItem?.requests?.[0]?.id ?? "";
  }, [selectedPatient?.complianceTimeline?.timeLineItems]);

  const { data: request } = useGetRequestByIdQuery(requestId, {
    skip: !requestId,
    pollingInterval: pollForRequest ? 1000 : 0,
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  useEffect(() => {
    setPollForRequest(request?.status === "processing");

    if (request?.status === "completed") {
      notifications.update({
        id: requestId,
        title: `Referral completed`,
        message: `Referral for ${selectedPatient?.firstName + " " + selectedPatient?.lastName} has been completed`,
        color: "green",
        autoClose: 5000,
        loading: false,
        withBorder: true,
      });

      invalidateResourceTag(dispatch, [
        {
          type: "Resources",
          id: selectedPatient?.id ?? "",
        },
        {
          type: "Patient",
          id: selectedPatient?.id ?? "",
        },
        {
          type: "Resources",
          id: "LIST",
        },
      ]);
    }
  }, [request?.status]);

  useEffect(() => {
    setTimeLineItems(
      selectedPatient?.complianceTimeline?.timeLineItems?.filter(
        (t) => t.title,
      ) ?? [],
    );
  }, [selectedPatient?.complianceTimeline?.timeLineItems]);

  const renderAlert = () => {
    if (
      selectedPatient?.status !== "Pending" ||
      request?.status === "processing"
    )
      return null;

    return (
      <>
        {request?.validationErrors?.length === 0 ? (
          <>
            <Alert
              w="100%"
              variant="light"
              color="blue"
              title="Referral Complete"
              style={{ textAlign: "left" }}
            >
              <Text c="blue" size="sm">
                Please verify the referral details for accuracy.
              </Text>
              <Button
                variant="filled"
                color="blue"
                mt="sm"
                onClick={() => setIsCompleteReferralModalOpen(true)}
              >
                Review Referral
              </Button>
            </Alert>
          </>
        ) : (
          <>
            <Divider my="md" />
            <Alert
              w="100%"
              variant="light"
              color="red"
              title="Referral Incomplete"
              style={{ textAlign: "left" }}
            >
              <Text c="red" size="sm">
                Please enter the missing information to complete this referral.
              </Text>
              <Button
                variant="filled"
                color="red"
                mt="sm"
                onClick={() => setIsCompleteReferralModalOpen(true)}
              >
                Enter Information
              </Button>
              <Button
                variant="outline"
                color="red"
                mt="sm"
                ml="sm"
                onClick={() => setIsAcceptReferralModalOpen(true)}
              >
                Accept Referral as Complete
              </Button>
            </Alert>
          </>
        )}
      </>
    );
  };

  const handleAcceptReferralAsComplete = async () => {
    setIsAcceptReferralModalOpen(false);
    setPollForRequest(true);

    notifications.show({
      id: requestId,
      loading: true,
      title: `Completing referral...`,
      message: `Updating ${selectedPatient?.firstName + " " + selectedPatient?.lastName}'s chart`,
      color: "blue",
      position: "top-right",
      withBorder: true,
      autoClose: false,
      withCloseButton: false,
    });

    await reviewRequest({
      requestId: requestId,
      userId: currentUser?.id ?? "",
      manualChangeset: request?.changes,
    }).unwrap();
  };

  const getStepIcon = (status?: string) => {
    switch (status) {
      case "pending":
        return <IconAlertCircle size={22} color="red" />;
      case "not_started":
        return <IconInfoCircle size={22} color="gray" />;
      default:
        return null;
    }
  };

  const getStepBackgroundColor = (status?: string) => {
    switch (status) {
      case "completed":
        return "var(--mantine-color-blue-6)";
      case "error":
        return "var(--mantine-color-red-1)";
      case "pending":
        return "var(--mantine-color-red-1)";
      default:
        return "var(--mantine-color-gray-1)";
    }
  };

  const getStepColor = (status?: string) => {
    switch (status) {
      case "awaiting_approval":
        return "red";
      case "pending":
        return "red";
      case "error":
        return "red";
      default:
        return "blue";
    }
  };

  return (
    <>
      {selectedPatient && timeLineItems.length > 0 && (
        <Stepper
          active={timeLineItems.findIndex(
            (item) => item.status !== "completed",
          )}
          completedIcon={<IconCircleCheck size={22} />}
          m="lg"
          size="xs"
        >
          {timeLineItems.map((step, index) => (
            <Stepper.Step
              key={index}
              label={step.title}
              description={
                step.completedAt
                  ? new Date(step.completedAt).toLocaleDateString()
                  : step.status === "pending"
                    ? "Action Required"
                    : step.status === "in_progress"
                      ? "In Progress"
                      : step.status === "not_started"
                        ? "Not Started"
                        : undefined
              }
              color={getStepColor((step as any).status)}
              // completedIcon={getStepIcon((step as any).status)}
              icon={getStepIcon((step as any).status)}
              loading={step.status === "in_progress"}
              styles={{
                stepIcon: {
                  backgroundColor: getStepBackgroundColor(step.status),
                  borderWidth:
                    step.status === "completed" || step.status === "in_progress"
                      ? 2
                      : 0,
                },
                // stepWrapper: {
                //   backgroundColor: 'var(--mantine-color-red-1)',
                // }
              }}
            />
          ))}
        </Stepper>
      )}
      {renderAlert()}
      {selectedPatient && isCompleteReferralModalOpen && (
        <NoteModal
          opened={isCompleteReferralModalOpen}
          onClose={() => setIsCompleteReferralModalOpen(false)}
          onSubmit={onNoteModalSubmit}
          selectedPatient={selectedPatient}
          request={request}
        />
      )}
      {selectedPatient && (
        <AcceptReferralModal
          opened={isAcceptReferralModalOpen}
          onClose={() => setIsAcceptReferralModalOpen(false)}
          onConfirm={handleAcceptReferralAsComplete}
          request={request}
        />
      )}
    </>
  );
}
