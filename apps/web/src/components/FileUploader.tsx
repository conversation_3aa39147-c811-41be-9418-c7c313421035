import React, { useState, useRef } from 'react';
import { 
  Group, 
  Text, 
  Button,
  Stack,
  Box,
  ActionIcon
} from '@mantine/core';
import { IconTrash, IconUpload } from '@tabler/icons-react';

interface FileUploaderProps {
  maxFiles?: number;
  maxSize?: number;
  onUploadComplete?: (fileIds: string[]) => void;
}

export function FileUploader({ 
  maxFiles = 5, 
  maxSize = 5 * 1024 * 1024, // 5MB
  onUploadComplete 
}: FileUploaderProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const selectedFiles = Array.from(event.target.files);
      
      // Check if we're exceeding the max files limit
      if (files.length + selectedFiles.length > maxFiles) {
        // Only add files up to the limit
        const remainingSlots = maxFiles - files.length;
        const filesToAdd = selectedFiles.slice(0, remainingSlots);
        setFiles([...files, ...filesToAdd]);
        return;
      }
      
      setFiles([...files, ...selectedFiles]);
    }
  };

  // Handle file removal
  const handleRemove = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (files.length === 0) return;
    
    setUploading(true);
    
    // Simulate file upload
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate successful upload
    const fileIds = files.map((_, index) => `file-${Date.now()}-${index}`);
    
    // Reset state
    setFiles([]);
    setUploading(false);
    
    // Notify parent component
    if (onUploadComplete) {
      onUploadComplete(fileIds);
    }
  };

  return (
    <Stack gap="md">
      <Box 
        style={{ 
          border: '2px dashed #ced4da', 
          borderRadius: '4px', 
          padding: '20px',
          textAlign: 'center',
          cursor: 'pointer'
        }}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          onChange={handleFileChange}
          multiple
          accept="image/*,application/pdf,text/*"
        />
        <IconUpload size={40} style={{ marginBottom: '10px' }} />
        <Text size="lg">Drag files here or click to select</Text>
        <Text size="sm" c="dimmed">
          Attach up to {maxFiles} files, each file should not exceed {formatFileSize(maxSize)}
        </Text>
      </Box>

      {files.length > 0 && (
        <Stack gap="sm">
          {files.map((file, index) => (
            <Group key={index} justify="space-between">
              <Group gap="xs">
                <IconUpload size={16} />
                <div>
                  <Text size="sm" fw={500}>
                    {file.name}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {formatFileSize(file.size)}
                  </Text>
                </div>
              </Group>
              
              {!uploading && (
                <ActionIcon 
                  color="red" 
                  onClick={() => handleRemove(index)}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              )}
            </Group>
          ))}
          
          <Button 
            onClick={handleUpload} 
            loading={uploading}
            disabled={files.length === 0}
          >
            {uploading ? 'Uploading...' : 'Upload Files'}
          </Button>
        </Stack>
      )}
    </Stack>
  );
}
