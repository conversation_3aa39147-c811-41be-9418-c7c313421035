import { useState } from 'react'
import { IconPlus } from '@tabler/icons-react';
import { ActionIcon, Affix } from '@mantine/core';
import { NoteModal } from './modals/NoteModal';
import { Patient } from '@hospice-os/apptypes';

interface FloatingActionButtonProps {
  selectedPatient: Patient | null;
  onNoteModalSubmit: (templateName: string, requestId: string) => void;
}

function FloatingActionButton(props: FloatingActionButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <>
      {isOpen && props.selectedPatient && <NoteModal opened={isOpen} onClose={() => setIsOpen(false)}
      onSubmit={props.onNoteModalSubmit}
      selectedPatient={props.selectedPatient} />}
      <Affix position={{ bottom: 30, right: 30 }}>
        <ActionIcon color="blue" radius="xl" size={60} onClick={() => setIsOpen(true)}>
          <IconPlus stroke={1.5} size={30} />
        </ActionIcon>
      </Affix>
    </>
  );
}

export default FloatingActionButton