import React, { useEffect, useState } from "react";
import {
  Group,
  Badge,
  Select,
  Button,
  Box,
  Paper,
  Text,
  Title,
  Alert,
} from "@mantine/core";
import {
  IconAlertTriangle,
  IconCalendarEvent,
  IconInfoCircle,
  IconPlus,
  IconUser,
} from "@tabler/icons-react";
import {
  useGetPatientsQuery,
} from "../store/api_extensions/patientApiExt";
import { Branch, Patient, User } from "@hospice-os/apptypes";
import { useNavigate } from "react-router-dom";
import { useGetResourceByIdQuery } from "../store/api_extensions/resourceApiExt";
import Markdown from "react-markdown";
import { useGetUserByIdQuery } from "../store/api_extensions/userApiExt";
import { formatPropertyName } from "../utils/schemaToMarkdown";
import { ComplianceTimeline } from "./ComplianceTimeline";
import { CompleteReferralModal } from "./modals/CompleteReferralModal";

export const getPatientStatusBadgeColor = (status: string) => {
  switch (status) {
    case "Active":
      return "green";
    case "Discharged":
      return "blue";
    case "Deceased":
      return "gray";
    case "Pending Review":
      return "yellow";
    default:
      return "gray";
  }
};

interface PatientChartHeaderProps {
  selectedPatient: Patient | undefined;
  onAddPatientClick: () => void;
  onNoteModalSubmit: (templateName: string, requestId: string) => void;
}

export function PatientChartHeader({
  selectedPatient: selectedPatient_,
  onAddPatientClick,
  onNoteModalSubmit
}: PatientChartHeaderProps) {
  const { data: patients } = useGetPatientsQuery(
    ["firstName", "lastName", "id"],
    {
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
      refetchOnReconnect: true,
    },
  );

  const navigate = useNavigate();

  const [selectedPatient, setSelectedPatient] = useState(selectedPatient_);

  useEffect(() => {
    setSelectedPatient(selectedPatient_);
  }, [selectedPatient_]);

  const { data: branch_ } = useGetResourceByIdQuery(
    { resourceType: "Branch", id: selectedPatient?.branch?.id ?? "" },
    { skip: !selectedPatient?.branch?.id },
  );
  const { data: caseManager_, isLoading: isCaseManagerLoading } =
    useGetUserByIdQuery(selectedPatient?.caseManager?.id ?? "", {
      skip: !selectedPatient?.caseManager?.id,
    });

  const branch = branch_ as Branch;
  const caseManager = caseManager_ as User;

  const onPatientChange = (patientId: string | null) => {
    const foundPatient = patients?.find((patient) => patient.id === patientId);
    if (patientId && foundPatient) {
      setSelectedPatient(foundPatient);
      navigate(`/patient/${patientId}`);
    }
  };

  const getBenefitPeriodText = () => {
    let text = "N/A";
    if (
      selectedPatient &&
      selectedPatient.benefitPeriods &&
      selectedPatient.benefitPeriods.length > 0
    ) {
      const currentPeriod =
        selectedPatient.benefitPeriods[
          selectedPatient.benefitPeriods.length - 1
        ];
      text = `${currentPeriod.period}: (${currentPeriod.startDate} - ${currentPeriod.endDate})`;
    }

    return <Text>{text}</Text>;
  };

  return (
    <>
      <Paper p="md" withBorder mb="md">
        <Group justify="space-between" pb="sm">
          <Title order={4}>Patient Chart</Title>
          <Group gap="xs">
            <IconUser size={20} />
            <Text fw={500}>Case Manager:</Text>
            {!isCaseManagerLoading && (
              <Text>
                {caseManager
                  ? `${caseManager.firstName} ${caseManager.lastName}`
                  : "N/A"}
              </Text>
            )}
            <IconCalendarEvent size={20} />
            <Text fw={500}>Benefit Period:</Text>
            {getBenefitPeriodText()}
            <IconCalendarEvent size={20} />
            <Text fw={500}>Next IDG:</Text>
            <Text>
              {branch?.nextIdgDate
                ? new Date(branch?.nextIdgDate).toLocaleDateString()
                : "N/A"}
            </Text>
          </Group>
        </Group>

        <Box>
          {/* Patient Selection */}
          <Box mb="md">
            <Group justify="stretch" align="center">
              {selectedPatient && (
                <Badge
                  color={getPatientStatusBadgeColor(
                    selectedPatient.status ?? "",
                  )}
                  size="lg"
                  radius="sm"
                >
                  {selectedPatient.status}
                </Badge>
              )}
              <Select
                placeholder="Choose a patient to continue"
                data={patients?.map((patient) => ({
                  value: patient.id,
                  label: `${patient.firstName} ${patient.lastName}`,
                }))}
                value={selectedPatient?.id}
                onChange={onPatientChange}
                searchable
                required
                style={{ flex: 1 }}
              />
              <Button
                variant="outline"
                color="green"
                leftSection={<IconPlus size={16} />}
                onClick={onAddPatientClick}
              >
                Add Patient
              </Button>
            </Group>
          </Box>
          <ComplianceTimeline selectedPatient={selectedPatient} onNoteModalSubmit={onNoteModalSubmit} />
        </Box>
      </Paper>
    </>
  );
}
