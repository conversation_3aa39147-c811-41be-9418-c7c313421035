import { useState, useEffect, useCallback } from "react";
import { Box, Text, Loader, Stack, Button, Group } from "@mantine/core";
import { CollapsibleMarkdown } from "./CollapsibleMarkdown";
import "../styles/markdown.css";
import { dataToMarkdown } from "../utils/schemaToMarkdown";
import { useGetSchemaQuery, useGetUISchemasBySchemaQuery } from "../store/api";
import { useGetResourceByIdQuery } from "../store/api_extensions/resourceApiExt";
import { BaseResource } from "@hospice-os/apptypes";
import { IconArrowLeft } from "@tabler/icons-react";

interface ResourceMarkdownViewProps {
  resourceId: string | undefined;
  resourceType: string | undefined;
  onClose?: () => void;
  pollingInterval?: number;
}

export function ResourceMarkdownView({
  resourceId,
  resourceType,
  onClose,
  pollingInterval = 50000,
}: ResourceMarkdownViewProps) {
  const [markdownContent, setMarkdownContent] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use provided patientData or fall back to Redux state if available
  const {
    data: displayedResource,
    isLoading: isLoadingResource,
    error: resourceError,
  } = useGetResourceByIdQuery(
    { resourceType: resourceType || "", id: resourceId || "" },
    {
      skip: !resourceId || !resourceType,
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
      pollingInterval: pollingInterval,
    },
  );

  // Fetch schema
  const {
    data: schemaData,
    isLoading: isLoadingSchema,
    error: schemaError,
  } = useGetSchemaQuery(displayedResource?.schemaId || "", {
    skip: !displayedResource?.schemaId,
  });

  // Fetch UI schemas for this schema
  const { data: uiSchemas, isLoading: isLoadingUISchemas } =
    useGetUISchemasBySchemaQuery(displayedResource?.schemaId || "", {
      skip: !displayedResource?.schemaId,
    });

  // Generate markdown when schema and uiSchema are available
  useEffect(() => {
    const generateMarkdown = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!schemaData || isLoadingSchema || isLoadingUISchemas) {
          return;
        }

        // Get the first UI schema if available
        const foundUISchema =
          uiSchemas && uiSchemas.length > 0 ? uiSchemas[0].content : undefined;

        // Generate markdown directly from the patient data
        const markdown = await dataToMarkdown(
          displayedResource,
          foundUISchema,
          0, // nestingLevel = 0 (top level)
          schemaData.schema, // Pass the JSON schema
        );

        setMarkdownContent(markdown);
      } catch (err) {
        console.error("Error generating markdown:", err);
        setError(
          "Failed to generate patient data view. Please try again later.",
        );
      } finally {
        setIsLoading(false);
      }
    };

    generateMarkdown();
  }, [schemaData, uiSchemas, isLoadingSchema, displayedResource]);

  if (isLoadingSchema || isLoadingUISchemas || isLoading || isLoadingResource) {
    return (
      <Box p="md">
        <Stack align="center" gap="md">
          <Loader size="md" />
          <Text>Loading patient data...</Text>
        </Stack>
      </Box>
    );
  }

  if (schemaError || error) {
    return (
      <Box p="md" style={{ textAlign: "left" }}>
        <Text color="red">
          {error || "Error loading patient data. Please try again later."}
        </Text>
      </Box>
    );
  }

  return (
    <Box style={{ textAlign: "left" }}>
      {onClose && (
        <Group mb="md">
          <Button
            variant="subtle"
            leftSection={<IconArrowLeft size={16} />}
            onClick={onClose}
          >
            Back
          </Button>
        </Group>
      )}
      <CollapsibleMarkdown markdown={markdownContent} />
    </Box>
  );
}
