import {
  Text,
  ScrollArea,
  Paper,
  Group,
  Box,
  Stack,
  Badge,
} from "@mantine/core";
import { EntryTagColors } from "../features/patient_chart/types";
import { useAppSelector } from "../store/hooks";
import { Request } from "@hospice-os/apptypes";

interface TimelineSectionProps {
  entries: Request[];
  onEntryClick: (entry: Request) => void;
}

export function TimelineSection({
  entries,
  onEntryClick,
}: TimelineSectionProps) {
  return (
    <Box>
      <ScrollArea h="calc(100vh - 160px)" offsetScrollbars>
        <Stack gap="md">
          {entries.map((entry) => (
            <TimelineEntryItem
              key={entry.id}
              entry={entry}
              onClick={onEntryClick}
            />
          ))}
        </Stack>
      </ScrollArea>
    </Box>
  );
}

interface TimelineEntryItemProps {
  entry: Request;
  onClick: (entry: Request) => void;
}

export function TimelineEntryItem({ entry, onClick }: TimelineEntryItemProps) {
  // Get the current timeline item from Redux store
  const { currentTimelineItem } = useAppSelector((state) => state.patientChart);
  const isSelected = currentTimelineItem?.id === entry.id;

  const handleClick = () => {
    onClick(entry);
  };

  return (
    <Paper
      withBorder
      p="sm"
      onClick={handleClick}
      style={{
        cursor: "pointer",
        backgroundColor: isSelected ? "var(--mantine-color-blue-0)" : undefined,
        borderColor: isSelected ? "var(--mantine-color-blue-5)" : undefined,
        "&:hover": {
          backgroundColor: isSelected
            ? "var(--mantine-color-blue-1)"
            : "var(--mantine-color-gray-0)",
        },
      }}
    >
      <Group justify="space-between" mb={5}>
        <Text fw={500} size="sm">
          {entry.title}
        </Text>
        <Text size="xs" c="dimmed">
          {(() => {
            const date = entry.madeBy?.date
              ? new Date(entry.madeBy.date)
              : new Date();
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            const isToday = date.toDateString() === today.toDateString();
            const isYesterday =
              date.toDateString() === yesterday.toDateString();

            if (isToday) {
              return `Today at ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit", hour12: true })}`;
            } else if (isYesterday) {
              return `Yesterday at ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit", hour12: true })}`;
            } else {
              return date.toLocaleString([], {
                month: "numeric",
                day: "numeric",
                year: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
              });
            }
          })()}
        </Text>
      </Group>

      <Text size="sm" lineClamp={2} mb={10} style={{ textAlign: "left" }}>
        {entry.summary}
      </Text>

      {entry.madeBy?.name && (
        <Text size="xs" c="dimmed" style={{ textAlign: "left" }}>
          {entry.madeBy.name}
        </Text>
      )}
      <Group mt={10} gap="xs">
        {entry.status === "completed" ? (
          <>
            {entry.tags?.map((tag) => (
              <Badge
                key={`${tag}-${entry.id}`}
                color={EntryTagColors[tag as keyof typeof EntryTagColors]}
              >
                {tag}
              </Badge>
            ))}
          </>
        ) : (
          <>
            {entry.status === "error" ? (
              <Badge color="red">Error</Badge>
            ) : entry.status === "pending_review" ? (
              <Badge color="blue">In Progress</Badge>
            ) : (
              <Badge color="green">Processing</Badge>
            )}
          </>
        )}
      </Group>

      {/* {entry.description && (
        <Text size="xs" fs="italic" mt={5} c="dimmed">
          {entry.description}
        </Text>
      )} */}
    </Paper>
  );
}
