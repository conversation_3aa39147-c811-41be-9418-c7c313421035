import type { <PERSON>a, StoryObj } from "@storybook/react";
import { VersionHistorySection } from "./VersionHistorySection";
import { ChangesetItem } from "@hospice-os/apptypes";

const meta: Meta<typeof VersionHistorySection> = {
  title: "Components/VersionHistorySection",
  component: VersionHistorySection,
};

export default meta;

type Story = StoryObj<typeof VersionHistorySection>;

const sampleChanges: any[] = [
  {
    op: "add",
    name: "Whey Protein",
    path: "/medications/-",
    fieldName: "medications",
    value: {
      name: "Whey Protein",
      notes: "",
      route: "oral",
      dosage: "50 grams",
      status: "Active",
      covered: false,
      endDate: "",
      highRisk: "",
      strength: "50 grams",
      frequency: "TID",
      startDate: "2025-06-09",
      indication: "Nutritional supplementation",
      providedBy: "",
      finalRuleAddendumRequired: "",
      relatedToTerminalDiagnosis: "Unknown",
    },
    reason: "Whey protein was prescribed at...",
    oldValue: "",
    readBackRequired: true,
    readBackPerformed: false,
  },
  {
    op: "add",
    name: "Testosterone",
    path: "/medications/-",
    fieldName: "medications",
    value: {
      form: "Injection",
      name: "Testosterone",
      notes: "",
      route: "intramuscular",
      dosage: "100 mg",
      status: "Active",
      covered: false,
      endDate: "",
      highRisk: "",
      frequency: "BIW",
      startDate: "2025-06-09",
      indication: "Testosterone replacement",
      providedBy: "",
      finalRuleAddendumRequired: "",
    },
    reason: "Testosterone was prescribed at...",
    oldValue: "",
    readBackRequired: true,
    readBackPerformed: false,
  },
];

export const Default: Story = {
  args: {
    changes: sampleChanges,
    allowRemoval: true,
  },
};

export const Empty: Story = {
  args: {
    changes: [],
  },
};

export const RemovalDisabled: Story = {
  args: {
    changes: sampleChanges,
    allowRemoval: false,
  },
};

const sampleChanges2: any[] = [
  {
    op: "replace",
    name: "Whey Protein",
    path: "/medications/34",
    value: {
      form: null,
      name: "Whey Protein",
      notes: "",
      route: "oral",
      dosage: "200 grams",
      status: "Active",
      covered: false,
      endDate: null,
      highRisk: null,
      strength: "200 grams",
      frequency: "BID",
      startDate: "2025-06-10",
      indication: "Nutritional supplementation",
      providedBy: "",
      finalRuleAddendumRequired: null,
      relatedToTerminalDiagnosis: "Unknown",
    },
    reason:
      "The transcript states the patient needs to increase their whey protein medication to 200 grams twice daily. This updates the dosage, strength, frequency, and start date.",
    oldValue: {
      name: "Whey Protein",
      notes: "",
      quantity: null,
    },
    readBackRequired: true,
    readBackPerformed: false,
  },
];

export const WithNulls: Story = {
  args: {
    changes: sampleChanges2,
  },
};

const sampleChanges3: any[] = [
  {
    op: "replace",
    name: "Whey Protein",
    path: "/medications/34/dosage",
    value: "200 grams",
  },
  {
    op: "replace",
    name: "Creatine",
    path: "/medications/34/dosage",
    value: "100 grams",
  },
  {
    op: "replace",
    name: "Whey Protein",
    path: "/medications/34/strength",
    value: "200 grams",
  },
];

export const GroupingSeparates: Story = {
  args: {
    changes: sampleChanges3,
  },
};
