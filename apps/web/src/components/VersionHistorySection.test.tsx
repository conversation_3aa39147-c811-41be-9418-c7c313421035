import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { MantineProvider } from "@mantine/core";
import { VersionHistorySection } from "./VersionHistorySection";
import { ChangesetItem } from "@hospice-os/apptypes";

describe("VersionHistorySection", () => {
  const mockOnChangesUpdate = vi.fn();
  const mockOnClose = vi.fn();

  const initialChanges: ChangesetItem[] = [
    {
      op: "add",
      name: "Whey Protein",
      path: "/medications/-",
      fieldName: "medications",
      value: {
        name: "Whey Protein",
        notes: "",
        route: "oral",
        dosage: "50 grams",
        status: "Active",
        covered: false,
        endDate: "",
        highRisk: "",
        strength: "50 grams",
        frequency: "TID",
        startDate: "2025-06-09",
        indication: "Nutritional supplementation",
        providedBy: "",
        finalRuleAddendumRequired: "",
        relatedToTerminalDiagnosis: "Unknown",
      },
      reason: "Whey protein was prescribed at...",
      oldValue: "",
      readBackRequired: true,
      readBackPerformed: false,
    },
    {
      op: "add",
      name: "Testosterone",
      path: "/medications/-",
      fieldName: "medications",
      value: {
        form: "Injection",
        name: "Testosterone",
        notes: "",
        route: "intramuscular",
        dosage: "100 mg",
        status: "Active",
        covered: false,
        endDate: "",
        highRisk: "",
        frequency: "BIW",
        startDate: "2025-06-09",
        indication: "Testosterone replacement",
        providedBy: "",
        finalRuleAddendumRequired: "",
      },
      reason: "Testosterone was prescribed at...",
      oldValue: "",
      readBackRequired: true,
      readBackPerformed: false,
    },
  ];

  const defaultProps = {
    changes: initialChanges,
    allowRemoval: true,
    onChangesUpdate: mockOnChangesUpdate,
    onClose: mockOnClose,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders medication changes correctly", () => {
    render(
      <MantineProvider>
        <VersionHistorySection {...defaultProps} />
      </MantineProvider>,
    );

    // Check that medications section is rendered
    expect(screen.getByText("Medications")).toBeInTheDocument();

    // Check that both medication names are visible
    expect(screen.getByText("Whey Protein")).toBeInTheDocument();
    expect(screen.getByText("Testosterone")).toBeInTheDocument();
  });

  it("allows excluding specific fields and calls onChangesUpdate with modified changes", async () => {
    render(
      <MantineProvider>
        <VersionHistorySection {...defaultProps} />
      </MantineProvider>,
    );

    // Open the medications accordion to see the changes
    const medicationsAccordion = screen.getByText("Medications");
    fireEvent.click(medicationsAccordion);

    // Wait for the accordion to open and show the changes
    await waitFor(() => {
      expect(screen.getByText("Whey Protein")).toBeInTheDocument();
    });

    // Open individual medication accordions to see fields
    const wheyProteinAccordion = screen.getByText("Whey Protein");
    fireEvent.click(wheyProteinAccordion);

    const testosteroneAccordion = screen.getByText("Testosterone");
    fireEvent.click(testosteroneAccordion);

    await waitFor(() => {
      // Look for both covered fields (both medications have covered field)
      expect(screen.getAllByText(/Covered:/)).toHaveLength(2);
      // Look for route fields (both medications have route field)
      expect(screen.getAllByText(/Route:/)).toHaveLength(2);
    });

    // Find and click the exclude button for "covered" field in Whey Protein
    // Since both medications have covered field, we need to be more specific
    const coveredTexts = screen.getAllByText(/Covered:/);
    const routeTexts = screen.getAllByText(/Route:/);

    // Find the first covered field (Whey Protein) and its exclude button
    const firstCoveredText = coveredTexts[0];
    const firstCoveredGroup =
      firstCoveredText.closest('[class*="Group"]') ||
      firstCoveredText.parentElement;
    const firstCoveredExcludeButton = firstCoveredGroup?.querySelector(
      'button[title*="Exclude"]',
    );

    if (firstCoveredExcludeButton) {
      fireEvent.click(firstCoveredExcludeButton);
    }

    // Find the second route field (Testosterone) and its exclude button
    const secondRouteText = routeTexts[1];
    const secondRouteGroup =
      secondRouteText.closest('[class*="Group"]') ||
      secondRouteText.parentElement;
    const secondRouteExcludeButton = secondRouteGroup?.querySelector(
      'button[title*="Exclude"]',
    );

    if (secondRouteExcludeButton) {
      fireEvent.click(secondRouteExcludeButton);
    }

    // Wait for onChangesUpdate to be called
    await waitFor(() => {
      expect(mockOnChangesUpdate).toHaveBeenCalled();
    });

    // Get the last call to onChangesUpdate
    const lastCall =
      mockOnChangesUpdate.mock.calls[mockOnChangesUpdate.mock.calls.length - 1];
    const updatedChanges = lastCall[0] as ChangesetItem[];

    // Validate that the changes are correct
    expect(updatedChanges).toHaveLength(2);

    // First change (Whey Protein) should have "covered" field removed
    const wheyProteinChange = updatedChanges.find(
      (change) => change.name === "Whey Protein",
    );
    expect(wheyProteinChange).toBeDefined();
    expect(wheyProteinChange?.value).toBeDefined();
    expect(wheyProteinChange?.value).not.toHaveProperty("covered");

    // All other fields should remain
    expect(wheyProteinChange?.value).toHaveProperty("name", "Whey Protein");
    expect(wheyProteinChange?.value).toHaveProperty("route", "oral");
    expect(wheyProteinChange?.value).toHaveProperty("dosage", "50 grams");
    expect(wheyProteinChange?.value).toHaveProperty("status", "Active");

    // Second change (Testosterone) should have "route" field removed
    const testosteroneChange = updatedChanges.find(
      (change) => change.name === "Testosterone",
    );
    expect(testosteroneChange).toBeDefined();
    expect(testosteroneChange?.value).toBeDefined();
    expect(testosteroneChange?.value).not.toHaveProperty("route");

    // All other fields should remain
    expect(testosteroneChange?.value).toHaveProperty("name", "Testosterone");
    expect(testosteroneChange?.value).toHaveProperty("form", "Injection");
    expect(testosteroneChange?.value).toHaveProperty("dosage", "100 mg");
    expect(testosteroneChange?.value).toHaveProperty("status", "Active");
    expect(testosteroneChange?.value).toHaveProperty("covered", false);
  });

  it("handles field exclusion correctly for complex nested objects", async () => {
    render(
      <MantineProvider>
        <VersionHistorySection {...defaultProps} />
      </MantineProvider>,
    );

    // Initially, both changes should be present
    expect(mockOnChangesUpdate).not.toHaveBeenCalled();

    // Open accordions to access fields
    const medicationsAccordion = screen.getByText("Medications");
    fireEvent.click(medicationsAccordion);

    await waitFor(() => {
      expect(screen.getByText("Whey Protein")).toBeInTheDocument();
    });

    const wheyProteinAccordion = screen.getByText("Whey Protein");
    fireEvent.click(wheyProteinAccordion);

    // Wait for fields to be visible
    await waitFor(() => {
      expect(screen.getAllByText(/Covered:/)).toHaveLength(2);
    });

    // Exclude the covered field from Whey Protein (first one)
    const coveredTexts = screen.getAllByText(/Covered:/);
    const firstCoveredText = coveredTexts[0];
    const excludeButton = firstCoveredText.parentElement?.querySelector(
      'button[title*="Exclude"]',
    );

    if (excludeButton) {
      fireEvent.click(excludeButton);
    }

    await waitFor(() => {
      expect(mockOnChangesUpdate).toHaveBeenCalledTimes(1);
    });

    const firstCallChanges = mockOnChangesUpdate.mock
      .calls[0][0] as ChangesetItem[];

    // Verify the structure is maintained but the specific field is removed
    expect(firstCallChanges).toHaveLength(2);

    const modifiedWheyProtein = firstCallChanges.find(
      (change) => change.name === "Whey Protein",
    );
    expect(modifiedWheyProtein?.value).not.toHaveProperty("covered");
    expect(modifiedWheyProtein?.value).toHaveProperty("name", "Whey Protein");

    // The other change should remain unmodified
    const unchangedTestosterone = firstCallChanges.find(
      (change) => change.name === "Testosterone",
    );
    expect(unchangedTestosterone?.value).toEqual(initialChanges[1].value);
  });

  it("shows readback badges correctly", () => {
    render(
      <MantineProvider>
        <VersionHistorySection {...defaultProps} />
      </MantineProvider>,
    );

    // Both changes have readBackRequired: true and readBackPerformed: false
    // So they should show "Readback Required" badges
    expect(screen.getAllByText("Readback Required")).toHaveLength(2);
  });

  it("handles empty changes array", () => {
    render(
      <MantineProvider>
        <VersionHistorySection {...defaultProps} changes={[]} />
      </MantineProvider>,
    );

    expect(
      screen.getByText(
        "Changes to the patient record will appear here after processing an audio recording.",
      ),
    ).toBeInTheDocument();
  });

  it("disables removal functionality when allowRemoval is false", () => {
    render(
      <MantineProvider>
        <VersionHistorySection {...defaultProps} allowRemoval={false} />
      </MantineProvider>,
    );

    // Open accordions
    const medicationsAccordion = screen.getByText("Medications");
    fireEvent.click(medicationsAccordion);

    // Exclude buttons should not be present when allowRemoval is false
    const excludeButtons = screen.queryAllByRole("button", {
      name: /exclude/i,
    });
    expect(excludeButtons).toHaveLength(0);
  });

  it("only shows fields that actually changed (hides identical before/after values)", async () => {
    // Create a change where some fields are the same and some different
    const changeWithSameAndDifferentFields: ChangesetItem[] = [
      {
        op: "replace",
        name: "Ibuprofen",
        path: "/medications/0",
        fieldName: "medications",
        value: {
          name: "Ibuprofen", // Same in both
          route: "oral", // Same in both
          dosage: "1000mg", // Changed from 800mg
          status: "Active", // Same in both
          covered: false, // Same in both
          strength: "1000mg", // Changed from 800mg
          frequency: "Daily", // Same in both
        },
        oldValue: {
          name: "Ibuprofen", // Same in both
          route: "oral", // Same in both
          dosage: "800mg", // Changed to 1000mg
          status: "Active", // Same in both
          covered: false, // Same in both
          strength: "800mg", // Changed to 1000mg
          frequency: "Daily", // Same in both
        },
        reason: "Updated dosage from 800mg to 1000mg",
        readBackRequired: true,
        readBackPerformed: false,
      },
    ];

    render(
      <MantineProvider>
        <VersionHistorySection
          {...defaultProps}
          changes={changeWithSameAndDifferentFields}
        />
      </MantineProvider>,
    );

    // Open accordions
    const medicationsAccordion = screen.getByText("Medications");
    fireEvent.click(medicationsAccordion);

    await waitFor(() => {
      expect(screen.getByText("Ibuprofen")).toBeInTheDocument();
    });

    const ibuprofen = screen.getByText("Ibuprofen");
    fireEvent.click(ibuprofen);

    await waitFor(() => {
      // Fields that changed should be visible
      expect(screen.getByText(/Dosage: 800mg/)).toBeInTheDocument(); // Before
      expect(screen.getByText(/Dosage: 1000mg/)).toBeInTheDocument(); // After
      expect(screen.getByText(/Strength: 800mg/)).toBeInTheDocument(); // Before
      expect(screen.getByText(/Strength: 1000mg/)).toBeInTheDocument(); // After

      // Fields that are the same should NOT be visible
      expect(screen.queryByText(/Name: Ibuprofen/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Route: oral/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Status: Active/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Covered: false/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Frequency: Daily/)).not.toBeInTheDocument();
    });
  });
});
