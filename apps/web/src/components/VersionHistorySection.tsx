import {
  Paper,
  Title,
  Text,
  Group,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  Accordion,
  Badge,
  ActionIcon,
  Tooltip,
} from "@mantine/core";
import { IconArrowLeft, IconArrowRight, IconX } from "@tabler/icons-react";
import { useCallback, useEffect, useState } from "react";
import { formatPropertyName } from "../utils/schemaToMarkdown";
import { ChangesetItem } from "@hospice-os/apptypes";

const empty = (obj: any) => {
  if (typeof obj === "object" && obj !== null) {
    return Object.keys(obj).length === 0;
  } else {
    return obj === undefined || obj === null || obj === "";
  }
};

interface VersionHistorySectionProps {
  selectedDate?: string;
  onClose?: () => void;
  changes?: ChangesetItem[];
  sectionedChanges?: { sectionTitle: string; changes: ChangesetItem[] }[];
  allowRemoval?: boolean;
  onChangesUpdate?: (changes: ChangesetItem[]) => void;
}

type ChangesetSection = {
  name: string;
  changes: ChangesetItem[] | ChangesetSection[];
};

function getSortedKeys(obj1: object, obj2: object) {
  const keys = new Set([
    ...Object.keys(obj1 ?? {}),
    ...Object.keys(obj2 ?? {}),
  ]);
  return Array.from(keys).sort();
}

export function VersionHistorySection({
  selectedDate,
  onClose,
  changes,
  sectionedChanges,
  allowRemoval,
  onChangesUpdate,
}: VersionHistorySectionProps) {
  // State to track excluded changes
  const [excludedChanges, setExcludedChanges] = useState<Set<string>>(
    new Set(),
  );

  // Generate unique ID for each change
  const getChangeId = useCallback(
    (change: ChangesetItem, parentName: string) => {
      // Use a more stable identifier that includes parent context
      return `${change.path}-${change.name}-${parentName}`;
    },
    [],
  );

  // Handle toggling change exclusion
  const toggleChangeExclusion = useCallback(
    (changeId: string) => {
      setExcludedChanges((prev) => {
        const newExcluded = new Set(prev);
        if (newExcluded.has(changeId)) {
          newExcluded.delete(changeId);
        } else {
          newExcluded.add(changeId);
        }

        // Call the callback with filtered/modified changes
        if (onChangesUpdate && changes) {
          // Organize the changes first, then reverse with exclusions applied
          const organizedChanges = organizeChanges(changes);
          const updatedChanges = reverseOrganizedChanges(
            organizedChanges,
            newExcluded,
          );
          onChangesUpdate(updatedChanges);
        }

        return newExcluded;
      });
    },
    [changes, onChangesUpdate, getChangeId],
  );

  // Method to reverse organized changes back to flat array with exclusions applied
  const reverseOrganizedChanges = useCallback(
    (
      organizedChanges: ChangesetSection[],
      excludedIds: Set<string>,
    ): ChangesetItem[] => {
      const flatChanges: ChangesetItem[] = [];

      const processSection = (section: ChangesetSection) => {
        // Check if section.changes contains ChangesetItem[] or ChangesetSection[]
        if (section.changes.length > 0) {
          const firstItem = section.changes[0];

          // If it has a 'path' property, it's a ChangesetItem[]
          if ("path" in firstItem) {
            // This is a leaf section with actual ChangesetItem[]
            (section.changes as ChangesetItem[]).forEach((change) => {
              const changeId = getChangeId(change, section.name);

              // If the entire change is excluded, skip it
              if (excludedIds.has(changeId)) {
                return;
              }

              // If this change has complex values and some fields are excluded, modify the change
              if (
                (typeof change.value === "object" && change.value !== null) ||
                (change.value === null && typeof change.oldValue === "object")
              ) {
                const modifyOldValue =
                  change.value === null && typeof change.oldValue === "object"
                    ? true
                    : false;
                const changeToModify = modifyOldValue
                  ? change.oldValue
                  : change.value;

                const modifiedChange = { ...change };
                const modifiedValue = Array.isArray(changeToModify)
                  ? [...changeToModify]
                  : { ...changeToModify };
                let hasExclusions = false;

                // Check for excluded nested fields
                const checkAndRemoveExcluded = (
                  obj: unknown,
                  path = "",
                ): unknown => {
                  if (Array.isArray(obj)) {
                    return obj.map((item, idx) => {
                      const fieldId = getChangeId(
                        change,
                        `${section.name}${path}[${idx}]`,
                      );
                      const isExcluded = excludedIds.has(fieldId);
                      if (isExcluded) {
                        hasExclusions = true;
                        return modifyOldValue ? item : null;
                      }
                      return typeof item === "object" && item !== null
                        ? checkAndRemoveExcluded(item, `${path}[${idx}]`)
                        : modifyOldValue
                          ? null
                          : item;
                    });
                  } else if (typeof obj === "object" && obj !== null) {
                    const result: { [key: string]: unknown } = {};
                    Object.entries(obj).forEach(([key, value]) => {
                      const fieldPath = path ? `${path}.${key}` : key;
                      const fieldId = getChangeId(
                        change,
                        `${section.name}.${fieldPath}`,
                      );
                      if (excludedIds.has(fieldId)) {
                        result[key] = modifyOldValue ? value : null;
                        hasExclusions = true;
                      } else if (typeof value === "object" && value !== null) {
                        result[key] = checkAndRemoveExcluded(value, fieldPath);
                      } else {
                        result[key] = modifyOldValue ? null : value;
                      }
                    });
                    return result;
                  }
                  return obj;
                };

                modifiedChange.value = checkAndRemoveExcluded(modifiedValue);

                // Also handle oldValue if it exists
                if (
                  typeof change.oldValue === "object" &&
                  change.oldValue !== null &&
                  !modifyOldValue
                ) {
                  const modifiedOldValue = Array.isArray(change.oldValue)
                    ? [...change.oldValue]
                    : { ...change.oldValue };
                  modifiedChange.oldValue =
                    checkAndRemoveExcluded(modifiedOldValue);
                }

                // Recursively check if all values in an object are null
                const areAllValuesNull = (obj: any): boolean => {
                  if (obj === null) return true;
                  if (typeof obj !== "object") return false;
                  if (Array.isArray(obj))
                    return obj.every((item) => areAllValuesNull(item));
                  return Object.values(obj).every((value) =>
                    areAllValuesNull(value),
                  );
                };

                // If all values are null or the objects are the same, don't add this change
                if (
                  !areAllValuesNull(modifiedChange.value) &&
                  JSON.stringify(modifiedChange.value) !==
                    JSON.stringify(modifiedChange.oldValue)
                ) {
                  flatChanges.push(hasExclusions ? modifiedChange : change);
                }
              } else {
                flatChanges.push(change);
              }
            });
          } else {
            // This is a nested section with ChangesetSection[], recurse
            (section.changes as ChangesetSection[]).forEach(processSection);
          }
        }
      };

      organizedChanges.forEach(processSection);
      return flatChanges;
    },
    [getChangeId],
  );

  // Format the date for display
  const formattedDate = selectedDate
    ? new Date(selectedDate).toLocaleDateString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "Unknown Date";

  const organizeChanges = useCallback(
    (changesToOrganize: ChangesetItem[] = changes || []) => {
      const sections: { [key: string]: ChangesetItem[] } = {};

      changesToOrganize
        .filter(
          (change) =>
            JSON.stringify(change.oldValue) !== JSON.stringify(change.value),
        )
        .forEach((change: ChangesetItem) => {
          // Get the section name from the first part of the path and capitalize first letter
          const splitPath = change.path.split("/");
          const sectionName = formatPropertyName(
            splitPath[1].charAt(0).toUpperCase() + splitPath[1].slice(1),
          );

          // Initialize the section if it doesn't exist
          if (!sections[sectionName]) {
            sections[sectionName] = [];
          }

          // Add the change as-is, preserving original structure
          sections[sectionName].push(change);
        });

      // Convert sections object to array format and group by name property
      return Object.entries(sections).map(([sectionName, sectionChanges]) => {
        // Group changes by their name property
        const groupedChanges = sectionChanges.reduce(
          (acc, change) => {
            if (!acc[change.name]) {
              acc[change.name] = [];
            }
            acc[change.name].push(change);
            return acc;
          },
          {} as { [key: string]: ChangesetItem[] },
        );

        return {
          name: sectionName || "",
          changes: Object.entries(groupedChanges).map(([name, changes]) => ({
            name: String(name ?? ""),
            changes,
          })),
        } as ChangesetSection;
      });
    },
    [changes],
  );

  useEffect(() => {
    if (onChangesUpdate && changes && excludedChanges.size > 0) {
      const organizedChanges = organizeChanges(changes);
      const updatedChanges = reverseOrganizedChanges(
        organizedChanges,
        excludedChanges,
      );
      onChangesUpdate(updatedChanges);
    }
  }, [changes]);

  const getStringValueForField = (fieldName: string, fieldValue: any) => {
    let fieldValueText = fieldValue;

    if (fieldValue === true || fieldValue === "true") {
      return "Yes";
    } else if (fieldValue === false || fieldValue === "false") {
      return "No";
    }

    if (fieldName.toLowerCase().includes("date")) {
      return new Date(fieldValue as string).toLocaleDateString("en-US", {
        timeZone: "UTC",
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }

    return String(fieldValueText);
  };

  const renderChangeValue = (
    change: ChangesetItem,
    type: "before" | "after",
    parentPath = "",
  ) => {
    if (!change) return null;

    const fieldValue = type === "before" ? change.oldValue : change.value;
    const compareValue = type === "before" ? change.value : change.oldValue;
    const textColor = type === "before" ? "red" : "green";

    // Helper function to render nested objects
    const renderNestedValue = (
      value: unknown,
      key?: string,
    ): React.ReactNode => {
      if (value === null || value === undefined || value === "") return null;

      if (typeof value === "object" && !Array.isArray(value)) {
        return (
          <Stack gap="xs" pl="md">
            {Object.entries(value as Record<string, unknown>).map(([k, v]) => (
              <Box key={k}>
                {typeof v === "object" && v !== null ? (
                  <>
                    <Text size="sm" fw={700} c={textColor}>
                      {formatPropertyName(k)}:
                    </Text>
                    <Paper
                      withBorder
                      p="sm"
                      radius="sm"
                      mt="sm"
                      bg="transparent"
                    >
                      {renderNestedValue(v, k)}
                    </Paper>
                  </>
                ) : (
                  <Text size="sm" c={textColor}>
                    <b>{formatPropertyName(k)}:</b>{" "}
                    {getStringValueForField(key ?? "", v)}
                  </Text>
                )}
              </Box>
            ))}
          </Stack>
        );
      }

      if (Array.isArray(value)) {
        return (
          <Stack gap="xs" pl="md">
            {value.map((item, index) => (
              <Box key={index}>
                {typeof item === "object" && item !== null ? (
                  <Paper withBorder p="sm" radius="sm" mt="sm" bg="transparent">
                    {renderNestedValue(item)}
                  </Paper>
                ) : (
                  <Text size="sm" pl="xs" pr="lg" c={textColor}>
                    {getStringValueForField(key ?? "", item)}
                  </Text>
                )}
              </Box>
            ))}
          </Stack>
        );
      }

      return (
        <Text size="sm" pl="xs" pr="lg" c={textColor}>
          {getStringValueForField(key ?? "", value)}
        </Text>
      );
    };

    if (
      fieldValue &&
      typeof fieldValue === "object" &&
      !Array.isArray(fieldValue)
    ) {
      // Check if there is a type difference between new/old value
      if (
        (typeof compareValue !== "object" && typeof fieldValue === "object") ||
        (typeof compareValue === "object" && typeof fieldValue !== "object")
      ) {
        // When comparing object to primitive (or null/array), show the entire object as changed
        const fieldId = getChangeId(change, parentPath ?? "root");
        const isExcluded = excludedChanges.has(fieldId);

        return (
          <Group wrap="nowrap">
            <Group
              justify="space-between"
              w="100%"
              bg={
                type === "before"
                  ? "rgba(255, 200, 200, 0.2)"
                  : "rgba(200, 255, 200, 0.2)"
              }
              p="xs"
              wrap="nowrap"
              style={{ opacity: isExcluded ? 0.5 : 1 }}
            >
              <Box>
                {renderNestedValue(fieldValue)}
              </Box>
              {allowRemoval &&
                (type === "after" ||
                  (type === "before" && change.value === null)) && (
                  <Tooltip
                    label={renderTooltipText(isExcluded, change, type)}
                    position="top"
                    withArrow
                  >
                    <ActionIcon
                      variant={isExcluded ? "filled" : "subtle"}
                      color={isExcluded ? "green" : "red"}
                      size="sm"
                      onClick={() => toggleChangeExclusion(fieldId)}
                    >
                      <IconX size={16} />
                    </ActionIcon>
                  </Tooltip>
                )}
            </Group>
            {type === "before" &&
              change.oldValue &&
              change.value !== undefined &&
              change.value !== null && (
                <IconArrowRight size={16} style={{ minWidth: 16 }} />
              )}
          </Group>
        );
      }

      // Both values are objects - do field-by-field comparison
      let keys = getSortedKeys(fieldValue, compareValue);
      keys = keys.filter((key) =>
        empty(fieldValue[key]) &&
        (empty(compareValue) || empty(compareValue[key]))
          ? false
          : true,
      );
      return keys.map((key, i) => {
        const thisValue = fieldValue[key];
        const otherValue = compareValue ? compareValue[key] : undefined;

        // Only show fields that actually changed
        if (JSON.stringify(thisValue) === JSON.stringify(otherValue)) {
          return null;
        }

        const fieldPath = parentPath ? `${parentPath}.${key}` : key;
        const fieldId = getChangeId(change, fieldPath);
        const isExcluded = excludedChanges.has(fieldId);

        return (
          <Group wrap="nowrap">
            <Group
              key={`${change.path}-${key}-${i}`}
              justify="space-between"
              w="100%"
              bg={
                type === "before"
                  ? "rgba(255, 200, 200, 0.2)"
                  : "rgba(200, 255, 200, 0.2)"
              }
              p="xs"
              wrap="nowrap"
              style={{ opacity: isExcluded ? 0.5 : 1 }}
            >
              <Box>
                <Text size="sm" pl="xs" pr="lg" c={textColor}>
                  <b>{formatPropertyName(key)}:</b>{" "}
                  {typeof thisValue !== "object" &&
                  thisValue !== null &&
                  thisValue !== undefined &&
                  thisValue !== ""
                    ? getStringValueForField(key ?? "", thisValue)
                    : typeof thisValue === "object" &&
                        thisValue !== null &&
                        thisValue !== undefined
                      ? ""
                      : "Empty"}
                </Text>
                {typeof thisValue === "object" &&
                  thisValue !== null &&
                  renderNestedValue(thisValue, key)}
              </Box>
              {allowRemoval &&
                (type === "after" ||
                  (type === "before" && change.value === null)) && (
                  <Tooltip
                    label={renderTooltipText(isExcluded, change, type)}
                    position="top"
                    withArrow
                  >
                    <ActionIcon
                      variant={isExcluded ? "filled" : "subtle"}
                      color={isExcluded ? "green" : "red"}
                      size="sm"
                      onClick={() => toggleChangeExclusion(fieldId)}
                    >
                      <IconX size={16} />
                    </ActionIcon>
                  </Tooltip>
                )}
            </Group>
            {type === "before" &&
              change.oldValue &&
              change.value !== undefined &&
              change.value !== null && (
                <IconArrowRight size={16} style={{ minWidth: 16 }} />
              )}
          </Group>
        );
      });
    } else if (Array.isArray(fieldValue)) {
      return fieldValue.map((item: unknown, index: number) => {
        const fieldPath = `[${index}]`;
        const fieldId = getChangeId(change, fieldPath);
        const isExcluded = excludedChanges.has(fieldId);

        return (
          <Group wrap="nowrap">
            <Group
              wrap="nowrap"
              w="100%"
              p="sm"
              key={`${change.path}-${index}`}
              justify="space-between"
              style={{ opacity: isExcluded ? 0.5 : 1 }}
              bg={
                type === "before"
                  ? "rgba(255, 200, 200, 0.2)"
                  : "rgba(200, 255, 200, 0.2)"
              }
            >
              {renderNestedValue(item)}
              {allowRemoval &&
                (type === "after" ||
                  (type === "before" && change.value === null)) && (
                  <Tooltip
                    label={renderTooltipText(isExcluded, change, type)}
                    position="top"
                    withArrow
                  >
                    <ActionIcon
                      variant={isExcluded ? "filled" : "subtle"}
                      color={isExcluded ? "green" : "red"}
                      size="sm"
                      onClick={() => toggleChangeExclusion(fieldId)}
                    >
                      <IconX size={16} />
                    </ActionIcon>
                  </Tooltip>
                )}
            </Group>
            {type === "before" &&
              change.oldValue &&
              change.value !== undefined &&
              change.value !== null && (
                <IconArrowRight size={16} style={{ alignSelf: "center" }} />
              )}
          </Group>
        );
      });
    }

    // Handle simple values
    const showFieldName =
      type === "before" || (type === "after" && !change.oldValue);

    // Get field name from path
    const pathParts = change.path.split("/");
    const fieldName = pathParts[pathParts.length - 1];

    const fieldId = getChangeId(change, parentPath ?? "root");
    const isExcluded = excludedChanges.has(fieldId);

    return (
      <Group wrap="nowrap">
        <Group
          key={fieldId}
          justify="space-between"
          wrap="nowrap"
          p="sm"
          w="100%"
          style={{ opacity: isExcluded ? 0.5 : 1 }}
          bg={
            type === "before"
              ? "rgba(255, 200, 200, 0.2)"
              : "rgba(200, 255, 200, 0.2)"
          }
        >
          <Text size="sm" pl="xs" pr="lg" c={textColor}>
            {showFieldName ? (
              <b>{formatPropertyName(fieldName ?? "")}: </b>
            ) : (
              ""
            )}
            {getStringValueForField(fieldName ?? "", fieldValue)}
          </Text>
          {allowRemoval &&
            (type === "after" ||
              (type === "before" && change.value === null)) && (
              <Tooltip
                label={renderTooltipText(isExcluded, change, type)}
                position="top"
                withArrow
              >
                <ActionIcon
                  variant={isExcluded ? "filled" : "subtle"}
                  color={isExcluded ? "green" : "red"}
                  size="sm"
                  onClick={() => toggleChangeExclusion(fieldId)}
                >
                  <IconX size={16} />
                </ActionIcon>
              </Tooltip>
            )}
        </Group>
        {type === "before" &&
          change.oldValue &&
          change.value !== undefined &&
          change.value !== null && (
            <IconArrowRight size={16} style={{ alignSelf: "center" }} />
          )}
      </Group>
    );
  };

  const renderTooltipText = (
    isExcluded: boolean,
    change: ChangesetItem,
    type: "before" | "after",
  ) => {
    const changeType =
      type === "before" && change.value === null ? "removal" : "addition";
    return isExcluded
      ? `Include this ${changeType}`
      : `Exclude this ${changeType}`;
  };

  const renderChanges = (
    changes: ChangesetItem[] | ChangesetSection[],
    groupName?: string,
  ) => {
    if (changes.length === 0) return null;

    // If leaf (ChangesetItem)
    if ("path" in changes[0]) {
      return (
        <Box key={groupName}>
          {(changes as ChangesetItem[]).map((change) => {
            const changeId = getChangeId(change, groupName || "root");
            const isExcluded = excludedChanges.has(changeId);

            return (
              <Box
                key={changeId}
                mb="xs"
                style={{ opacity: isExcluded ? 0.5 : 1 }}
              >
                {change.reason && (
                  <Text size="sm" pb="sm" c="dimmed">
                    {change.reason}
                  </Text>
                )}
                <Group
                  gap="xs"
                  align="stretch"
                  style={{ alignItems: "stretch" }}
                >
                  {change.oldValue !== undefined &&
                    change.oldValue !== null &&
                    !(
                      change.oldValue === false &&
                      typeof change.value === "object"
                    ) && (
                      <Box
                        style={{
                          flex: 1,
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <Stack
                          style={{
                            borderRadius: "4px",
                            flex: 1,
                          }}
                          gap="xs"
                        >
                          {renderChangeValue(change, "before", groupName)}
                        </Stack>
                      </Box>
                    )}

                  {change.value !== undefined && change.value !== null && (
                    <Box
                      style={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <Stack
                        style={{
                          borderRadius: "4px",
                          flex: 1,
                        }}
                        gap="xs"
                      >
                        {renderChangeValue(change, "after", groupName)}
                      </Stack>
                    </Box>
                  )}
                </Group>
              </Box>
            );
          })}
        </Box>
      );
    }

    // If group (ChangesetSection)
    return (
      <Stack gap="md" mt="xs">
        {(changes as ChangesetSection[]).map((changeGroup) => {
          const accordion = (
            <Accordion
              key={changeGroup.name}
              multiple
              defaultValue={[changeGroup.name]}
            >
              <Accordion.Item value={changeGroup.name}>
                <Accordion.Control>
                  <Group justify="space-between">
                    <Text fw={500} size="md">
                      {changeGroup.name}
                    </Text>
                    <Group gap="xs" mr="sm">
                      {renderBadge(changeGroup.changes)}
                    </Group>
                  </Group>
                </Accordion.Control>
                <Accordion.Panel>
                  {renderChanges(changeGroup.changes, changeGroup.name)}
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>
          );
          // Only wrap in Paper if this is not the most nested (i.e., if the children are not ChangesetItem)
          const isMostNested =
            changeGroup.changes.length > 0 && "path" in changeGroup.changes[0];
          return isMostNested ? (
            accordion
          ) : (
            <Paper withBorder key={changeGroup.name}>
              {accordion}
            </Paper>
          );
        })}
      </Stack>
    );
  };

  const renderBadge = (changes: ChangesetItem[] | ChangesetSection[]) => {
    if (changes.length === 0) return null;

    // Check if the first item is a ChangesetItem
    if ("readBackPerformed" in changes[0]) {
      const firstChange = changes[0] as ChangesetItem;
      if (firstChange.readBackPerformed) {
        return <Badge color="green">Readback Performed</Badge>;
      } else if (
        "readBackRequired" in firstChange &&
        firstChange.readBackRequired &&
        !firstChange.readBackPerformed
      ) {
        return <Badge color="red">Readback Required</Badge>;
      }
    }
    return null;
  };

  // Helper to render a single section
  const renderSection = (
    sectionTitle: string | undefined,
    sectionChanges: ChangesetItem[] | undefined,
  ) => {
    const safeChanges = Array.isArray(sectionChanges) ? sectionChanges : [];
    return (
      <Box mb="md" style={{ textAlign: "left" }}>
        {(() => {
          const organized = organizeChanges(safeChanges);

          return (
            <Stack>
              {organized.map((changeSection) => (
                <Box
                  key={String(changeSection.name ?? "")}
                  style={{ textAlign: "left" }}
                >
                  <Paper withBorder p="md" radius="md">
                    <Accordion
                      multiple
                      defaultValue={[String(changeSection.name ?? "")]}
                    >
                      <Accordion.Item
                        key={String(changeSection.name ?? "")}
                        value={String(changeSection.name ?? "")}
                      >
                        <Accordion.Control>
                          <Group justify="space-between">
                            <Text size="xl" fw={500}>
                              {changeSection.name}
                            </Text>
                            <Group gap="xs" mr="sm">
                              <Badge>
                                {changeSection.changes.length}{" "}
                                {changeSection.changes.length > 1
                                  ? "changes"
                                  : "change"}
                              </Badge>
                            </Group>
                          </Group>
                        </Accordion.Control>
                        <Accordion.Panel>
                          {renderChanges(changeSection.changes)}
                        </Accordion.Panel>
                      </Accordion.Item>
                    </Accordion>
                  </Paper>
                </Box>
              ))}
            </Stack>
          );
        })()}
      </Box>
    );
  };

  // Render logic
  if (sectionedChanges && sectionedChanges.length > 0) {
    return (
      <Accordion
        multiple
        defaultValue={[String(sectionedChanges[0]?.sectionTitle ?? "")]}
      >
        {sectionedChanges.map((section) => (
          <Accordion.Item
            key={section.sectionTitle}
            value={String(section.sectionTitle)}
          >
            <Accordion.Control>
              <Group justify="space-between">
                <Title order={5} mb="xs">
                  {section.sectionTitle}
                </Title>
                <Badge color="blue" mr={"sm"}>
                  {section.changes.length}{" "}
                  {section.changes.length > 1 ? "changes" : "change"}
                </Badge>
              </Group>
            </Accordion.Control>
            <Accordion.Panel>
              {renderSection(section.sectionTitle, section.changes)}
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </Accordion>
    );
  }
  // Fallback to old single-section logic
  return (
    <>
      {onClose && (
        <>
          <Group mb="md">
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              onClick={onClose}
            >
              Back
            </Button>
            <Title order={4}>Version History</Title>
          </Group>
          <Box mb="lg">
            <Title order={5}>{formattedDate}</Title>
            <Text c="dimmed">Changes made to patient record</Text>
          </Box>

          <Divider mb="md" />
        </>
      )}

      {changes && changes.length === 0 ? (
        <Text>No changes to display.</Text>
      ) : (
        <Stack>
          {organizeChanges().map((changeSection) => (
            <Box
              key={String(changeSection.name ?? "")}
              style={{ textAlign: "left" }}
            >
              <Paper withBorder p="md" radius="md">
                <Accordion
                  multiple
                  defaultValue={[String(changeSection.name ?? "")]}
                >
                  <Accordion.Item
                    key={String(changeSection.name ?? "")}
                    value={String(changeSection.name ?? "")}
                  >
                    <Accordion.Control>
                      <Group justify="space-between">
                        <Text size="xl" fw={500}>
                          {changeSection.name}
                        </Text>
                        <Group gap="xs" mr="sm">
                          <Badge>
                            {changeSection.changes.length}{" "}
                            {changeSection.changes.length > 1
                              ? "changes"
                              : "change"}
                          </Badge>
                        </Group>
                      </Group>
                    </Accordion.Control>
                    <Accordion.Panel>
                      {renderChanges(changeSection.changes)}
                    </Accordion.Panel>
                  </Accordion.Item>
                </Accordion>
              </Paper>
            </Box>
          ))}
        </Stack>
      )}
    </>
  );
}
