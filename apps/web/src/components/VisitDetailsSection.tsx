import { 
  Paper, 
  Title, 
  Text, 
  Group, 
  Button, 
  Stack, 
  Box, 
  Divider,
  Badge
} from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { Visit } from '../features/patient_chart';

interface VisitDetailsSectionProps {
  visit: Visit;
  onClose: () => void;
}

export function VisitDetailsSection({ visit, onClose }: VisitDetailsSectionProps) {
  const visitDate = new Date(visit.date);
  const formattedDate = visitDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  return (
    <Paper p="md" withBorder style={{ height: 'calc(100vh - 160px)', overflow: 'auto' }}>
      <Group mb="md">
        <Button 
          variant="subtle" 
          leftSection={<IconArrowLeft size={16} />}
          onClick={onClose}
        >
          Back
        </Button>
        <Title order={4}>Visit Details</Title>
      </Group>
      
      <Box mb="lg">
        <Group mb="xs">
          <Title order={5}>{formattedDate}</Title>
          <Badge color={visit.type === 'Routine' ? 'blue' : 'red'}>
            {visit.type}
          </Badge>
        </Group>
        <Text c="dimmed">Provider: {visit.provider.name}</Text>
      </Box>
      
      <Divider mb="md" />
      
      <Stack gap="lg">
        <Box>
          <Title order={5} mb="xs">Vital Signs</Title>
          <Group gap="lg">
            {visit.vitalSigns.map((vital) => (
              <Box key={vital.name}>
                <Text fw={500} size="sm">{vital.name}</Text>
                <Text size="lg">{vital.value} {vital.unit}</Text>
              </Box>
            ))}
          </Group>
        </Box>
        
        <Box>
          <Title order={5} mb="xs">Assessment</Title>
          <Text>{visit.assessment}</Text>
        </Box>
        
        <Box>
          <Title order={5} mb="xs">Plan</Title>
          <Text>{visit.plan}</Text>
        </Box>
        
        {visit.medications && visit.medications.length > 0 && (
          <Box>
            <Title order={5} mb="xs">Medications</Title>
            <Stack gap="xs">
              {visit.medications.map((med, index) => (
                <Box key={index}>
                  <Text fw={500}>{med.name}</Text>
                  <Text size="sm">
                    {med.dosage}, {med.route}, {med.frequency}
                  </Text>
                </Box>
              ))}
            </Stack>
          </Box>
        )}
        
        {visit.orders && visit.orders.length > 0 && (
          <Box>
            <Title order={5} mb="xs">Orders</Title>
            <Stack gap="xs">
              {visit.orders.map((order, index) => (
                <Box key={index}>
                  <Text fw={500}>{order.type}</Text>
                  <Text size="sm">{order.details}</Text>
                </Box>
              ))}
            </Stack>
          </Box>
        )}
      </Stack>
    </Paper>
  );
}
