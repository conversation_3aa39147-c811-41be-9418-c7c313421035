import { Divider, Box, Title, Paper } from '@mantine/core'
import { ResourceMarkdownView } from './ResourceMarkdownView'
import { Request } from '@hospice-os/apptypes'
import { VersionHistorySection } from './VersionHistorySection';

interface VisitNoteSectionProps {
  currentRequest: Request;
  handleCloseDetailView: () => void;
}

function VisitNoteSection({ currentRequest, handleCloseDetailView }: VisitNoteSectionProps) {  
  return (
    <Paper p="md" withBorder style={{ height: 'calc(100vh - 340px)', overflow: 'auto', textAlign: 'left' }}>
      <ResourceMarkdownView
        resourceId={currentRequest.noteCreated?.id || ''}
        resourceType={currentRequest.noteCreated?.resourceType || ''}
        onClose={handleCloseDetailView}
        pollingInterval={5000}
      />
      {currentRequest.changes &&
        <Paper p="md" withBorder>
          <Box mb="lg">
            <Title size="lg">Patient Record Changes</Title>
          </Box>

          <Divider mb="md" />
          <VersionHistorySection
            changes={currentRequest.changes}
          />
        </Paper>}
    </Paper>
  )
}

export default VisitNoteSection