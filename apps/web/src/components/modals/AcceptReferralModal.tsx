import { useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text, But<PERSON>, Alert, Group, List } from "@mantine/core";
import { IconAlertTriangle } from "@tabler/icons-react";
import { Request } from "@hospice-os/apptypes";
import { formatPropertyName } from "../../utils/schemaToMarkdown";

interface AcceptReferralModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: () => void;
  request?: Request;
}

export function AcceptReferralModal({
  opened,
  onClose,
  onConfirm,
  request,
}: AcceptReferralModalProps) {
  const validationData = useMemo(() => {
    if (!request?.validationErrors) return null;

    return (request.validationErrors ?? [])
      .filter((e) => e.keyword === "required")
      .reduce(
        (groups, e) => {
          const pathParts = e.instancePath
            .split("/")
            .filter((part: string) => part.length > 0);
          const groupKey = pathParts.length > 0 ? pathParts[0] : "other";

          if (!groups[groupKey]) {
            groups[groupKey] = [];
          }

          groups[groupKey].push({
            propertyName: e.params["missingProperty"] ?? "",
            errorType: e.keyword,
            instancePath: e.instancePath,
            message: e.message,
          });

          return groups;
        },
        {} as Record<
          string,
          Array<{
            propertyName: string;
            errorType: string;
            instancePath: string;
            message: string;
          }>
        >,
      );
  }, [request?.validationErrors]);

  const renderMissingFields = () => {
    if (!validationData) return null;

    return (
      <Stack gap="md">
        {Object.entries(validationData).map(([groupKey, errors]) => (
          <div key={groupKey}>
            <Text fw={600} size="sm" mb="xs">
              {groupKey === "other"
                ? ""
                : formatPropertyName(groupKey)}
            </Text>
            <List size="sm" spacing="xs">
              {(errors as any[]).map((error: any, index: number) => (
                <List.Item key={index}>
                  {formatPropertyName(error.propertyName)}
                </List.Item>
              ))}
            </List>
          </div>
        ))}
      </Stack>
    );
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Accept Referral as Complete"
      size="md"
      centered
    >
      <Stack gap="md">
        <Alert
          variant="light"
          color="orange"
          icon={<IconAlertTriangle size={16} />}
          title="Confirm Action"
        >
          <Text size="sm">
            You are about to accept this referral as complete despite missing
            information. <b>You can always add this information later.</b>
          </Text>
        </Alert>

        {validationData && Object.keys(validationData).length > 0 && (
          <>
            <Text fw={600} size="sm">
              The following information is still missing:
            </Text>
            {renderMissingFields()}
          </>
        )}

        <Text size="sm" c="dimmed">
          Are you sure you want to proceed and accept this referral as complete?
        </Text>

        <Group justify="flex-end" mt="md">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button color="orange" onClick={onConfirm}>
            Accept as Complete
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
