import React, { useState, useRef } from 'react';
import {
  Modal,
  Stack,
  TextInput,
  Box,
  Text,
  FileButton,
  Button,
  List,
  Group,
  Badge,
  ActionIcon,
  LoadingOverlay,
  Loader
} from '@mantine/core';
import { IconUpload, IconX } from '@tabler/icons-react';
import { Patient } from '@hospice-os/apptypes';
import { uuidv7 } from 'uuidv7';
import { useCreatePatientMutation } from '../../store/api_extensions/patientApiExt';
import { useCreateRequestMutation, useGetUploadUrlMutation } from '../../store/api_extensions/requestApiExt';
import { notifications } from '@mantine/notifications';

interface AddPatientModalProps {
  opened: boolean;
  onClose: () => void;
  onAddPatient: (patient: Partial<Patient>, documents: File[]) => void;
}

export function AddPatientModal({ opened, onClose, onAddPatient }: AddPatientModalProps) {
  const [newPatientFirstName, setNewPatientFirstName] = useState<string>('');
  const [newPatientLastName, setNewPatientLastName] = useState<string>(''); ``
  const [patientDocuments, setPatientDocuments] = useState<File[]>([]);
  const [isCreatingPatient, setIsCreatingPatient] = useState<boolean>(false);

  const [createPatient] = useCreatePatientMutation();
  const [getUploadUrl] = useGetUploadUrlMutation();
  const [createRequest] = useCreateRequestMutation();
  const resetRef = useRef<() => void>(null);


  // TODO - move to backend
  const handleAddPatient = async () => {
    setIsCreatingPatient(true)
    try {
      if (newPatientFirstName.trim() && newPatientLastName.trim()) {
        const requestId = uuidv7()

        // Create a new patient
        const newPatient: Partial<Patient> = {
          firstName: newPatientFirstName.trim(),
          lastName: newPatientLastName.trim(),
          status: 'Pending',
          createdAt: new Date().toISOString(),
          complianceTimeline: {
            timeLineItems: [
              {
                id: uuidv7(),
                createdAt: new Date().toISOString(),
                status: 'completed',
                startedAt: new Date().toISOString(),
                completedAt: new Date().toISOString(),
                title: 'Referral Received',
                type: 'ReferralReceived',
                requests: [{
                  resourceType: 'Request',
                  id: requestId
                }]
              },
              {
                id: uuidv7(),
                createdAt: new Date().toISOString(),
                status: patientDocuments.length > 0 ? 'in_progress' : 'pending',
                startedAt: new Date().toISOString(),
                title: 'Referral Processed',
                type: 'ReferralDataProcessed',
                requests: [{
                  resourceType: 'Request',
                  id: requestId
                }]
              },
              {
                id: uuidv7(),
                createdAt: new Date().toISOString(),
                status: 'not_started',
                title: 'Referral Accepted',
                type: 'ReferralAccepted'
              }
            ]
          },
          referralDetails: {
            dateAndTime: new Date().toISOString()
          }
        };

        const createdPatient = await createPatient({
          resourceType: 'Patient',
          schemaId: '6cb5c6e8-22c2-4df5-beb4-5cf2509e2673', // TODO
          ...newPatient
        }).unwrap()
        newPatient.id = createdPatient.id

        let allFilePaths = []

        // Upload files to S3
        if (patientDocuments.length > 0) {
          const uploadPromises = patientDocuments.map(async (file) => {
            const { url, filePath } = await getUploadUrl({
              fileName: file.name,
              fileType: file.type,
              patientId: createdPatient.id
            }).unwrap();

            // Upload file to S3 using pre-signed URL
            await fetch(url, {
              method: 'PUT',
              body: file,
              headers: {
                'Content-Type': file.type,
              },
            });

            return filePath;
          });

          const filePaths = await Promise.all(uploadPromises);
          allFilePaths.push(...filePaths);
        }

        const createdRequest = await createRequest({
          id: requestId,
          schemaId: 'fb016bdd-1d9e-489a-b05e-08bd7fcb188b', // TODO: hardcoded Request schema ID
          status: 'processing',
          fileAttachments: allFilePaths,
          title: "Referral Received",
          patient: { id: createdPatient.id, resourceType: 'Patient' },
          noteSchemaId: '581540b2-a9bf-4bc7-ba7f-275555f2f591',
          type: 'new_patient'
        }).unwrap();

        // Update the patient with the request id
        

        // Call the onAddPatient callback
        onAddPatient(newPatient, patientDocuments);

        // Reset form
        resetForm();
        onClose();

        notifications.show({
          title: 'Patient Created',
          message: `${newPatient.firstName} ${newPatient.lastName} has been created.`,
          color: 'green',
          position: 'top-right',
          withBorder: true
        })
      }
    } catch (error) {
      console.error('Error creating patient', error);
    } finally {
      setIsCreatingPatient(false)
    }
  };

  const resetForm = () => {
    setNewPatientFirstName('');
    setNewPatientLastName('');
    setPatientDocuments([]);
    resetRef.current?.();
    onClose();
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...patientDocuments];
    newFiles.splice(index, 1);
    setPatientDocuments(newFiles);
    console.log('newFiles', newFiles);
    // Reset the file input when a file is removed
    resetRef.current?.();
  };

  return (
    <Modal
      opened={opened}
      onClose={() => {
        resetForm();
      }}
      title="Add New Patient"
      size="md"
    >
      <LoadingOverlay visible={isCreatingPatient} loaderProps={{ children: <Loader size="md" /> }} />
      <Stack>
        <TextInput
          label="First Name"
          placeholder="Enter first name"
          value={newPatientFirstName}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => setNewPatientFirstName(event.currentTarget.value)}
          required
        />

        <TextInput
          label="Last Name"
          placeholder="Enter last name"
          value={newPatientLastName}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => setNewPatientLastName(event.currentTarget.value)}
          required
        />

        <Box>
          <Text size="sm" fw={500} mb="xs">Documents</Text>
          <FileButton onChange={(files) => files && setPatientDocuments([...patientDocuments, ...files])} accept="application/pdf,image/*,text/*" multiple resetRef={resetRef}>
            {(props) => (
              <Button variant="outline" leftSection={<IconUpload size={16} />} {...props} fullWidth>
                Upload Patient Documents
              </Button>
            )}
          </FileButton>

          <Group pt={15}>
            {patientDocuments.length > 0 && (
              <>
                <Stack gap="xs">
                  <Group gap="xs">
                    {patientDocuments.map((file, index) => (
                      <Badge
                        key={index}
                        color="blue"
                        size="md"
                        rightSection={
                          <ActionIcon
                            size="xs"
                            variant="transparent"
                            onClick={() => handleRemoveFile(index)}
                          >
                            <IconX size={15} color={"white"} />
                          </ActionIcon>
                        }
                      >
                        {file.name}
                      </Badge>
                    ))}
                  </Group>
                </Stack>
              </>
            )}
          </Group>

        </Box>

        <Button
          mt="md"
          onClick={handleAddPatient}
          disabled={!newPatientFirstName.trim() || !newPatientLastName.trim()}
        >
          Add Patient
        </Button>
      </Stack>
    </Modal>
  );
}