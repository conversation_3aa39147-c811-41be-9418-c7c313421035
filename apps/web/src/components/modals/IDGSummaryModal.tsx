import { <PERSON><PERSON><PERSON><PERSON>, Patient } from "@hospice-os/apptypes";
import {
  Box,
  Button,
  Group,
  Loader,
  LoadingOverlay,
  Modal,
  Paper,
  ScrollArea,
  Stack,
  Text,
} from "@mantine/core";
import { IconSend, IconUpload } from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { AudioRecorder } from "@hospice-os/ui-components";
import {
  useGetRequestByIdQuery,
  useGetUploadUrlMutation,
  useModifyChangesetMutation,
  useReviewRequestMutation,
} from "../../store/api_extensions/requestApiExt";
import { useGetCurrentUserQuery } from "../../store/api_extensions/userApiExt";
import { getAudioMimeType } from "../../utils/audioUtil";
import { ResourceMarkdownView } from "../ResourceMarkdownView";
import { VersionHistorySection } from "../VersionHistorySection";
import { invalidateResourceTag } from "../../store/api";
import { useAppDispatch } from "../../store/hooks";

interface IDGSummaryModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: () => void;
  selectedPatient: Patient;
  requestId: string;
}

export function IDGSummaryModal({
  opened,
  onClose,
  onSubmit,
  selectedPatient,
  requestId,
}: IDGSummaryModalProps) {
  const { data: currentUser } = useGetCurrentUserQuery();
  const [getUploadURL] = useGetUploadUrlMutation();
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Poll for the request to be updated with the summary & changes
  const { data: request } = useGetRequestByIdQuery(requestId, {
    skip: !requestId,
    pollingInterval: 5000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });
  const [filteredChanges, setFilteredChanges] = useState<ChangesetItem[]>(request?.changes || []);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [reviewRequest] = useReviewRequestMutation();
  const [modifyChangeset] = useModifyChangesetMutation();
  const handleRecordingComplete = (url: string) => {
    setAudioUrl(url);
  };
  const dispatch = useAppDispatch()

  useEffect(() => {
    setFilteredChanges(request?.changes || []);
  }, [request?.changes]);

  useEffect(() => {
    if (request?.status === "completed") {
      setIsSubmitting(false);
      invalidateResourceTag(dispatch, [
        {
          type: "Patient",
          id: selectedPatient.id,
        },
        {
          type: "Patient",
          id: "PATIENTS_LIST",
        },
      ]);
      onClose();
    }
  }, [request?.status, onClose]);

  const handleSubmit = async () => {
    if (!requestId) return;
    try {
      setIsSubmitting(true);
      console.log("filteredChanges submitted", filteredChanges);
      await reviewRequest({
        requestId: requestId,
        userId: currentUser?.id ?? "",
        manualChangeset: filteredChanges || request?.changes,
      }).unwrap();
      onSubmit()
    } catch (error) {
      console.error(error);
    }
  };

  const handleUpdate = async () => {
    if (!audioUrl) {
      console.error("No audio blob URL");
      return;
    }
    setIsUploading(true);
    try {
      const audioBlob = await fetch(audioUrl).then((res) => res.blob());
      const fileType = getAudioMimeType();

      const { url, filePath } = await getUploadURL({
        fileName: `recording-${Date.now()}.${fileType.split("/")[1]}`,
        fileType,
        patientId: selectedPatient.id,
      }).unwrap();

      // Upload audio recording to S3 using pre-signed URL
      await fetch(url, {
        method: "PUT",
        body: audioBlob,
        headers: {
          "Content-Type": fileType,
        },
      });

      console.log("Regenerating summary");
      await modifyChangeset({
        requestId: requestId,
        audioUrl: filePath,
      });
    } catch (error) {
      console.error(error);
    }
    setIsUploading(false);
  };

  return (
    <Modal
      opened={opened}
      closeButtonProps={{
        disabled: request?.status !== "completed",
      }}
      onClose={() => {
        if (request?.status === "completed") {
          onClose();
        }
      }}
      title={
        <Text size="xl" fw={700}>
          {`IDG Summary for ${selectedPatient.firstName + " " + selectedPatient.lastName}`}
        </Text>
      }
      size={10000}
    >
      <LoadingOverlay
        visible={
          isUploading || request?.status === "processing" || isSubmitting
        }
        h="100%"
        w="100%"
        loaderProps={{ children: <Loader size="md" /> }}
      />
      <Stack>
        <Group align="flex-start" grow>
          <Paper p="xs" withBorder style={{ width: "100%", minWidth: 0 }}>
            <Group justify="space-between" mb="xs">
              <Text fw={700} size="lg">
                IDG Summary
              </Text>
            </Group>
            <ScrollArea h="50vh">
              <Box>
                <ResourceMarkdownView
                  resourceId={request?.noteCreated?.id}
                  resourceType={request?.noteCreated?.resourceType}
                  pollingInterval={5000}
                />
              </Box>
            </ScrollArea>
          </Paper>
          <Paper p="xs" withBorder style={{ width: "100%", minWidth: 0 }}>
            <Group justify="space-between" mb="xs">
              <Text fw={700} size="lg">
                Patient Record Changes
              </Text>
            </Group>
            <ScrollArea h="50vh">
              <VersionHistorySection
                changes={request?.changes || []}
                allowRemoval={true}
                onChangesUpdate={setFilteredChanges}
              />
            </ScrollArea>
          </Paper>
        </Group>
        <Group mt="md">
          <Button
            onClick={handleSubmit}
            rightSection={<IconSend size={16} />}
            color="green"
          >
            Submit
          </Button>
          {isUploading ? (
            <Loader size="md" />
          ) : (
            <Group gap="xs">
              <AudioRecorder onRecordingComplete={handleRecordingComplete} />
              {audioUrl && (
                <Button
                  onClick={handleUpdate}
                  color="blue"
                  leftSection={<IconUpload size={16} />}
                >
                  Update
                </Button>
              )}
            </Group>
          )}
        </Group>
      </Stack>
    </Modal>
  );
}
