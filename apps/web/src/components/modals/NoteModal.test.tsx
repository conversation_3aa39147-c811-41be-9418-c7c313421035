/* eslint-disable @typescript-eslint/no-explicit-any */
import { render, screen, waitFor } from "@testing-library/react";
import { fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { NoteModal } from "./NoteModal";
import { Patient, ChangesetItem } from "@hospice-os/apptypes";
import { MantineProvider } from "@mantine/core";

// Mock external hooks and components used in NoteModal
vi.mock("../../store/api_extensions/schemaApiExt", () => ({
  useGetNoteTemplatesQuery: () => ({ data: [], isLoading: false }),
  useGetResolvedSchemaQuery: () => ({ data: null, isLoading: false }),
}));

let requestStatus: string | null = null;
const createRequestMock = vi.fn();
const getUploadUrlMock = vi.fn();
const sendMoreAudioMock = vi.fn();
const reviewRequestMock = vi.fn();
const updatePatientMock = vi.fn();

vi.mock("../../store/api_extensions/requestApiExt", () => ({
  useCreateRequestMutation: () => [createRequestMock],
  useGetRequestByIdQuery: () => ({
    data: requestStatus
      ? {
          status: requestStatus,
          noteCreated: { id: "note", resourceType: "Note" },
          changes: [],
        }
      : null,
  }),
  useGetUploadUrlMutation: () => [getUploadUrlMock],
  useSendMoreAudioMutation: () => [sendMoreAudioMock],
  useReviewRequestMutation: () => [reviewRequestMock],
}));

vi.mock("../../store/api_extensions/userApiExt", () => ({
  useGetCurrentUserQuery: () => ({ data: { id: "user" } }),
}));

vi.mock("../../store/api_extensions/patientApiExt", () => ({
  useGetPatientByIdQuery: () => ({ data: null }),
  useUpdatePatientMutation: () => [updatePatientMock],
}));

vi.mock("../../store/api", () => ({
  useGetUISchemasBySchemaQuery: () => ({ data: [], isLoading: false }),
  api: {
    injectEndpoints: vi.fn(() => ({ endpoints: {} })),
  },
}));

vi.mock("../../utils/schemaToMarkdown", () => ({
  schemaToMarkdown: vi.fn().mockResolvedValue(""),
}));

vi.mock("../../utils/audioUtil", () => ({
  getAudioMimeType: () => "audio/mp3",
}));

let mediaBlobUrl: string | null = null;

vi.mock("react-media-recorder", () => ({
  useReactMediaRecorder: () => ({
    status: "idle",
    startRecording: vi.fn(),
    stopRecording: vi.fn(),
    mediaBlobUrl,
  }),
}));

vi.mock("uuidv7", () => ({ uuidv7: () => "uuid" }));

vi.mock("../ResourceMarkdownView", () => ({
  ResourceMarkdownView: () => <div>ResourceMarkdownView</div>,
}));

vi.mock("../VersionHistorySection", () => ({
  VersionHistorySection: vi.fn(() => <div>VersionHistorySection Mock</div>),
}));

vi.mock("../CollapsibleMarkdown", () => ({
  CollapsibleMarkdown: () => <div>CollapsibleMarkdown</div>,
}));

import { VersionHistorySection } from "../VersionHistorySection";
const mockVersionHistorySection = vi.mocked(VersionHistorySection);

describe("NoteModal", () => {
  const patient: Patient = {
    id: "1",
    resourceType: "Patient",
    schemaId: "schema",
    firstName: "John",
    lastName: "Doe",
    dateOfBirth: "",
    updatedAt: "2024-01-01T00:00:00Z",
    createdAt: "2024-01-01T00:00:00Z",
  };

  const defaultProps = {
    opened: true,
    onClose: vi.fn(),
    onSubmit: vi.fn(),
    onComplete: vi.fn(),
    selectedPatient: patient,
  };

  beforeEach(() => {
    mediaBlobUrl = null;
    requestStatus = null;
    createRequestMock.mockReset();
    getUploadUrlMock.mockReset();
    sendMoreAudioMock.mockReset();
    reviewRequestMock.mockReset();
    updatePatientMock.mockReset();
    createRequestMock.mockReturnValue({
      unwrap: () => Promise.resolve({ id: "req1" }),
    });
    getUploadUrlMock.mockReturnValue({
      unwrap: () => Promise.resolve({ url: "upload", filePath: "file.mp3" }),
    });
    sendMoreAudioMock.mockReturnValue({ unwrap: () => Promise.resolve() });
    reviewRequestMock.mockReturnValue({ unwrap: () => Promise.resolve() });
    updatePatientMock.mockReturnValue({ unwrap: () => Promise.resolve() });
    (defaultProps.onClose as any).mockReset?.();
    (defaultProps.onSubmit as any).mockReset?.();
    (defaultProps.onComplete as any).mockReset?.();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders patient name in title", () => {
    render(
      <MantineProvider>
        <NoteModal {...defaultProps} />
      </MantineProvider>,
    );
    expect(
      screen.getByText("Add Chart Entry for John Doe"),
    ).toBeInTheDocument();
  });

  it("has disabled Process button with no input", () => {
    render(
      <MantineProvider>
        <NoteModal {...defaultProps} />
      </MantineProvider>,
    );
    const btn = screen.getByRole("button", { name: /process/i });
    expect(btn).toBeDisabled();
  });

  it("is not visible when closed", () => {
    render(
      <MantineProvider>
        <NoteModal {...defaultProps} opened={false} />
      </MantineProvider>,
    );
    expect(
      screen.queryByText("Add Chart Entry for John Doe"),
    ).not.toBeInTheDocument();
  });

  it("handles full workflow of processing and submitting audio", async () => {
    mediaBlobUrl = "blob:audio1";
    getUploadUrlMock.mockReturnValue({
      unwrap: () => Promise.resolve({ url: "upload1", filePath: "audio1.mp3" }),
    });
    createRequestMock.mockReturnValue({
      unwrap: () => Promise.resolve({ id: "req1" }),
    });
    (global as any).fetch = vi
      .fn()
      .mockResolvedValueOnce({
        blob: vi.fn().mockResolvedValue(new Blob(["a"])),
      })
      .mockResolvedValueOnce({});

    const { rerender } = render(
      <MantineProvider>
        <NoteModal {...defaultProps} />
      </MantineProvider>,
    );

    await waitFor(() =>
      expect(
        screen.getByRole("button", { name: /process/i }),
      ).not.toBeDisabled(),
    );
    fireEvent.click(screen.getByRole("button", { name: /process/i }));

    await waitFor(() => expect(createRequestMock).toHaveBeenCalled());
    expect(defaultProps.onSubmit).not.toHaveBeenCalled();

    // send additional audio
    mediaBlobUrl = "blob:audio2";
    getUploadUrlMock.mockReturnValue({
      unwrap: () => Promise.resolve({ url: "upload2", filePath: "audio2.mp3" }),
    });
    (global as any).fetch = vi
      .fn()
      .mockResolvedValueOnce({
        blob: vi.fn().mockResolvedValue(new Blob(["b"])),
      })
      .mockResolvedValueOnce({});
    rerender(
      <MantineProvider>
        <NoteModal {...defaultProps} />
      </MantineProvider>,
    );

    await waitFor(() =>
      expect(
        screen.getByRole("button", { name: /process/i }),
      ).not.toBeDisabled(),
    );
    fireEvent.click(screen.getByRole("button", { name: /process/i }));

    await waitFor(() =>
      expect(sendMoreAudioMock).toHaveBeenCalledWith({
        requestId: "req1",
        audioUrls: ["audio2.mp3"],
      }),
    );

    // finalize note
    requestStatus = "pending_review";
    rerender(
      <MantineProvider>
        <NoteModal {...defaultProps} />
      </MantineProvider>,
    );

    const submitButton = screen.getByRole("button", { name: /submit/i });
    expect(submitButton).not.toBeDisabled();
    fireEvent.click(submitButton);

    await waitFor(() =>
      expect(reviewRequestMock).toHaveBeenCalledWith({
        requestId: "req1",
        userId: "user",
      }),
    );
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  describe("VersionHistorySection integration", () => {
    const mockChanges: ChangesetItem[] = [
      {
        op: "replace",
        path: "/patient/demographics",
        name: "Demographics",
        fieldName: "demographics",
        value: { firstName: "John", lastName: "Doe" },
        oldValue: { firstName: "Jane", lastName: "Smith" },
        reason: "Updated patient name",
        readBackRequired: true,
        readBackPerformed: false,
      },
      {
        op: "replace",
        path: "/patient/vitals",
        name: "Vitals",
        fieldName: "vitals",
        value: { bloodPressure: "120/80", heartRate: 72 },
        oldValue: { bloodPressure: "110/70", heartRate: 68 },
        reason: "New vital signs recorded",
        readBackRequired: false,
        readBackPerformed: false,
      },
    ];

    let capturedOnChangesUpdate: ((changes: ChangesetItem[]) => void) | null =
      null;

    beforeEach(() => {
      capturedOnChangesUpdate = null;

      mockVersionHistorySection.mockImplementation(
        ({ onChangesUpdate, changes, allowRemoval }: any) => {
          // Capture the callback for use in tests
          capturedOnChangesUpdate = onChangesUpdate;

          return (
            <div data-testid="version-history-section">
              <div>Original changes: {changes?.length || 0}</div>
              {allowRemoval && (
                <button data-testid="exclude-first-change">
                  Exclude First Change
                </button>
              )}
              {allowRemoval && (
                <button data-testid="exclude-field-from-first-change">
                  Exclude Field From First Change
                </button>
              )}
            </div>
          );
        },
      );
    });

    it("calls onChangesUpdate when entire change is excluded", async () => {
      // Set up request with changes
      requestStatus = "pending_review";

      // Create a test component with an initial request
      const testProps = {
        ...defaultProps,
        request: {
          id: "req1",
          status: "pending_review" as const,
          resourceType: "Request" as const,
          schemaId: "test-schema",
          updatedAt: "2024-01-01T00:00:00Z",
          createdAt: "2024-01-01T00:00:00Z",
          noteCreated: { id: "note", resourceType: "Note" as const },
          changes: mockChanges,
        },
      };

      render(
        <MantineProvider>
          <NoteModal {...testProps} />
        </MantineProvider>,
      );

      // Wait for the component to render with pending_review state
      await waitFor(() => {
        expect(
          screen.getByTestId("version-history-section"),
        ).toBeInTheDocument();
      });

      // Verify the callback was captured
      expect(capturedOnChangesUpdate).toBeTruthy();

      // Simulate excluding the first change by calling the callback directly
      const filteredChanges = mockChanges.slice(1); // Remove first change
      capturedOnChangesUpdate!(filteredChanges);

      // Give React time to update state
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Submit the form
      const submitButton = screen.getByRole("button", { name: /submit/i });
      fireEvent.click(submitButton);

      // Verify that reviewRequest was called with filtered changes (only the second change)
      await waitFor(() =>
        expect(reviewRequestMock).toHaveBeenCalledWith({
          requestId: "req1",
          userId: "user",
          manualChangeset: [mockChanges[1]], // Only the second change should remain
        }),
      );
    });

    it("calls onChangesUpdate when field within change is excluded", async () => {
      // Set up request with changes
      requestStatus = "pending_review";

      // Create a test component with an initial request
      const testProps = {
        ...defaultProps,
        request: {
          id: "req1",
          status: "pending_review" as const,
          resourceType: "Request" as const,
          schemaId: "test-schema",
          updatedAt: "2024-01-01T00:00:00Z",
          createdAt: "2024-01-01T00:00:00Z",
          noteCreated: { id: "note", resourceType: "Note" as const },
          changes: mockChanges,
        },
      };

      render(
        <MantineProvider>
          <NoteModal {...testProps} />
        </MantineProvider>,
      );

      // Wait for the component to render with pending_review state
      await waitFor(() => {
        expect(
          screen.getByTestId("version-history-section"),
        ).toBeInTheDocument();
      });

      // Verify the callback was captured
      expect(capturedOnChangesUpdate).toBeTruthy();

      // Simulate excluding a field from the first change
      const modifiedChange = {
        ...mockChanges[0],
        value: { firstName: mockChanges[0].value.firstName }, // Remove lastName
        oldValue: { firstName: mockChanges[0].oldValue.firstName }, // Remove lastName
      };
      const filteredChanges = [modifiedChange, mockChanges[1]];
      capturedOnChangesUpdate!(filteredChanges);

      // Give React time to update state
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Submit the form
      const submitButton = screen.getByRole("button", { name: /submit/i });
      fireEvent.click(submitButton);

      // Verify that reviewRequest was called with modified changes
      await waitFor(() => {
        expect(reviewRequestMock).toHaveBeenCalledWith(
          expect.objectContaining({
            requestId: "req1",
            userId: "user",
            manualChangeset: expect.arrayContaining([
              expect.objectContaining({
                ...mockChanges[0],
                value: { firstName: "John" }, // lastName should be excluded
                oldValue: { firstName: "Jane" }, // lastName should be excluded
              }),
              mockChanges[1], // Second change should remain unchanged
            ]),
          }),
        );
      });
    });

    it("passes changes and allowRemoval=true to VersionHistorySection in pending_review state", () => {
      requestStatus = "pending_review";

      const testProps = {
        ...defaultProps,
        request: {
          id: "req1",
          status: "pending_review" as const,
          resourceType: "Request" as const,
          schemaId: "test-schema",
          updatedAt: "2024-01-01T00:00:00Z",
          createdAt: "2024-01-01T00:00:00Z",
          noteCreated: { id: "note", resourceType: "Note" as const },
          changes: mockChanges,
        },
      };

      render(
        <MantineProvider>
          <NoteModal {...testProps} />
        </MantineProvider>,
      );

      // Verify VersionHistorySection was called with allowRemoval=true
      expect(mockVersionHistorySection).toHaveBeenCalledWith(
        expect.objectContaining({
          allowRemoval: true,
          onChangesUpdate: expect.any(Function),
        }),
      );
    });

    it("passes changes but allowRemoval=undefined to VersionHistorySection in non-pending_review state", () => {
      requestStatus = "processing";

      const testProps = {
        ...defaultProps,
        selectedPatient: { ...patient, id: "test-patient" },
        request: {
          id: "req1",
          status: "processing" as const,
          resourceType: "Request" as const,
          schemaId: "test-schema",
          updatedAt: "2024-01-01T00:00:00Z",
          createdAt: "2024-01-01T00:00:00Z",
          noteCreated: { id: "note", resourceType: "Note" as const },
          changes: [],
        },
      };

      // Set selectedTemplate so VersionHistorySection gets rendered
      render(
        <MantineProvider>
          <NoteModal {...testProps} />
        </MantineProvider>,
      );

      // Select a template to trigger VersionHistorySection rendering
      const selectInput = screen.getByLabelText("Select an action");
      fireEvent.click(selectInput);

      // Add a mock template option and select it
      const templateOption = screen.getByRole("option", { name: /note/i });
      fireEvent.click(templateOption);

      // Wait for VersionHistorySection to be rendered
      waitFor(() => {
        expect(mockVersionHistorySection).toHaveBeenCalledWith(
          expect.objectContaining({
            changes: expect.any(Array),
          }),
        );
      });
    });

    it("updates filteredChanges state when onChangesUpdate is called", async () => {
      // Simple test to verify the state update mechanism works
      requestStatus = "pending_review";

      const testProps = {
        ...defaultProps,
        request: {
          id: "req1",
          status: "pending_review" as const,
          resourceType: "Request" as const,
          schemaId: "test-schema",
          updatedAt: "2024-01-01T00:00:00Z",
          createdAt: "2024-01-01T00:00:00Z",
          noteCreated: { id: "note", resourceType: "Note" as const },
          changes: mockChanges,
        },
      };

      render(
        <MantineProvider>
          <NoteModal {...testProps} />
        </MantineProvider>,
      );

      // Wait for the component to render
      await waitFor(() => {
        expect(
          screen.getByTestId("version-history-section"),
        ).toBeInTheDocument();
      });

      // Verify the callback was captured
      expect(capturedOnChangesUpdate).toBeTruthy();

      // Call the callback directly with some test changes
      const testChanges = [mockChanges[1]]; // Just the second change
      capturedOnChangesUpdate!(testChanges);

      // Give React time to update state
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Submit to see if the filtered changes are used
      const submitButton = screen.getByRole("button", { name: /submit/i });
      fireEvent.click(submitButton);

      // The key test: verify that the manualChangeset contains our test changes
      await waitFor(() =>
        expect(reviewRequestMock).toHaveBeenCalledWith(
          expect.objectContaining({
            requestId: "req1",
            userId: "user",
            manualChangeset: testChanges,
          }),
        ),
      );
    });
  });
});
