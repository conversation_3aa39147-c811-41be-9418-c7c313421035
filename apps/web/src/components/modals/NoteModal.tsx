import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import {
  Modal,
  Stack,
  Grid,
  Select,
  Paper,
  Group,
  Text,
  Badge,
  ScrollArea,
  Button,
  FileButton,
  Box,
  ActionIcon,
  LoadingOverlay,
  Loader,
  Alert,
  Tabs,
  Tooltip,
} from "@mantine/core";
import { IconUpload, IconSend, IconTemplate, IconX } from "@tabler/icons-react";
import { AudioRecorder, formatPropertyName } from "@hospice-os/ui-components";
import { useGetNoteTemplatesQuery } from "../../store/api_extensions/schemaApiExt";
import { schemaToMarkdown } from "../../utils/schemaToMarkdown";
import { useGetResolvedSchemaQuery } from "../../store/api_extensions/schemaApiExt";
import { CollapsibleMarkdown } from "../CollapsibleMarkdown";
import { useGetUISchemasBySchemaQuery } from "../../store/api";
import {
  useCreateRequestMutation,
  useGetRequestByIdQuery,
  useGetUploadUrlMutation,
  useSendMoreAudioMutation,
  useReviewRequestMutation,
} from "../../store/api_extensions/requestApiExt";
import { getAudioMimeType } from "../../utils/audioUtil";
import {
  Action,
  Request,
  Createable,
  Patient,
  ChangesetItem,
} from "@hospice-os/apptypes";
import {
  useGetPatientByIdQuery,
  useUpdatePatientMutation,
} from "../../store/api_extensions/patientApiExt";
import { ResourceMarkdownView } from "../ResourceMarkdownView";
import { VersionHistorySection } from "../VersionHistorySection";
import { IconInfoCircle } from "@tabler/icons-react";
import Markdown from "react-markdown";
import { useGetCurrentUserQuery } from "../../store/api_extensions/userApiExt";
import { useGetLastNoteSummaryQuery } from "../../store/api_extensions/noteApiExt";
import remarkGfm from "remark-gfm";
import ReactMarkdown from "react-markdown";

interface NoteModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (templateName: string, requestId: string) => void;
  selectedPatient: Patient;
  patientLabel?: string;
  fromAction?: Action;
  request?: Request;
}

export function NoteModal({
  opened,
  onClose,
  onSubmit,
  selectedPatient,
  patientLabel = "",
  fromAction,
  request,
}: NoteModalProps) {
  const [isReferralData, setIsReferralData] = useState<boolean>(false);
  const { data: currentUser } = useGetCurrentUserQuery();
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [templateFiles, setTemplateFiles] = useState<File[]>([]);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [templateContent, setTemplateContent] = useState<string>("");
  const resetRef = useRef<() => void>(null);
  // Fetch note templates
  const { data: noteTemplates = [], isLoading: isLoadingNoteTemplates } =
    useGetNoteTemplatesQuery(undefined, { skip: isReferralData });
  // TODO: still needed?
  // const { data: parentRequest } = useGetRequestByIdQuery(
  //   fromAction?.requestRef?.id || "",
  //   { skip: !fromAction?.requestRef?.id },
  // );

  // TODO: implement a more elegant way of handling this? (excluding specific note types, and determining what note types show the last note summary)
  const EXCLUDED_NOTE_TYPES = ["IDGPatientNote"];
  const HIDE_LAST_NOTE_SUMMARY_NOTE_TYPES = [
    "ChartUpdate",
    "CoordinationNote",
    "WrittenCTI",
    "VerbalCTI",
    "DeathDischarge",
  ];
  const getSchemaNoteType = (schema?: {
    schema?: {
      allOf?: Array<{ properties?: { noteType?: { const: string } } }>;
    };
  }) => {
    if (!schema?.schema?.allOf) return undefined;
    return schema.schema.allOf.find((item) => item.properties?.noteType?.const)
      ?.properties?.noteType?.const;
  };

  const [getUploadUrl] = useGetUploadUrlMutation();
  const [createRequest] = useCreateRequestMutation();

  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [currentRequestId, setCurrentRequestId] = useState<string | null>(
    request?.id || null,
  );
  const { data: patient } = useGetPatientByIdQuery(selectedPatient.id, {
    skip: !selectedPatient,
  });
  const [updatePatient] = useUpdatePatientMutation();

  const { data: currentRequest = request } = useGetRequestByIdQuery(
    currentRequestId || "",
    {
      skip: !currentRequestId,
      pollingInterval: 5000,
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
      refetchOnReconnect: true,
      skipPollingIfUnfocused: true,
    },
  );
  const [filteredChanges, setFilteredChanges] = useState<ChangesetItem[]>(
    currentRequest?.changes || [],
  );

  const [sendMoreAudio] = useSendMoreAudioMutation();
  const [reviewRequest] = useReviewRequestMutation();

  const [activeTab, setActiveTab] = useState<string | null>("currentVisit");

  // Fetch resolved schema for selected template
  const { data: resolvedTemplate, isLoading: isResolvedTemplateLoading } =
    useGetResolvedSchemaQuery(selectedTemplate!, {
      skip: !selectedTemplate,
    });

  const [recorderStatus, setRecorderStatus] = useState<string | null>(null);

  // Handle completed recordings from AudioRecorder
  const handleRecordingComplete = (url: string) => {
    console.log("setting audio url: ", url);
    setAudioUrl(url);
  };

  // Fetch UI schemas for this schema
  const { data: uiSchemas, isLoading: isLoadingUISchemas } =
    useGetUISchemasBySchemaQuery("a30b1e44-c7e2-4b5c-90cd-49dfda584e50", {
      skip: !selectedTemplate,
    }); // TODO: hardcoding NoteBaseUISchema ID for now

  useEffect(() => {
    if (selectedPatient) {
      setIsReferralData(selectedPatient.status === "Pending");
    }
  }, [fromAction]);

  useEffect(() => {
    if (isReferralData) {
      setSelectedTemplate("581540b2-a9bf-4bc7-ba7f-275555f2f591"); // Chart Update schema ID
    }
  }, [isReferralData]);

  useEffect(() => {
    if (currentRequest?.noteSchemaId) {
      setSelectedTemplate(currentRequest?.noteSchemaId);
    }
    setFilteredChanges(currentRequest?.changes || []);
  }, [currentRequest]);

  const { data: lastNoteSummary, isFetching: isLoadingLastNoteSummary } =
    useGetLastNoteSummaryQuery(
      { patientId: selectedPatient.id, noteSchemaId: selectedTemplate ?? "" },
      { skip: !selectedTemplate },
    );

  // Update template content when template selection changes
  useEffect(() => {
    const updateTemplateContent = async () => {
      if (isResolvedTemplateLoading) return;

      if (selectedTemplate) {
        const uiSchema =
          uiSchemas && uiSchemas.length > 0 ? uiSchemas[0]?.content : undefined;
        if (resolvedTemplate) {
          // Use resolved schema from API
          const markdown = await schemaToMarkdown(
            resolvedTemplate,
            undefined,
            uiSchema,
          );
          setTemplateContent(markdown);
        } else {
          // Fallback to raw template if resolved not available
          const template = noteTemplates.find((t) => t.id === selectedTemplate);
          if (template && template.schema) {
            const markdown = await schemaToMarkdown(
              template.schema,
              undefined,
              uiSchema,
            );
            setTemplateContent(markdown);
          }
        }
      } else {
        setTemplateContent("");
      }
    };

    updateTemplateContent();
  }, [
    selectedTemplate,
    noteTemplates,
    resolvedTemplate,
    patientLabel,
    uiSchemas,
    isResolvedTemplateLoading,
  ]);

  // Clear state when modal closes
  useEffect(() => {
    if (!opened) {
      setSelectedTemplate(null);
      setTemplateFiles([]);
      setAudioUrl(null);
      setTemplateContent("");
      setCurrentRequestId(null);
      // Clear the file input
      resetRef.current?.();
    }
  }, [opened]);

  // Handle file upload
  const handleFileUpload = (uploadedFiles: File[] | null) => {
    console.log("uploadedFiles", uploadedFiles);
    if (uploadedFiles && uploadedFiles.length > 0) {
      setTemplateFiles([...templateFiles, ...uploadedFiles]);
      console.log(
        `Added ${uploadedFiles.length} files to template ${selectedTemplate}`,
      );
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...templateFiles];
    newFiles.splice(index, 1);
    setTemplateFiles(newFiles);
    console.log("newFiles", newFiles);
    // Reset the file input when a file is removed
    resetRef.current?.();
  };

  // Process submission
  const handleProcess = async () => {
    // Capture all the values we need before closing the modal
    const capturedTemplate = selectedTemplate;
    const capturedFiles = [...templateFiles];
    const capturedAudioUrl = audioUrl;
    const capturedTemplateName =
      noteTemplates.find((t) => t.id === selectedTemplate)?.name || "Note";
    const capturedFromAction = fromAction ? { ...fromAction } : undefined;

    try {
      setIsUploading(true);
      const allFilePaths: string[] = [];
      // let s3AudioFilePath: string | null = null;

      // Upload audio recording to S3
      if (capturedAudioUrl) {
        const audioBlob = await fetch(capturedAudioUrl).then((res) =>
          res.blob(),
        );
        const fileType = getAudioMimeType();

        const { url, filePath } = await getUploadUrl({
          fileName: `recording-${Date.now()}.${fileType.split("/")[1]}`,
          fileType,
          patientId: selectedPatient.id,
        }).unwrap();

        // s3AudioFilePath = filePath;

        // Upload audio recording to S3 using pre-signed URL
        await fetch(url, {
          method: "PUT",
          body: audioBlob,
          headers: {
            "Content-Type": fileType,
          },
        });

        allFilePaths.push(filePath);
      }

      // Upload files to S3 for initial request only
      if (capturedFiles.length > 0) {
        const uploadPromises = capturedFiles.map(async (file) => {
          const { url, filePath } = await getUploadUrl({
            fileName: file.name,
            fileType: file.type,
            patientId: selectedPatient.id,
          }).unwrap();

          // Upload files to S3 using pre-signed URL
          await fetch(url, {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": file.type,
            },
          });

          return filePath;
        });

        const filePaths = await Promise.all(uploadPromises);
        allFilePaths.push(...filePaths);
      }

      if (currentRequestId) {
        if (allFilePaths.length > 0) {
          await sendMoreAudio({
            requestId: currentRequestId,
            audioUrls: allFilePaths,
          }).unwrap();
        }
      } else {
        const newRequest: Createable<Request> = {
          resourceType: "Request",
          schemaId: "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", // TODO: hardcoded Request schema ID
          fileAttachments: allFilePaths,
          title: `${capturedTemplateName}`,
          patient: { id: selectedPatient.id, resourceType: "Patient" },
          status: "processing",
          noteSchemaId: capturedTemplate ?? undefined,
        };
        if (capturedFromAction?.requestRef?.id) {
          newRequest.parentRequest = {
            id: capturedFromAction.requestRef.id,
            resourceType: "Request",
          };
        }

        const newPatient = { ...patient };

        const request = await createRequest(newRequest).unwrap();
        setCurrentRequestId(request.id);
        if (patient) {
          if (capturedFromAction) {
            const updatedAction = { ...capturedFromAction };
            updatedAction.completed = true;

            newPatient.agencyActions = newPatient?.agencyActions?.filter(
              (action) => action.uuid !== updatedAction.uuid,
            );
            newPatient?.agencyActions?.push(updatedAction);
          } else {
            let itemDescription = "";
            if (capturedFiles.length > 0) {
              itemDescription += `Documents uploaded: ${capturedFiles.map((f) => f.name).join(", ")}. `;
            }

            if (capturedAudioUrl) {
              itemDescription += "Audio recording uploaded";
            }
          }
          await updatePatient(newPatient).unwrap();
        }
      }

      setAudioUrl(null);
      setTemplateFiles([]);
      // Clear the file input
      resetRef.current?.();
    } catch (error) {
      console.error(error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async () => {
    if (!currentRequestId) return;
    try {
      const templateName =
        noteTemplates.find((t) => t.id === selectedTemplate)?.name || "Note";

      onClose();
      onSubmit(templateName, currentRequestId);

      await reviewRequest({
        requestId: currentRequestId,
        userId: currentUser?.id ?? "",
        manualChangeset: filteredChanges || currentRequest?.changes,
      }).unwrap();
    } catch (error) {
      // TODO: handle error
      console.error(error);
    }
  };

  const handleOnClose = () => {
    if (
      recorderStatus === "recording" ||
      recorderStatus === "paused" ||
      audioUrl
    ) {
      const confirmed = window.confirm(
        "You have an unsaved recording. Are you sure you want to exit? You can save your recording by clicking the Process button.",
      );
      if (!confirmed) {
        return;
      } else {
        onClose();
      }
    } else {
      onClose();
    }
  };

  const validationData = useMemo(() => {
    console.log("request", request);
    if (!request?.validationErrors) return null;

    return (request.validationErrors ?? [])
      .filter((e) => e.keyword === "required")
      .reduce(
        (groups, e) => {
          const pathParts = e.instancePath
            .split("/")
            .filter((part: string) => part.length > 0);
          const groupKey = pathParts.length > 0 ? pathParts[0] : "other";

          if (!groups[groupKey]) {
            groups[groupKey] = [];
          }

          groups[groupKey].push({
            propertyName: e.params["missingProperty"] ?? "",
            errorType: e.keyword,
            instancePath: e.instancePath,
            message: e.message,
          });

          return groups;
        },
        {} as Record<
          string,
          Array<{
            propertyName: string;
            errorType: string;
            instancePath: string;
            message: string;
          }>
        >,
      );
  }, [currentRequest?.validationErrors]);

  const renderMissingReferralInfo = useCallback(() => {
    if (!validationData) return null;

    let markdownContent = "";
    const hasErrors = Object.entries(validationData).length > 0;

    Object.entries(validationData).forEach(([groupKey, errors]) => {
      if (groupKey === "other") {
        // For "other" group, just show bulleted list without header
        (errors as any[]).forEach((error: any) => {
          markdownContent += `* ${formatPropertyName(error.propertyName)}\n`;
        });
      } else {
        // For other groups, show section header
        const sectionTitle = formatPropertyName(groupKey);
        markdownContent += `#### ${sectionTitle}\n`;
        (errors as any[]).forEach((error: any) => {
          markdownContent += `* ${formatPropertyName(error.propertyName)}\n`;
        });
        markdownContent += "\n";
      }
    });

    return (
      <Box mb="md">
        {hasErrors ? (
          <>
            <Alert
              w="100%"
              variant="light"
              color="red"
              title="Missing Information"
              icon={<IconInfoCircle />}
            >
              <Markdown>{markdownContent}</Markdown>
            </Alert>
          </>
        ) : (
          <Alert
            w="100%"
            variant="light"
            color="green"
            title="Referral Details Completed"
            icon={<IconInfoCircle />}
          >
            <Text>
              Please review the referral details for accuracy. If you need to
              make updates, record a new audio and dictate the changes you would
              like made. When you are ready to complete and accept this
              referral, press the <b>Submit</b> button below.
            </Text>
          </Alert>
        )}
      </Box>
    );
  }, [validationData]);

  return (
    <Modal
      opened={opened}
      onClose={handleOnClose}
      closeOnClickOutside={false}
      closeOnEscape={false}
      title={
        <>
          <Text size="xl" fw={700}>
            {fromAction
              ? `Respond to ${fromAction.title} Action`
              : isReferralData
                ? `Complete Referral for ${selectedPatient.firstName + " " + selectedPatient.lastName}`
                : `Add Chart Entry for ${selectedPatient.firstName + " " + selectedPatient.lastName}`}
          </Text>
          {isReferralData && (
            <Text size="sm" c="dimmed">
              Upload relevant documents and/or record audio to complete this
              referral.
            </Text>
          )}
        </>
      }
      fullScreen
    >
      <LoadingOverlay
        visible={isUploading || currentRequest?.status === "processing"}
        h="100%"
        w="100%"
        loaderProps={{ children: <Loader size="md" /> }}
      />
      {/* TODO: hardcoded PlanOfCare schema ID */}
      <Grid>
        <Grid.Col span={12}>
          {fromAction && <Text size="sm">{fromAction.content}</Text>}
        </Grid.Col>

        {/* Select an action picker for non-referral input */}
        {!isReferralData && (
          <Grid.Col span={12}>
            {isLoadingNoteTemplates ? (
              <Box ta="center">
                <Loader size="md" />
              </Box>
            ) : (
              <>
                {!fromAction && (
                  <Select
                    label="Select an action"
                    placeholder="Select an action"
                    data={[...noteTemplates]
                      .filter(
                        (n) =>
                          !EXCLUDED_NOTE_TYPES.includes(
                            getSchemaNoteType(n) ?? "",
                          ),
                      ) // TODO: keep the excluded note types hard coded?
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((template) => ({
                        value: template.id,
                        label: template.name,
                      }))}
                    value={selectedTemplate}
                    onChange={setSelectedTemplate}
                    clearable
                    searchable
                    disabled={
                      isUploading ||
                      currentRequest?.status === "pending_review" ||
                      currentRequest?.status === "processing"
                    }
                    nothingFoundMessage="No templates found"
                    leftSection={<IconTemplate size={16} />}
                  />
                )}
              </>
            )}
          </Grid.Col>
        )}

        <>
          {selectedTemplate && (
            <>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper
                  p="md"
                  withBorder
                  style={{ width: "100%", overflow: "scroll" }}
                  h="55vh"
                >
                  {!isReferralData ? (
                    <>
                      <Group justify="space-between" mb="xs">
                        <Text fw={700} size="lg">
                          {
                            noteTemplates.find((t) => t.id === selectedTemplate)
                              ?.name
                          }
                        </Text>
                      </Group>
                      {selectedTemplate ===
                        "581540b2-a9bf-4bc7-ba7f-275555f2f591" && (
                        <Text pb="20">
                          Record an update to this patient's chart.
                        </Text>
                      )}
                    </>
                  ) : (
                    <Text fw={700} size="lg" mb="sm">
                      Current Patient Chart
                    </Text>
                  )}

                  <Tabs
                    variant="pills"
                    value={activeTab}
                    onChange={setActiveTab}
                  >
                    {noteTemplates.length > 0 &&
                      !HIDE_LAST_NOTE_SUMMARY_NOTE_TYPES.includes(
                        getSchemaNoteType(
                          noteTemplates.find((t) => t.id === selectedTemplate)!,
                        ) ?? "",
                      ) && (
                        <Tabs.List pb="20">
                          <Tabs.Tab value="currentVisit">
                            Current Visit
                          </Tabs.Tab>
                          <Tabs.Tab value="pastVisit">
                            Last Visit Summary
                          </Tabs.Tab>
                        </Tabs.List>
                      )}

                    <Tabs.Panel value="currentVisit">
                      <ScrollArea>
                        <div style={{ textAlign: "left" }}>
                          {selectedTemplate ===
                          "581540b2-a9bf-4bc7-ba7f-275555f2f591" ? (
                            <>
                              <>{isReferralData && renderMissingReferralInfo()}</>
                              <ResourceMarkdownView
                                resourceId={`${patient?.id}`}
                                resourceType={patient?.resourceType}
                              />
                            </>
                          ) : currentRequest?.noteCreated?.id ? (
                            <ResourceMarkdownView
                              resourceId={currentRequest?.noteCreated?.id}
                              resourceType={
                                currentRequest?.noteCreated?.resourceType
                              }
                              pollingInterval={
                                currentRequest?.status === "pending_review"
                                  ? 5000
                                  : undefined
                              }
                            />
                          ) : (
                            <>
                              {isResolvedTemplateLoading ||
                              isLoadingUISchemas ? (
                                <Box ta="center">
                                  <Loader size="md" />
                                </Box>
                              ) : (
                                <CollapsibleMarkdown
                                  markdown={templateContent}
                                />
                              )}
                            </>
                          )}
                        </div>
                      </ScrollArea>
                    </Tabs.Panel>
                    <Tabs.Panel value="pastVisit">
                      <ScrollArea>
                        <div style={{ textAlign: "left" }}>
                          {isLoadingLastNoteSummary ? (
                            <Box ta="center">
                              <Loader size="md" />
                            </Box>
                          ) : (
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {lastNoteSummary}
                            </ReactMarkdown>
                          )}
                        </div>
                      </ScrollArea>
                    </Tabs.Panel>
                  </Tabs>
                </Paper>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper
                  p="md"
                  withBorder
                  style={{ width: "100%", overflow: "scroll" }}
                  h="55vh"
                >
                  <Group justify="space-between" mb="xs">
                    <Text fw={700} size="lg">
                      {isReferralData
                        ? "Added Referral Details"
                        : "Patient Record Changes"}
                    </Text>
                  </Group>
                  <ScrollArea>
                    {currentRequest?.changes ? (
                      <VersionHistorySection
                        changes={currentRequest?.changes || []}
                        onChangesUpdate={
                          currentRequest?.status === "pending_review"
                            ? setFilteredChanges
                            : undefined
                        }
                        allowRemoval={
                          currentRequest?.status === "pending_review" &&
                          !isReferralData
                        }
                      />
                    ) : (
                      <Text>
                        Changes to the patient record will appear here after
                        processing an audio recording.
                      </Text>
                    )}
                  </ScrollArea>
                </Paper>
              </Grid.Col>
            </>
          )}

          {!isReferralData && (
            <Grid.Col span={12}>
              <Paper p="xs" withBorder style={{ width: "100%" }}>
                <Group justify="space-between" mb="xs">
                  {currentRequest?.qa && currentRequest?.qa !== "" ? (
                    <Alert
                      w="100%"
                      variant="light"
                      color="red"
                      title="QA Audit Warnings"
                      icon={<IconInfoCircle />}
                    >
                      Please resolve the following items before submitting:
                      <Markdown>{currentRequest?.qa}</Markdown>
                    </Alert>
                  ) : currentRequest?.qa === "" ? (
                    <Alert
                      w="100%"
                      variant="light"
                      color="green"
                      title="QA Audit Passed"
                    />
                  ) : null}
                </Group>
              </Paper>
            </Grid.Col>
          )}

          <Grid.Col span={12}>
            <Group grow>
              <FileButton
                onChange={handleFileUpload}
                accept="application/pdf,image/*,text/*"
                multiple
                resetRef={resetRef}
              >
                {(props) => (
                  <Button
                    variant="outline"
                    leftSection={<IconUpload size={16} />}
                    {...props}
                  >
                    Upload Documents
                  </Button>
                )}
              </FileButton>

              <AudioRecorder
                onRecordingComplete={handleRecordingComplete}
                onStatusChange={setRecorderStatus}
              />
            </Group>
          </Grid.Col>

          <Grid.Col span={12}>
            <Group>
              {templateFiles.length > 0 && (
                <>
                  <Stack gap="xs">
                    <Text fw={500} size="sm">
                      Documents Selected:
                    </Text>
                    <Group gap="xs">
                      {templateFiles.map((file, index) => (
                        <Badge
                          key={index}
                          color="blue"
                          size="md"
                          rightSection={
                            <ActionIcon
                              size="xs"
                              variant="transparent"
                              onClick={() => handleRemoveFile(index)}
                            >
                              <IconX size={15} color={"white"} />
                            </ActionIcon>
                          }
                        >
                          {file.name}
                        </Badge>
                      ))}
                    </Group>
                  </Stack>
                </>
              )}
            </Group>
          </Grid.Col>

          {/* Audio Recording Preview */}
          {audioUrl && (
            <Grid.Col span={12}>
              <Box>
                <Group align="center" mb="xs">
                  <Text fw={500} size="sm">
                    Audio Recording:
                  </Text>
                  {/* <Button
                    size="xs"
                    variant="light"
                    onClick={processAudioInput}
                    loading={isProcessing}
                    leftSection={<IconSparkles size={14} />}
                  >
                    Process
                  </Button> */}
                </Group>
                <audio src={audioUrl} controls style={{ width: "100%" }}>
                  <track kind="captions" src="" label="English captions" />
                </audio>
              </Box>
            </Grid.Col>
          )}

          <Grid.Col span={12}>
            <Group mt="md">
              <Tooltip
                label="Please record audio or upload files before processing"
                disabled={
                  !(
                    isUploading ||
                    !selectedTemplate ||
                    (selectedTemplate &&
                      !audioUrl &&
                      templateFiles.length === 0) ||
                    false
                  )
                }
              >
                <Button
                  onClick={handleProcess}
                  rightSection={<IconSend size={16} />}
                  disabled={
                    isUploading ||
                    !selectedTemplate ||
                    (selectedTemplate &&
                      !audioUrl &&
                      templateFiles.length === 0) ||
                    false
                  }
                >
                  Process
                </Button>
              </Tooltip>
              <Tooltip
                label="Please add the required information before submitting"
                disabled={!currentRequest?.validationErrors || currentRequest?.validationErrors?.length === 0}
              >
                <Button
                  onClick={handleSubmit}
                  rightSection={<IconSend size={16} />}
                  color="green"
                  disabled={
                    isUploading ||
                    currentRequest?.status !== "pending_review" ||
                    (currentRequest?.validationErrors?.length ?? 0) > 0
                  }
                >
                  Submit
                </Button>
              </Tooltip>
            </Group>
          </Grid.Col>
        </>
      </Grid>
    </Modal>
  );
}
