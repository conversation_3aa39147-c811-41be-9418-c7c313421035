import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Stack,
  Select,
  Paper,
  Group,
  Text,
  Badge,
  ScrollArea,
  Button,
  FileButton,
  Box,
} from "@mantine/core";
import {
  IconUpload,
  IconSend,
  IconSparkles,
  IconTemplate,
} from "@tabler/icons-react";
import { AudioRecorder } from "@hospice-os/ui-components";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useGetOrderTemplatesQuery } from "../../store/api_extensions/schemaApiExt";

interface OrderModalProps {
  opened: boolean;
  onClose: () => void;
  selectedPatient: string | null;
  patientLabel?: string;
  onSubmit: (templateContent: string, templateName: string) => void;
}

export function OrderModal({
  opened,
  onClose,
  selectedPatient,
  patientLabel = "",
  onSubmit,
}: OrderModalProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [templateFiles, setTemplateFiles] = useState<File[]>([]);
  const [templateTranscriptions, setTemplateTranscriptions] = useState<
    string[]
  >([]);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [templateContent, setTemplateContent] = useState<string>("");

  // Fetch order templates
  const { data: orderTemplates = [] } = useGetOrderTemplatesQuery();

  // Handle completed recordings from AudioRecorder
  const handleRecordingComplete = (url: string) => {
    setAudioUrl(url);
  };

  // Define types for schema sections and fields
  interface SchemaField {
    name: string;
    label?: string;
    placeholder?: string;
    type?: string;
  }

  interface SchemaSection {
    title: string;
    fields?: SchemaField[];
  }

  // Convert schema to markdown
  const schemaToMarkdown = (schema: Record<string, unknown>): string => {
    // This is a simplified conversion - in a real app, you would have a more robust conversion
    let markdown = `# ${schema.title || "Order"}\n\n`;

    // Add patient information
    markdown += `**Patient Name:** ${patientLabel || "[Patient Name]"}\n`;
    markdown += `**Patient ID:** ${selectedPatient || "[Patient ID]"}\n`;
    markdown += `**Date:** ${new Date().toLocaleDateString()}\n`;
    markdown += `**Provider:** [Provider Name]\n\n`;

    // Add sections based on schema properties
    if (schema.sections && Array.isArray(schema.sections)) {
      (schema.sections as SchemaSection[]).forEach((section) => {
        if (section.title) {
          markdown += `## ${section.title}\n`;

          if (section.fields && Array.isArray(section.fields)) {
            section.fields.forEach((field) => {
              markdown += `**${field.label || field.name}:** ${field.placeholder || "[Enter value]"}\n`;
            });
          }

          markdown += "\n";
        }
      });
    }

    return markdown;
  };

  // Update template content when template selection changes
  useEffect(() => {
    if (selectedTemplate) {
      const template = orderTemplates.find((t) => t.id === selectedTemplate);
      if (template && template.schema) {
        // Convert schema to markdown
        const markdown = schemaToMarkdown(template.schema);
        console.log("Generated markdown for order template:", markdown);
        setTemplateContent(markdown);
      }
    } else {
      setTemplateContent("");
    }
  }, [selectedTemplate, orderTemplates, patientLabel]);

  // Clear state when modal closes
  useEffect(() => {
    if (!opened) {
      setSelectedTemplate(null);
      setTemplateFiles([]);
      setTemplateTranscriptions([]);
      setAudioUrl(null);
      setTemplateContent("");
    }
  }, [opened]);

  // Handle file upload
  const handleFileUpload = (uploadedFiles: File[] | null) => {
    if (uploadedFiles && uploadedFiles.length > 0) {
      setTemplateFiles([...templateFiles, ...uploadedFiles]);
      console.log(
        `Added ${uploadedFiles.length} files to template ${selectedTemplate}`,
      );
    }
  };

  // Process audio recording
  const processAudioInput = () => {
    if (!audioUrl) {
      return;
    }

    setIsProcessing(true);

    // Simulate transcription and processing delay
    setTimeout(() => {
      // Simulate transcription result
      const simulatedTranscription = `${patientLabel} reports improved pain control with current medication regimen.`;

      setTemplateTranscriptions([
        ...templateTranscriptions,
        simulatedTranscription,
      ]);
      console.log(
        `Added transcription to template ${selectedTemplate}: ${simulatedTranscription}`,
      );

      // Clear the audio URL
      setAudioUrl(null);
      setIsProcessing(false);
    }, 1500);
  };

  // Handle submit
  const handleSubmit = () => {
    if (selectedTemplate && templateContent) {
      const templateName =
        orderTemplates.find((t) => t.id === selectedTemplate)?.name || "Order";
      onSubmit(templateContent, templateName);
      onClose();
    }
  };

  return (
    <Modal opened={opened} onClose={onClose} title="Add Order" size="lg">
      <Stack>
        <Select
          label="Select a template"
          placeholder="Choose a template"
          data={orderTemplates.map((template) => ({
            value: template.id,
            label: template.name,
          }))}
          value={selectedTemplate}
          onChange={setSelectedTemplate}
          clearable
          searchable
          nothingFoundMessage="No templates found"
          leftSection={<IconTemplate size={16} />}
        />

        {selectedTemplate && (
          <>
            <Paper p="xs" withBorder style={{ width: "100%" }}>
              <Group justify="space-between" mb="xs">
                <Text fw={700} size="lg">
                  {orderTemplates.find((t) => t.id === selectedTemplate)?.name}
                </Text>
                <Group>
                  {templateFiles.length > 0 && (
                    <Badge color="blue" size="sm">
                      {templateFiles.length} Document
                      {templateFiles.length > 1 ? "s" : ""}
                    </Badge>
                  )}
                  {templateTranscriptions.length > 0 && (
                    <Badge color="green" size="sm">
                      {templateTranscriptions.length} Recording
                      {templateTranscriptions.length > 1 ? "s" : ""}
                    </Badge>
                  )}
                </Group>
              </Group>
              <ScrollArea h={350}>
                <div style={{ textAlign: "left" }}>
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {templateContent}
                  </ReactMarkdown>
                </div>
              </ScrollArea>
            </Paper>

            <Group grow>
              <FileButton
                onChange={handleFileUpload}
                accept="application/pdf,image/*,text/*"
                multiple
              >
                {(props) => (
                  <Button
                    variant="outline"
                    leftSection={<IconUpload size={16} />}
                    {...props}
                  >
                    Upload Documents
                  </Button>
                )}
              </FileButton>

              <AudioRecorder onRecordingComplete={handleRecordingComplete} />
            </Group>

            {/* Audio Recording Preview */}
            {audioUrl && (
              <Box>
                <Group align="center" mb="xs">
                  <Text fw={500} size="sm">
                    Audio Recording:
                  </Text>
                  <Button
                    size="xs"
                    variant="light"
                    onClick={processAudioInput}
                    loading={isProcessing}
                    leftSection={<IconSparkles size={14} />}
                  >
                    Process
                  </Button>
                </Group>
                <audio src={audioUrl} controls style={{ width: "100%" }}>
                  <track kind="captions" src="" label="English captions" />
                </audio>
              </Box>
            )}

            <Button
              onClick={handleSubmit}
              rightSection={<IconSend size={16} />}
              color="green"
              disabled={!selectedTemplate}
              mt="md"
            >
              Submit
            </Button>
          </>
        )}
      </Stack>
    </Modal>
  );
}
