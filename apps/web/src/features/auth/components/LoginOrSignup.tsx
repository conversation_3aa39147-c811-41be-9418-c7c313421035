import { StytchB2B } from '@stytch/react/b2b';
import { AuthFlowType, B2BOAuthProviders, B2BProducts } from '@stytch/vanilla-js/b2b';

export const LoginOrSignup = () => {
  const config = {
    products: [B2BProducts.emailMagicLinks, B2BProducts.passwords, B2BProducts.oauth],
    authFlowType: AuthFlowType.Discovery,
    sessionOptions: {
      sessionDurationMinutes: 60 * 8, // 8 hours // TODO: set to 1 hour but refresh the session as needed
    },
    oauthOptions: {
      providers: [{ type: B2BOAuthProviders.Google }],
      loginRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
      signupRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
      discoveryRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
    },
    emailMagicLinksOptions: {
      loginRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
      signupRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
      discoveryRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
    },
    ssoOptions: {
      loginRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
      signupRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
      discoveryRedirectURL: import.meta.env.VITE_REDIRECT_URL + '/authenticate',
    },
    directLoginForSingleMembership: {
      status: true,
      ignoreInvites: true,
      ignoreJitProvisioning: true
    }
  };

  const styles = {
    fontFamily: "Arial",
    hideHeaderText: true,
    container: {
      backgroundColor: "#FFFFFF",
      width: "400px",
      borderColor: "#FFFFFF",
    },
    colors: {
      primary: "#19303D",
      success: "#0C5A56",
      error: "#8B1214"
    },
    buttons: {
      primary: {
        backgroundColor: "#19303D",
        textColor: "#FFFFFF",
      }
    },
    inputs: {
      borderColor: "#19303D",
      borderRadius: "4px",
    },
  };

  return <StytchB2B config={config} styles={styles} />;
};
