import { createContext, useContext, ReactNode } from 'react';
import { useGetCurrentUserQuery } from '../../../store/api_extensions/userApiExt';

// Define the User context type
interface UserContextType {
  user: {
    id: string;
    resourceType: 'User';
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    agencyId: string;
    stytchId?: string;
  } | null;
  isLoading: boolean;
  error: unknown;
}

// Create the context with a default value
const UserContext = createContext<UserContextType>({
  user: null,
  isLoading: false,
  error: null,
});

// Provider component
export function UserProvider({ children }: { children: ReactNode }) {
  const { data: user, isLoading, error } = useGetCurrentUserQuery();

  return (
    <UserContext.Provider value={{ user: user || null, isLoading, error }}>
      {children}
    </UserContext.Provider>
  );
}

// Hook to use the user context
export function useUser() {
  return useContext(UserContext);
}
