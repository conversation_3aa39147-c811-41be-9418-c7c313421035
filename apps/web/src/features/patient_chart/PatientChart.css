/* Patient Chart Styles */

.timeline-entry {
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.timeline-entry:hover {
  background-color: var(--mantine-color-gray-0);
}

.timeline-entry-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-entry-content {
  margin-bottom: 8px;
}

.timeline-entry-metadata {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--mantine-color-gray-6);
}

.timeline-entry-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  margin-right: 4px;
}

.timeline-entry-tag.document {
  background-color: var(--mantine-color-blue-1);
  color: var(--mantine-color-blue-8);
}

.timeline-entry-tag.medication {
  background-color: var(--mantine-color-green-1);
  color: var(--mantine-color-green-8);
}

.timeline-entry-tag.audio {
  background-color: var(--mantine-color-orange-1);
  color: var(--mantine-color-orange-8);
}

.timeline-entry-tag.visit {
  background-color: var(--mantine-color-violet-1);
  color: var(--mantine-color-violet-8);
}

.patient-info-section {
  margin-bottom: 24px;
}

.patient-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.patient-info-name {
  font-size: 1.5rem;
  font-weight: 600;
}

.patient-info-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.patient-info-item {
  margin-bottom: 8px;
}

.patient-info-label {
  font-size: 0.8rem;
  color: var(--mantine-color-gray-6);
  margin-bottom: 4px;
}

.patient-info-value {
  font-weight: 500;
}

.vital-sign {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--mantine-color-gray-0);
  min-width: 100px;
  text-align: center;
}

.vital-sign-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.vital-sign-label {
  font-size: 0.8rem;
  color: var(--mantine-color-gray-6);
}

.medication-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.medication-item {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--mantine-color-gray-3);
}

.medication-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.medication-details {
  font-size: 0.9rem;
  color: var(--mantine-color-gray-7);
}

.visit-note {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--mantine-color-gray-3);
  margin-bottom: 16px;
}

.visit-note-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.visit-note-title {
  font-weight: 600;
}

.visit-note-date {
  font-size: 0.9rem;
  color: var(--mantine-color-gray-6);
}

.visit-note-content {
  margin-bottom: 12px;
}

.visit-note-footer {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--mantine-color-gray-6);
}
