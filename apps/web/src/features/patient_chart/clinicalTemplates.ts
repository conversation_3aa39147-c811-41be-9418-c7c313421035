/**
 * Clinical Templates for Hospice Documentation
 * 
 * These templates provide structured data for various clinical assessments
 * and documentation needs in hospice care.
 */

/**
 * Comprehensive Assessment Template
 */
export const comprehensiveAssessmentTemplate = {
  title: "Comprehensive Assessment",
  sections: [
    {
      title: "Physical Assessment",
      fields: [
        {
          name: "vitalSigns",
          label: "Vital Signs",
          type: "group",
          fields: [
            { name: "bloodPressure", label: "Blood Pressure", type: "text", required: true },
            { name: "heartRate", label: "Heart Rate", type: "number", unit: "bpm", required: true },
            { name: "respiratoryRate", label: "Respiratory Rate", type: "number", unit: "breaths/min", required: true },
            { name: "temperature", label: "Temperature", type: "number", unit: "°F", required: true },
            { name: "oxygenSaturation", label: "Oxygen Saturation", type: "number", unit: "%", required: true },
            { name: "weight", label: "Weight", type: "number", unit: "lbs" }
          ]
        },
        {
          name: "painAssessment",
          label: "Pain Assessment",
          type: "group",
          fields: [
            { name: "painLevel", label: "Pain Level (0-10)", type: "number", min: 0, max: 10, required: true },
            { name: "painLocation", label: "Pain Location", type: "text", required: true },
            { name: "painCharacteristics", label: "Pain Characteristics", type: "select", options: [
              "Sharp", "Dull", "Burning", "Aching", "Throbbing", "Stabbing", "Cramping", "Other"
            ]},
            { name: "painFrequency", label: "Pain Frequency", type: "select", options: [
              "Constant", "Intermittent", "Occasional", "Rare"
            ]},
            { name: "painRelieving", label: "Pain Relieving Factors", type: "text" },
            { name: "painAggravating", label: "Pain Aggravating Factors", type: "text" }
          ]
        },
        {
          name: "neurologicalStatus",
          label: "Neurological Status",
          type: "group",
          fields: [
            { name: "consciousness", label: "Level of Consciousness", type: "select", options: [
              "Alert", "Lethargic", "Obtunded", "Stuporous", "Comatose"
            ], required: true },
            { name: "orientation", label: "Orientation", type: "multiselect", options: [
              "Person", "Place", "Time", "Situation"
            ], required: true },
            { name: "pupilReaction", label: "Pupil Reaction", type: "select", options: [
              "Equal and Reactive", "Unequal", "Sluggish", "Fixed"
            ]},
            { name: "motorStrength", label: "Motor Strength", type: "select", options: [
              "5/5 - Normal", "4/5 - Reduced", "3/5 - Against Gravity", "2/5 - With Gravity Eliminated", "1/5 - Trace", "0/5 - No Movement"
            ]}
          ]
        },
        {
          name: "respiratoryStatus",
          label: "Respiratory Status",
          type: "group",
          fields: [
            { name: "respiratoryPattern", label: "Respiratory Pattern", type: "select", options: [
              "Regular", "Irregular", "Labored", "Shallow", "Deep", "Cheyne-Stokes"
            ], required: true },
            { name: "breathSounds", label: "Breath Sounds", type: "select", options: [
              "Clear", "Diminished", "Wheezes", "Crackles", "Rhonchi", "Absent"
            ], required: true },
            { name: "oxygenUse", label: "Oxygen Use", type: "boolean" },
            { name: "oxygenAmount", label: "Oxygen Amount", type: "text", conditional: { field: "oxygenUse", value: true } }
          ]
        }
      ]
    },
    {
      title: "Psychosocial Assessment",
      fields: [
        {
          name: "moodAssessment",
          label: "Mood Assessment",
          type: "group",
          fields: [
            { name: "mood", label: "Current Mood", type: "select", options: [
              "Euthymic", "Depressed", "Anxious", "Irritable", "Labile", "Apathetic"
            ], required: true },
            { name: "affect", label: "Affect", type: "select", options: [
              "Appropriate", "Flat", "Blunted", "Constricted", "Labile"
            ], required: true },
            { name: "suicidalIdeation", label: "Suicidal Ideation", type: "boolean", required: true },
            { name: "suicidalPlan", label: "Suicidal Plan", type: "text", conditional: { field: "suicidalIdeation", value: true } }
          ]
        },
        {
          name: "copingAssessment",
          label: "Coping Assessment",
          type: "group",
          fields: [
            { name: "copingMechanisms", label: "Coping Mechanisms", type: "multiselect", options: [
              "Prayer/Spirituality", "Family Support", "Friends Support", "Hobbies", "Meditation", "Exercise", "Therapy", "Other"
            ]},
            { name: "copingEffectiveness", label: "Coping Effectiveness", type: "select", options: [
              "Effective", "Somewhat Effective", "Ineffective"
            ]},
            { name: "supportSystem", label: "Support System", type: "text", required: true }
          ]
        }
      ]
    },
    {
      title: "Spiritual Assessment",
      fields: [
        { name: "spiritualBeliefs", label: "Spiritual Beliefs", type: "text" },
        { name: "religiousAffiliation", label: "Religious Affiliation", type: "text" },
        { name: "spiritualConcerns", label: "Spiritual Concerns", type: "textarea" },
        { name: "spiritualSupport", label: "Spiritual Support Needed", type: "boolean" },
        { name: "spiritualSupportType", label: "Type of Spiritual Support", type: "text", conditional: { field: "spiritualSupport", value: true } }
      ]
    }
  ]
};

/**
 * Visit Note Template
 */
export const visitNoteTemplate = {
  title: "Visit Note",
  sections: [
    {
      title: "Visit Information",
      fields: [
        { name: "visitDate", label: "Visit Date", type: "date", required: true },
        { name: "visitType", label: "Visit Type", type: "select", options: [
          "Routine", "Emergency", "Follow-up"
        ], required: true },
        { name: "visitLocation", label: "Visit Location", type: "select", options: [
          "Home", "Nursing Facility", "Assisted Living", "Hospital", "Other"
        ], required: true }
      ]
    },
    {
      title: "Assessment",
      fields: [
        {
          name: "vitalSigns",
          label: "Vital Signs",
          type: "group",
          fields: [
            { name: "bloodPressure", label: "Blood Pressure", type: "text" },
            { name: "heartRate", label: "Heart Rate", type: "number", unit: "bpm" },
            { name: "respiratoryRate", label: "Respiratory Rate", type: "number", unit: "breaths/min" },
            { name: "temperature", label: "Temperature", type: "number", unit: "°F" },
            { name: "oxygenSaturation", label: "Oxygen Saturation", type: "number", unit: "%" }
          ]
        },
        { name: "painLevel", label: "Pain Level (0-10)", type: "number", min: 0, max: 10, required: true },
        { name: "symptoms", label: "Current Symptoms", type: "multiselect", options: [
          "Pain", "Shortness of Breath", "Nausea", "Vomiting", "Constipation", "Diarrhea", 
          "Fatigue", "Anxiety", "Depression", "Confusion", "Insomnia", "Anorexia", "Other"
        ]},
        { name: "otherSymptoms", label: "Other Symptoms", type: "text", conditional: { field: "symptoms", includes: "Other" } },
        { name: "assessment", label: "Assessment", type: "textarea", required: true }
      ]
    },
    {
      title: "Plan",
      fields: [
        { name: "plan", label: "Plan", type: "textarea", required: true },
        { name: "medicationChanges", label: "Medication Changes", type: "boolean" },
        { name: "medicationDetails", label: "Medication Change Details", type: "textarea", conditional: { field: "medicationChanges", value: true } },
        { name: "followUpNeeded", label: "Follow-up Needed", type: "boolean" },
        { name: "followUpDetails", label: "Follow-up Details", type: "text", conditional: { field: "followUpNeeded", value: true } }
      ]
    }
  ]
};

/**
 * Medication Order Template
 */
export const medicationOrderTemplate = {
  title: "Medication Order",
  sections: [
    {
      title: "Medication Information",
      fields: [
        { name: "medicationName", label: "Medication Name", type: "text", required: true },
        { name: "dosage", label: "Dosage", type: "text", required: true },
        { name: "route", label: "Route", type: "select", options: [
          "Oral", "Sublingual", "Buccal", "Topical", "Transdermal", "Subcutaneous", 
          "Intramuscular", "Intravenous", "Rectal", "Inhalation", "Other"
        ], required: true },
        { name: "frequency", label: "Frequency", type: "text", required: true },
        { name: "duration", label: "Duration", type: "text" },
        { name: "indication", label: "Indication", type: "text", required: true },
        { name: "prn", label: "PRN (As Needed)", type: "boolean" },
        { name: "prnInstructions", label: "PRN Instructions", type: "text", conditional: { field: "prn", value: true } }
      ]
    },
    {
      title: "Prescriber Information",
      fields: [
        { name: "prescriberName", label: "Prescriber Name", type: "text", required: true },
        { name: "prescriberCredentials", label: "Prescriber Credentials", type: "text", required: true },
        { name: "prescriberPhone", label: "Prescriber Phone", type: "text", required: true }
      ]
    }
  ]
};

/**
 * IDG Meeting Template
 */
export const idgMeetingTemplate = {
  title: "IDG Meeting",
  sections: [
    {
      title: "Meeting Information",
      fields: [
        { name: "meetingDate", label: "Meeting Date", type: "date", required: true },
        { name: "attendees", label: "Attendees", type: "multiselect", options: [
          "Physician", "Nurse", "Social Worker", "Chaplain", "Aide", "Volunteer Coordinator", "Bereavement Coordinator", "Other"
        ], required: true },
        { name: "otherAttendees", label: "Other Attendees", type: "text", conditional: { field: "attendees", includes: "Other" } }
      ]
    },
    {
      title: "Patient Review",
      fields: [
        { name: "currentStatus", label: "Current Status", type: "select", options: [
          "Stable", "Declining", "Actively Dying", "Improving"
        ], required: true },
        { name: "symptomManagement", label: "Symptom Management", type: "textarea", required: true },
        { name: "psychosocialIssues", label: "Psychosocial Issues", type: "textarea" },
        { name: "spiritualIssues", label: "Spiritual Issues", type: "textarea" },
        { name: "familyIssues", label: "Family Issues", type: "textarea" }
      ]
    },
    {
      title: "Plan",
      fields: [
        { name: "nursingPlan", label: "Nursing Plan", type: "textarea", required: true },
        { name: "socialWorkPlan", label: "Social Work Plan", type: "textarea" },
        { name: "spiritualCarePlan", label: "Spiritual Care Plan", type: "textarea" },
        { name: "aidePlan", label: "Aide Plan", type: "textarea" },
        { name: "volunteerPlan", label: "Volunteer Plan", type: "textarea" },
        { name: "nextReview", label: "Next Review Date", type: "date", required: true }
      ]
    }
  ]
};

/**
 * Discharge Summary Template
 */
export const dischargeSummaryTemplate = {
  title: "Discharge Summary",
  sections: [
    {
      title: "Discharge Information",
      fields: [
        { name: "dischargeDate", label: "Discharge Date", type: "date", required: true },
        { name: "dischargeReason", label: "Discharge Reason", type: "select", options: [
          "Death", "Revocation", "Extended Prognosis", "Moved Out of Service Area", "Transfer to Another Hospice", "Other"
        ], required: true },
        { name: "otherDischargeReason", label: "Other Discharge Reason", type: "text", conditional: { field: "dischargeReason", value: "Other" } }
      ]
    },
    {
      title: "Death Information",
      fields: [
        { name: "deathDate", label: "Date of Death", type: "date", conditional: { field: "dischargeReason", value: "Death" }, required: true },
        { name: "deathTime", label: "Time of Death", type: "time", conditional: { field: "dischargeReason", value: "Death" } },
        { name: "deathLocation", label: "Location of Death", type: "select", options: [
          "Home", "Nursing Facility", "Assisted Living", "Hospital", "Inpatient Hospice Unit", "Other"
        ], conditional: { field: "dischargeReason", value: "Death" } },
        { name: "presentAtDeath", label: "Present at Death", type: "multiselect", options: [
          "Family", "Hospice Staff", "Facility Staff", "No One", "Other"
        ], conditional: { field: "dischargeReason", value: "Death" } }
      ]
    },
    {
      title: "Summary",
      fields: [
        { name: "hospiceStay", label: "Length of Hospice Stay", type: "text", required: true },
        { name: "diagnosisSummary", label: "Diagnosis Summary", type: "textarea", required: true },
        { name: "treatmentSummary", label: "Treatment Summary", type: "textarea", required: true },
        { name: "symptomManagement", label: "Symptom Management Summary", type: "textarea", required: true },
        { name: "psychosocialSummary", label: "Psychosocial Summary", type: "textarea" },
        { name: "spiritualSummary", label: "Spiritual Summary", type: "textarea" }
      ]
    },
    {
      title: "Follow-up",
      fields: [
        { name: "bereavementReferral", label: "Bereavement Referral", type: "boolean", conditional: { field: "dischargeReason", value: "Death" } },
        { name: "bereavementNotes", label: "Bereavement Notes", type: "textarea", conditional: { field: "bereavementReferral", value: true } },
        { name: "followUpRecommendations", label: "Follow-up Recommendations", type: "textarea", conditional: { field: "dischargeReason", value: "!Death" } }
      ]
    }
  ]
};

// Export all templates
export const clinicalTemplates = {
  comprehensiveAssessment: comprehensiveAssessmentTemplate,
  visitNote: visitNoteTemplate,
  medicationOrder: medicationOrderTemplate,
  idgMeeting: idgMeetingTemplate,
  dischargeSummary: dischargeSummaryTemplate
};
