import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Group, 
  Paper, 
  Select, 
  Stack, 
  Text, 
  Textarea,
  Loader
} from '@mantine/core';
import { IconFileText, IconPill, IconSend } from '@tabler/icons-react';
import { FileUploader } from '../../../components/FileUploader';
import { CollapsibleMarkdown } from '../../../components/CollapsibleMarkdown';
import { useCreateRequestMutation } from '../../../store/api_extensions/requestApiExt';
import { 
  useGetNoteTemplatesQuery, 
  useGetOrderTemplatesQuery 
} from '../../../store/api_extensions/schemaApiExt';

interface CreateRequestFormProps {
  patientId: string;
  onRequestCreated: () => void;
}

export function CreateRequestForm({ patientId, onRequestCreated }: CreateRequestFormProps) {
  const [content, setContent] = useState('');
  const [files, setFiles] = useState<string[]>([]);
  const [selectedNoteTemplate, setSelectedNoteTemplate] = useState<string | null>(null);
  const [selectedOrderTemplate, setSelectedOrderTemplate] = useState<string | null>(null);
  const [templateContent, setTemplateContent] = useState<string | null>(null);
  
  // API hooks
  const [createRequest, { isLoading }] = useCreateRequestMutation();
  const { data: noteTemplates = [], isLoading: isLoadingNoteTemplates } = useGetNoteTemplatesQuery();
  const { data: orderTemplates = [], isLoading: isLoadingOrderTemplates } = useGetOrderTemplatesQuery();
  
  // Generate template content from schema
  const generateTemplateContent = (schema: { 
    id: string; 
    name: string; 
    description?: string; 
    schema: Record<string, unknown>;
  }): string => {
    // This is a simplified version - in a real app, you would generate markdown from the schema
    const schemaObj = schema.schema as { properties?: Record<string, { type?: string }> };
    
    return `# ${schema.name}

## Description
${schema.description || 'No description provided'}

## Fields
${schemaObj.properties 
  ? Object.keys(schemaObj.properties).map(key => 
      `- ${key}: ${schemaObj.properties?.[key]?.type || 'any'}`
    ).join('\n')
  : 'No fields defined'}
`;
  };
  
  // Handle template selection
  const handleNoteTemplateChange = (value: string | null) => {
    setSelectedNoteTemplate(value);
    if (value) {
      // Clear order template if note template is selected
      setSelectedOrderTemplate(null);
      // Set template content
      const template = noteTemplates.find(t => t.id === value);
      if (template) {
        setTemplateContent(generateTemplateContent(template));
      }
    } else {
      setTemplateContent(null);
    }
  };
  
  const handleOrderTemplateChange = (value: string | null) => {
    setSelectedOrderTemplate(value);
    if (value) {
      // Clear note template if order template is selected
      setSelectedNoteTemplate(null);
      // Set template content
      const template = orderTemplates.find(t => t.id === value);
      if (template) {
        setTemplateContent(generateTemplateContent(template));
      }
    } else {
      setTemplateContent(null);
    }
  };
  
  // Handle file upload completion
  const handleUploadComplete = (fileIds: string[]) => {
    setFiles([...files, ...fileIds]);
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    if (!content.trim()) return;
    
    try {
      // Create request data with optional properties
      interface RequestData {
        resourceType: 'Request';
        content: string;
        patient: {
          id: string;
          resourceType: 'Patient';
        };
        createdAt: string;
        fileAttachments: string[];
        noteTemplate?: {
          id: string;
          resourceType: 'Note';
        };
        orderTemplate?: {
          id: string;
          resourceType: 'Order';
        };
      }
      
      const requestData: RequestData = {
        resourceType: 'Request',
        content,
        patient: {
          id: patientId,
          resourceType: 'Patient'
        },
        createdAt: new Date().toISOString(),
        fileAttachments: files
      };
      
      // Add note template if selected
      if (selectedNoteTemplate) {
        requestData.noteTemplate = {
          id: selectedNoteTemplate,
          resourceType: 'Note'
        };
      }
      
      // Add order template if selected
      if (selectedOrderTemplate) {
        requestData.orderTemplate = {
          id: selectedOrderTemplate,
          resourceType: 'Order'
        };
      }
      
      await createRequest(requestData).unwrap();
      
      // Reset form
      setContent('');
      setFiles([]);
      setSelectedNoteTemplate(null);
      setSelectedOrderTemplate(null);
      setTemplateContent(null);
      
      // Notify parent
      onRequestCreated();
    } catch (error) {
      console.error('Error creating request:', error);
    }
  };
  
  return (
    <Stack gap="md">
      <Textarea
        placeholder="Enter your request details..."
        minRows={3}
        value={content}
        onChange={(event) => setContent(event.currentTarget.value)}
      />
      
      <Group align="flex-start">
        <Select
          label="Note Template"
          placeholder="Select a note template"
          data={noteTemplates.map(template => ({
            value: template.id,
            label: template.name
          }))}
          value={selectedNoteTemplate}
          onChange={handleNoteTemplateChange}
          leftSection={<IconFileText size={16} />}
          style={{ flex: 1 }}
          clearable
          disabled={isLoadingNoteTemplates}
          rightSection={isLoadingNoteTemplates ? <Loader size="xs" /> : null}
        />
        
        <Select
          label="Order Template"
          placeholder="Select an order template"
          data={orderTemplates.map(template => ({
            value: template.id,
            label: template.name
          }))}
          value={selectedOrderTemplate}
          onChange={handleOrderTemplateChange}
          leftSection={<IconPill size={16} />}
          style={{ flex: 1 }}
          clearable
          disabled={isLoadingOrderTemplates}
          rightSection={isLoadingOrderTemplates ? <Loader size="xs" /> : null}
        />
      </Group>
      
      {templateContent && (
        <Box mt="md">
          <Text fw={500} size="sm" mb="xs">Template Preview:</Text>
          <Paper p="xs" withBorder>
            <CollapsibleMarkdown markdown={templateContent} />
          </Paper>
        </Box>
      )}
      
      <Box>
        <Text fw={500} size="sm" mb="xs">Attach Files</Text>
        <FileUploader onUploadComplete={handleUploadComplete} />
      </Box>
      
      <Button 
        onClick={handleSubmit} 
        loading={isLoading}
        disabled={!content.trim()}
        rightSection={<IconSend size={16} />}
      >
        Submit Request
      </Button>
    </Stack>
  );
}
