import React, { useState } from 'react';
import { 
  Box, 
  Group, 
  Text, 
  Paper, 
  Tabs,
  Code,
  Divider
} from '@mantine/core';
import { IconGitCompare, IconMarkdown, IconList } from '@tabler/icons-react';
import { generateMarkdownFromSurvey } from '../utils';
import { getSampleSurveyData } from '../mockData';

/**
 * Component to display the difference between the original markdown and the hierarchical markdown
 */
export function MarkdownDiffView() {
  // Generate the markdown from the sample survey data
  const survey = { data: getSampleSurveyData() };
  const markdown = generateMarkdownFromSurvey(survey as any);
  
  // Split the markdown into sections based on heading level
  const sections = markdown.split(/(?=# )/g);
  
  return (
    <Box>
      <Group mb="md">
        <IconGitCompare size={20} />
        <Text fw={500}>Markdown Structure Comparison</Text>
      </Group>
      
      <Tabs defaultValue="original">
        <Tabs.List>
          <Tabs.Tab value="original" leftSection={<IconMarkdown size={14} />}>
            Original Markdown
          </Tabs.Tab>
          <Tabs.Tab value="hierarchical" leftSection={<IconList size={14} />}>
            Hierarchical Structure
          </Tabs.Tab>
        </Tabs.List>
        
        <Tabs.Panel value="original" pt="md">
          <Paper p="md" withBorder>
            <Text fw={500} mb="md">Original Markdown (Flat Structure)</Text>
            <Code block style={{ maxHeight: '500px', overflow: 'auto' }}>
              {markdown}
            </Code>
          </Paper>
        </Tabs.Panel>
        
        <Tabs.Panel value="hierarchical" pt="md">
          <Paper p="md" withBorder>
            <Text fw={500} mb="md">Hierarchical Structure (Nested Dropdowns)</Text>
            <Text mb="md">
              The markdown content is now organized into a hierarchical structure with collapsible sections:
            </Text>
            
            {sections.map((section, index) => {
              // Extract the section title (first line)
              const lines = section.trim().split('\n');
              const title = lines[0].replace(/^# /, '');
              
              // Skip empty sections
              if (!title) return null;
              
              return (
                <Box key={index} mb="lg">
                  <Paper withBorder p="md" style={{ backgroundColor: '#f8f9fa' }}>
                    <Text fw={700} mb="xs">Level 1: {title}</Text>
                    <Text size="sm" mb="md">This is a top-level section that can be collapsed</Text>
                    
                    {/* Show level 2 headings */}
                    {section.split(/(?=## )/g).slice(1).map((subsection, subIndex) => {
                      const subLines = subsection.trim().split('\n');
                      const subTitle = subLines[0].replace(/^## /, '');
                      
                      return (
                        <Box key={`${index}-${subIndex}`} ml="md" mb="md">
                          <Paper withBorder p="md" style={{ backgroundColor: '#eef2ff' }}>
                            <Text fw={600} mb="xs">Level 2: {subTitle}</Text>
                            <Text size="sm" mb="md">This is a second-level section that can be collapsed</Text>
                            
                            {/* Show level 3 headings */}
                            {subsection.split(/(?=### )/g).slice(1).map((subsubsection, subsubIndex) => {
                              const subsubLines = subsubsection.trim().split('\n');
                              const subsubTitle = subsubLines[0].replace(/^### /, '');
                              
                              return (
                                <Box key={`${index}-${subIndex}-${subsubIndex}`} ml="md" mb="md">
                                  <Paper withBorder p="md" style={{ backgroundColor: '#e6fffa' }}>
                                    <Text fw={500} mb="xs">Level 3: {subsubTitle}</Text>
                                    <Text size="sm">This is a third-level section that can be collapsed</Text>
                                  </Paper>
                                </Box>
                              );
                            })}
                          </Paper>
                        </Box>
                      );
                    })}
                  </Paper>
                </Box>
              );
            })}
          </Paper>
        </Tabs.Panel>
      </Tabs>
    </Box>
  );
}
