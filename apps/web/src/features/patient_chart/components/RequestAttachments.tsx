import React from 'react';
import { 
  Box, 
  Text, 
  Group, 
  Paper, 
  Stack, 
  Button,
  ActionIcon
} from '@mantine/core';
import { 
  IconFile, 
  IconDownload, 
  IconTrash, 
  IconFileText, 
  IconPhoto, 
  IconFileZip 
} from '@tabler/icons-react';

interface RequestAttachmentsProps {
  attachments: string[];
  onDownload?: (attachmentId: string) => void;
  onDelete?: (attachmentId: string) => void;
  readOnly?: boolean;
}

export function RequestAttachments({ 
  attachments, 
  onDownload, 
  onDelete, 
  readOnly = false 
}: RequestAttachmentsProps) {
  // Function to get file icon based on file extension
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return <IconFileText size={20} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <IconPhoto size={20} />;
      case 'zip':
      case 'rar':
        return <IconFileZip size={20} />;
      default:
        return <IconFile size={20} />;
    }
  };
  
  // Function to get file name from attachment ID
  // In a real app, this would fetch the actual file name from the server
  const getFileName = (attachmentId: string) => {
    // This is a simplified version - in a real app, you would get the actual file name
    const fileExtensions = ['pdf', 'jpg', 'png', 'docx', 'xlsx'];
    const randomExt = fileExtensions[Math.floor(Math.random() * fileExtensions.length)];
    
    // Extract a short ID from the attachment ID
    const shortId = attachmentId.split('-').pop()?.substring(0, 6);
    
    return `attachment-${shortId}.${randomExt}`;
  };
  
  // Function to get file size
  // In a real app, this would fetch the actual file size from the server
  const getFileSize = () => {
    // This is a simplified version - in a real app, you would get the actual file size
    const sizes = ['1.2 MB', '3.5 MB', '512 KB', '2.7 MB', '890 KB'];
    return sizes[Math.floor(Math.random() * sizes.length)];
  };
  
  if (attachments.length === 0) {
    return (
      <Box>
        <Text c="dimmed" size="sm">No attachments</Text>
      </Box>
    );
  }
  
  return (
    <Stack gap="xs">
      {attachments.map((attachmentId) => {
        const fileName = getFileName(attachmentId);
        const fileSize = getFileSize();
        
        return (
          <Paper key={attachmentId} p="xs" withBorder>
            <Group justify="space-between">
              <Group gap="xs">
                {getFileIcon(fileName)}
                <div>
                  <Text size="sm" fw={500}>
                    {fileName}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {fileSize}
                  </Text>
                </div>
              </Group>
              
              <Group gap="xs">
                {onDownload && (
                  <ActionIcon 
                    variant="light" 
                    color="blue" 
                    onClick={() => onDownload(attachmentId)}
                  >
                    <IconDownload size={16} />
                  </ActionIcon>
                )}
                
                {!readOnly && onDelete && (
                  <ActionIcon 
                    variant="light" 
                    color="red" 
                    onClick={() => onDelete(attachmentId)}
                  >
                    <IconTrash size={16} />
                  </ActionIcon>
                )}
              </Group>
            </Group>
          </Paper>
        );
      })}
      
      {onDownload && attachments.length > 1 && (
        <Button 
          variant="light" 
          leftSection={<IconDownload size={16} />}
          onClick={() => attachments.forEach(id => onDownload(id))}
          size="xs"
          mt="xs"
        >
          Download All
        </Button>
      )}
    </Stack>
  );
}
