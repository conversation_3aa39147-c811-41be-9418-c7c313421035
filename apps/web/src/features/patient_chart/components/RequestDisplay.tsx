import { ResourceMarkdownView } from "../../../components/ResourceMarkdownView";
import { useGetResourceByIdQuery } from "../../../store/api_extensions/resourceApiExt";


export interface RequestDisplayProps {
    requestId: string;
}

export const RequestDisplay = ({ requestId }: RequestDisplayProps) => {
    const { data: request, isLoading: isLoadingRequest, error: requestError } = useGetResourceByIdQuery({ resourceType: 'Request', id: requestId }, { skip: !requestId });

    return (
        <>
            { request?.orderCreated?.id && (
                <ResourceMarkdownView resourceId={request?.orderCreated?.id} resourceType="Order" />
            )}
            { request?.noteCreated?.id && (
                <ResourceMarkdownView resourceId={request?.noteCreated?.id} resourceType="Note" />
            )}
        </>
    )
}