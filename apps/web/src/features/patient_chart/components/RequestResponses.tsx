import React, { useState } from 'react';
import { 
  Box, 
  Text, 
  Group, 
  Paper, 
  Stack, 
  Textarea,
  Button,
  Avatar,
  Divider
} from '@mantine/core';
import { IconSend } from '@tabler/icons-react';
import { RequestResponse } from '../types';

interface RequestResponsesProps {
  responses: RequestResponse[];
  onAddResponse?: (content: string) => void;
  readOnly?: boolean;
}

export function RequestResponses({ 
  responses, 
  onAddResponse, 
  readOnly = false 
}: RequestResponsesProps) {
  const [newResponse, setNewResponse] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Handle submit response
  const handleSubmit = async () => {
    if (!newResponse.trim() || !onAddResponse) return;
    
    setIsSubmitting(true);
    
    try {
      await onAddResponse(newResponse);
      setNewResponse('');
    } catch (error) {
      console.error('Error adding response:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  
  return (
    <Stack gap="md">
      {/* Response list */}
      {responses.length > 0 ? (
        <Stack gap="md">
          {responses.map((response, index) => (
            <Paper key={index} p="sm" withBorder>
              <Group gap="xs" mb="xs">
                <Avatar 
                  size="sm" 
                  color={response.sentBy === 'tallio' ? 'blue' : 'green'}
                  radius="xl"
                >
                  {response.sentBy === 'tallio' ? 'AI' : 'U'}
                </Avatar>
                <div>
                  <Text size="sm" fw={500}>
                    {response.sentBy === 'tallio' ? 'Tallio Assistant' : 'User'}
                  </Text>
                </div>
              </Group>
              <Text size="sm">{response.content}</Text>
            </Paper>
          ))}
        </Stack>
      ) : (
        <Text c="dimmed" size="sm">No responses yet</Text>
      )}
      
      {/* Response input */}
      {!readOnly && onAddResponse && (
        <>
          <Divider />
          <Box>
            <Textarea
              placeholder="Type your response..."
              minRows={2}
              value={newResponse}
              onChange={(event) => setNewResponse(event.currentTarget.value)}
              mb="xs"
            />
            <Button
              onClick={handleSubmit}
              loading={isSubmitting}
              disabled={!newResponse.trim()}
              rightSection={<IconSend size={16} />}
              size="sm"
            >
              Send Response
            </Button>
          </Box>
        </>
      )}
    </Stack>
  );
}
