import React, { ReactNode, useState } from 'react';
import { 
  Paper, 
  Group, 
  Badge, 
  Text, 
  Box, 
  Divider,
  UnstyledButton,
  Stack,
  Collapse,
  ActionIcon
} from '@mantine/core';
import { 
  IconNotes, 
  IconFileText, 
  IconMicrophone, 
  IconPill, 
  IconUserCircle, 
  IconSparkles,
  IconHistory,
  IconMessage,
  IconChevronDown,
  IconChevronUp,
  IconPaperclip
} from '@tabler/icons-react';
import { TimelineEntry } from '../types';
import { formatTimestamp } from '../utils';
import { RequestAttachments } from './RequestAttachments';
import { RequestResponses } from './RequestResponses';

interface TimelineEntryItemProps {
  entry: TimelineEntry;
  onClick?: (entry: TimelineEntry) => void;
}

interface CollapsibleSectionProps {
  title: string;
  icon: ReactNode;
  children: ReactNode;
}

function CollapsibleSection({ title, icon, children }: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <Box>
      <Group justify="space-between" mb={isOpen ? 'xs' : 0}>
        <Group gap="xs">
          {icon}
          <Text size="sm" fw={500}>{title}</Text>
        </Group>
        <ActionIcon size="sm" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
        </ActionIcon>
      </Group>
      
      <Collapse in={isOpen}>
        <Box pl="md" pt="xs">
          {children}
        </Box>
      </Collapse>
    </Box>
  );
}

/**
 * Component to display a single timeline entry
 */
export function TimelineEntryItem({ entry, onClick }: TimelineEntryItemProps) {
  // Get icon based on entry type
  const getEntryIcon = (type: string): ReactNode => {
    switch (type) {
      case 'note':
      case 'Visit Note':
      case 'Text':
        return <IconNotes size={18} />;
      case 'document':
      case 'Doc/PDF':
        return <IconFileText size={18} />;
      case 'audio':
      case 'Audio':
        return <IconMicrophone size={18} />;
      case 'medication':
      case 'Medications':
        return <IconPill size={18} />;
      case 'demographics':
      case 'Demographics':
        return <IconUserCircle size={18} />;
      case 'system':
        return <IconSparkles size={18} />;
      case 'Template':
        return <IconFileText size={18} />;
      case 'request':
        return <IconMessage size={18} />;
      default:
        return <IconNotes size={18} />;
    }
  };

  // Get color based on tag or type
  const getTagColor = (tag: string): string => {
    switch (tag) {
      case 'note':
      case 'Visit Note':
      case 'Text':
        return 'blue';
      case 'document':
      case 'Doc/PDF':
        return 'green';
      case 'audio':
      case 'Audio':
        return 'violet';
      case 'medication':
      case 'Medications':
        return 'orange';
      case 'demographics':
      case 'Demographics':
        return 'cyan';
      case 'Diagnosis':
        return 'red';
      case 'POC':
        return 'violet';
      case 'system':
        return 'gray';
      case 'Template':
        return 'indigo';
      case 'request':
        return 'teal';
      case 'visit_note':
        return 'blue';
      case 'order':
        return 'orange';
      case 'poc_update':
        return 'violet';
      case 'demographic_update':
        return 'cyan';
      default:
        return 'gray';
    }
  };

  // Format timestamp using utility function
  const formattedTimestamp = formatTimestamp(new Date(entry.timestamp));

  // Use only the tags from the entry
  const getTags = (): string[] => {
    if (entry.type === 'request' && entry.requestTags) {
      return entry.requestTags;
    }
    return entry.tags || [];
  };
  
  // Check if entry has attachments
  const hasAttachments = (): boolean => {
    return !!(
      (entry.fileAttachments && entry.fileAttachments.length > 0) ||
      (entry.audioAttachments && entry.audioAttachments.length > 0)
    );
  };
  
  // Get response count
  const getResponseCount = (): number => {
    return entry.responses?.length || 0;
  };

  return (
    <Paper withBorder p="md" mb="md">
      <Group justify="space-between" mb="xs">
        <Text fw={500} size="sm">{entry.title}</Text>
        <Box>
          <Text size="xs" c="dimmed" ta="right" mb="xs">{formattedTimestamp}</Text>
          {entry.mainTag && (
            <Badge 
              size="sm" 
              variant="light" 
              color={getTagColor(entry.mainTag)}
              leftSection={getEntryIcon(entry.mainTag)}
              style={{ float: 'right' }}
            >
              {entry.mainTag}
            </Badge>
          )}
        </Box>
      </Group>
      
      <Group mb="xs">
        {getTags().length > 0 ? (
          getTags().map((tag, index) => (
            <Badge 
              key={index} 
              size="sm" 
              variant="light" 
              color={getTagColor(tag)}
              leftSection={getEntryIcon(tag)}
            >
              {tag}
            </Badge>
          ))
        ) : (
          <Text size="xs" c="dimmed">No tags</Text>
        )}
      </Group>
      
      <Text size="sm" mb="md">
        {entry.aiSummary || entry.content}
      </Text>
      
      {/* Request-specific sections */}
      {entry.type === 'request' && (
        <Stack gap="xs" mb="md">
          {/* Attachments section */}
          {hasAttachments() && (
            <CollapsibleSection 
              title="Attachments" 
              icon={<IconPaperclip size={16} />}
            >
              <RequestAttachments 
                fileAttachments={entry.fileAttachments} 
                audioAttachments={entry.audioAttachments} 
              />
            </CollapsibleSection>
          )}
          
          {/* Responses section */}
          <CollapsibleSection 
            title={`Responses (${getResponseCount()})`} 
            icon={<IconMessage size={16} />}
          >
            <RequestResponses 
              requestId={entry.id} 
              responses={entry.responses} 
            />
          </CollapsibleSection>
        </Stack>
      )}
      
      <Group gap="xs" mb="xs">
        <Text size="xs" c="dimmed" fw={500}>By:</Text>
        <Text size="xs" c="dimmed">{entry.user.name} ({entry.user.role})</Text>
      </Group>
      
      {/* Show "Signed by" for medication orders */}
      {entry.type === 'medication' && (
        <Group gap="xs" mb="xs">
          <Text size="xs" c="dimmed" fw={500}>Signed by:</Text>
          <Text size="xs" c="dimmed">Dr. Robert Williams (Medical Director)</Text>
        </Group>
      )}
      
      <Box>
        <Divider my="xs" />
        <UnstyledButton 
          onClick={() => onClick && onClick(entry)}
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '8px',
            borderRadius: '4px',
            color: '#228be6',
          }}
          styles={{
            root: {
              '&:hover': {
                backgroundColor: '#f8f9fa',
              },
            },
          }}
        >
          <Group gap="xs">
            <IconHistory size={14} />
            <Text size="xs" fw={500}>
              {entry.tags?.includes('Visit Note') ? 'View Visit Details' : 'View Changes'}
            </Text>
          </Group>
        </UnstyledButton>
      </Box>
    </Paper>
  );
}
