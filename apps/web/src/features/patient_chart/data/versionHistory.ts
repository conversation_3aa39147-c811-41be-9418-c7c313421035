import { VersionHistoryItem } from '../types';

// Sample version history data
export interface VersionHistoryByDate {
  [date: string]: VersionHistoryItem[];
}

export const versionHistoryByDate: VersionHistoryByDate = {
  "2024-04-15": [
    {
      id: "v1",
      timestamp: new Date(2024, 3, 15, 10, 30),
      user: {
        name: "Dr. <PERSON>",
        role: "Hospice Physician"
      },
      changes: [
        {
          field: "Pain Level",
          oldValue: "5/10",
          newValue: "3/10"
        },
        {
          field: "Medication Effectiveness",
          oldValue: "Moderate",
          newValue: "Good"
        }
      ]
    },
    {
      id: "v2",
      timestamp: new Date(2024, 3, 15, 10, 35),
      user: {
        name: "Dr. <PERSON>",
        role: "Hospice Physician"
      },
      changes: [
        {
          field: "Plan of Care",
          oldValue: "Continue current regimen",
          newValue: "Adjust medication schedule"
        }
      ]
    }
  ],
  "2024-04-10": [
    {
      id: "v3",
      timestamp: new Date(2024, 3, 10, 11, 15),
      user: {
        name: "<PERSON>",
        role: "Ho<PERSON>ice Nurse"
      },
      changes: [
        {
          field: "Emergency Contact",
          oldValue: "<PERSON> (Son)",
          newValue: "<PERSON> (Daughter)"
        },
        {
          field: "Emergency Contact Phone",
          oldValue: "************",
          newValue: "************"
        }
      ]
    }
  ]
};
