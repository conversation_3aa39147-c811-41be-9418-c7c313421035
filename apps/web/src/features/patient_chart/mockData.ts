import { 
  Diagnosis, 
  Medication, 
  IDGMeeting, 
  Visit, 
  TimelineEntry, 
  VersionHistoryEntry,
  SurveyData,
  EntryTag
} from './types';

// Sample patient chart survey JSON
export const patientChartJson = {
  title: "Patient Chart",
  pages: [
    {
      name: "patientChartPage",
      elements: [
        {
          type: "panel",
          name: "demographics",
          title: "Demographics",
          state: "expanded",
          elements: [
            {
              type: "text",
              name: "patientN<PERSON>",
              title: "Patient Name",
              isRequired: true
            },
            {
              type: "text",
              name: "dateOfBirth",
              title: "Date of Birth",
              inputType: "date",
              isRequired: true
            },
            {
              type: "text",
              name: "medicalRecordN<PERSON>ber",
              title: "Medical Record Number"
            },
            {
              type: "dropdown",
              name: "gender",
              title: "Gender",
              choices: ["Male", "Female", "Other", "Prefer not to say"]
            },
            {
              type: "text",
              name: "address",
              title: "Address"
            },
            {
              type: "text",
              name: "phoneN<PERSON>ber",
              title: "Phone Number"
            }
          ]
        },
        {
          type: "panel",
          name: "medicalHistory",
          title: "Medical History",
          state: "collapsed",
          elements: [
            {
              type: "comment",
              name: "chief<PERSON><PERSON><PERSON><PERSON>",
              title: "Chief Complaint",
              rows: 3
            },
            {
              type: "comment",
              name: "historyOfPresentIllness",
              title: "History of Present Illness",
              rows: 5
            },
            {
              type: "checkbox",
              name: "pastMedicalHistory",
              title: "Past Medical History",
              choices: [
                "Hypertension", 
                "Diabetes", 
                "Heart Disease", 
                "Stroke", 
                "Cancer", 
                "Respiratory Disease", 
                "Kidney Disease", 
                "Liver Disease"
              ],
              hasOther: true,
              otherText: "Other conditions"
            },
            {
              type: "comment",
              name: "medications",
              title: "Current Medications",
              rows: 3
            },
            {
              type: "comment",
              name: "allergies",
              title: "Allergies",
              rows: 2
            }
          ]
        },
        {
          type: "panel",
          name: "hospiceAssessment",
          title: "Hospice Assessment",
          state: "collapsed",
          elements: [
            {
              type: "dropdown",
              name: "functionalStatus",
              title: "Functional Status (Palliative Performance Scale)",
              choices: ["100% - Normal", "90% - Minor symptoms", "80% - Normal activity with effort", 
                       "70% - Unable normal job/work", "60% - Unable hobby/housework", 
                       "50% - Unable to do any work, significant disease", 
                       "40% - Mainly in bed", "30% - Totally bed bound", 
                       "20% - Very sick, supportive treatment needed", 
                       "10% - Comatose or barely arousable"]
            },
            {
              type: "checkbox",
              name: "symptomsPresent",
              title: "Symptoms Present",
              choices: [
                "Pain", 
                "Dyspnea", 
                "Nausea/Vomiting", 
                "Constipation", 
                "Fatigue", 
                "Anxiety", 
                "Depression", 
                "Confusion"
              ],
              hasOther: true
            },
            {
              type: "rating",
              name: "painLevel",
              title: "Pain Level (0-10)",
              rateMin: 0,
              rateMax: 10
            },
            {
              type: "comment",
              name: "caregiverAssessment",
              title: "Caregiver Assessment",
              rows: 3
            },
            {
              type: "comment",
              name: "spiritualAssessment",
              title: "Spiritual Assessment",
              rows: 3
            }
          ]
        },
        {
          type: "panel",
          name: "carePlan",
          title: "Care Plan",
          state: "collapsed",
          elements: [
            {
              type: "comment",
              name: "goals",
              title: "Goals of Care",
              rows: 3
            },
            {
              type: "comment",
              name: "interventions",
              title: "Interventions",
              rows: 5
            },
            {
              type: "checkbox",
              name: "services",
              title: "Services Needed",
              choices: [
                "Nursing", 
                "Home Health Aide", 
                "Social Work", 
                "Chaplain", 
                "Volunteer", 
                "Physical Therapy", 
                "Occupational Therapy", 
                "Speech Therapy", 
                "Dietary Counseling"
              ]
            },
            {
              type: "comment",
              name: "equipmentNeeded",
              title: "Equipment Needed",
              rows: 2
            },
            {
              type: "dropdown",
              name: "visitFrequency",
              title: "Visit Frequency",
              choices: ["Daily", "2-3 times per week", "Weekly", "Every 2 weeks", "Monthly", "As needed"]
            }
          ]
        }
      ]
    }
  ]
};

// Sample client information
export const clientInfo = {
  name: "John Doe",
  ssn: "XXX-XX-XXXX",
  hospiceCenter: "Compass Regional Hospice Inc",
  dob: "1940-08-15",
  room: "Drum",
  age: 84,
  address: "225 Cornet Drive",
  phone: "(*************",
  city: "Centerville, MD 21617-2647",
  admitDate: "11/03/2024",
  certification: "12/02/2024",
  certProgram: "Hospice Care",
  team: "Queen Anne Hospice House",
  status: "Active",
  levelOfCare: "Routine Home Care",
  certPeriod: "12/02/2024 - 02/01/2025",
  benefitPeriod: "1",
  location: "Queen Anne Hospice House",
  physician: "Carl Littlejohn, CRNP",
  nursingProvider: "Interim Health",
  clientTransportation: "Queenstown Branch",
  payorSource: "Medicare",
  caseManager: "Keating, Jacquelyn RN"
};

// Sample diagnosis data with ICD-10 codes
export const sampleDiagnoses: Diagnosis[] = [
  { code: '331.0', description: 'Alzheimer\'s', date: '' },
  { code: '338.3', description: 'Neoplasm related pain (acute)', date: '11/03/2024' },
  { code: '799.3', description: 'Debility unspecified', date: '11/03/2024' },
  { code: '780.79', description: 'Other malaise and fatigue', date: '11/03/2024' },
  { code: '787.2', description: 'Dysphagia', date: '11/03/2024' },
  { code: '263.9', description: 'Unspecified protein-calorie malnutrition', date: '11/03/2024' },
  { code: '331.82', description: 'Dementia with behavioral disturbance', date: '11/03/2024' },
  { code: '162.9', description: 'Malignant neoplasm of bronchus and lung', date: '' },
  { code: '786.2', description: 'Cough', date: '11/10/2024' },
  { code: 'V55.3', description: 'Attention to gastrostomy [endoscopy]', date: '11/05/2024' },
  { code: 'V44.1', description: 'Gastrostomy status', date: '' },
  { code: '780.60', description: 'Fever', date: '' },
  { code: '496', description: 'Chronic airway obstruction, not elsewhere specified', date: '11/03/2024' }
];

// Sample medications data
export const sampleMedications: Medication[] = [
  { name: 'morphine sulfate', dosage: '5 mg/ml', route: 'oral', frequency: 'PRN', indication: 'severe pain 0.25 ml', startDate: '11/12/2024' },
  { name: 'haloperidol', dosage: '5 mg', route: 'oral', frequency: 'PRN', indication: 'every hour as needed', startDate: '11/16/2024' },
  { name: 'lorazepam', dosage: '2 mg', route: 'oral', frequency: 'R, AN, BID', indication: 'every hour as needed', startDate: '11/11/2024' },
  { name: 'senna', dosage: '8.6 mg (2T)', route: 'oral', frequency: 'PRN', indication: 'once a day', startDate: '11/16/2024' },
  { name: 'bisacodyl', dosage: '10 mg', route: 'oral', frequency: 'PRN', indication: 'one dose', startDate: '11/16/2024' },
  { name: 'acetaminophen', dosage: '500 mg', route: 'oral', frequency: 'PRN', indication: '', startDate: '11/15/2024' },
  { name: 'haloperidol', dosage: 'tablet', route: 'oral', frequency: 'R, AN', indication: '[agitation]', startDate: '12/16/2024' },
  { name: 'Ativan', dosage: 'tablet', route: 'oral', frequency: 'RPAN', indication: 'Anxiety/Restlessness', startDate: '12/16/2024' },
  { name: 'phenobarbital', dosage: 'tablet', route: 'oral', frequency: 'R, AN', indication: 'Other - see "Currently Active"', startDate: '12/16/2024' },
  { name: 'phenobarbital', dosage: 'solution', route: 'rectal', frequency: 'RPAN', indication: 'Rectal [seiz]', startDate: '12/16/2024' },
  { name: 'SenegaLax', dosage: 'tab', route: 'oral', frequency: 'RPAN', indication: '[const]', startDate: '12/16/2024' },
  { name: 'LevemisL', dosage: 'tablet, subling', route: 'oral', frequency: 'R, AN', indication: '[blister]', startDate: '12/16/2024' },
  { name: 'hyoscyamine', dosage: 'tablet', route: 'oral', frequency: 'R, AN', indication: '[pain/sol]', startDate: '12/16/2024' },
  { name: 'morphine', dosage: 'solution', route: 'oral', frequency: 'R, AN', indication: '[pain/ sol]', startDate: '12/16/2024' },
  { name: 'metoprolol succinate', dosage: 'Toprol XL tablet', route: 'oral', frequency: 'R', indication: 'extended release [24h] [injury?, 44/40mm], Severe CMH', startDate: '11/15/2024' },
  { name: 'gabapentin', dosage: 'capsule', route: 'oral', frequency: 'R, AN', indication: '(seizure, severe CMA)', startDate: '11/15/2024' }
];

// Sample IDG meetings data
export const sampleIDGMeetings: IDGMeeting[] = [
  { 
    date: '12/22/2024',
    time: '15:00',
    status: 'Completed',
    topic: 'SW Services hold further for prn/docu',
    attendees: [
      'Rickard, Saul', 'Barbee ARD', 'Boesko, NR', 'Lorin RN', 'Brown, Betty CR/CMH', 
      'Thywaits BC (CF)', 'Schwaly, Mary LCFW, LBSW, MSW', 'Knott, R',
      'Brunt, Rome LCSW', 'Geram, Sheila ARNP, CRNP', 'Falla, Lise LCSW', 
      'Flannery, Myles MSW', 'Ford, Arc KB', 'Jackson RP', 'Waterman, Nigel MD', 
      'Tviddle, Rn CDL', 'Quinnell, Terea, CRNP'
    ],
    clinicalInfo: {
      diagnoses: 'CHF (congestive heart failure), COPD, generalized anxiety disorder, depression',
      changes: [
        'ADPIE (Assessment, Diagnosis, Plan, Implementation, Evaluation): Assessments/Intv-Via list attached',
        'PRN use: Extended - 10 mg oral [2/sq. cm] [2-3-5h]',
        'Latest INJ AD computed: Decreased 2 U/mg'
      ]
    },
    providerInfo: {
      meetingProvider: 'Ronal Ard, MD',
      nurseCaseManager: 'Roshill',
      medicalDirector: 'Ron Arnsin, MD'
    },
    notes: 'Flagged re Dept Tsko—Extend-inhaler for Bronxisol-Verbal order 5/28 initiated patient admission/information sheets per Brittany 1/28/97 stating at current level.',
    familyNotifications: [
      'Mother: A-norm, SW note "cology" 19h update 15m',
      'Sister BSO DASHT TMO',
      'Brother calls "Relative-Flex Standard," nurse status updates to Zuck Revs'
    ],
    clinicalInterventions: [
      'Physical changes, submissions, Senatorial spec, by Davis, Asics on daily calapoics/other improvements',
      'No signs of recent PAC AD',
      'Plan 1 TAB of capsule abd 2x1 rx prn alves',
      'Notes from Pamela Rdgar: Stems terminology abruptly changed',
      'Changes in physical appearance noted after 21 hr visit',
      'Brunt LCSWing waiting continued personal narratives'
    ],
    formalReport: [
      'Use inefficiency not resolved',
      'As of 12/15/2024: Est has empathy, functional levels remain intact'
    ],
    conclusion: [
      'PPO remains',
      'SW TSKO speaks with dials re reg Abstr noun ref beyond Callum and Kopad LGBT care'
    ],
    followUp: {
      date: '12/29/2024',
      time: '15:30',
      agenda: 'Hold meds for hw sw',
      contact: 'Metral, Tidy 7/19/95 21h',
      notes: [
        'Latest checks 2024-2025 - Sec check lists formalized January 2025',
        'Decision OR preferred for tie-rods for efficient factual diagnoses - align with Canal / skid observation',
        'Meeting closure ratified by MBI July 2018 authorization notice'
      ]
    }
  },
  { 
    date: '12/15/2024',
    time: '14:00',
    status: 'Completed',
    topic: 'Initial care planning meeting',
    attendees: ['Dr. Johnson', 'RN Smith', 'SW Garcia', 'Chaplain Davis'],
    clinicalInfo: {
      diagnoses: 'End-stage liver disease with hepatic metastases of posterior wall of bladder',
      changes: [
        'Patient showing signs of decline',
        'Pain management protocol updated'
      ]
    },
    providerInfo: {
      meetingProvider: 'Dr. Johnson',
      nurseCaseManager: 'RN Smith',
      medicalDirector: 'Dr. Williams'
    },
    notes: 'Initial care planning meeting to establish hospice care plan and coordinate services.',
    familyNotifications: [
      'Daughter Susan informed of care plan',
      'Husband Robert present during meeting'
    ],
    clinicalInterventions: [
      'Pain management protocol established',
      'Spiritual care needs assessed',
      'Psychosocial support plan developed'
    ],
    formalReport: [
      'Patient admitted to hospice care',
      'Prognosis discussed with family'
    ],
    conclusion: [
      'Care plan established',
      'Follow-up visit scheduled'
    ],
    followUp: {
      date: '12/22/2024',
      time: '14:00',
      agenda: 'Review care plan implementation',
      contact: 'RN Smith',
      notes: [
        'Monitor pain management effectiveness',
        'Assess family coping'
      ]
    }
  }
];

// Sample standalone physician orders
export const samplePhysicianOrders = [
  {
    date: '12/20/2024',
    orderType: 'Comprehensive Order',
    clinician: 'German, Chelsea, RN',
    method: 'Electronic Order',
    instructions: 'Comprehensive order submitted by RN and approved by physician. Includes medication changes, plan of care updates, and diagnosis additions.',
    signedBy: 'Dr. Sarah Johnson',
    details: {
      medications: [
        { name: 'Morphine', dosage: '5mg oral', frequency: 'q4h PRN', action: 'Added' },
        { name: 'Lorazepam', dosage: '0.5mg oral', frequency: 'BID PRN', action: 'Modified' }
      ],
      planOfCare: [
        { problem: 'Pain Management', intervention: 'Increase pain medication as ordered', action: 'Modified' },
        { problem: 'Respiratory Distress', intervention: 'Oxygen therapy as needed', action: 'Added' }
      ],
      diagnoses: [
        { code: '786.2', description: 'Cough', action: 'Added' }
      ]
    }
  },
  {
    date: '12/18/2024',
    orderType: 'MEDICAL TX ORDERS: Activity as Tolerated, DME',
    clinician: 'Germann, Chelsea, RN',
    method: 'NURSE DAILY CARE: SKIN CARE - LOTION AS NEEDED; TURN AND POSITION AS NEEDED NUTRITION: DIET: REGULAR, SEASONINGS TABLE SALT SUBSTITUTES NOT LIMITED PROTEIN: CALCIUM SUPPLEMENT BID NURSE IV FREQUENCY: Daily',
    instructions: 'Discussed with physician on 12/2/24 11:36',
    signedBy: 'Alexis Alden, ARNP'
  },
  {
    date: '12/15/2024',
    orderType: 'Medication Order',
    clinician: 'Clemmons, Chad B, RN',
    method: 'Verbal Order',
    instructions: 'Instructions received on the 12/18/2024 to restart/continue the use of metoprolol 25 mg tablet once daily as required by the Direction of Care.',
    signedBy: 'Culler, Brittany CRNP'
  },
  {
    date: '12/02/2024',
    orderType: 'Durable Medical Equipment (DME)',
    clinician: 'Germann, Chelsea, RN',
    method: 'Written Order',
    instructions: 'Hospital Bed',
    signedBy: 'Germann, Chelsea, RN'
  }
];

// Sample visit information
export const sampleVisits: Visit[] = [
  {
    date: '12/20/2024',
    time: '10:00 AM - 11:30 AM',
    madeBy: 'German, Chelsea',
    discipline: 'RN',
    subType: 'Routine In Person Visit',
    assessments: {
      pain: 'PAINAD Score: 4',
      respiratory: 'Breathing: Normal, Occasional labored breathing',
      mental: 'Lethargic, Non-responsive, Nonverbal',
      vitalSigns: {
        respRate: '18',
        heartRate: '104',
        temperature: '98.6',
        bloodPressure: '110/64'
      },
      physical: {
        lungs: 'Clear',
        bowelSounds: 'Active',
        edema: 'None',
        cardiacRhythm: 'Sinus',
        turgor: 'Normal',
        oralMucous: 'Moist'
      }
    },
    notes: 'Patient unable to articulate. Terminal secretions audible. Breathing is even and regular at this time. Activity becoming more limited but patient is at rest in bed with eyes closed. Requires frequent repositioning for comfort. Appetite is very limited. Requires substantial support and cueing for safe intake of food and fluids. Continues to decline physical activity, more comfort through EOL transition.',
    planOfCare: [
      { 
        problem: 'Gastrointestinal Bleeding Patterns Altered', 
        description: 'Due to disease progression', 
        goal: 'Patient will have reduced GI bleeding and improved comfort within 48 hours',
        interventions: [
          'Monitor bowel patterns and adjust plan as needed',
          'Administer prescribed medications per physician orders',
          'Educate patient and family on dietary modifications'
        ],
        startDate: '12/20/2024', 
        endDate: 'N/A' 
      },
      { 
        problem: 'Respiratory Pattern Change', 
        description: 'Breathing difficulty and sleep disturbance', 
        goal: 'Patient will demonstrate improved respiratory status with decreased episodes of dyspnea',
        interventions: [
          'Position patient for optimal lung expansion',
          'Administer oxygen therapy as ordered',
          'Monitor respiratory rate and effort'
        ],
        startDate: '12/20/2024', 
        endDate: '12/22/2024' 
      },
      { 
        problem: 'Bowel Elimination Altered', 
        description: 'Irregular bowel movements, secondary to cancer treatment effects', 
        goal: 'Patient will maintain normal bowel elimination pattern',
        interventions: [
          'Administer prescribed bowel regimen',
          'Encourage fluid intake as appropriate',
          'Monitor bowel movements and document'
        ],
        startDate: '12/20/2024', 
        endDate: '12/23/2024' 
      },
      { 
        problem: 'Skin Integrity at Risk', 
        description: 'Due to prolonged immobility and effects of treatment/non-compliance', 
        goal: 'Patient will maintain intact skin without evidence of breakdown',
        interventions: [
          'Reposition patient every 2 hours',
          'Apply barrier cream per protocol',
          'Assess skin condition with each visit'
        ],
        startDate: '12/21/2024', 
        endDate: '12/25/2024' 
      },
      { 
        problem: 'Communication Route Impaired', 
        description: 'Patient cannot speak due to illness/medication effects', 
        goal: 'Patient will have alternative means to express needs and preferences',
        interventions: [
          'Use communication board or gestures as necessary',
          'Establish consistent method for yes/no responses',
          'Educate family on communication techniques'
        ],
        startDate: '12/21/2024', 
        endDate: '12/22/2024' 
      },
      { 
        problem: 'Nutrition Altered Less Than Body Requirements', 
        description: 'Factors such as fatigue, anorexia, pain, and cancer treatment contribute', 
        goal: 'Patient will maintain current weight and adequate hydration',
        interventions: [
          'Implement a nutritional plan with family and patient',
          'Offer small, frequent meals as tolerated',
          'Monitor intake and output'
        ],
        startDate: '12/20/2024', 
        endDate: '12/23/2024' 
      },
      { 
        problem: 'Pain Acute/Chronic Uncontrolled', 
        description: 'Cancer-related', 
        goal: 'Patient will report pain at acceptable level (3/10 or less) within 24 hours',
        interventions: [
          'Assess pain level and administer medications per orders',
          'Implement non-pharmacological pain management techniques',
          'Evaluate effectiveness of pain management interventions'
        ],
        startDate: '12/20/2024', 
        endDate: '12/22/2024' 
      },
      { 
        problem: 'Mobility Disabled/Risk for Falls', 
        description: 'Reduced safety awareness and cognitive impairment', 
        goal: 'Patient will remain free from falls and injury',
        interventions: [
          'Assist with ambulation and transfer safely at all times',
          'Maintain bed in low position with side rails up as appropriate',
          'Ensure call light and personal items within reach'
        ],
        startDate: '12/20/2024', 
        endDate: '12/23/2024' 
      }
    ],
    interventions: [
      { description: 'Provide care to enhance comfort and alleviate pain with reduction of bowel alterations', startDate: '12/20/2024', endDate: 'Ongoing' },
      { description: 'Reposition patient every 2 hours for skin check', startDate: '12/21/2024', endDate: 'Ongoing' },
      { description: 'Apply barrier cream per protocol', startDate: '12/21/2024', endDate: 'Ongoing' },
      { description: 'Use communication board or gestures as necessary', startDate: '12/21/2024', endDate: 'Ongoing' },
      { description: 'Implement a nutritional plan with family and patient', startDate: '12/20/2024', endDate: 'Ongoing' },
      { description: 'Assess pain level and administer medications per orders', startDate: '12/20/2024', endDate: 'Ongoing' },
      { description: 'Assist with ambulation and transfer safely at all times', startDate: '12/20/2024', endDate: 'Ongoing' },
      { description: 'Monitor bowel patterns and adjust plan as needed', startDate: '12/20/2024', endDate: 'Ongoing' },
      { description: 'Monitor breathing patterns and administer oxygen if necessary', startDate: '12/20/2024', endDate: 'Ongoing' },
      { description: 'Reassess breathing/skin every visit using rating scale acceptable to patient', startDate: '12/21/2024', endDate: 'Ongoing' },
      { description: 'Educate family on comfort measures and symptom management', startDate: '12/22/2024', endDate: 'Ongoing' },
      { description: 'Follow pain management protocol', startDate: '12/23/2024', endDate: 'Ongoing' }
    ],
    physicianOrders: [
      {
        date: '12/20/2024',
        orderType: 'MEDICAL TX ORDERS: Oxygen Therapy',
        clinician: 'German, Chelsea, RN',
        method: 'Verbal Order',
        instructions: 'Oxygen via nasal cannula at 2L/min for shortness of breath',
        signedBy: 'Alexis Alden, ARNP'
      }
    ]
  },
  {
    date: '12/19/2024',
    time: '11:15 AM - 12:15 PM',
    madeBy: 'Brown, Robyn',
    discipline: 'Chaplain',
    subType: 'Routine In Person Visit',
    assessments: {
      spiritual: 'Identified Spiritual Distress Level: 3',
      support: 'Patient; Chaplain'
    },
    notes: 'Patient rested sitting comfortably. This chaplain prayed for patient and sat quietly. Patient did not awaken during visit.',
    planOfCare: [
      { 
        problem: 'Spiritual Support', 
        description: 'Acceptance of approaching death related to bladder cancer', 
        goal: 'Patient will express spiritual peace and comfort',
        interventions: [
          'Provide spiritual support through prayer and presence',
          'Facilitate religious rituals as requested',
          'Support patient in expressing spiritual concerns'
        ],
        startDate: '12/15/2024', 
        endDate: '12/19/2024' 
      },
      { 
        problem: 'Family Support', 
        description: 'Incorporation of Family, Utilizing and disbursing funding in tandem with dying process as well as establishing ecclesiastics of continued collaborations', 
        goal: 'Family will demonstrate understanding of patient\'s condition and participate in care planning',
        interventions: [
          'Provide emotional support to family members',
          'Educate family on end-of-life process',
          'Include family in care decisions as appropriate'
        ],
        startDate: '12/19/2024', 
        endDate: '12/20/2024' 
      }
    ],
    interventions: [
      { description: 'Chaplain to Offer words of comfort and empathy', startDate: '12/18/2024', endDate: '12/20/2024' }
    ]
  },
  {
    date: '12/17/2024',
    time: '2:00 PM - 3:15 PM',
    madeBy: 'McLean, Kellee',
    discipline: 'RN',
    subType: 'Routine In Person Visit',
    assessments: {
      pain: 'PAINAD Total Score: 8',
      respiratory: 'Breathing Independent of Vocalization: Normal',
      mental: 'Negative Vocalization: Moaning, Facial Expression: Slightly Frowning, Body Language: Tense, Consolability: Not Consolable',
      vitalSigns: {
        respRate: '18',
        heartRate: '104',
        temperature: '98.6',
        bloodPressure: '110/64'
      },
      physical: {
        lungs: 'Clear',
        bowelSounds: 'Active',
        edema: 'None',
        cardiacRhythm: 'Normal',
        turgor: 'Normal',
        oralMucous: 'Moist'
      }
    },
    notes: 'Patient connected to alternative non-invasive route to administer Oxygen per CNP Signature. Continue to provide intervention for pain (as per GIP vs RLC Routine) while awaiting Family Consult. Patient exhibits restlessness when self-harm behavior is noted.',
    planOfCare: [
      { 
        problem: 'Continuity of care Acute In-Patient Admission', 
        description: 'To do Acute Medical crisis related to bladder cancer', 
        goal: 'Patient will transition smoothly between care settings with all needs addressed',
        interventions: [
          'Coordinate care with facility staff',
          'Ensure medication reconciliation during transitions',
          'Communicate plan of care to all providers'
        ],
        startDate: '12/09/2024', 
        endDate: '12/14/2024' 
      }
    ],
    interventions: [
      { description: 'Refer to and discuss all treatments and procedures with Facility staff. Awareness of change in behavior', startDate: '12/09/2024', endDate: '12/14/2024' },
      { description: 'Communicate with Facility regarding evaluation/treatment including observations and changes made 100% of time', startDate: '12/10/2024', endDate: '12/12/2024' },
      { description: 'Assess and communicate feelings and fears associated with diagnosis', startDate: '12/13/2024', endDate: '12/15/2024' },
      { description: 'Provide additional resource information for anxiety', startDate: '12/14/2024', endDate: '12/15/2024' },
      { description: 'Assess and communicate feelings associated with anxiety', startDate: '12/15/2024', endDate: '12/16/2024' },
      { description: 'Educate staff, family, and patient regarding factors associated with increased anxiety', startDate: '12/16/2024', endDate: '12/17/2024' },
      { description: 'Teach relaxation/stress relief techniques/plan. Encourage the use daily/PRN', startDate: '12/17/2024', endDate: '12/18/2024' },
      { description: 'Provide information on alternative/complementary treatments', startDate: '12/18/2024', endDate: '12/19/2024' },
      { description: 'Implement non-pharmacologic interventions for anxiety', startDate: '12/19/2024', endDate: '12/20/2024' },
      { description: 'Teach relaxation techniques; encourage practice', startDate: '12/20/2024', endDate: '12/21/2024' }
    ]
  },
  {
    date: '12/15/2024',
    time: '11:45 AM - 12:45 PM',
    madeBy: 'Flannery, Myisha',
    discipline: 'MSW',
    subType: 'Routine In Person Visit',
    assessments: {
      psychosocial: 'Unable to respond due to limited articulation',
      environmental: 'Acute looked around the room during each visit and observed patient\'s surroundings. Comfortable and familiar',
      support: 'Observed to be sister and family',
      spiritual: 'Noted to be important as discussed',
      familyUnderstanding: 'Fair understanding of each type of care involved',
      safety: 'Safe options for routine daily care are evaluated by Social Worker Observed away from fear and unknown'
    },
    notes: 'SW requested to attend patient\'s new attempts on visiting hours at Barnette Center. Upon SW arrival, patient is laying on the hospital bed. Family reported less interest at 3pm long term. Daughter speaks in solace for awhile. Will recommend review of patient\'s pass-time preference as well as general understanding of nursing. Collaborated with primary care upon admitting overnight from hospital. Will continue to monitor and provide support.',
    planOfCare: [
      { 
        problem: 'Psychosocial Support', 
        description: 'Continue work of family. Maintain integration of support in close group such as a compositional classroom', 
        goal: 'Patient and family will utilize available support systems effectively',
        interventions: [
          'Facilitate family meetings to address concerns',
          'Connect family with community resources as needed',
          'Provide counseling for grief and loss issues'
        ],
        startDate: '12/15/2024', 
        endDate: '1/8/2025' 
      }
    ],
    interventions: [
      { description: 'Chaplain Interventions - Continuity of opening stores for family', startDate: '12/15/2024', endDate: '12/20/2024' },
      { description: 'Solicit Routine Outreach Module', startDate: '12/15/2024', endDate: '12/19/2024' },
      { description: 'Results/Outcomes Affirmed', startDate: '12/18/2024', endDate: '12/20/2024' },
      { description: 'Chaplain to Provide pastoral counseling', startDate: '12/18/2024', endDate: '12/20/2024' }
    ]
  }
];

// Sample version history
export interface VersionHistoryByDate {
  date: string;
  time: string;
  user: {
    name: string;
    discipline: string;
  };
  sections: {
    [section: string]: {
      changes: {
        field: string;
        oldValue: string;
        newValue: string;
      }[];
    };
  };
}

export const versionHistoryByDate: VersionHistoryByDate[] = [
  {
    date: '12/20/2024',
    time: '10:15',
    user: {
      name: 'German, Chelsea',
      discipline: 'RN'
    },
    sections: {
      "Medications": {
        changes: [
          { field: 'Medication', oldValue: '', newValue: 'Morphine (5mg oral, q4h PRN)' },
          { field: 'Medication', oldValue: 'Lorazepam (0.25mg oral, BID PRN)', newValue: 'Lorazepam (0.5mg oral, BID PRN)' }
        ]
      },
      "Plan of Care": {
        changes: [
          { field: 'Problem', oldValue: 'Pain Management - Administer pain medication as ordered', newValue: 'Pain Management - Increase pain medication as ordered' },
          { field: 'Problem', oldValue: '', newValue: 'Respiratory Distress' },
          { field: 'Intervention', oldValue: '', newValue: 'Oxygen therapy as needed' }
        ]
      },
      "Diagnosis": {
        changes: [
          { field: 'Diagnosis', oldValue: '', newValue: 'Cough (786.2)' }
        ]
      }
    }
  },
  {
    date: '12/22/2024',
    time: '14:35',
    user: {
      name: 'Williams, Janet',
      discipline: 'Administrative'
    },
    sections: {
      "Client Information": {
        changes: [
          { field: 'Phone Number', oldValue: '(*************', newValue: '(*************' },
          { field: 'Address', oldValue: '123 Main St, Anytown, USA', newValue: '225 Cornet Drive, Centerville, MD 21617-2647' }
        ]
      }
    }
  },
  {
    date: '12/21/2024',
    time: '16:40',
    user: {
      name: 'German, Chelsea',
      discipline: 'RN'
    },
    sections: {
      "Plan of Care": {
        changes: [
          { field: 'Problem', oldValue: '', newValue: 'Skin Integrity at Risk' },
          { field: 'Description', oldValue: '', newValue: 'Due to prolonged immobility and effects of treatment/non-compliance' },
          { field: 'Problem', oldValue: '', newValue: 'Communication Route Impaired' },
          { field: 'Description', oldValue: '', newValue: 'Patient cannot speak due to illness/medication effects' }
        ]
      }
    }
  },
  {
    date: '12/20/2024',
    time: '15:47',
    user: {
      name: 'Johnson, Sarah',
      discipline: 'Physician'
    },
    sections: {
      "Diagnosis": {
        changes: [
          { field: 'Diagnosis', oldValue: '', newValue: 'Cough (786.2)' },
          { field: 'Diagnosis Date', oldValue: '', newValue: '11/10/2024' }
        ]
      },
      "Plan of Care": {
        changes: [
          { field: 'Problem', oldValue: '', newValue: 'Gastrointestinal Bleeding Patterns Altered' },
          { field: 'Description', oldValue: '', newValue: 'Due to disease progression' },
          { field: 'Problem', oldValue: '', newValue: 'Respiratory Pattern Change' },
          { field: 'Description', oldValue: '', newValue: 'Breathing difficulty and sleep disturbance' },
          { field: 'Problem', oldValue: '', newValue: 'Bowel Elimination Altered' },
          { field: 'Description', oldValue: '', newValue: 'Irregular bowel movements, secondary to cancer treatment effects' },
          { field: 'Problem', oldValue: '', newValue: 'Nutrition Altered Less Than Body Requirements' },
          { field: 'Description', oldValue: '', newValue: 'Factors such as fatigue, anorexia, pain, and cancer treatment contribute' },
          { field: 'Problem', oldValue: '', newValue: 'Pain Acute/Chronic Uncontrolled' },
          { field: 'Description', oldValue: '', newValue: 'Cancer-related' },
          { field: 'Problem', oldValue: '', newValue: 'Mobility Disabled/Risk for Falls' },
          { field: 'Description', oldValue: '', newValue: 'Reduced safety awareness and cognitive impairment' }
        ]
      }
    }
  },
  {
    date: '12/19/2024',
    time: '11:50',
    user: {
      name: 'Brown, Robyn',
      discipline: 'Chaplain'
    },
    sections: {
      "Plan of Care": {
        changes: [
          { field: 'Problem', oldValue: '', newValue: 'Family Support' },
          { field: 'Description', oldValue: '', newValue: 'Incorporation of Family, Utilizing and disbursing funding in tandem with dying process as well as establishing ecclesiastics of continued collaborations' }
        ]
      }
    }
  },
  {
    date: '12/16/2024',
    time: '13:20',
    user: {
      name: 'McLean, Kellee',
      discipline: 'RN'
    },
    sections: {
      "Medications": {
        changes: [
          { field: 'Medication', oldValue: '', newValue: 'haloperidol (tablet)' },
          { field: 'Medication', oldValue: '', newValue: 'Ativan (tablet)' },
          { field: 'Medication', oldValue: '', newValue: 'phenobarbital (tablet)' },
          { field: 'Medication', oldValue: '', newValue: 'phenobarbital (solution)' },
          { field: 'Medication', oldValue: '', newValue: 'SenegaLax (tab)' },
          { field: 'Medication', oldValue: '', newValue: 'LevemisL (tablet, subling)' },
          { field: 'Medication', oldValue: '', newValue: 'hyoscyamine (tablet)' },
          { field: 'Medication', oldValue: '', newValue: 'morphine (solution)' }
        ]
      }
    }
  },
  {
    date: '12/15/2024',
    time: '09:30',
    user: {
      name: 'Brown, Robyn',
      discipline: 'Chaplain'
    },
    sections: {
      "Plan of Care": {
        changes: [
          { field: 'Problem', oldValue: '', newValue: 'Spiritual Support' },
          { field: 'Description', oldValue: '', newValue: 'Acceptance of approaching death related to bladder cancer' }
        ]
      }
    }
  },
  {
    date: '12/15/2024',
    time: '09:22',
    user: {
      name: 'Williams, Janet',
      discipline: 'Administrative'
    },
    sections: {
      "Client Information": {
        changes: [
          { field: 'Emergency Contact', oldValue: 'Jane Doe (Sister)', newValue: 'Robert Wainwright (Husband)' },
          { field: 'Emergency Contact Phone', oldValue: '(*************', newValue: '(*************' }
        ]
      }
    }
  },
  {
    date: '12/10/2024',
    time: '11:05',
    user: {
      name: 'Williams, Janet',
      discipline: 'Administrative'
    },
    sections: {
      "Client Information": {
        changes: [
          { field: 'Insurance Provider', oldValue: 'Private Insurance', newValue: 'Medicare' },
          { field: 'Insurance ID', oldValue: 'PI12345678', newValue: 'MC98765432' }
        ]
      }
    }
  },
  {
    date: '12/05/2024',
    time: '10:30',
    user: {
      name: 'Johnson, Sarah',
      discipline: 'Physician'
    },
    sections: {
      "Diagnosis": {
        changes: [
          { field: 'Diagnosis', oldValue: '', newValue: 'Attention to gastrostomy [endoscopy] (V55.3)' },
          { field: 'Diagnosis Date', oldValue: '', newValue: '11/05/2024' }
        ]
      }
    }
  },
  {
    date: '11/16/2024',
    time: '09:45',
    user: {
      name: 'German, Chelsea',
      discipline: 'RN'
    },
    sections: {
      "Medications": {
        changes: [
          { field: 'Medication', oldValue: '', newValue: 'haloperidol (5 mg)' },
          { field: 'Medication', oldValue: '', newValue: 'senna (8.6 mg (2T))' },
          { field: 'Medication', oldValue: '', newValue: 'bisacodyl (10 mg)' }
        ]
      }
    }
  },
  {
    date: '11/15/2024',
    time: '14:10',
    user: {
      name: 'German, Chelsea',
      discipline: 'RN'
    },
    sections: {
      "Medications": {
        changes: [
          { field: 'Medication', oldValue: '', newValue: 'acetaminophen (500 mg)' },
          { field: 'Medication', oldValue: '', newValue: 'metoprolol succinate (Toprol XL tablet)' },
          { field: 'Medication', oldValue: '', newValue: 'gabapentin (capsule)' },
          { field: 'Medication', oldValue: '', newValue: 'morphine (solution)' },
          { field: 'Medication', oldValue: '', newValue: 'lorazepam (Ativan)' },
          { field: 'Medication', oldValue: '', newValue: 'morphine (concentration)' }
        ]
      }
    }
  },
  {
    date: '11/12/2024',
    time: '11:30',
    user: {
      name: 'McLean, Kellee',
      discipline: 'RN'
    },
    sections: {
      "Medications": {
        changes: [
          { field: 'Medication', oldValue: '', newValue: 'morphine sulfate (5 mg/ml)' }
        ]
      }
    }
  },
  {
    date: '11/11/2024',
    time: '10:15',
    user: {
      name: 'McLean, Kellee',
      discipline: 'RN'
    },
    sections: {
      "Medications": {
        changes: [
          { field: 'Medication', oldValue: '', newValue: 'lorazepam (2 mg)' }
        ]
      }
    }
  },
  {
    date: '11/03/2024',
    time: '08:15',
    user: {
      name: 'Johnson, Sarah',
      discipline: 'Physician'
    },
    sections: {
      "Diagnosis": {
        changes: [
          { field: 'Diagnosis', oldValue: '', newValue: 'Neoplasm related pain (acute) (338.3)' },
          { field: 'Diagnosis', oldValue: '', newValue: 'Debility unspecified (799.3)' },
          { field: 'Diagnosis', oldValue: '', newValue: 'Other malaise and fatigue (780.79)' },
          { field: 'Diagnosis', oldValue: '', newValue: 'Dysphagia (787.2)' },
          { field: 'Diagnosis', oldValue: '', newValue: 'Unspecified protein-calorie malnutrition (263.9)' },
          { field: 'Diagnosis', oldValue: '', newValue: 'Dementia with behavioral disturbance (331.82)' },
          { field: 'Diagnosis', oldValue: '', newValue: 'Chronic airway obstruction, not elsewhere specified (496)' }
        ]
      }
    }
  }
];

// For backward compatibility
export const versionHistory: { [section: string]: VersionHistoryEntry[] } = {
  "Client Information": [],
  "Diagnosis": [],
  "Medications": [],
  "Plan of Care": []
};

// Populate the versionHistory object from versionHistoryByDate
versionHistoryByDate.forEach(entry => {
  Object.keys(entry.sections).forEach(section => {
    if (!versionHistory[section]) {
      versionHistory[section] = [];
    }
    
    versionHistory[section].push({
      date: entry.date,
      time: entry.time,
      user: `${entry.user.name} (${entry.user.discipline})`,
      changes: entry.sections[section].changes
    });
  });
});

// Sample initial data for the survey
export const getSampleSurveyData = (): SurveyData => {
  return {
    patientName: "John Doe",
    dateOfBirth: "1955-05-15",
    medicalRecordNumber: "MRN12345678",
    gender: "Male",
    address: "123 Main St, Anytown, USA",
    phoneNumber: "(*************",
    chiefComplaint: "Progressive weakness and shortness of breath",
    historyOfPresentIllness: "Patient with advanced COPD experiencing increased dyspnea and fatigue over past 3 months. Multiple hospitalizations in the past 6 months.",
    pastMedicalHistory: ["Respiratory Disease", "Heart Disease"],
    medications: "Albuterol inhaler, Spiriva, Prednisone, Furosemide, Morphine 5mg oral q4h PRN",
    allergies: "Penicillin, Sulfa drugs",
    functionalStatus: "40% - Mainly in bed",
    symptomsPresent: ["Dyspnea", "Fatigue", "Pain"],
    painLevel: 7
  };
};

// Sample timeline entries
export const getSampleTimelineEntries = (): TimelineEntry[] => {
  // Create a base date for the timeline entries
  const baseDate = new Date();
  
  // Create an array to hold all timeline entries
  const entries: TimelineEntry[] = [];
  
  // Add recent entries
  entries.push({
    id: '1',
    timestamp: new Date(baseDate.getTime() - 1 * 60 * 60 * 1000), // 1 hour ago
    type: 'note',
    title: 'Visit Note',
    content: 'Patient reports increased pain in lower back, rate it as 7 out of 10. Also experiencing more difficulty breathing at night.',
    user: {
      name: "Dr. Sarah Johnson",
      role: "Hospice Physician"
    },
    changes: [
      { field: 'Pain level', value: '7/10' },
      { field: 'Symptoms', value: ['Dyspnea', 'Fatigue', 'Pain'] }
    ],
    tags: ['Visit Note', 'POC'],
    mainTag: 'Text',
    aiSummary: 'Patient assessment documented with changes to pain levels and related symptoms. Follow-up care plan adjusted accordingly.'
  });
  
  entries.push({
    id: '2',
    timestamp: new Date(baseDate.getTime() - 3 * 60 * 60 * 1000), // 3 hours ago
    type: 'document',
    title: 'Lab Results.pdf',
    content: 'Lab results show elevated blood glucose levels and normal hemoglobin.',
    user: {
      name: "Lab Technician",
      role: "Clinical Laboratory"
    },
    changes: [
      { field: 'Blood glucose', value: '135 mg/dL (High)' },
      { field: 'Hemoglobin', value: '13.2 g/dL (Normal)' }
    ],
    tags: ['Lab Results'],
    mainTag: 'Doc/PDF',
    aiSummary: 'New lab results uploaded showing elevated blood glucose levels (135 mg/dL) and normal hemoglobin (13.2 g/dL).'
  });
  
  // Add entries for each visit
  sampleVisits.forEach((visit, index) => {
    const visitDate = new Date(visit.date);
    const visitTimeParts = visit.time.split(' - ')[0].split(':');
    visitDate.setHours(parseInt(visitTimeParts[0]), parseInt(visitTimeParts[1]));
    
    // Determine tags based on visit content
    const tags: EntryTag[] = ['Visit Note'];
    
    // Add POC tag if there are plan of care items
    if (visit.planOfCare && visit.planOfCare.length > 0) {
      tags.push('POC');
    }
    
    // Add Medication tag if there are medication-related orders
    if (visit.physicianOrders && visit.physicianOrders.some(order => 
      order.orderType.toLowerCase().includes('medication') || 
      order.instructions.toLowerCase().includes('medication'))) {
      tags.push('Medications');
    }
    
    // Generate AI summary based on visit content
    let aiSummary = '';
    if (visit.notes.toLowerCase().includes('pain')) {
      aiSummary = 'Patient assessment documented with changes to pain levels and related symptoms. Follow-up care plan adjusted accordingly.';
    } else if (visit.planOfCare && visit.planOfCare.length > 0) {
      aiSummary = `${visit.discipline} visit completed with updates to plan of care. ${visit.planOfCare.length} care items were reviewed and updated.`;
    } else {
      aiSummary = `Routine ${visit.discipline} visit completed. Patient assessment performed and documentation updated.`;
    }
    
    entries.push({
      id: `visit-${index}`,
      timestamp: visitDate,
      type: 'note',
      title: `${visit.discipline} Visit`,
      content: visit.notes,
      user: {
        name: visit.madeBy,
        role: visit.discipline
      },
      changes: visit.planOfCare ? visit.planOfCare.map(item => ({
        field: item.problem || 'Plan of Care',
        value: item.description
      })) : [],
      tags,
      mainTag: 'Template',
      aiSummary
    });
  });
  
  // Add entry for demographics update
  entries.push({
    id: 'demographics-update',
    timestamp: new Date(baseDate.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    type: 'demographics',
    title: 'Demographics Update',
    content: 'Updated patient contact information and emergency contacts.',
    user: {
      name: "Admin Staff",
      role: "Administrative Assistant"
    },
    changes: [
      { field: 'Phone Number', value: clientInfo.phone },
      { field: 'Address', value: clientInfo.address }
    ],
    tags: ['Demographics'],
    mainTag: 'Doc/PDF',
    aiSummary: 'Updated patient demographic information including contact details and emergency contacts.'
  });
  
  // Add entry for physician orders
  samplePhysicianOrders.forEach((order, index) => {
    const orderDate = new Date(order.date);
    orderDate.setHours(10, 0); // Assume 10:00 AM for orders
    
    // Determine tags based on order type
    const tags = new Set<EntryTag>();
    
    if (order.orderType.toLowerCase().includes('medication')) {
      tags.add('Medications');
    }
    
    if (order.instructions.toLowerCase().includes('plan of care') || 
        order.instructions.toLowerCase().includes('care plan')) {
      tags.add('POC');
    }
    
    if (order.instructions.toLowerCase().includes('diagnosis') || 
        order.instructions.toLowerCase().includes('diagnoses')) {
      tags.add('Diagnosis');
    }
    
    // For comprehensive orders, add all relevant tags
    if (order.orderType === 'Comprehensive Order') {
      tags.add('Medications');
      tags.add('POC');
      tags.add('Diagnosis');
    }
    
    // Convert Set to Array
    const tagsArray = Array.from(tags);
    
    // Generate AI summary based on order type
    let aiSummary = '';
    if (tags.has('Medications') && tags.has('POC') && tags.has('Diagnosis')) {
      aiSummary = `Comprehensive order submitted by ${order.clinician} and approved by ${order.signedBy}. Includes medication changes, plan of care updates, and diagnosis additions.`;
    } else if (tags.has('Medications')) {
      aiSummary = `Medication order submitted by ${order.clinician}. Changes to the patient's medication regimen documented.`;
    } else {
      aiSummary = `${order.orderType} submitted by ${order.clinician} and signed by ${order.signedBy}.`;
    }
    
    entries.push({
      id: `order-${index}`,
      timestamp: orderDate,
      type: 'medication',
      title: `Physician Order: ${order.orderType}`,
      content: order.instructions,
      user: {
        name: order.clinician,
        role: "Clinician"
      },
      changes: [
        { field: 'Order Type', value: order.orderType },
        { field: 'Method', value: order.method }
      ],
      tags: tagsArray,
      mainTag: 'Template',
      aiSummary
    });
  });
  
  // Sort entries by timestamp (newest first)
  entries.sort((a: TimelineEntry, b: TimelineEntry) => b.timestamp.getTime() - a.timestamp.getTime());
  
  return entries;
};
