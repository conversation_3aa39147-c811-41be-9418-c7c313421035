// TODO_ASAP: move these to apptypes

// Import request types
import { Request, RequestTag, FileAttachment, AudioAttachment, RequestResponse } from './types/request';

// Define types for timeline entries
export type EntryType = 'visit note' | 'document' | 'audio' | 'medications' | 'demographics' | 'system' | 'request';
export type EntryTag = 'Visit Notes' | 'Demographics' | 'POC' | 'Medications' | 'Diagnosis' | 'Plan Details' | 'Lab Results';
export type EntryMainTag = 'Audio' | 'Template' | 'Text' | 'Doc/PDF' | 'Request';

export const EntryTagColors = {
  'Visit Note': 'blue',
  'Demographics': 'green',
  'POC': 'yellow',
  'Medications': 'red',
  'Diagnosis': 'purple',
  'Plan Details': 'orange',
  'Lab Results': 'gray',
  'DME': 'blue',
  'Supplies': 'gray'
};

// export interface TimelineEntry {
//   id: string;
//   timestamp: string;
//   type: EntryType;
//   title: string;
//   content: string;
//   user: {
//     name: string;
//     role: string;
//   };
//   changes?: {
//     field: string;
//     value: string | number | string[] | number[] | boolean;
//   }[];
//   metadata?: {
//     [key: string]: string | number | boolean;
//   };
//   aiSummary?: string;
//   hasVersionHistory?: boolean;
//   versionHistoryDate?: string;
//   tags?: EntryTag[];
//   mainTag?: EntryMainTag;
//   linkedVisitDate?: string;
//   linkedVisitProvider?: string;
//   linkedVisit?: Visit;
  
//   // Timeline item specific index for version history
//   timeLineItemIndex?: number;
  
//   // Request reference properties
//   requestRef?: {
//     id: string;
//     resourceType: string;
//   };
  
//   // Request-specific properties
//   resourceType?: 'Request';
//   requestTags?: RequestTag[];
//   responses?: RequestResponse[];
//   fileAttachments?: string[];
//   audioAttachments?: string[];
//   createdAt?: string;
// }

export interface VersionHistorySectionType {
  name: EntryTag;
  changes: VersionChange[];
}
// Interface for the timeLineItems from patient object
// export interface TimeLineItem {
//   tags: EntryTag[];
//   title: string;
//   summary: string;
//   madeBy: {
//     name: string;
//     date: string;
//   };
//   approvedBy: {
//     name: string;
//     date: string;
//   };
//   requestRef: {
//     id: string;
//     resourceType: string;
//   };
//   versionHistorySections: VersionHistorySectionType[];
// }

// Export request types
export type { Request, RequestTag, FileAttachment, AudioAttachment, RequestResponse };

// Interface for markdown sections
export interface MarkdownSection {
  title: string;
  content: string;
  level: number;
  children: MarkdownSection[];
}

// Interface for version history
export interface VersionChange {
  field: string;
  oldValue: any;
  newValue: any;
}

export interface VersionHistoryEntry {
  date: string;
  time: string;
  user: string;
  changes: VersionChange[];
}

export interface SectionVersionHistory {
  [section: string]: VersionHistoryEntry[];
}

// export interface VersionHistoryItem {
//   id: string;
//   title: string;
//   summary: string;
//   timestamp: string;
//   user: {
//     name: string;
//     role: string;
//   };
//   changes: VersionChange[];
// }

export interface VersionHistoryByDate {
  date: string;
  time: string;
  user: {
    name: string;
    discipline: string;
  };
  sections: {
    [section: string]: {
      changes: VersionChange[];
    };
  };
}

// Interfaces for sample data
export interface Diagnosis {
  code: string;
  description: string;
  date: string;
}

export interface Medication {
  name: string;
  dosage: string;
  route: string;
  frequency: string;
  indication: string;
  startDate: string;
}

export interface IDGMeeting {
  date: string;
  time: string;
  status: string;
  topic: string;
  attendees: string[];
  clinicalInfo: {
    diagnoses: string;
    changes: string[];
  };
  providerInfo: {
    meetingProvider: string;
    nurseCaseManager: string;
    medicalDirector: string;
  };
  notes: string;
  familyNotifications: string[];
  clinicalInterventions: string[];
  formalReport: string[];
  conclusion: string[];
  followUp: {
    date: string;
    time: string;
    agenda: string;
    contact: string;
    notes: string[];
  };
}

export interface VisitAssessments {
  [key: string]: string | {
    respRate?: string;
    heartRate?: string;
    temperature?: string;
    bloodPressure?: string;
    lungs?: string;
    bowelSounds?: string;
    edema?: string;
    cardiacRhythm?: string;
    turgor?: string;
    oralMucous?: string;
  };
}

export interface PlanOfCareItem {
  problem?: string;
  description: string;
  goal?: string;
  interventions?: string[];
  startDate: string;
  endDate: string;
}

export interface Intervention {
  description: string;
  startDate: string;
  endDate: string;
}

export interface PhysicianOrder {
  date: string;
  orderType: string;
  clinician: string;
  method: string;
  instructions: string;
  signedBy: string;
}

export interface Visit {
  date: string;
  time: string;
  madeBy: string;
  discipline: string;
  subType: string;
  assessments: VisitAssessments;
  notes: string;
  planOfCare?: PlanOfCareItem[];
  interventions?: Intervention[];
  physicianOrders?: PhysicianOrder[];
}

export interface ClientInfo {
  name: string;
  ssn: string;
  hospiceCenter: string;
  dob: string;
  room: string;
  age: number;
  address: string;
  phone: string;
  city: string;
  admitDate: string;
  certification: string;
  certProgram: string;
  team: string;
  status: string;
  levelOfCare: string;
  certPeriod: string;
  benefitPeriod: string;
  location: string;
  physician: string;
  nursingProvider: string;
  clientTransportation: string;
  payorSource: string;
  caseManager: string;
}

// Interface for survey data
export interface SurveyData {
  patientName?: string;
  dateOfBirth?: string;
  medicalRecordNumber?: string;
  gender?: string;
  address?: string;
  phoneNumber?: string;
  chiefComplaint?: string;
  historyOfPresentIllness?: string;
  pastMedicalHistory?: string[];
  medications?: string;
  allergies?: string;
  functionalStatus?: string;
  symptomsPresent?: string[];
  painLevel?: number;
  goals?: string;
  interventions?: string;
  services?: string[];
  equipmentNeeded?: string;
  visitFrequency?: string;
}
