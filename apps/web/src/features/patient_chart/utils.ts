import { TimelineEntry, Visit } from './types';

// Format timestamp for display
export const formatTimestamp = (timestamp: Date): string => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  const date = new Date(timestamp);
  const isToday = date >= today && date < new Date(today.getTime() + 24 * 60 * 60 * 1000);
  const isYesterday = date >= yesterday && date < today;
  
  const timeString = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  
  if (isToday) {
    return `Today at ${timeString}`;
  } else if (isYesterday) {
    return `Yesterday at ${timeString}`;
  } else {
    return `${date.toLocaleDateString()} at ${timeString}`;
  }
};

// Sample timeline entries for demonstration
export const getSampleTimelineEntries = (): TimelineEntry[] => {
  const currentUser = {
    name: "<PERSON>. <PERSON>",
    role: "Hospice Physician"
  };
  
  const sampleVisit: Visit = {
    date: "2024-04-15",
    time: "10:30 AM",
    madeBy: "Dr. Sarah <PERSON>",
    discipline: "Physician",
    subType: "Routine Visit",
    assessments: {
      painLevel: "3/10",
      respRate: "18",
      heartRate: "72",
      temperature: "98.6",
      bloodPressure: "120/80",
      lungs: "Clear to auscultation",
      bowelSounds: "Normal",
      edema: "None",
      cardiacRhythm: "Regular",
      turgor: "Good",
      oralMucous: "Moist"
    },
    notes: "Patient reports improved pain control with current medication regimen. No new concerns reported.",
    planOfCare: [
      {
        description: "Pain Management",
        goal: "Maintain pain level below 4/10",
        interventions: ["Medication review", "Non-pharmacological interventions"],
        startDate: "2024-03-01",
        endDate: "2024-06-01"
      }
    ]
  };
  
  return [
    {
      id: "1",
      timestamp: new Date(2024, 3, 15, 10, 30),
      type: "note",
      title: "Visit Note",
      content: "Patient reports improved pain control with current medication regimen. No new concerns reported.",
      user: currentUser,
      tags: ["Visit Note"],
      mainTag: "Text",
      hasVersionHistory: true,
      versionHistoryDate: "2024-04-15",
      linkedVisit: sampleVisit,
      changes: []
    },
    {
      id: "2",
      timestamp: new Date(2024, 3, 14, 15, 45),
      type: "medication",
      title: "Medication Order",
      content: "Morphine Sulfate 15mg PO q4h PRN for pain",
      user: currentUser,
      tags: ["Medications"],
      mainTag: "Text",
      changes: []
    },
    {
      id: "3",
      timestamp: new Date(2024, 3, 13, 9, 0),
      type: "document",
      title: "Document Upload",
      content: "Uploaded document: Patient_History.pdf",
      user: currentUser,
      metadata: {
        fileName: "Patient_History.pdf",
        fileSize: 1024 * 1024 * 2.5,
        fileType: "application/pdf"
      },
      mainTag: "Doc/PDF",
      changes: []
    },
    {
      id: "4",
      timestamp: new Date(2024, 3, 12, 14, 30),
      type: "audio",
      title: "Audio Note",
      content: "Patient reports increased pain in lower back. Will adjust medication dosage.",
      user: currentUser,
      metadata: {
        audioUrl: "/sample-audio.mp3",
        duration: "1:24"
      },
      mainTag: "Audio",
      changes: []
    },
    {
      id: "5",
      timestamp: new Date(2024, 3, 10, 11, 15),
      type: "demographics",
      title: "Demographics Update",
      content: "Updated emergency contact information.",
      user: currentUser,
      tags: ["Demographics"],
      mainTag: "Text",
      changes: [
        { field: "Emergency Contact", value: "Jane Smith (Daughter)" },
        { field: "Emergency Contact Phone", value: "************" }
      ]
    }
  ];
};
