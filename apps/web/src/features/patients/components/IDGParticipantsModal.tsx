import { useState } from "react";
import { Modal, MultiSelect, Button, Group, Text, Alert } from "@mantine/core";
import { useGetUsersQuery } from "../../../store/api_extensions/userApiExt";
import { IDGMeeting, User } from "@hospice-os/apptypes";
import {
  useCreateResourceMutation,
  useGetResourcesQuery,
} from "../../../store/api_extensions/resourceApiExt";
import { useGetPatientsQuery } from "../../../store/api_extensions/patientApiExt";

interface IDGParticipantsModalProps {
  opened: boolean;
  onClose: () => void;
  onStartMeeting: () => void;
}

export function IDGParticipantsModal({
  opened,
  onClose,
  onStartMeeting,
}: IDGParticipantsModalProps) {
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>(
    [],
  );
  const { data: users } = useGetUsersQuery({ role: "all" });

  const [createResource] = useCreateResourceMutation();
  const { data: patients } = useGetPatientsQuery(undefined); // TODO: Need to filter for IDG patients
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<boolean>(false);

  const { data: idgMeetings } = useGetResourcesQuery({
    resourceType: "IDGMeeting",
    filters: {
      status: "In Progress",
      limit: 1,
    },
  }) as { data: IDGMeeting[] | undefined };
  const [idgMeetingInProgress, _] = useState<boolean>(
    (idgMeetings ?? []).length > 0,
  );

  const participantOptions =
    users?.map((user: User) => ({
      value: user.id,
      label: `${user.firstName} ${user.lastName} (${user.role})`,
    })) || [];

  const handleStartMeeting = async () => {
    if (idgMeetingInProgress) {
      onStartMeeting();
      setSelectedParticipants([]);
      onClose();
      return;
    }

    setIsLoading(true);
    setError(false);

    try {
      // Create IDG Meeting Resource
      const idgMeeting = await createResource({
        resourceType: "IDGMeeting",
        participants: selectedParticipants.map((s) => ({
          id: s,
          resourceType: "User",
        })),
        patients: patients?.map((p) => ({ id: p.id, resourceType: "Patient" })),
        startTime: new Date().toISOString(),
        status: "In Progress",
        schemaId: "cb223201-8923-427f-af00-00a27221f5c1", // IDG Meeting Schema
      }).unwrap();

      onStartMeeting();
      setSelectedParticipants([]);
      onClose();
    } catch (error) {
      console.error("Error creating IDG meeting: ", error);
      setError(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Select IDG Meeting Participants"
      centered
    >
      {idgMeetingInProgress ? (
        <Alert color="yellow" mb="md">
          <Text c="yellow">An IDG meeting is already in progress.</Text>
        </Alert>
      ) : (
        <>
          <Text mb="md">
            Please select the participants for this IDG meeting. You can select
            multiple participants from different disciplines.
          </Text>

          {error && (
            <Alert color="red" mb="md">
              <Text c="red">
                An error occurred while creating the IDG meeting. Please try
                again.
              </Text>
            </Alert>
          )}

          <MultiSelect
            data={participantOptions}
            value={selectedParticipants}
            onChange={setSelectedParticipants}
            placeholder="Select participants..."
            searchable
            clearable
            mb="xl"
          />
        </>
      )}

      <Group justify="flex-end">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        {idgMeetingInProgress ? (
          <Button
          loading={isLoading}
          onClick={handleStartMeeting}
          >
            Continue Meeting
          </Button>
        ) : (
          <Button
            loading={isLoading}
            onClick={handleStartMeeting}
            disabled={selectedParticipants.length === 0 || patients?.length === 0}
          >
            Start Meeting
          </Button>
        )}
      </Group>
    </Modal>
  );
}
