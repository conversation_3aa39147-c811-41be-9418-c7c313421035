import { useState, useMemo } from "react";
// We don't need the user context since all filtering is done on the server
// import { useUser } from '../../auth/components';
import {
  Table,
  TextInput,
  Group,
  Text,
  Badge,
  Card,
  ScrollArea,
  rem,
  Anchor,
  <PERSON><PERSON>,
  Loader,
} from "@mantine/core";
import { IconPlus, IconSearch, IconUsers } from "@tabler/icons-react";
import { IDGMeeting, Patient } from "@hospice-os/apptypes";
import { Link, useNavigate } from "react-router-dom";
import { AddPatientModal } from "../../../components/modals/AddPatientModal";
import { useAppDispatch } from "../../../store/hooks";
import { setCurrentPatientId } from "../../../store/patientChartSlice";
import { IDGParticipantsModal } from "./IDGParticipantsModal";
import { useGetResourcesQuery } from "../../../store/api_extensions/resourceApiExt";
import { getPatientStatusBadgeColor } from "../../../components/PatientChartHeader";

interface PatientsListProps {
  patients: Patient[];
  isLoading?: boolean;
  error?: string;
}

// Commented out as it's not currently used
// const getIdgStatusColor = (status?: string) => {
//   switch (status) {
//     case 'Completed':
//       return 'green';
//     case 'Pending':
//       return 'orange';
//     case 'Not Required':
//       return 'gray';
//     default:
//       return 'blue';
//   }
// };

const formatTerminalDiagnosis = (diagnosis: any) => {
  if (!diagnosis) return "N/A";

  // If it's a string, return as is
  if (typeof diagnosis === "string") {
    return diagnosis;
  }

  // If it's an object with code and description, combine them
  if (
    typeof diagnosis === "object" &&
    diagnosis.code &&
    diagnosis.description
  ) {
    return `${diagnosis.description} (${diagnosis.code})`;
  }

  return "N/A";
};

export function PatientsList({
  patients,
  isLoading,
  error,
}: PatientsListProps) {
  // We don't need to use the user context here since all filtering is done on the server
  // const { user } = useUser();
  const [searchQuery, setSearchQuery] = useState("");
  const [idgParticipantsModalOpen, setIdgParticipantsModalOpen] =
    useState(false);
  const navigate = useNavigate();
  const [addPatientModalOpen, setAddPatientModalOpen] = useState(false);
  const dispatch = useAppDispatch();

  const { data: idgMeetings } = useGetResourcesQuery({
    resourceType: "IDGMeeting",
    filters: {
      status: "In Progress",
      limit: 1,
    },
  }) as { data: IDGMeeting[] | undefined };

  const filteredPatients = useMemo(() => {
    // Filter by search query only
    // Agency filtering is handled by the server
    if (!searchQuery.trim()) {
      return patients;
    }

    const lowerCaseQuery = searchQuery.toLowerCase();

    return patients.filter((patient) => {
      return (
        patient.firstName.toLowerCase().includes(lowerCaseQuery) ||
        patient.lastName.toLowerCase().includes(lowerCaseQuery) ||
        patient.terminalDiagnosis?.toLowerCase().includes(lowerCaseQuery) ||
        patient.status?.toLowerCase().includes(lowerCaseQuery) ||
        (patient.dateOfBirth && patient.dateOfBirth.includes(lowerCaseQuery))
      );
    });
  }, [patients, searchQuery]);

  const formatDate = (dateString: string) => {
    if (!dateString) return "";

    // If it's already in MM/DD/YYYY format, return as is
    if (dateString.includes("/")) return dateString;

    // Otherwise, assume ISO format and convert
    // Add 'T00:00:00' to ensure the date is interpreted in local timezone
    const date = new Date(dateString + "T00:00:00");
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
      });
    }
    return dateString;
  };

  const handleStartIDGMeeting = () => {
    navigate("/idg-meeting");
  };

  if (isLoading) {
    return (
      <>
        <Loader size="md" />
        <Text>Loading patients...</Text>
      </>
    );
  }

  if (error) {
    return <Text c="red">Error loading patients: {error}</Text>;
  }

  return (
    <>
      <AddPatientModal
        opened={addPatientModalOpen}
        onClose={() => setAddPatientModalOpen(false)}
        onAddPatient={(patient) => {
          if (patient) {
            dispatch(setCurrentPatientId(patient.id));
            console.log("NEW PATIENT ID: ", patient.id);
            navigate(`/patient/${patient.id}`);
          }
        }}
      />
      <IDGParticipantsModal
        opened={idgParticipantsModalOpen}
        onClose={() => setIdgParticipantsModalOpen(false)}
        onStartMeeting={handleStartIDGMeeting}
      />
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group justify="space-between" mb="md">
          <Text fw={500} size="lg">
            Patients
          </Text>
          <Group>
            <Button
              variant="outline"
              color="green"
              leftSection={<IconPlus size={16} />}
              onClick={() => setAddPatientModalOpen(true)}
            >
              Add Patient
            </Button>
            <Button
              leftSection={<IconUsers size={16} />}
              onClick={() => {
                if (idgMeetings && idgMeetings.length > 0) {
                  navigate(`/idg-meeting`);
                } else {
                  setIdgParticipantsModalOpen(true);
                }
              }}
              variant="outline"
            >
              {idgMeetings && idgMeetings.length > 0
                ? "Continue IDG Meeting"
                : "Start IDG Meeting"}
            </Button>
            <TextInput
              placeholder="Search patients..."
              value={searchQuery}
              onChange={(event) => setSearchQuery(event.currentTarget.value)}
              leftSection={
                <IconSearch
                  style={{ width: rem(16), height: rem(16) }}
                  stroke={1.5}
                />
              }
              width={300}
            />
          </Group>
        </Group>

        <ScrollArea h={500}>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Name</Table.Th>
                <Table.Th>DOB</Table.Th>
                <Table.Th>Terminal Diagnosis</Table.Th>
                <Table.Th>Admission Date</Table.Th>
                <Table.Th>Status</Table.Th>
                {/* <Table.Th>IDG Status</Table.Th> */}
                {/* <Table.Th>Last IDG</Table.Th> */}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredPatients.length === 0 ? (
                <Table.Tr>
                  <Table.Td colSpan={8}>
                    <Text ta="center">No patients found</Text>
                  </Table.Td>
                </Table.Tr>
              ) : (
                filteredPatients.map((patient) => (
                  <Table.Tr key={patient.id}>
                    <Table.Td>
                      <Anchor component={Link} to={`/patient/${patient.id}`}>
                        {patient.lastName}, {patient.firstName}
                      </Anchor>
                    </Table.Td>
                    <Table.Td>{formatDate(patient.dateOfBirth)}</Table.Td>
                    <Table.Td>
                      {formatTerminalDiagnosis(patient.terminalDiagnosis)}
                    </Table.Td>
                    <Table.Td>
                      {patient.admissionDate
                        ? formatDate(patient.admissionDate)
                        : "N/A"}
                    </Table.Td>
                    <Table.Td>
                      <Badge
                        color={getPatientStatusBadgeColor(patient.status ?? "")}
                      >
                        {patient.status}
                      </Badge>
                    </Table.Td>

                    {/* <Table.Td>
                    {patient.lastIdgDate ? formatDate(patient.lastIdgDate) : 'N/A'}
                  </Table.Td> */}
                  </Table.Tr>
                ))
              )}
            </Table.Tbody>
          </Table>
        </ScrollArea>
        {/* <Modal
          opened={idgModalOpen}
          onClose={() => setIdgModalOpen(false)}
          title="IDG Meeting Patient Selection"
          size="xl"
        >
          <IDGPatientsList patients={filteredPatients} onClose={() => setIdgModalOpen(false)} />
        </Modal> */}
      </Card>
    </>
  );
}

interface IDGPatientsListProps {
  patients: Patient[];
  onClose: () => void;
}

function IDGPatientsList({ patients, onClose }: IDGPatientsListProps) {
  const navigate = useNavigate();

  const startIDGForPatient = (patientId: string) => {
    onClose();
    navigate(`/idg-meeting/${patientId}`);
  };

  return (
    <ScrollArea h={500}>
      <Table striped highlightOnHover>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Name</Table.Th>
            <Table.Th>MRN</Table.Th>
            <Table.Th>Primary Diagnosis</Table.Th>
            <Table.Th>Status</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {patients.length === 0 ? (
            <Table.Tr>
              <Table.Td colSpan={5}>
                <Text ta="center">No patients found</Text>
              </Table.Td>
            </Table.Tr>
          ) : (
            patients.map((patient) => (
              <Table.Tr key={patient.id}>
                <Table.Td>
                  {patient.lastName}, {patient.firstName}
                </Table.Td>
                <Table.Td>{patient.medicalRecordNumber}</Table.Td>
                <Table.Td>
                  {formatTerminalDiagnosis(patient.terminalDiagnosis)}
                </Table.Td>
                <Table.Td>
                  <Badge
                    color={getPatientStatusBadgeColor(patient.status ?? "")}
                  >
                    {patient.status}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Button
                    size="xs"
                    onClick={() => startIDGForPatient(patient.id)}
                  >
                    Start IDG
                  </Button>
                </Table.Td>
              </Table.Tr>
            ))
          )}
        </Table.Tbody>
      </Table>
    </ScrollArea>
  );
}
