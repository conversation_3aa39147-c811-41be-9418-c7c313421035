import { Patient } from "@hospice-os/apptypes";

export const mockPatients: Patient[] = [
  {
    id: "1",
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    dateOfBirth: "1940-08-15",
    gender: "Male",
    address: "225 Cornet Drive, Centerville, MD 21617-2647",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN12345678",
    insuranceProvider: "Medicare",
    insuranceNumber: "MC98765432",
    emergencyContactName: "<PERSON>",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Alzheimer's",
    secondaryDiagnoses: [
      "Neoplasm related pain (acute)",
      "Debility unspecified",
      "Other malaise and fatigue"
    ],
    allergies: ["Penicillin", "Sulfa drugs"],
    medications: ["Morphine", "Lorazepam", "Haloperidol"],
    admissionDate: "2024-11-03",
    status: "Active",
    idgStatus: "Pending",
    lastIdgDate: "2024-12-15"
  },
  {
    id: "2",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    dateOfBirth: "1945-03-22",
    gender: "Female",
    address: "456 Oak Street, Riverdale, MD 20737",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN87654321",
    insuranceProvider: "Blue Cross",
    insuranceNumber: "BC12345678",
    emergencyContactName: "Michael Smith",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Metastatic Breast Cancer",
    secondaryDiagnoses: [
      "Chronic pain",
      "Anxiety",
      "Insomnia"
    ],
    allergies: ["Codeine", "Latex"],
    medications: ["Morphine", "Lorazepam", "Dexamethasone"],
    admissionDate: "2024-10-15",
    status: "Active",
    idgStatus: "Completed",
    lastIdgDate: "2025-03-10"
  },
  {
    id: "3",
    firstName: "Robert",
    lastName: "Johnson",
    dateOfBirth: "1938-11-10",
    gender: "Male",
    address: "789 Pine Avenue, Lakeside, MD 21122",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN23456789",
    insuranceProvider: "Aetna",
    insuranceNumber: "AE87654321",
    emergencyContactName: "Susan Johnson",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "End-stage COPD",
    secondaryDiagnoses: [
      "Congestive Heart Failure",
      "Type 2 Diabetes",
      "Hypertension"
    ],
    allergies: ["Aspirin", "Shellfish"],
    medications: ["Albuterol", "Furosemide", "Metformin"],
    admissionDate: "2024-09-28",
    status: "Active",
    idgStatus: "Pending",
    lastIdgDate: "2025-02-20"
  },
  {
    id: "4",
    firstName: "Mary",
    lastName: "Williams",
    dateOfBirth: "1942-05-18",
    gender: "Female",
    address: "123 Maple Road, Hillcrest, MD 21228",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN34567890",
    insuranceProvider: "United Healthcare",
    insuranceNumber: "UH76543210",
    emergencyContactName: "James Williams",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Pancreatic Cancer",
    secondaryDiagnoses: [
      "Malnutrition",
      "Depression",
      "Chronic pain"
    ],
    allergies: ["Morphine", "Iodine"],
    medications: ["Hydromorphone", "Ondansetron", "Sertraline"],
    admissionDate: "2024-11-05",
    status: "Active",
    idgStatus: "Not Required",
    lastIdgDate: null
  },
  {
    id: "5",
    firstName: "David",
    lastName: "Brown",
    dateOfBirth: "1950-09-30",
    gender: "Male",
    address: "567 Cedar Lane, Woodville, MD 21162",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN45678901",
    insuranceProvider: "Cigna",
    insuranceNumber: "CI65432109",
    emergencyContactName: "Patricia Brown",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Amyotrophic Lateral Sclerosis (ALS)",
    secondaryDiagnoses: [
      "Dysphagia",
      "Respiratory insufficiency",
      "Depression"
    ],
    allergies: ["Sulfa drugs"],
    medications: ["Riluzole", "Baclofen", "Fluoxetine"],
    admissionDate: "2024-10-10",
    status: "Active",
    idgStatus: "Completed",
    lastIdgDate: "2025-03-25"
  },
  {
    id: "6",
    firstName: "Patricia",
    lastName: "Davis",
    dateOfBirth: "1948-12-05",
    gender: "Female",
    address: "890 Elm Street, Brookside, MD 21093",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN56789012",
    insuranceProvider: "Medicare",
    insuranceNumber: "MC54321098",
    emergencyContactName: "Thomas Davis",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "End-stage Renal Disease",
    secondaryDiagnoses: [
      "Hypertension",
      "Anemia",
      "Secondary Hyperparathyroidism"
    ],
    allergies: ["Penicillin", "Contrast dye"],
    medications: ["Epoetin alfa", "Sevelamer", "Calcitriol"],
    admissionDate: "2024-09-15",
    status: "Active",
    idgStatus: "Pending",
    lastIdgDate: "2025-01-05"
  },
  {
    id: "7",
    firstName: "James",
    lastName: "Miller",
    dateOfBirth: "1935-07-22",
    gender: "Male",
    address: "234 Birch Boulevard, Riverside, MD 21221",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN67890123",
    insuranceProvider: "Humana",
    insuranceNumber: "HU43210987",
    emergencyContactName: "Jennifer Miller",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Parkinson's Disease",
    secondaryDiagnoses: [
      "Dementia",
      "Orthostatic Hypotension",
      "Dysphagia"
    ],
    allergies: ["Codeine"],
    medications: ["Levodopa", "Carbidopa", "Rivastigmine"],
    admissionDate: "2024-08-20",
    status: "Active",
    idgStatus: "Completed",
    lastIdgDate: "2025-03-15"
  },
  {
    id: "8",
    firstName: "Linda",
    lastName: "Wilson",
    dateOfBirth: "1943-02-14",
    gender: "Female",
    address: "678 Spruce Street, Meadowview, MD 21222",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN78901234",
    insuranceProvider: "Medicare",
    insuranceNumber: "MC32109876",
    emergencyContactName: "Richard Wilson",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Ovarian Cancer",
    secondaryDiagnoses: [
      "Ascites",
      "Malnutrition",
      "Chronic pain"
    ],
    allergies: ["Latex", "Iodine"],
    medications: ["Oxycodone", "Furosemide", "Dexamethasone"],
    admissionDate: "2024-10-25",
    status: "Active",
    idgStatus: "Pending",
    lastIdgDate: "2025-02-10"
  },
  {
    id: "9",
    firstName: "William",
    lastName: "Taylor",
    dateOfBirth: "1937-06-08",
    gender: "Male",
    address: "345 Aspen Avenue, Lakeview, MD 21226",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN89012345",
    insuranceProvider: "Tricare",
    insuranceNumber: "TR21098765",
    emergencyContactName: "Elizabeth Taylor",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Glioblastoma Multiforme",
    secondaryDiagnoses: [
      "Seizures",
      "Hemiparesis",
      "Cognitive impairment"
    ],
    allergies: ["Phenytoin"],
    medications: ["Dexamethasone", "Levetiracetam", "Temozolomide"],
    admissionDate: "2024-11-01",
    status: "Active",
    idgStatus: "Not Required",
    lastIdgDate: null
  },
  {
    id: "10",
    firstName: "Barbara",
    lastName: "Anderson",
    dateOfBirth: "1940-10-12",
    gender: "Female",
    address: "901 Willow Way, Forestville, MD 21227",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN90123456",
    insuranceProvider: "Medicare",
    insuranceNumber: "MC10987654",
    emergencyContactName: "Charles Anderson",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Congestive Heart Failure",
    secondaryDiagnoses: [
      "Chronic Kidney Disease",
      "Atrial Fibrillation",
      "Type 2 Diabetes"
    ],
    allergies: ["ACE inhibitors", "Shellfish"],
    medications: ["Furosemide", "Metoprolol", "Apixaban"],
    admissionDate: "2024-09-10",
    status: "Active",
    idgStatus: "Completed",
    lastIdgDate: "2025-03-05"
  },
  {
    id: "11",
    firstName: "Michael",
    lastName: "Thompson",
    dateOfBirth: "1952-04-25",
    gender: "Male",
    address: "123 Sycamore Street, Valleyview, MD 21228",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN01234567",
    insuranceProvider: "Blue Cross",
    insuranceNumber: "BC09876543",
    emergencyContactName: "Sarah Thompson",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Liver Cirrhosis",
    secondaryDiagnoses: [
      "Hepatic Encephalopathy",
      "Ascites",
      "Esophageal Varices"
    ],
    allergies: ["Sulfa drugs", "Penicillin"],
    medications: ["Lactulose", "Spironolactone", "Propranolol"],
    admissionDate: "2024-10-05",
    status: "Active",
    idgStatus: "Pending",
    lastIdgDate: "2025-01-20"
  },
  {
    id: "12",
    firstName: "Elizabeth",
    lastName: "Garcia",
    dateOfBirth: "1947-08-30",
    gender: "Female",
    address: "456 Redwood Road, Hillside, MD 21229",
    phoneNumber: "(*************",
    email: "<EMAIL>",
    medicalRecordNumber: "MRN12345670",
    insuranceProvider: "Aetna",
    insuranceNumber: "AE98765432",
    emergencyContactName: "Jose Garcia",
    emergencyContactPhone: "(*************",
    primaryDiagnosis: "Multiple Sclerosis",
    secondaryDiagnoses: [
      "Neurogenic Bladder",
      "Spasticity",
      "Depression"
    ],
    allergies: ["Contrast dye"],
    medications: ["Baclofen", "Oxybutynin", "Sertraline"],
    admissionDate: "2024-08-15",
    status: "Active",
    idgStatus: "Completed",
    lastIdgDate: "2025-02-28"
  }
];
