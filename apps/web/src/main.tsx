import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { MantineProvider, createTheme } from '@mantine/core';
import '@mantine/core/styles.css';
import './index.css';
import { store } from './store';
import { Router } from './Router';
import { StytchB2BProvider } from '@stytch/react/b2b';
import { StytchB2BUIClient } from '@stytch/vanilla-js/b2b';
import { Notifications } from '@mantine/notifications';

// Create Mantine theme
const theme = createTheme({
  defaultRadius: 'md',
});

// Initialize Stytch client
const stytchOptions = {
  cookieOptions: {
    path: "",
    availableToSubdomains: true,
    domain: ".tallio.com",
  },
};

const stytch = new StytchB2BUIClient(
  import.meta.env.STYTCH_PUBLIC_TOKEN,
  stytchOptions
);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      <StytchB2BProvider stytch={stytch}>
        <MantineProvider theme={theme} defaultColorScheme="light">
          <Notifications />
          <Router />
        </MantineProvider>
      </StytchB2BProvider>
    </Provider>
  </StrictMode>,
);
