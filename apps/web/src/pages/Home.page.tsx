import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Patient as BasePatient } from "@hospice-os/apptypes";

import {
  Container,
  Paper,
  Title,
  Text,
  Button,
  Group,
  Avatar,
  Textarea,
  ScrollArea,
  Box,
  Loader,
  Center,
  Stack,
  Select,
  Badge,
  Alert,
  ActionIcon,
  Tooltip,
  FileButton,
  Divider,
  Grid,
  Checkbox,
  Modal,
  TextInput,
  List,
  Stepper,
  Tabs,
} from "@mantine/core";
import {
  IconRobot,
  IconUser,
  IconSend,
  IconAlertCircle,
  IconTemplate,
  IconUpload,
  IconSparkles,
  IconPlus,
} from "@tabler/icons-react";
import { AudioRecorder } from "@hospice-os/ui-components";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// Chat message interface
interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  context?: string;
}

// Import mock patients data
// import { mockPatients } from '../features/patients/mockData';
import { useGetPatientsQuery } from "../store/api_extensions/patientApiExt";
import { OrderModal } from "../components/modals/OrderModal";
import { NoteModal } from "../components/modals/NoteModal";
import { AddPatientModal } from "../components/modals/AddPatientModal";
import { PatientChartHeader } from "../components/PatientChartHeader";
import { TimelineSection } from "../components/TimelineSection";
import { TimelineEntry } from "../features/patient_chart";

export function HomePage() {
  const navigate = useNavigate();
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState<string>("");
  const [isAiTyping, setIsAiTyping] = useState<boolean>(false);
  const [selectedPatient, setSelectedPatient] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [orderModalOpen, setOrderModalOpen] = useState<boolean>(false);
  const [noteModalOpen, setNoteModalOpen] = useState<boolean>(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalType, setModalType] = useState<
    "order" | "visit-note" | "add-patient" | null
  >(null);

  // Add patient state
  const [addPatientModalOpen, setAddPatientModalOpen] =
    useState<boolean>(false);
  const { data: rawPatients } = useGetPatientsQuery();

  const patients =
    rawPatients?.map((patient) => ({
      value: patient.id,
      label: `${patient.firstName} ${patient.lastName}`,
    })) ?? [];

  const handleRecordingComplete = (url: string) => {
    setAudioUrl(url);
  };

  // Track uploaded files and transcriptions for template filling
  const [templateFiles, setTemplateFiles] = useState<File[]>([]);
  const [templateTranscriptions, setTemplateTranscriptions] = useState<
    string[]
  >([]);

  // Handle file upload
  const handleFileUpload = (uploadedFiles: File[] | null) => {
    if (uploadedFiles && uploadedFiles.length > 0) {
      setFiles([...files, ...uploadedFiles]);

      if (selectedTemplate) {
        // If a template is selected, store files for template filling
        setTemplateFiles([...templateFiles, ...uploadedFiles]);

        // Show a toast or notification that files are added to the template
        // For now, we'll just log to console
        console.log(
          `Added ${uploadedFiles.length} files to template ${selectedTemplate}`,
        );
      } else {
        // Regular file upload flow for chat
        // Add document message to chat
        const newMessage: ChatMessage = {
          id: Math.random().toString(36).substring(2, 9),
          role: "user",
          content: `Uploaded document: ${uploadedFiles.map((f) => f.name).join(", ")}`,
          timestamp: new Date(),
          context: selectedPatient
            ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
            : undefined,
        };

        setChatMessages([...chatMessages, newMessage]);

        // Simulate AI response
        setIsAiTyping(true);
        setTimeout(() => {
          const aiResponse: ChatMessage = {
            id: Math.random().toString(36).substring(2, 9),
            role: "assistant",
            content: `I've received the document(s): ${uploadedFiles.map((f) => f.name).join(", ")}. Would you like me to analyze the content or extract specific information from these files?`,
            timestamp: new Date(),
            context: selectedPatient
              ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
              : undefined,
          };

          setChatMessages([...chatMessages, newMessage, aiResponse]);
          setIsAiTyping(false);
        }, 1500);
      }
    }
  };

  // Process audio recording
  const processAudioInput = () => {
    if (!audioUrl) {
      return;
    }

    setIsProcessing(true);

    // Simulate transcription and processing delay
    setTimeout(() => {
      // Simulate transcription result
      const patientName = patients.find(
        (p) => p.value === selectedPatient,
      )?.label;
      const simulatedTranscription = `${patientName} reports improved pain control with current medication regimen.`;

      if (selectedTemplate) {
        // If a template is selected, store transcription for template filling
        setTemplateTranscriptions([
          ...templateTranscriptions,
          simulatedTranscription,
        ]);

        // Show a toast or notification that transcription is added to the template
        // For now, we'll just log to console
        console.log(
          `Added transcription to template ${selectedTemplate}: ${simulatedTranscription}`,
        );

        // Clear the audio URL without adding to chat
        setAudioUrl(null);
        setIsProcessing(false);
      } else {
        // Regular audio processing flow for chat
        // Create a new message
        const newMessage: ChatMessage = {
          id: Math.random().toString(36).substring(2, 9),
          role: "user",
          content: `Audio transcription: "${simulatedTranscription}"`,
          timestamp: new Date(),
          context: selectedPatient
            ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
            : undefined,
        };

        setChatMessages([...chatMessages, newMessage]);

        // Simulate AI response
        setIsAiTyping(true);
        setTimeout(() => {
          const aiResponse: ChatMessage = {
            id: Math.random().toString(36).substring(2, 9),
            role: "assistant",
            content: `I've processed your audio note about ${patientName}'s pain control. Would you like me to update the patient record with this information or draft a more detailed note based on this observation?`,
            timestamp: new Date(),
            context: selectedPatient
              ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
              : undefined,
          };

          setChatMessages([...chatMessages, newMessage, aiResponse]);
          setIsAiTyping(false);
        }, 1000);

        // Clear the audio URL
        setAudioUrl(null);
        setIsProcessing(false);
      }
    }, 1500);
  };

  // Clear template data when template changes or is cleared
  useEffect(() => {
    if (!selectedTemplate) {
      setTemplateFiles([]);
      setTemplateTranscriptions([]);
    }
  }, [selectedTemplate]);

  // Close template modal
  const closeTemplateModal = () => {
    setModalOpen(false);
    setSelectedTemplate(null);
    setModalType(null);
  };

  // Open modal with pre-selected template type
  const openTemplateModal = (type: "order" | "visit-note") => {
    if (type === "order") {
      setOrderModalOpen(true);
    } else if (type === "visit-note") {
      setNoteModalOpen(true);
    }

    // Legacy modal code - to be removed in future
    setModalType(type);
    if (type === "order") {
      setSelectedTemplate("medication-order");
    } else if (type === "visit-note") {
      setSelectedTemplate("visit-note");
    }
    setModalOpen(false); // Set to false to use the new modals instead
  };

  // Handle template submission
  const handleTemplateSubmit = (
    templateContent: string,
    templateName: string,
  ) => {
    // Create a message with the template content
    const messageContent = `Selected template: ${templateName}`;

    const userMessage: ChatMessage = {
      id: Math.random().toString(36).substring(2, 9),
      role: "user",
      content: messageContent,
      timestamp: new Date(),
      context: selectedPatient
        ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
        : undefined,
    };

    setChatMessages([...chatMessages, userMessage]);
    setIsAiTyping(true);

    // Simulate AI response
    setTimeout(() => {
      // Generate a response that acknowledges the template
      let responseContent = `I've prepared the ${templateName} for ${patients.find((p) => p.value === selectedPatient)?.label}.`;
      responseContent +=
        " Based on the provided information, I've filled in some sections:";

      // Simulate filling in template sections
      if (
        templateName.toLowerCase().includes("medication") ||
        templateName.toLowerCase().includes("order")
      ) {
        responseContent +=
          "\n\n**Medication Name:** Morphine Sulfate\n**Dosage:** 5mg/ml\n**Route:** Oral\n**Frequency:** PRN\n\nWould you like me to complete the remaining sections?";
      } else {
        responseContent +=
          "\n\n**Subjective:**\nPatient reports improved pain control with current medication regimen.\n\n**Assessment:**\nStable pain management with current medications.\n\nWould you like me to elaborate on any specific section?";
      }

      const aiResponse: ChatMessage = {
        id: Math.random().toString(36).substring(2, 9),
        role: "assistant",
        content: responseContent,
        timestamp: new Date(),
        context: selectedPatient
          ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
          : undefined,
      };

      setChatMessages([...chatMessages, userMessage, aiResponse]);
      setIsAiTyping(false);
    }, 1500);
  };

  // Handle AI chat submit
  const handleChatSubmit = () => {
    if (!chatInput.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Math.random().toString(36).substring(2, 9),
      role: "user",
      content: chatInput,
      timestamp: new Date(),
      context: selectedPatient
        ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
        : undefined,
    };

    setChatMessages([...chatMessages, userMessage]);
    setChatInput("");
    setIsAiTyping(true);

    // Simulate AI response
    setTimeout(() => {
      // Generate AI response based on user input and context
      let aiResponse = "";

      if (selectedPatient) {
        // Patient-specific responses
        const patientName = patients.find(
          (p) => p.value === selectedPatient,
        )?.label;

        if (
          chatInput.toLowerCase().includes("summary") ||
          chatInput.toLowerCase().includes("overview")
        ) {
          aiResponse =
            `### Here's a summary for ${patientName}:\n\n` +
            `- ${patientName} is an 84-year-old patient with advanced COPD and heart failure, admitted to hospice care on 11/03/2024.\n` +
            `- Recent visits show gradual decline in functional status with increased dependence for ADLs.\n` +
            `- Pain management has been a focus, with recent improvement noted (7/10 → 5/10).\n` +
            `- Family is supportive and present. Next IDG meeting scheduled for 12/29/2024.\n\n` +
            `Would you like more details on any specific aspect of the patient's care?`;
        } else if (
          chatInput.toLowerCase().includes("medication") ||
          chatInput.toLowerCase().includes("prescribe")
        ) {
          aiResponse =
            `${patientName} is currently on the following medications:\n\n` +
            "1. Morphine sulfate 5mg/ml oral PRN for severe pain\n" +
            "2. Lorazepam 2mg oral BID for anxiety\n" +
            "3. Senna 8.6mg oral daily for constipation\n\n" +
            "Would you like me to draft a medication order for any adjustments?";
        } else {
          aiResponse =
            `I understand you're asking about ${patientName}. ` +
            `Based on the patient's records, I can provide information about their current status, medication regimen, ` +
            `recent assessments, or help draft documentation. What specific information would be most helpful for you right now?`;
        }
      } else {
        // General responses (no patient selected)
        if (chatInput.toLowerCase().includes("template")) {
          aiResponse =
            "I can help you with various clinical templates. Here are some commonly used ones:\n\n" +
            "1. Visit Note - For documenting patient visits\n" +
            "2. Medication Order - For prescribing new medications\n" +
            "3. Pain Assessment - For evaluating pain levels and characteristics\n" +
            "4. DME Order - For ordering durable medical equipment\n\n" +
            "Would you like me to help you fill out any of these templates?";
        } else if (
          chatInput.toLowerCase().includes("patient") ||
          chatInput.toLowerCase().includes("find")
        ) {
          aiResponse =
            "To access patient-specific information, please select a patient from the dropdown above the chat. " +
            "This will allow me to provide contextual information about that specific patient.";
        } else if (
          chatInput.toLowerCase().includes("help") ||
          chatInput.toLowerCase().includes("what can you do")
        ) {
          aiResponse =
            "I'm your clinical AI assistant. I can help with:\n\n" +
            "• Drafting clinical documentation\n" +
            "• Providing patient information and summaries\n" +
            "• Suggesting medication orders and treatment plans\n" +
            "• Answering clinical questions\n" +
            "• Filling out templates\n\n" +
            "Select a patient from the dropdown to get patient-specific assistance, or ask me general clinical questions.";
        } else {
          aiResponse =
            "I understand you're asking about " +
            chatInput.split(" ").slice(0, 3).join(" ") +
            "... " +
            "For more specific assistance, you might want to select a patient from the dropdown above. " +
            "Otherwise, I can help with general clinical questions, documentation templates, or workflow guidance.";
        }
      }

      // Add AI response
      const assistantMessage: ChatMessage = {
        id: Math.random().toString(36).substring(2, 9),
        role: "assistant",
        content: aiResponse,
        timestamp: new Date(),
        context: selectedPatient
          ? `Patient: ${patients.find((p) => p.value === selectedPatient)?.label}`
          : undefined,
      };

      setChatMessages([...chatMessages, userMessage, assistantMessage]);
      setIsAiTyping(false);
    }, 1500);
  };

  // Sample tasks
  // const [tasks, setTasks] = useState([]);
  //   { id: '0', title: 'Start IDG Meeting', completed: false, dueDate: '4/11/2025', priority: 'high', action: '/idg-meeting' },
  //   { id: '1', title: 'Review John Doe Admission', completed: false, dueDate: '4/8/2025', priority: 'high' },
  //   { id: '2', title: 'Admission Documentation Ready (Jane Smith)', completed: false, dueDate: '4/7/2025', priority: 'high' },
  //   // { id: '3', title: 'Schedule IDG meeting', completed: false, dueDate: '4/11/2025', priority: 'high' },
  //   { id: '4', title: 'Verbal CTI Missing (Jane Smith)', completed: false, dueDate: '4/9/2025', priority: 'medium' },
  //   { id: '5', title: 'Medication Order Pending Signature', completed: false, dueDate: '4/10/2025', priority: 'low' }
  // ]);

  const [actions, setActions] = useState<Action[]>(
    selectedPatient
      ? (
          rawPatients?.find(
            (patient) => patient.id === selectedPatient,
          ) as Patient
        )?.agencyActions || []
      : [],
  );

  useEffect(() => {
    if (selectedPatient) {
      const patient = rawPatients?.find(
        (patient) => patient.id === selectedPatient,
      ) as Patient;
      if (patient) {
        setActions(patient.agencyActions || []);
      }
    }
  }, [rawPatients, selectedPatient]);

  // Toggle task completion
  // const toggleTaskCompletion = (taskId: string) => {
  //   setTasks(tasks.map(task =>
  //     task.id === taskId ? { ...task, completed: !task.completed } : task
  //   ));
  // };

  return (
    <Container fluid py="xl">
      <Grid gutter="md">
        {/* Task List Sidebar */}
        <Grid.Col span={{ base: 12, md: 3 }}>
          <Paper
            withBorder
            p="md"
            radius="md"
            style={{ height: "calc(100vh - 120px)", overflow: "auto" }}
          >
            <Tabs defaultValue="actions" variant="outline" radius="md">
              <Tabs.List style={{ width: "100%" }}>
                <Tabs.Tab value="actions" style={{ flex: 1 }}>
                  Actions
                </Tabs.Tab>
                <Tabs.Tab value="timeline" style={{ flex: 1 }}>
                  Timeline
                </Tabs.Tab>
              </Tabs.List>
              <Tabs.Panel value="actions" pt="xs">
                <Stack gap="xs">
                  {actions.map((task) => (
                    <Paper
                      key={task.id}
                      withBorder
                      p="sm"
                      radius="md"
                      style={{
                        opacity: task.completed ? 0.7 : 1,
                        borderLeft: `4px solid ${
                          task.priority === "high"
                            ? "red"
                            : task.priority === "medium"
                              ? "orange"
                              : "green"
                        }`,
                        cursor: task.action ? "pointer" : "default",
                      }}
                      onClick={() => {
                        if (task.action) {
                          navigate(task.action);
                        }
                      }}
                    >
                      <Group justify="space-between" mb="xs">
                        <Group>
                          <Checkbox
                            checked={task.completed}
                            // onChange={() => toggleTaskCompletion(task.id)}
                            styles={{
                              input: { cursor: "pointer" },
                            }}
                          />
                          <Text
                            style={{
                              textDecoration: task.completed
                                ? "line-through"
                                : "none",
                              fontWeight: task.completed ? "normal" : 500,
                            }}
                          >
                            {task.title}
                          </Text>
                        </Group>
                      </Group>
                      <Group justify="space-between" mt="xs">
                        <Text size="xs" c="dimmed">
                          Due: {task.dueDate}
                        </Text>
                        <Badge
                          size="sm"
                          color={
                            task.priority === "high"
                              ? "red"
                              : task.priority === "medium"
                                ? "orange"
                                : "green"
                          }
                        >
                          {task.priority}
                        </Badge>
                      </Group>
                    </Paper>
                  ))}
                  {/* <Button 
                    variant="subtle" 
                    leftSection={<IconPlus size={16} />}
                    mt="md"
                  >
                    Add Task
                  </Button> */}
                </Stack>
              </Tabs.Panel>
              <Tabs.Panel value="timeline" pt="xs">
                <TimelineSection
                  entries={
                    selectedPatient
                      ? (
                          rawPatients?.find(
                            (patient) => patient.id === selectedPatient,
                          ) as Patient
                        )?.timelineEntries || []
                      : []
                  }
                  onEntryClick={() => {}}
                />
              </Tabs.Panel>
            </Tabs>
          </Paper>
        </Grid.Col>

        {/* Main Content */}
        <Grid.Col span={{ base: 12, md: 9 }}>
          <Paper
            withBorder
            p="xl"
            radius="md"
            style={{ height: "calc(100vh - 120px)" }}
          >
            <Title order={2} ta="center" mb="lg"></Title>

            {/* Patient Chart Header */}
            <PatientChartHeader
              selectedPatient={selectedPatient}
              onPatientChange={setSelectedPatient}
              onAddPatientClick={() => setAddPatientModalOpen(true)}
              patients={patients}
            />

            {/* Add Patient Modal */}
            <AddPatientModal
              opened={addPatientModalOpen}
              onClose={() => setAddPatientModalOpen(false)}
              onAddPatient={(newPatient, documents) => {
                // In a real app, you would save this to your backend
                // For now, we'll just add it to the dropdown
                const updatedPatients = [
                  ...patients,
                  {
                    value: newPatient.id!,
                    label: `${newPatient.firstName} ${newPatient.lastName}`,
                  },
                ];

                // Sort patients alphabetically by label
                updatedPatients.sort((a, b) => a.label.localeCompare(b.label));

                // Update the patients array
                // In a real app, you would update your state management store

                // Show a success message
                const userMessage: ChatMessage = {
                  id: Math.random().toString(36).substring(2, 9),
                  role: "user",
                  content: `Added new patient: ${newPatient.firstName} ${newPatient.lastName}`,
                  timestamp: new Date(),
                };

                setChatMessages([...chatMessages, userMessage]);

                // Simulate AI response
                setIsAiTyping(true);
                setTimeout(() => {
                  const aiResponse: ChatMessage = {
                    id: Math.random().toString(36).substring(2, 9),
                    role: "assistant",
                    content: `I've added ${newPatient.firstName} ${newPatient.lastName} to the patient list. Would you like to add more details to their profile or upload additional documents?`,
                    timestamp: new Date(),
                  };

                  setChatMessages([...chatMessages, userMessage, aiResponse]);
                  setIsAiTyping(false);
                }, 1000);

                // Select the new patient
                setSelectedPatient(newPatient.id!);
              }}
            />

            {!selectedPatient ? (
              <Alert
                icon={<IconAlertCircle size={16} />}
                title="Patient selection required"
                color="blue"
                radius="md"
                style={{ maxWidth: "500px", margin: "20px auto" }}
              >
                Please select a patient to start. This ensures all responses are
                contextual to the specific patient.
              </Alert>
            ) : (
              <Box
                style={{
                  display: "flex",
                  flexDirection: "column",
                  height: "calc(100vh - 280px)",
                }}
              >
                {/* Order Modal */}
                <OrderModal
                  opened={orderModalOpen}
                  onClose={() => setOrderModalOpen(false)}
                  selectedPatient={selectedPatient}
                  patientLabel={
                    patients.find((p) => p.value === selectedPatient)?.label
                  }
                  onSubmit={handleTemplateSubmit}
                />

                {/* Note Modal */}
                <NoteModal
                  opened={noteModalOpen}
                  onClose={() => setNoteModalOpen(false)}
                  selectedPatient={selectedPatient}
                  patientLabel={
                    patients.find((p) => p.value === selectedPatient)?.label
                  }
                  onSubmit={handleTemplateSubmit}
                />

                {/* Audio Recording Preview */}
                {audioUrl && (
                  <Box mb="md">
                    <Group align="center" mb="xs">
                      <Text fw={500} size="sm">
                        Audio Recording:
                      </Text>
                      {/* <Button 
                        size="xs" 
                        variant="light"
                        onClick={processAudioInput} 
                        loading={isProcessing}
                        leftSection={<IconSparkles size={14} />}
                      >
                        Process
                      </Button> */}
                    </Group>
                    <audio src={audioUrl} controls style={{ width: "100%" }}>
                      <track kind="captions" src="" label="English captions" />
                    </audio>
                  </Box>
                )}

                {/* Chat Messages */}
                <Divider my="md" />
                <ScrollArea style={{ flex: 1 }} p="md">
                  {chatMessages.length === 0 ? (
                    <Center style={{ height: "100%" }}>
                      <Stack align="center" gap="lg" pb="xl">
                        <IconRobot size={48} opacity={0.5} />
                        <Text c="dimmed" size="lg" ta="center" maw={600}>
                          Ask me anything about{" "}
                          {
                            patients.find((p) => p.value === selectedPatient)
                              ?.label
                          }
                          's care, clinical documentation, or treatment
                          planning.
                        </Text>
                        <Group gap="md">
                          {/* <Button 
                            variant="light" 
                            onClick={() => setChatInput(`Give me a summary of ${patients.find(p => p.value === selectedPatient)?.label}'s current status`)}
                          >
                            Recent Changes
                          </Button>
                          <Button 
                            variant="light" 
                            onClick={() => setChatInput(`What medications is ${patients.find(p => p.value === selectedPatient)?.label} currently taking?`)}
                          >
                            Current Medications
                          </Button>
                          <Button 
                            variant="light" 
                            onClick={() => setChatInput(`Help me draft a visit note for ${patients.find(p => p.value === selectedPatient)?.label}`)}
                          >
                            Visit Prep
                          </Button> */}
                        </Group>
                      </Stack>
                    </Center>
                  ) : (
                    chatMessages.map((message) => (
                      <Box
                        key={message.id}
                        mb="md"
                        style={{
                          display: "flex",
                          flexDirection:
                            message.role === "user" ? "row-reverse" : "row",
                          alignItems: "flex-start",
                        }}
                      >
                        <Avatar
                          color={message.role === "user" ? "blue" : "teal"}
                          radius="xl"
                        >
                          {message.role === "user" ? (
                            <IconUser size={20} />
                          ) : (
                            <IconRobot size={20} />
                          )}
                        </Avatar>
                        <Paper
                          p="md"
                          withBorder
                          style={{
                            maxWidth: "80%",
                            marginLeft: message.role === "user" ? 0 : "12px",
                            marginRight: message.role === "user" ? "12px" : 0,
                            backgroundColor:
                              message.role === "user" ? "#e7f5ff" : "#f8f9fa",
                          }}
                        >
                          {message.role === "assistant" ? (
                            <div style={{ textAlign: "left" }}>
                              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                {message.content}
                              </ReactMarkdown>
                            </div>
                          ) : (
                            <Text style={{ whiteSpace: "pre-line" }}>
                              {message.content}
                            </Text>
                          )}
                          <Text size="xs" c="dimmed" mt="xs">
                            {message.timestamp.toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </Text>
                        </Paper>
                      </Box>
                    ))
                  )}
                  {isAiTyping && (
                    <Box
                      mb="md"
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "flex-start",
                      }}
                    >
                      <Avatar color="teal" radius="xl">
                        <IconRobot size={20} />
                      </Avatar>
                      <Paper
                        p="md"
                        withBorder
                        style={{
                          maxWidth: "80%",
                          marginLeft: "12px",
                          backgroundColor: "#f8f9fa",
                        }}
                      >
                        <Loader size="sm" />
                      </Paper>
                    </Box>
                  )}
                </ScrollArea>

                <Divider my="md" />

                {/* Input Area at the bottom */}
                <Stack>
                  <Group justify="center">
                    <Button
                      leftSection={<IconTemplate size={16} />}
                      onClick={() => openTemplateModal("order")}
                      variant="light"
                    >
                      Add Order
                    </Button>

                    <Button
                      leftSection={<IconTemplate size={16} />}
                      onClick={() => openTemplateModal("visit-note")}
                      variant="light"
                    >
                      Add Visit Note
                    </Button>
                    <Button
                      variant="light"
                      onClick={() =>
                        setChatInput(
                          `Give me a summary of ${patients.find((p) => p.value === selectedPatient)?.label}'s current status`,
                        )
                      }
                    >
                      Patient Summary
                    </Button>
                    <Button
                      variant="light"
                      onClick={() =>
                        setChatInput(
                          `What medications is ${patients.find((p) => p.value === selectedPatient)?.label} currently taking?`,
                        )
                      }
                    >
                      Current Medications
                    </Button>
                  </Group>

                  <Group mt="xs">
                    <Textarea
                      placeholder={`Ask about ${patients.find((p) => p.value === selectedPatient)?.label}'s care...`}
                      value={chatInput}
                      onChange={(
                        event: React.ChangeEvent<HTMLTextAreaElement>,
                      ) => setChatInput(event.currentTarget.value)}
                      style={{ flex: 1 }}
                      autosize
                      minRows={1}
                      maxRows={3}
                      onKeyDown={(e: React.KeyboardEvent) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          handleChatSubmit();
                        }
                      }}
                    />

                    <FileButton
                      onChange={handleFileUpload}
                      accept="application/pdf,image/*,text/*"
                      multiple
                    >
                      {(props) => (
                        <Tooltip label="Upload Document">
                          <ActionIcon
                            variant="light"
                            color="blue"
                            size="lg"
                            {...props}
                          >
                            <IconUpload size={20} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </FileButton>

                    <AudioRecorder
                      onRecordingComplete={handleRecordingComplete}
                    />

                    <Button
                      onClick={handleChatSubmit}
                      disabled={!chatInput.trim() || isAiTyping}
                      rightSection={<IconSend size={16} />}
                    >
                      Send
                    </Button>
                  </Group>
                </Stack>
              </Box>
            )}
          </Paper>
        </Grid.Col>
      </Grid>
    </Container>
  );
}
