/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Badge,
  Box,
  Button,
  Center,
  Container,
  Grid,
  Group,
  List,
  Loader,
  LoadingOverlay,
  Menu,
  Modal,
  Paper,
  Stack,
  Tabs,
  Text,
  Title,
  Tooltip,
} from "@mantine/core";

import {
  IconAlertCircle,
  IconArrowLeft,
  IconArrowRight,
  IconCheck,
  IconDotsVertical,
  IconMicrophone,
  IconMicrophoneOff,
  IconUsers,
} from "@tabler/icons-react";

import ReactMarkdown from "react-markdown";
import { AudioRecorder, AudioRecorderHandle } from "@hospice-os/ui-components";
import remarkGfm from "remark-gfm";

import { IDGMeeting, Patient, Request } from "@hospice-os/apptypes";
import { notifications } from "@mantine/notifications";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { IDGSummaryModal } from "../components/modals/IDGSummaryModal";
import { VersionHistorySection } from "../components/VersionHistorySection";
import {
  useGetPatientsQuery,
  useGetPatientSummaryForIDGQuery,
} from "../store/api_extensions/patientApiExt";
import {
  useCreateRequestMutation,
  useGetRequestsByPatientQuery,
  useGetUploadUrlMutation,
} from "../store/api_extensions/requestApiExt";
import {
  resourceApiExt,
  useGetResourcesQuery,
  useUpdateResourceMutation,
} from "../store/api_extensions/resourceApiExt";
import { getAudioMimeType } from "../utils/audioUtil";
import { store } from "../store";
import { dataToMarkdown } from "../utils/schemaToMarkdown";
import { CollapsibleMarkdown } from "../components/CollapsibleMarkdown";

export function IDGMeetingPage() {
  const { id: selectedPatientId } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const { data: patients_ } = useGetPatientsQuery([
    "firstName",
    "lastName",
    "terminalDiagnosis",
    "dateOfBirth",
    "status",
    "benefitPeriods",
    "medications",
  ]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingStarted, setRecordingStarted] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const recorderRef = useRef<AudioRecorderHandle>(null);
  const [recorderStatus, setRecorderStatus] = useState<string>("idle");

  const [completedPatients, setCompletedPatients] = useState<string[]>([]);
  const [inProgressPatients, setInProgressPatients] = useState<string[]>([]);
  const [incompletePatients, setIncompletePatients] = useState<string[]>([]);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [concludeModalOpen, setConcludeModalOpen] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string | null>("changes");
  const [patientMedsMarkdown, setPatientMedsMarkdown] = useState<string | null>(
    null,
  );
  const [patientIdgNoteRequest, setPatientIdgNoteRequest] =
    useState<Request | null>(null);
  const [createRequest] = useCreateRequestMutation();
  const [getUploadUrl] = useGetUploadUrlMutation();
  const [updateResource] = useUpdateResourceMutation();

  // TODO: Only select the requests for the appropriate IDG time window (e.g last 2 weeks)
  // These are the requests for the current patient so we can combine all the changes and show the diff view
  const { data: requests, isFetching: isLoadingRequests } =
    useGetRequestsByPatientQuery(
      { patientId: selectedPatient?.id ?? "", status: "completed" },
      {
        skip: !selectedPatient,
        refetchOnFocus: true,
        pollingInterval: 1000 * 3, // 30 seconds
      },
    );

  const { data: patientSummary, isFetching: isLoadingPatientSummary } =
    useGetPatientSummaryForIDGQuery(selectedPatient?.id ?? "", {
      skip: !selectedPatient,
    });

  // TODO: Polling for now, but we should use websockets or smarter invalidation
  const {
    data: idgMeetings_,
    refetch: refetchIdgMeeting,
    isFetching: isLoadingIdgMeeting,
  } = useGetResourcesQuery(
    {
      resourceType: "IDGMeeting",
      filters: {
        status: "In Progress",
        limit: 1,
      },
    },
    {
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
      pollingInterval: 1000 * 5, // 5 seconds
    },
  ) as { data: IDGMeeting[] | undefined; refetch: any; isFetching: boolean };
  const idgMeeting = useMemo(() => {
    return idgMeetings_ && idgMeetings_.length > 0
      ? (idgMeetings_[0] as IDGMeeting)
      : null;
  }, [idgMeetings_]);

  const patients = useMemo(() => {
    if (!patients_ || !idgMeeting) return [];

    const filteredPatients = patients_.filter((patient) =>
      (idgMeeting as IDGMeeting).patients?.some(
        (idgPatient) => idgPatient.id === patient.id,
      ),
    );
    setCompletedPatients([
      ...((idgMeeting as IDGMeeting).patients
        ?.filter((p) => p.isCompleted)
        .map((p) => p.id) ?? []),
    ]);
    setInProgressPatients([
      ...((idgMeeting as IDGMeeting).patients
        ?.filter((p) => p.idgRequest?.id && !p.isCompleted)
        .map((p) => p.id) ?? []),
    ]);
    setIncompletePatients([
      ...((idgMeeting as IDGMeeting).patients
        ?.filter((p) => !p.idgRequest?.id && !p.isCompleted)
        .map((p) => p.id) ?? []),
    ]);

    return filteredPatients;
  }, [idgMeeting, patients_]);

  // Handle automatic patient selection
  useEffect(() => {
    if (
      idgMeeting?.patients &&
      idgMeeting.patients.length > 0 &&
      !selectedPatientId
    ) {
      const firstUncompletedPatient = idgMeeting.patients.find(
        (p) => !p.isCompleted,
      );
      if (firstUncompletedPatient) {
        navigate(`/idg-meeting/${firstUncompletedPatient.id}`);
      } else {
        navigate(`/idg-meeting/${idgMeeting.patients[0].id}`);
      }
    }
  }, [idgMeeting, selectedPatientId, navigate]);

  useEffect(() => {
    if (idgMeetings_ && idgMeetings_.length === 0 && !isLoadingIdgMeeting) {
      notifications.show({
        position: "top-right",
        color: "red",
        title: "No IDG meeting found.",
        message: "Please start a new meeting.",
      });
      navigate("/patients");
    }
  }, [isLoadingIdgMeeting, idgMeetings_, selectedPatientId]);

  // Pre-fetch next patient's summary
  const nextPatientIndex =
    patients?.findIndex((p) => p.id === selectedPatient?.id) ?? -1;
  const nextPatient =
    nextPatientIndex >= 0 && nextPatientIndex < (patients?.length ?? 0) - 1
      ? patients[nextPatientIndex + 1]
      : null;
  // Don't care about the data, just want to pre-fetch
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data: _unusedNextPatientSummary } = useGetPatientSummaryForIDGQuery(
    nextPatient?.id ?? "",
    {
      skip: !selectedPatient || !nextPatient,
    },
  );

  useEffect(() => {
    // So browser back button works
    if (!selectedPatientId || !patients) return;

    if (!selectedPatient || selectedPatient?.id !== selectedPatientId) {
      const patient = patients?.find((p) => p.id === selectedPatientId);
      if (patient) {
        setSelectedPatient(patient);
      }
    }
  }, [selectedPatientId, patients, selectedPatient, idgMeeting]);

  useEffect(() => {
    const patientIdgRequest = idgMeeting?.patients?.find(
      (p) => p.id === selectedPatientId,
    )?.idgRequest;
    if (patientIdgRequest) {
      store
        .dispatch(
          resourceApiExt.endpoints.getResourceById.initiate({
            resourceType: "Request",
            id: patientIdgRequest.id,
          }),
        )
        .unwrap()
        .then((request: any) => {
          if (request.status !== "completed") {
            setPatientIdgNoteRequest(request as Request);
          }
        });
    }
  }, [selectedPatientId, idgMeeting]);

  useEffect(() => {
    const setMedsMarkdown = async () => {
      if (selectedPatient) {
        const markdown = await dataToMarkdown(
          { medications: selectedPatient?.medications ?? [] },
          {
            medications: {
              "ui:widget": "table",
              "ui:options": {
                columns: [
                  "name",
                  "form",
                  "highRisk",
                  "strength",
                  "dosage",
                  "route",
                  "frequency",
                  "startDate",
                  "endDate",
                  "indication",
                  "covered",
                  "providedBy",
                ],
                collapsible: false,
              },
            },
          },
        );
        setPatientMedsMarkdown(markdown);
      }
    };

    if (selectedPatient) {
      setMedsMarkdown();
    }
  }, [selectedPatient]);

  // Refactored: Group requests by madeBy.date for sectioned changes
  const allRequests = useMemo(() => {
    let unkownDateIndex = 0;
    return (
      requests?.filter(
        (r) => (r.changes ?? []).length > 0 && !r.hideOnTimeline,
      ) ?? []
    ).map((r) => ({
      sectionTitle: r.approvedBy?.date
        ? `${r.title} - ${new Date(r.approvedBy.date)
            .toLocaleString(undefined, {
              year: "2-digit",
              month: "numeric",
              day: "numeric",
              hour: "numeric",
              minute: "2-digit",
              hour12: true,
            })
            .replace(",", "")
            .replace(/\s([AP]M)$/, "$1")}`
        : `Unknown Date ${unkownDateIndex++}`, //causing conflicting keys.
      changes: r.changes ?? [],
    }));
  }, [requests]);

  const mediaBlobUrlRef = useRef<string | null>(null);

  // Ref to store the promise resolve function for waiting for recording completion
  const recordingCompleteResolveRef = useRef<((url: string) => void) | null>(
    null,
  );

  const handleRecordingComplete = (url: string) => {
    console.log("recording complete", url);
    mediaBlobUrlRef.current = url;
    setAudioUrl(url);
    if (recordingCompleteResolveRef.current) {
      recordingCompleteResolveRef.current(url);
      recordingCompleteResolveRef.current = null;
    }
  };

  // Set selected patient when ID changes
  useEffect(() => {
    if (selectedPatient?.id) {
      const patient = patients?.find((p) => p.id === selectedPatient?.id);
      if (patient) {
        setSelectedPatient(patient);
      }
    }
  }, [selectedPatient?.id, patients]);

  useEffect(() => {
    setRecordingStarted(
      recorderStatus === "recording" || recorderStatus === "paused",
    );
  }, [recorderStatus]);

  // Handle patient completion
  const completePatient = () => {
    if (recorderStatus === "recording" || recorderStatus === "paused") {
      recorderRef.current?.stop();
    }

    if (selectedPatient) {
      setConfirmModalOpen(true);
    }
  };

  // Helper function to wait for recording completion
  const waitForRecordingCompletion = (): Promise<string> => {
    return new Promise((resolve) => {
      if (mediaBlobUrlRef.current) {
        resolve(mediaBlobUrlRef.current);
      } else {
        recordingCompleteResolveRef.current = resolve;
      }
    });
  };

  // Confirm patient completion
  const confirmCompletion = async () => {
    if (selectedPatient) {
      // Close modal
      setConfirmModalOpen(false);
      setIsProcessing(true);

      let finalAudioUrl: string | null = null;

      if (recorderStatus === "recording" || recorderStatus === "paused") {
        recorderRef.current?.stop();
        console.log("recording stopping current status", recorderStatus);

        try {
          // Wait for the recording to complete and get the blob URL
          finalAudioUrl = await waitForRecordingCompletion();
          console.log("Recording completed, got blob URL:", finalAudioUrl);
        } catch (error) {
          console.error("Error waiting for recording completion:", error);
        }
      } else {
        // Use existing audio URL if not currently recording
        finalAudioUrl = audioUrl;
      }

      if (!idgMeeting) {
        notifications.show({
          title: "Error",
          position: "top-right",
          message: "No IDG meeting found. Please start a new meeting.",
          color: "red",
        });
        return;
      }

      let audioFilePath: string | null = null;
      const fileType = getAudioMimeType();

      if (finalAudioUrl) {
        const audioBlob = await fetch(finalAudioUrl).then((res) => res.blob());

        const { url, filePath } = await getUploadUrl({
          fileName: `recording-${Date.now()}.${fileType.split("/")[1]}`,
          fileType,
          patientId: selectedPatient.id,
        }).unwrap();

        // Upload audio recording to S3 using pre-signed URL
        await fetch(url, {
          method: "PUT",
          body: audioBlob,
          headers: {
            "Content-Type": fileType,
          },
        });

        audioFilePath = filePath;
      }

      // Create request
      const newRequest: Partial<Request> = {
        resourceType: "Request",
        schemaId: "fb016bdd-1d9e-489a-b05e-08bd7fcb188b", // TODO: hardcoded Request schema ID
        fileAttachments: audioFilePath ? [audioFilePath] : [],
        idgMeeting: {
          id: idgMeeting!.id,
          resourceType: "IDGMeeting",
        },
        title: `IDG Meeting Note`,
        patient: { id: selectedPatient.id, resourceType: "Patient" },
        status: "processing",
        summary: patientSummary,
        noteSchemaId: "477831b8-d693-4acb-9256-721bf2897bbb", // TOOD: hardcoded IDG Patient Note schema
      };

      let request: Request | null = null;

      try {
        request = await createRequest(newRequest).unwrap();
      } catch (error: any) {
        console.log("error", error);
      }

      setPatientIdgNoteRequest(request);
      // Update the IDG meeting patientwith the new request
      if (request) {
        console.log("updating idg meeting patient with request", request);
        updateResource({
          ...(idgMeeting as IDGMeeting),
          patients: idgMeeting?.patients?.map((p) =>
            p.id === selectedPatient.id
              ? {
                  ...p,
                  idgRequest: { id: request?.id, resourceType: "Request" },
                }
              : p,
          ),
        });
      }

      // Reset recording state
      setIsRecording(false);
      setRecordingStarted(false);
      setAudioUrl(null);
    }

    setIsProcessing(false);
  };

  useEffect(() => {
    if (selectedPatient) {
      setAudioUrl(null);
      setIsRecording(false);
      setRecordingStarted(false);
    }
  }, [selectedPatient]);

  // Handle IDG meeting conclusion
  const concludeMeeting = async () => {
    const freshIdgMeeting = await refetchIdgMeeting().unwrap();

    if (!freshIdgMeeting || freshIdgMeeting.length === 0) {
      notifications.show({
        title: "Error",
        position: "top-right",
        message: "No IDG meeting found. Please start a new meeting.",
        color: "red",
      });
      navigate("/patients");
      return;
    }

    console.log("freshIdgMeeting", freshIdgMeeting);

    const updatedIdg = await updateResource({
      ...(freshIdgMeeting[0] as IDGMeeting),
      status: "Completed",
      endDate: new Date().toISOString(),
    });

    console.log("updatedIdg", updatedIdg);

    notifications.show({
      position: "top-right",
      title: "IDG Meeting Concluded",
      message: "The IDG meeting has been concluded.",
      color: "green",
    });

    navigate("/patients");
  };

  // Navigate to previous or next incomplete patient
  const navigateToIncompletePatient = (direction: "previous" | "next") => {
    if (selectedPatient) {
      const currentIndex = incompletePatients.findIndex(
        (id) => id === selectedPatient.id,
      );

      let targetIncompletePatient: Patient | null = null;

      if (direction === "next") {
        // Find the next incomplete patient
        if (
          currentIndex !== -1 &&
          currentIndex < incompletePatients.length - 1
        ) {
          const nextIncompletePatientId = incompletePatients[currentIndex + 1];
          targetIncompletePatient =
            patients?.find((p) => p.id === nextIncompletePatientId) ?? null;
        }

        // If no incomplete patient found after current, look from the beginning
        if (!targetIncompletePatient && incompletePatients.length > 0) {
          const firstIncompletePatientId = incompletePatients[0];
          targetIncompletePatient =
            patients?.find((p) => p.id === firstIncompletePatientId) ?? null;
        }
      } else {
        // Find the previous incomplete patient
        if (currentIndex !== -1 && currentIndex > 0) {
          const prevIncompletePatientId = incompletePatients[currentIndex - 1];
          targetIncompletePatient =
            patients?.find((p) => p.id === prevIncompletePatientId) ?? null;
        }

        // If no incomplete patient found before current, look from the end
        if (!targetIncompletePatient && incompletePatients.length > 0) {
          const lastIncompletePatientId =
            incompletePatients[incompletePatients.length - 1];
          targetIncompletePatient =
            patients?.find((p) => p.id === lastIncompletePatientId) ?? null;
        }
      }

      if (targetIncompletePatient) {
        setSelectedPatient(targetIncompletePatient); // Immediate UI update
        navigate(`/idg-meeting/${targetIncompletePatient.id}`); // URL update
      } else if (direction === "next" && inProgressPatients.length === 0) {
        setConcludeModalOpen(true);
      }
    }
  };

  // Handle audio playback
  const togglePlayback = () => {
    if (!audioUrl) return;

    const audio = document.getElementById("recorded-audio") as HTMLAudioElement;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
    }
  };

  // Handle audio ended event
  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  // Check if current patient is already completed
  const isPatientCompleted = selectedPatient
    ? completedPatients.includes(selectedPatient.id)
    : false;

  return (
    <Container fluid>
      <LoadingOverlay
        visible={isProcessing}
        h="100%"
        w="100%"
        loaderProps={{
          children: (
            <Stack align="center">
              <Loader size="md" />
              <Text ta="center">Generating Patient IDG Summary...</Text>
            </Stack>
          ),
        }}
      />
      <Paper p="md" withBorder mb="md">
        <Group justify="space-between">
          <Group>
            <IconUsers size={24} />
            <Title order={3}>IDG Meeting</Title>
            <Badge size="lg" color="blue">
              {new Date().toLocaleDateString()}
            </Badge>
          </Group>
        </Group>
      </Paper>

      <Grid gutter="md">
        {/* Left sidebar - Patient list */}
        <Grid.Col span={{ base: 12, md: 3 }}>
          <Paper
            p="md"
            withBorder
            style={{ height: "calc(100vh - 180px)", overflow: "auto" }}
          >
            <Title order={4} mb="md">
              Patients
            </Title>
            <Stack gap="xs">
              {patients?.map((patient) => (
                <Paper
                  key={patient.id}
                  withBorder
                  p="md"
                  style={{
                    cursor: "pointer",
                    backgroundColor:
                      selectedPatient?.id === patient.id
                        ? "#f0f9ff"
                        : undefined,
                    borderLeft:
                      selectedPatient?.id === patient.id
                        ? "4px solid #228be6"
                        : undefined,
                    opacity: completedPatients.includes(patient.id) ? 0.7 : 1,
                  }}
                  onClick={() => {
                    setSelectedPatient(patient); // Immediate UI update
                    navigate(`/idg-meeting/${patient.id}`); // URL update
                  }}
                >
                  <Group justify="space-between">
                    <Text
                      fw={500}
                      td={
                        completedPatients.includes(patient.id)
                          ? `line-through`
                          : undefined
                      }
                    >
                      {patient.lastName}, {patient.firstName}
                    </Text>
                    {completedPatients.includes(patient.id) && (
                      <Badge
                        color="green"
                        leftSection={<IconCheck size={14} />}
                      >
                        Completed
                      </Badge>
                    )}
                    {inProgressPatients.includes(patient.id) && (
                      <Badge color="blue" leftSection={<IconCheck size={14} />}>
                        In Progress
                      </Badge>
                    )}
                  </Group>
                  {/* <Text size="sm" c="dimmed">MRN: {patient.medicalRecordNumber}</Text> */}
                  <Stack gap="xs" align="flex-start">
                    <Text size="sm" c="dimmed">
                      {patient.dateOfBirth}
                    </Text>
                    <Group>
                      <Badge color="blue">{patient.status}</Badge>
                      {patient.benefitPeriods &&
                        patient.benefitPeriods.length > 0 && (
                          <Badge color="gray">
                            Benefit Period: {patient.benefitPeriods[0].period}
                          </Badge>
                        )}
                    </Group>
                  </Stack>
                </Paper>
              ))}
            </Stack>
          </Paper>
        </Grid.Col>

        {/* Main content area */}
        <Grid.Col span={{ base: 12, md: 9 }}>
          {selectedPatient ? (
            <Box>
              {/* Patient header */}
              <Paper p="md" withBorder mb="md">
                <Group justify="space-between">
                  <Group>
                    <Title order={4}>
                      {selectedPatient.firstName} {selectedPatient.lastName}
                    </Title>
                    {isPatientCompleted && (
                      <Badge color="green">
                        Completed
                      </Badge>
                    )}
                  </Group>
                  <Group>
                    <Button
                      color="red"
                      onClick={() => setConcludeModalOpen(true)}
                    >
                      Conclude IDG Meeting
                    </Button>
                    <Button
                      variant="outline"
                      leftSection={<IconArrowLeft size={16} />}
                      onClick={() => navigateToIncompletePatient("previous")}
                      disabled={
                        patients?.findIndex(
                          (p) => p.id === selectedPatient?.id,
                        ) === 0
                      }
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      rightSection={<IconArrowRight size={16} />}
                      onClick={() => navigateToIncompletePatient("next")}
                      disabled={
                        patients?.findIndex(
                          (p) => p.id === selectedPatient?.id,
                        ) ===
                        (patients?.length ?? 0) - 1
                      }
                    >
                      Next
                    </Button>
                  </Group>
                </Group>
              </Paper>

              {/* Recording controls */}
              {!isPatientCompleted && <Paper p="md" withBorder mb="md">
                <Group justify="space-between">
                  <AudioRecorder
                    ref={recorderRef}
                    onRecordingComplete={handleRecordingComplete}
                    onStatusChange={setRecorderStatus}
                    showStopButton={false}
                  />
                  <Group>
                    <Tooltip
                      label={
                        isPatientCompleted
                          ? "This patient has already been completed for this IDG meeting"
                          : !recordingStarted
                            ? "You must start recording before you can complete the patient"
                            : ""
                      }
                      disabled={!isPatientCompleted && recordingStarted}
                    >
                      <Button
                        color="green"
                        leftSection={<IconCheck size={20} />}
                        onClick={completePatient}
                        disabled={!recordingStarted || isPatientCompleted}
                      >
                        {isPatientCompleted
                          ? "Already Completed"
                          : "Complete Patient"}
                      </Button>
                    </Tooltip>
                  </Group>
                </Group>

                {/* Hidden audio element for playback */}
                {audioUrl && (
                  <audio
                    id="recorded-audio"
                    src={audioUrl}
                    onEnded={handleAudioEnded}
                    style={{ display: "none" }}
                  >
                    <track kind="captions" src="" label="English captions" />
                  </audio>
                )}
              </Paper>}

              {/* Main content with collapsible sections */}
              <Grid gutter="md">
                <Grid.Col span={4}>
                  <Paper
                    p="md"
                    withBorder
                    style={{
                      height: "calc(100vh - 350px)",
                      overflow: "auto",
                      textAlign: "left",
                    }}
                  >
                    {isLoadingPatientSummary ? (
                      <Stack align="center" mt="xl">
                        <Loader />
                        <Text>Generating patient summary...</Text>
                      </Stack>
                    ) : (
                      <Stack gap="xs">
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {patientSummary}
                        </ReactMarkdown>
                      </Stack>
                    )}
                  </Paper>
                </Grid.Col>

                {/* Changes since last IDG */}
                <Grid.Col span={8}>
                  <Tabs
                    variant="outline"
                    value={activeTab}
                    onChange={setActiveTab}
                  >
                    <Tabs.List>
                      <Tabs.Tab value="changes">Changes</Tabs.Tab>
                      <Tabs.Tab value="medications">Medications</Tabs.Tab>
                    </Tabs.List>

                    <Tabs.Panel value="changes">
                      <Paper
                        p="md"
                        withBorder={false}
                        style={{
                          height: "calc(100vh - 385px)",
                          overflow: "auto",
                          borderLeft: "1px solid var(--mantine-color-gray-3)",
                          borderRight: "1px solid var(--mantine-color-gray-3)",
                          borderBottom: "1px solid var(--mantine-color-gray-3)",
                          borderRadius: "0 0 10px 10px",
                        }}
                      >
                        {isLoadingRequests &&
                        requests?.[0]?.patient?.id !== selectedPatient?.id ? (
                          <Loader />
                        ) : (
                          <VersionHistorySection
                            sectionedChanges={allRequests}
                          />
                        )}
                      </Paper>
                    </Tabs.Panel>
                    <Tabs.Panel value="medications">
                      <Paper
                        p="md"
                        withBorder={false}
                        style={{
                          height: "calc(100vh - 385px)",
                          overflow: "auto",
                          borderLeft: "1px solid var(--mantine-color-gray-3)",
                          borderRight: "1px solid var(--mantine-color-gray-3)",
                          borderBottom: "1px solid var(--mantine-color-gray-3)",
                          borderRadius: "0 0 10px 10px",
                          textAlign: "left",
                        }}
                      >
                        <CollapsibleMarkdown
                          markdown={patientMedsMarkdown ?? ""}
                        />
                      </Paper>
                    </Tabs.Panel>
                  </Tabs>
                </Grid.Col>
              </Grid>
            </Box>
          ) : (
            <Paper p="md" withBorder style={{ height: "calc(100vh - 180px)" }}>
              <Center style={{ height: "100%" }}>
                <Stack align="center">
                  <IconAlertCircle size={48} opacity={0.5} />
                  <Title order={3}>Select a Patient</Title>
                  <Text c="dimmed">
                    Please select a patient from the list to begin the IDG
                    review.
                  </Text>
                </Stack>
              </Center>
            </Paper>
          )}
        </Grid.Col>
      </Grid>

      {/* Confirmation Modal */}
      <Modal
        opened={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        title="Complete Patient Review"
        centered
      >
        <Text mb="md">
          Are you sure you want to complete the IDG review for{" "}
          {selectedPatient?.firstName} {selectedPatient?.lastName}?
        </Text>
        <Text mb="xl" c="dimmed" size="sm">
          This will generate an IDG summary note for this patient along with any
          changes to the patient record. You will be able to preview the note
          and changes before submitting.
        </Text>
        <Group justify="flex-end">
          <Button variant="outline" onClick={() => setConfirmModalOpen(false)}>
            Cancel
          </Button>
          <Button color="green" onClick={confirmCompletion}>
            Generate Summary
          </Button>
        </Group>
      </Modal>

      {/* Conclude IDG Modal */}
      <Modal
        opened={concludeModalOpen}
        onClose={() => setConcludeModalOpen(false)}
        title="Conclude IDG Meeting"
        centered
      >
        <Text mb="md">Are you sure you want to conclude this IDG meeting?</Text>

        {completedPatients.length < (patients?.length ?? 0) && (
          <Paper
            p="md"
            withBorder
            mb="md"
            style={{ backgroundColor: "#fff4e6" }}
          >
            <Group mb="xs">
              <IconAlertCircle size={20} color="orange" />
              <Text fw={500} c="orange">
                Warning: Incomplete Reviews
              </Text>
            </Group>
            <Text size="sm" mb="md">
              The following patients have not been reviewed:
            </Text>
            <List size="sm" spacing="xs">
              {patients
                ?.filter((p) => !completedPatients.includes(p.id))
                .map((p) => (
                  <List.Item key={p.id}>
                    {p.firstName} {p.lastName}
                  </List.Item>
                ))}
            </List>
          </Paper>
        )}

        <Text mb="xl" c="dimmed" size="sm">
          This will finalize all notes and conclude the IDG meeting. This action
          cannot be undone.
        </Text>

        <Group justify="flex-end">
          <Button variant="outline" onClick={() => setConcludeModalOpen(false)}>
            Cancel
          </Button>
          <Button color="red" onClick={concludeMeeting}>
            Conclude IDG Meeting
          </Button>
        </Group>
      </Modal>

      {selectedPatient && patientIdgNoteRequest && (
        <IDGSummaryModal
          opened={!!patientIdgNoteRequest}
          onClose={() => setPatientIdgNoteRequest(null)}
          onSubmit={() => {
            setPatientIdgNoteRequest(null);
            navigateToIncompletePatient("next")
          }}
          selectedPatient={selectedPatient}
          requestId={patientIdgNoteRequest?.id}
        />
      )}
    </Container>
  );
}
