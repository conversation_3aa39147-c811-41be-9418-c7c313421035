import { useState, useEffect, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import {
  setCurrentPatientId,
  setCurrentTimelineItem,
  setCurrentAction,
  PatientChartActionType,
} from "../store/patientChartSlice";
import {
  Container,
  Grid,
  Paper,
  Text,
  Group,
  Stack,
  Divider,
  Tabs,
  Checkbox,
  Badge,
  ScrollArea,
  Loader,
  Alert,
} from "@mantine/core";
import "../features/patient_chart/PatientChart.css";

// Import API hooks
import {
  useGetPatientByIdQuery,
  useUpdatePatientMutation,
} from "../store/api_extensions/patientApiExt";

// Import components
import { ResourceMarkdownView } from "../components/ResourceMarkdownView";
import { TimelineSection } from "../components/TimelineSection";
import { PatientChartHeader } from "../components/PatientChartHeader";

// Import from patient chart feature
import { Visit } from "../features/patient_chart";
import { Action, Request } from "@hospice-os/apptypes";
import FloatingActionButton from "../components/FloatingActionButton";

import {
  useGetRequestsByPatientQuery
} from "../store/api_extensions/requestApiExt";
import { AddPatientModal } from "../components/modals/AddPatientModal";
import { notifications } from "@mantine/notifications";
import { NoteModal } from "../components/modals/NoteModal";
import VisitNoteSection from "../components/VisitNoteSection";
import { IconInfoCircle } from "@tabler/icons-react";
import { api } from "../store/api";
import Markdown from "react-markdown";

export function PatientChartPage() {
  // Get patient ID from URL
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [pollingForRequestCompletion, setPollingForRequestCompletion] = useState<string | undefined>(undefined);

  const patientId = id || "";

  useEffect(() => {
    if (!patientId || patientId === "") navigate("/patients");
  }, [patientId]);

  const {
    data: patient,
    isLoading: isLoadingPatient,
    error: patientError
  } = useGetPatientByIdQuery(patientId, {
    skip: !patientId,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
    pollingInterval: 60000,
    skipPollingIfUnfocused: true,
  });

  const { data: requests, isLoading: isLoadingRequests } = useGetRequestsByPatientQuery({ patientId }, {
    skip: !patientId,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
    pollingInterval: pollingForRequestCompletion ? 1000 : 60000,
    skipPollingIfUnfocused: true,
  });

  // Use Redux state and dispatch
  const dispatch = useAppDispatch();
  const { currentTimelineItem } = useAppSelector((state) => state.patientChart);
  const [selectedVisit, setSelectedVisit] = useState<Visit | null>(null);
  const [addPatientModalOpen, setAddPatientModalOpen] =
    useState<boolean>(false);
  const [currentAgencyAction, setCurrentAgencyAction] = useState<Action | null>(
    null,
  );

  // Update Redux store when patient data changes
  useEffect(() => {
    if (patient) {
      dispatch(setCurrentPatientId(patient.id));
      dispatch(setCurrentAction(PatientChartActionType.VIEW_PATIENT));
      setCurrentRequest(null);
      dispatch(setCurrentTimelineItem(null));
      setSelectedVisit(null);
    }
  }, [patient, dispatch]);

  useEffect(() => {
    const pollingRequest = requests?.find(r => r.id === pollingForRequestCompletion)
    if (patient && pollingRequest && pollingRequest.status === "completed") {
      dispatch(api.util.invalidateTags([{ type: "Resources", id: patient.id }, { type: "Patient", id: patient.id }])) // Invalidate Patient tag to trigger refetch
      notifications.update({
        id: pollingForRequestCompletion,
        title: `Updated ${patient?.firstName + ' ' + patient?.lastName} chart`,
        message: 'The patient chart has been updated.',
        color: 'green',
        position: 'top-right',
        loading: false,
        withBorder: true,
        autoClose: true
      })
      setPollingForRequestCompletion(undefined)
    }
  }, [requests, pollingForRequestCompletion])

  const timeLineItemEntries = useMemo(() => {
    return [...(requests ?? [])]?.filter(r => !r.hideOnTimeline)?.sort(
        (a, b) =>
          new Date(b.madeBy?.date ?? '').getTime() - new Date(a.madeBy?.date ?? '').getTime(),
      );
  }, [requests]);

  const [currentRequest, setCurrentRequest] = useState<Request | null>(
    null,
  );

  // Request currently being edited in a modal
  const [noteModalRequest, setNoteModalRequest] = useState<Request | null>(null);

  const [updatePatient] = useUpdatePatientMutation();

  // Handle timeline entry click to show version history or visit details
  const handleTimelineEntryClick = (entry: Request) => {
    // If the entry is still being worked on, reopen the modal for further processing
    if (entry.status !== "completed") {
      setNoteModalRequest(entry);
      return;
    }

    // If a visit note exists, show it in the detail view
    if (entry.noteCreated) {
      setCurrentRequest(entry);

      dispatch(setCurrentTimelineItem(entry));
      dispatch(setCurrentAction(PatientChartActionType.VIEW_TIMELINE_ITEM));
      return;
    }

    // setCurrentRequestId(entry.requestRef?.id ?? null);
    // dispatch(setCurrentTimelineItem(entry));
  };

  // Handle closing detail views
  const handleCloseDetailView = () => {
    dispatch(setCurrentTimelineItem(null));
    setSelectedVisit(null);
  };

  const toggleTaskCompletion = (actionUuid: string) => {
    const updatedAction = {
      ...patient?.agencyActions?.find((action) => action.uuid === actionUuid),
    };

    if (updatedAction) {
      if (updatedAction.completed) {
        updatedAction.completed = false;
      } else {
        updatedAction.completed = true;
      }
    } else {
      return;
    }

    if (patient && patient.agencyActions) {
      const newPatient = { ...patient };
      // Create a new array with the updated action
      newPatient.agencyActions = newPatient?.agencyActions?.map((action) =>
        action.uuid === updatedAction.uuid ? updatedAction : action,
      ) as Action[];

      try {
        updatePatient(newPatient).unwrap();
      } catch (error) {
        console.error("Failed to update patient:", error);
      }
    }
  };

  const handleNoteModalSubmit = (templateName: string, requestId: string) => {
    // Poll continuously until request is completed
    setPollingForRequestCompletion(requestId)
    
    notifications.show({
      id: requestId,
      loading: true,
      title: `${templateName} is processing...` ,
      message: `Updating ${patient?.firstName + ' ' + patient?.lastName}'s chart`,
      color: 'blue',
      position: 'top-right',
      withBorder: true,
      autoClose: false,
      withCloseButton: false
    })
  }

  return (
    <Container fluid>
      {addPatientModalOpen && (
        <AddPatientModal
          opened={addPatientModalOpen}
          onClose={() => setAddPatientModalOpen(false)}
          onAddPatient={(newPatient, documents) => {}}
        />
      )}
      <Grid mt="md" gutter="md">
        {/* Left side - Timeline */}
        <Grid.Col span={{ base: 12, md: 4, lg: 3 }}>
          <Tabs defaultValue="timeline" variant="pills">
            <Paper
              p="md"
              withBorder
              mb="md"
              style={{ height: "calc(100vh - 125px)", overflow: "auto" }}
            >
              <Tabs.List style={{ width: "100%" }} pb="xs">
                <Tabs.Tab value="timeline" style={{ flex: 1 }}>
                  Timeline
                </Tabs.Tab>
                <Tabs.Tab value="actions" style={{ flex: 1 }}>
                  Actions
                </Tabs.Tab>
              </Tabs.List>
              <Divider pb="xs" />
              <Tabs.Panel value="timeline">
                {isLoadingRequests ? (
                  <Loader size="md" mt={10} />
                ) : (
                  <TimelineSection
                    entries={timeLineItemEntries ?? []}
                    onEntryClick={handleTimelineEntryClick}
                  />
                )}
              </Tabs.Panel>
              <Tabs.Panel value="actions">
                {/* TODO: move to a separate component */}
                <ScrollArea h="calc(100vh - 160px)" offsetScrollbars>
                  <Stack gap="xs">
                    {[...(patient?.agencyActions ?? [])]
                      .sort((a, b) => {
                        // First sort by completion status (non-completed first)
                        if (a.completed !== b.completed) {
                          return a.completed ? 1 : -1;
                        }
                        // Then sort by priority
                        const priorityOrder = {
                          high: 0,
                          medium: 1,
                          low: 2,
                        } as const;
                        return (
                          priorityOrder[
                            a.priority as keyof typeof priorityOrder
                          ] -
                          priorityOrder[
                            b.priority as keyof typeof priorityOrder
                          ]
                        );
                      })
                      .map((action) => (
                        <Paper
                          key={action.uuid}
                          withBorder
                          p="sm"
                          radius="md"
                          style={{
                            opacity: action.completed ? 0.7 : 1,
                            borderLeft: `4px solid ${
                              action.priority === "high"
                                ? "red"
                                : action.priority === "medium"
                                  ? "orange"
                                  : "green"
                            }`,
                            cursor:
                              action.actionType === "reminder"
                                ? "default"
                                : "pointer",
                          }}
                          onClick={() => {
                            if (action.actionType === "more_data") {
                              setCurrentAgencyAction(action);
                            }
                          }}
                        >
                          <Group justify="space-between" mb="xs">
                            <Group>
                              <Checkbox
                                checked={action.completed}
                                onChange={() => {
                                  if (action.actionType === "more_data") {
                                    setCurrentAgencyAction(action);
                                  } else {
                                    toggleTaskCompletion(action.uuid);
                                  }
                                }}
                                styles={{
                                  input: { cursor: "pointer" },
                                }}
                              />
                              <Text
                                style={{
                                  textDecoration: action.completed
                                    ? "line-through"
                                    : "none",
                                  fontWeight: action.completed ? "normal" : 500,
                                }}
                              >
                                {action.title}
                              </Text>
                              {action.actionType === "more_data" && (
                                <Badge color="blue" size="xs">
                                  More Data Needed
                                </Badge>
                              )}
                            </Group>
                            {action.content && (
                              <Text size="xs" c="dimmed">
                                {action.content}
                              </Text>
                            )}
                          </Group>
                          <Group justify="space-between" mt="xs">
                            {action.dueDate && (
                              <Text size="xs" c="dimmed">
                                Due: {action.dueDate}
                              </Text>
                            )}
                            <Badge
                              size="sm"
                              color={
                                action.priority === "high"
                                  ? "red"
                                  : action.priority === "medium"
                                    ? "orange"
                                    : "green"
                              }
                            >
                              {action.priority}
                            </Badge>
                          </Group>
                        </Paper>
                      ))}
                  </Stack>
                </ScrollArea>
              </Tabs.Panel>
            </Paper>
          </Tabs>
        </Grid.Col>

        {/* Right side - Patient Chart */}
        <Grid.Col span={{ base: 12, md: 8, lg: 9 }}>
          {/* Patient Chart Header with stepper timeline */}
          <PatientChartHeader
            selectedPatient={patient}
            onAddPatientClick={() => {
              // Navigate to add patient page or open modal
              // This would be implemented in a real app
              setAddPatientModalOpen(true);
            }}
            onNoteModalSubmit={handleNoteModalSubmit}
          />

          {isLoadingPatient ? (
            <Paper
              p="md"
              withBorder
              style={{ height: "calc(100vh - 340px)", overflow: "auto" }}
            >
              <Loader size="md" />
              <Text ta="center">Loading patient data...</Text>
            </Paper>
          ) : patientError ? (
            <Paper
              p="md"
              withBorder
              style={{ height: "calc(100vh - 340px)", overflow: "auto" }}
            >
              <Text c="red" ta="center">
                Error loading patient data
              </Text>
            </Paper>
          ) : currentTimelineItem ? (
            <>
              {currentRequest && currentRequest.noteCreated?.id && (
                <VisitNoteSection
                  currentRequest={currentRequest}
                  handleCloseDetailView={handleCloseDetailView}
                />
              )}
              {/* {currentRequestId && currentRequest && currentRequest.noteCreated?.id ? (
                <Paper p="md" withBorder style={{ height: 'calc(100vh - 340px)', overflow: 'auto' }}>
                  <ResourceMarkdownView
                    resourceId={currentRequest.noteCreated?.id || ''}
                    resourceType={currentRequest.noteCreated?.resourceType || ''}
                    onClose={handleCloseDetailView}
                    pollingInterval={5000}
                  />
                </Paper>
              ) : (
                <VersionHistorySection
                  selectedDate={currentTimelineItem.madeBy.date || ''}
                  onClose={handleCloseDetailView}
                  timelineItem={currentTimelineItem}
                />
              )} */}
            </>
          ) : (
            <Paper
              p="md"
              withBorder
              style={{ height: "calc(100vh - 340px)", overflow: "auto" }}
            >
              {/* <Alert
                    w="100%"
                    variant="light"
                    color="red"
                    title="Missing Required Information:"
                    icon={<IconInfoCircle />}
                    mb={"sm"}
                  >
                    <Markdown>{currentRequest?.qa}</Markdown>
                  </Alert> */}
              {/* Show patient data */}
              <ResourceMarkdownView
                resourceId={patient?.id || ""}
                resourceType={patient?.resourceType || ""}
              />
            </Paper>
          )}
        </Grid.Col>
      </Grid>
      {patient && patient.status !== "Pending" && <FloatingActionButton selectedPatient={patient} onNoteModalSubmit={handleNoteModalSubmit} />}

      {currentAgencyAction?.actionType === "more_data" && patient && (
        <NoteModal
          opened={true}
          onClose={() => {
            setCurrentAgencyAction(null);
            setCurrentTimelineItem(null);
            setCurrentRequest(null);
          }}
          selectedPatient={patient}
          fromAction={currentAgencyAction ?? undefined}
          request={currentRequest ?? undefined}
          onSubmit={handleNoteModalSubmit}
        />
      )}

      {noteModalRequest && patient && (
        <NoteModal
          opened={true}
          onClose={() => setNoteModalRequest(null)}
          selectedPatient={patient}
          request={noteModalRequest}
          onSubmit={handleNoteModalSubmit}
        />
      )}
    </Container>
  );
}
