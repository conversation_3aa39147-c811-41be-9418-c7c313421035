import { Container, Title, Space } from '@mantine/core';
import { PatientsList, mockPatients } from '../features/patients';
import { useGetPatientsQuery } from '../store/api_extensions/patientApiExt';
import { useUser } from '../features/auth/components';

export function PatientsPage() {
  const { isLoading: isUserLoading } = useUser();
  
  // Try to fetch patients from API, fallback to mock data if API fails
  const { data: apiPatients, isLoading: isPatientsLoading } = useGetPatientsQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true
  });
  
  const isLoading = isUserLoading || isPatientsLoading;
  
  const patients = apiPatients ?? [];

  return (
    <Container size="xl">
      <Title order={2} mb="md">Patients Directory</Title>
      <Space h="md" />
      <PatientsList 
        patients={patients} 
        isLoading={isLoading} 
        error={undefined} 
      />
    </Container>
  );
}
