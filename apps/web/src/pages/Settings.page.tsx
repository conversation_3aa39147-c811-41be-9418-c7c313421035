import { useState } from 'react';
import { 
  Container, 
  Paper, 
  Title, 
  Text, 
  Switch, 
  Stack, 
  Group, 
  Select, 
  Divider,
  useMantineColorScheme
} from '@mantine/core';

export function SettingsPage() {
  const { colorScheme, setColorScheme } = useMantineColorScheme();
  const [notifications, setNotifications] = useState(true);
  const [language, setLanguage] = useState('english');

  const toggleColorScheme = () => {
    setColorScheme(colorScheme === 'dark' ? 'light' : 'dark');
  };

  return (
    <Container>
      <Paper withBorder p="xl" radius="md" mt="xl">
        <Title order={1} ta="center" mb="lg">
          Settings
        </Title>
        
        <Stack>
          <Group justify="space-between">
            <div>
              <Text fw={500}>Dark Mode</Text>
              <Text size="sm" c="dimmed">Toggle between light and dark theme</Text>
            </div>
            <Switch 
              checked={colorScheme === 'dark'} 
              onChange={toggleColorScheme}
              size="lg"
            />
          </Group>
          
          <Divider my="sm" />
          
          <Group justify="space-between">
            <div>
              <Text fw={500}>Notifications</Text>
              <Text size="sm" c="dimmed">Receive notifications from the application</Text>
            </div>
            <Switch 
              checked={notifications} 
              onChange={(event) => setNotifications(event.currentTarget.checked)}
              size="lg"
            />
          </Group>
          
          <Divider my="sm" />
          
          <Group justify="space-between" align="flex-start">
            <div>
              <Text fw={500}>Language</Text>
              <Text size="sm" c="dimmed">Select your preferred language</Text>
            </div>
            <Select
              value={language}
              onChange={(value) => setLanguage(value || 'english')}
              data={[
                { value: 'english', label: 'English' },
                { value: 'spanish', label: 'Spanish' },
                { value: 'french', label: 'French' },
                { value: 'german', label: 'German' },
              ]}
              w={200}
            />
          </Group>
        </Stack>
      </Paper>
    </Container>
  );
}
