import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { AppDispatch } from "../store";

const baseUrl = import.meta.env.API_BASE_URL + "/api";

export const api = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({
    baseUrl,
    credentials: "include", // Include cookies in the request
  }),
  tagTypes: ["Schemas", "UISchemas", "Resources", "Patient", "Users"],
  endpoints: (builder) => ({
    // Define your endpoints here
    getExample: builder.query<{ message: string }, void>({
      query: () => "example",
    }),

    // Schema endpoints
    getSchema: builder.query({
      query: (id: string) => `/schemas/${id}`,
      providesTags: (result, error, id: string) => [{ type: "Schemas", id }],
    }),

    // UI Schema endpoints
    getUISchemasBySchema: builder.query({
      query: (schemaId: string) => `/ui-schemas/schema/${schemaId}`,
      providesTags: (result, error, schemaId: string) => [
        { type: "UISchemas", id: `schema-${schemaId}` },
        ...(result
          ? result.map(({ id }: { id: string }) => ({
              type: "UISchemas" as const,
              id,
            }))
          : []),
      ],
    }),
  }),
});

export const {
  useGetExampleQuery,
  useGetSchemaQuery,
  useGetUISchemasBySchemaQuery,
} = api;

export const invalidateResourceTag = (
  dispatch: AppDispatch,
  tags:
    | {
        type: "Schemas" | "UISchemas" | "Resources" | "Patient" | "Users";
        id: string;
      }
    | {
        type: "Schemas" | "UISchemas" | "Resources" | "Patient" | "Users";
        id: string;
      }[],
) => {
  const tagsArray = Array.isArray(tags) ? tags : [tags];
  dispatch(api.util.invalidateTags(tagsArray as any));
};
