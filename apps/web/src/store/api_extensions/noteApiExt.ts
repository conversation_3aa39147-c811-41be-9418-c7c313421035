import { api } from "../api";

export const noteApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    getLastNoteSummary: builder.query<
      string,
      { patientId: string; noteSchemaId: string }
    >({
      query: ({ patientId, noteSchemaId }) => ({
        url: `/resources/Note/${noteSchemaId}/lastSummary?patientId=${patientId}`
      }),
      providesTags: (result, error, { patientId, noteSchemaId }) => [
        { type: "Resources", id: `${patientId}-${noteSchemaId}-Summary` },
      ],
    }),
  }),
});

export const { useGetLastNoteSummaryQuery } = noteApiExt;
