import { api } from "../api";
import { Patient } from "@hospice-os/apptypes";

export const patientApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    getPatientById: builder.query<Patient, string>({
      query: (id) => ({
        url: `/resources/Patient/${id}`,
        // The server will automatically validate that the requested patient
        // belongs to the user's agency based on the authenticated user's session
        // This ensures users can never access patients from other agencies
      }),
      providesTags: (result) => [{ type: "Patient", id: result?.id }],
    }),
    getPatients: builder.query<Patient[], string[] | undefined>({
      query: (fields) => ({
        url: "/resources/Patient",
        params: fields ? { fields: fields.join(",") } : undefined,
      }),
      // Transform the response to ensure we only get Patient resources
      // This is just a type safety measure, as the server should already
      // be filtering by resourceType
      transformResponse: (response: unknown) => {
        if (Array.isArray(response)) {
          return response.filter(
            (item) => item.resourceType === "Patient",
          ) as Patient[];
        }
        return [];
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Patient" as const, id })),
              { type: "Patient", id: "PATIENTS_LIST" },
            ]
          : [{ type: "Patient", id: "PATIENTS_LIST" }],
    }),
    // TODO: should frontend be able to create patients long-term?
    createPatient: builder.mutation<Patient, Partial<Patient>>({
      query: (resource) => ({
        url: "/resources",
        method: "POST",
        body: resource,
      }),
      transformResponse: (response: Patient) => response,
      invalidatesTags: [{ type: "Patient" as const, id: "PATIENTS_LIST" }],
    }),
    updatePatient: builder.mutation<Patient, Partial<Patient>>({
      query: (resource) => ({
        url: `/resources/${resource.id}`,
        method: "PUT",
        body: resource,
      }),
      // Add optimistic update logic
      async onQueryStarted(resource, { dispatch, queryFulfilled }) {
        // Create a patch result for updating the cache
        const patchResult = dispatch(
          patientApiExt.util.updateQueryData(
            "getPatientById",
            resource.id!,
            (draft) => {
              return { ...draft, ...resource };
            },
          ),
        );

        try {
          await queryFulfilled;
        } catch {
          // If the request fails, revert the optimistic update
          patchResult.undo();
        }
      },
      invalidatesTags: (result, error, resource) => [
        { type: "Patient" as const, id: "PATIENTS_LIST" },
        { type: "Patient", id: resource.id },
        { type: "Resources", id: resource.id },
      ],
    }),
    getPatientSummaryForIDG: builder.query<string, string>({
      query: (id) => ({
        url: `/resources/Patient/${id}/summary`,
        // The server will automatically validate that the requested patient
        // belongs to the user's agency based on the authenticated user's session
        // This ensures users can never access patients from other agencies
      }),
      providesTags: (result) => [{ type: "Patient", summary: true }],
    })
  }),
});

export const {
  useGetPatientByIdQuery,
  useGetPatientsQuery,
  useCreatePatientMutation,
  useUpdatePatientMutation,
  useGetPatientSummaryForIDGQuery
} = patientApiExt;
