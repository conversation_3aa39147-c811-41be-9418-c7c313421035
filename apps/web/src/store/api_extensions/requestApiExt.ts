import { api } from "../api";
import { Request } from "@hospice-os/apptypes";

export const requestApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get requests by patient ID
    getRequestsByPatient: builder.query<
      Request[],
      { patientId: string; status?: string }
    >({
      query: ({ patientId, status }) =>
        `/requests?patientId=${patientId}&status=${status}`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Resources" as const, id })),
              { type: "Resources", id: "REQUESTS_LIST" },
            ]
          : [{ type: "Resources", id: "REQUESTS_LIST" }],
    }),

    // Get request by ID
    getRequestById: builder.query<Request, string>({
      query: (id) => `/requests/${id}`,
      providesTags: (result) => [{ type: "Resources", id: result?.id }],
    }),

    // Create a new request
    createRequest: builder.mutation<Request, Partial<Request>>({
      query: (request) => ({
        url: "/requests",
        method: "POST",
        body: request,
      }),
      invalidatesTags: [{ type: "Resources", id: "REQUESTS_LIST" }],
    }),

    // Get pre-signed upload URL
    getUploadUrl: builder.mutation<
      { url: string; filePath: string },
      { fileName: string; fileType: string; patientId: string }
    >({
      query: ({ fileName, fileType, patientId }) => ({
        url: "/requests/file-upload",
        method: "POST",
        body: { fileName, fileType, patientId },
      }),
    }),

    // Add file attachment to request
    addFileAttachment: builder.mutation<
      Request,
      { requestId: string; fileId: string }
    >({
      query: ({ requestId, fileId }) => ({
        url: `/requests/${requestId}/attachments`,
        method: "POST",
        body: { fileId },
      }),
      invalidatesTags: (result) => [{ type: "Resources", id: result?.id }],
    }),

    // Add response to request
    addResponse: builder.mutation<
      Request,
      { requestId: string; content: string }
    >({
      query: ({ requestId, content }) => ({
        url: `/requests/${requestId}/responses`,
        method: "POST",
        body: { content },
      }),
      invalidatesTags: (result) => [{ type: "Resources", id: result?.id }],
    }),

    // Send more audio to an existing request
    sendMoreAudio: builder.mutation<
      { invocationId: string },
      { requestId: string; audioUrls: string[] }
    >({
      query: ({ requestId, audioUrls }) => ({
        url: `/requests/${requestId}/more-audio`,
        method: "POST",
        body: { audioUrls },
      }),
    }),

    // Review a request
    reviewRequest: builder.mutation<
      { invocationId: string; requestId: string },
      {
        requestId: string;
        userId: string;
        manualChangeset?: Request["changes"];
      }
    >({
      query: ({ requestId, userId, manualChangeset }) => ({
        url: `/requests/${requestId}/review`,
        method: "POST",
        body: { userId: userId, manualChangeset },
      }),
      invalidatesTags: (result) => [
        { type: "Resources", id: result?.requestId },
        { type: "Resources", id: "REQUESTS_LIST" },
      ],
    }),

    // Modify changeset
    modifyChangeset: builder.mutation<
      { invocationId: string },
      { requestId: string; audioUrl: string }
    >({
      query: ({ requestId, audioUrl }) => ({
        url: `/requests/${requestId}/modify-changeset`,
        method: "POST",
        body: { audioUrl },
      }),
    }),
  }),
});

export const {
  useGetRequestsByPatientQuery,
  useGetRequestByIdQuery,
  useCreateRequestMutation,
  useGetUploadUrlMutation,
  useAddFileAttachmentMutation,
  useAddResponseMutation,
  useSendMoreAudioMutation,
  useReviewRequestMutation,
  useModifyChangesetMutation,
} = requestApiExt;
