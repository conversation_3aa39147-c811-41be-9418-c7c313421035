import { BaseResource } from "@hospice-os/apptypes";
import { api } from "../api";

export const resourceApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    getResourceById: builder.query<
      unknown,
      { resourceType: string; id: string }
    >({
      query: ({ resourceType, id }) => ({
        url: `/resources/${resourceType}/${id}`,
      }),
      providesTags: (result, error, { resourceType, id }) => [
        { type: "Resources", id: id },
      ],
    }),
    getResources: builder.query<
      { data: Record<string, unknown>[] },
      {
        resourceType: string;
        filters?: Record<string, string | number | boolean>;
        fields?: string[];
      }
    >({
      query: ({ resourceType, filters, fields }) => {
        // Start with the base URL
        let queryString = `/resources/${resourceType}`;

        // Create params object
        const params = new URLSearchParams();

        // Add filters as query parameters if they exist
        if (filters && Object.keys(filters).length > 0) {
          for (const [key, value] of Object.entries(filters)) {
            params.append(key, String(value));
          }
        }

        // Add fields parameter if it exists
        if (fields && fields.length > 0) {
          params.append("fields", fields.join(","));
        }

        // Add query string if we have parameters
        if (params.toString()) {
          queryString += `?${params.toString()}`;
        }

        return {
          url: queryString,
        };
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map((resource) => ({
                type: "Resources" as const,
                id: resource.id,
              })),
              { type: "Resources", id: "LIST" },
            ]
          : [{ type: "Resources", id: "LIST" }],
    }),
    createResource: builder.mutation<unknown, Record<string, unknown>>({
      query: (resource) => ({
        url: `/resources`,
        method: "POST",
        body: resource,
      }),
      invalidatesTags: [{ type: "Resources", id: "LIST" }],
    }),
    updateResource: builder.mutation({
      query: (resource) => ({
        url: `/resources/${resource.id}`,
        method: "PUT",
        body: resource,
      }),
      invalidatesTags: (result, error, { id }: { id: string }) => [{ type: 'Resources', id }],
    }),
  }),
});

export const {
  useGetResourceByIdQuery,
  useGetResourcesQuery,
  useCreateResourceMutation,
  useUpdateResourceMutation
} = resourceApiExt;
