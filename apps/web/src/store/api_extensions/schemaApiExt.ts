import { api } from '../api';

// Define Schema interface
interface Schema {
  id: string;
  name: string;
  description?: string;
  schema: Record<string, unknown>;
  baseSchemaId?: string;
  version: string;
  createdAt: string;
  updatedAt: string;
}

export const schemaApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get schemas by base schema ID
    getSchemasByBase: builder.query<Schema[], string>({
      query: (baseSchemaId) => `/schemas?baseSchemaId=${baseSchemaId}`,
      providesTags: [{ type: 'Schemas', id: 'LIST' }]
    }),

    getResolvedSchema: builder.query<Schema['schema'], string>({
      query: (id) => `/schemas/${id}/resolved`,
      providesTags: (result, error, id: string) => [{ type: 'Schemas', id }]
    }),
    
    // Get note templates (schemas that extend NoteBase)
    getNoteTemplates: builder.query<Schema[], void>({
      query: () => `/schemas?baseSchemaId=NoteBase`,
      providesTags: [{ type: 'Schemas', id: 'NOTE_TEMPLATES' }]
    }),
    
    // Get order templates (schemas that extend OrderBase)
    getOrderTemplates: builder.query<Schema[], void>({
      query: () => `/schemas?baseSchemaId=OrderBase`,
      providesTags: [{ type: 'Schemas', id: 'ORDER_TEMPLATES' }]
    })
  })
});

export const {
  useGetSchemasByBaseQuery,
  useGetNoteTemplatesQuery,
  useGetOrderTemplatesQuery,
  useGetResolvedSchemaQuery
} = schemaApiExt;
