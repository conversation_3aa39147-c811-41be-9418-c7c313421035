import { api } from "../api";
import { UISchema } from "../../types/uiSchema";

// Define types for UI schema responses
interface UISchemaResponse {
  id: string;
  name: string;
  description?: string;
  schemaId: string;
  content: UISchema;
  createdAt: string;
  updatedAt: string;
}

export const uiSchemaApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    getUISchemaById: builder.query<UISchemaResponse, string>({
      query: (id) => ({
        url: `/ui-schemas/${id}`,
      }),
      providesTags: (result, error, id) => [{ type: 'UISchemas', id }],
    }),
    getUISchemasBySchemaId: builder.query<UISchemaResponse[], string>({
      query: (schemaId) => ({
        url: `/ui-schemas/schema/${schemaId}`
      }),
      providesTags: (result, error, schemaId) => [
        { type: 'UISchemas', id: `schema-${schemaId}` },
        ...(result ? result.map(({ id }) => ({ type: 'UISchemas' as const, id })) : [])
      ],
    }),
    getAllUISchemas: builder.query<UISchemaResponse[], void>({
      query: () => ({
        url: '/ui-schemas'
      }),
      providesTags: (result) => 
        result 
          ? [
              { type: 'UISchemas', id: 'LIST' },
              ...result.map(({ id }) => ({ type: 'UISchemas' as const, id }))
            ]
          : [{ type: 'UISchemas', id: 'LIST' }],
    }),
  }),
});

export const { 
  useGetUISchemaByIdQuery, 
  useGetUISchemasBySchemaIdQuery,
  useGetAllUISchemasQuery
} = uiSchemaApiExt;
