import { api } from "../api";

// Define the User interface locally
interface User {
  id: string;
  resourceType: "User";
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  agencyId: string;
  stytchId?: string;
}

export const userApiExt = api.injectEndpoints({
  endpoints: (builder) => ({
    getCurrentUser: builder.query<User, void>({
      query: () => "/users/me",
    }),
    getUserById: builder.query<User, string>({
      query: (id: string) => `/users/${id}`,
    }),
    getUsers: builder.query({
      query: ({ role, agencyId }: { role: string; agencyId?: string | null | undefined }) =>
        `/users/role/${role}?agencyId=${agencyId}`,
      providesTags: ["Users"],
    }),
  }),
});

export const { useGetCurrentUserQuery, useGetUserByIdQuery, useGetUsersQuery } = userApiExt;
