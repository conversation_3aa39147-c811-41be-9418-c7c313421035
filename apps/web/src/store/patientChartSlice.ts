import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Patient, TimeLineItem } from '@hospice-os/apptypes';

// Define action types as a string enum
export enum PatientChartActionType {
  VIEW_PATIENT = 'VIEW_PATIENT',
  VIEW_TIMELINE_ITEM = 'VIEW_TIMELINE_ITEM',
  EDIT_PATIENT = 'EDIT_PATIENT',
  ADD_NOTE = 'ADD_NOTE',
  // Add more as needed
}

// Define the state interface
interface PatientChartState {
  currentPatientId: string | undefined;
  currentAction: PatientChartActionType | null;
  currentTimelineItem: TimeLineItem | null;
}

const initialState: PatientChartState = {
  currentPatientId: undefined,
  currentAction: null,
  currentTimelineItem: null,
};

const patientChartSlice = createSlice({
  name: 'patientChart',
  initialState,
  reducers: {
    setCurrentPatientId: (state, action: PayloadAction<string | undefined>) => {
      state.currentPatientId = action.payload;
    },
    setCurrentAction: (state, action: PayloadAction<PatientChartActionType | null>) => {
      state.currentAction = action.payload;
    },
    setCurrentTimelineItem: (state, action: PayloadAction<TimeLineItem | null>) => {
      state.currentTimelineItem = action.payload;
    },
    // Reset all state
    resetPatientChart: (state) => {
      state.currentPatientId = undefined;
      state.currentAction = null;
      state.currentTimelineItem = null;
    },
  },
});

export const {
  setCurrentPatientId,
  setCurrentAction,
  setCurrentTimelineItem,
  resetPatientChart,
} = patientChartSlice.actions;

export default patientChartSlice.reducer;
