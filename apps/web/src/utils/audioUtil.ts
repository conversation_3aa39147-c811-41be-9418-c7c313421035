export const getAudioMimeType = () => {
  return MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 
            MediaRecorder.isTypeSupported('audio/wav') ? 'audio/wav' :
            MediaRecorder.isTypeSupported('audio/m4a') ? 'audio/m4a' : 
            MediaRecorder.isTypeSupported('audio/mp4') ? 'audio/mp4' :
            MediaRecorder.isTypeSupported('audio/mp3') ? 'audio/mp3' :
            MediaRecorder.isTypeSupported('audio/ogg') ? 'audio/ogg' : 
            'audio/wav'
}