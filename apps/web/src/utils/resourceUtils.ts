/**
 * Utilities for fetching and working with resources
 */
import { store } from '../store';
import { resourceApiExt } from '../store/api_extensions/resourceApiExt';

/**
 * Fetch a resource by type and ID
 * @param resourceType The type of resource to fetch
 * @param id The ID of the resource
 * @returns The resource data or null if not found
 */
export async function fetchResource(resourceType: string, id: string): Promise<Record<string, unknown> | null> {
  try {
    // Use RTK Query endpoint directly to take advantage of caching
    const result = await store.dispatch(
      resourceApiExt.endpoints.getResourceById.initiate({ resourceType, id })
    );
    
    if (result.error) {
      throw new Error(`Failed to fetch resource: ${JSON.stringify(result.error)}`);
    }
    
    return result.data || null;
  } catch (error) {
    console.error(`Error fetching ${resourceType} resource with ID ${id}:`, error);
    return null;
  }
}

/**
 * Extract specific fields from a resource
 * @param resource The resource object
 * @param fields Array of field names to extract (include mode)
 * @param hideFields Array of field names to exclude (exclude mode)
 * @returns Object containing the filtered fields
 */
export function extractResourceFields(
  resource: Record<string, unknown>, 
  fields?: string[],
  hideFields?: string[]
): Record<string, unknown> | null {
  if (!resource) return null;
  
  // If neither fields nor hideFields are specified, return the entire resource
  if ((!fields || fields.length === 0) && (!hideFields || hideFields.length === 0)) {
    return resource;
  }
  
  const result: Record<string, unknown> = {};
  
  // If fields is specified, only include those fields (include mode)
  if (fields && fields.length > 0) {
    for (const field of fields) {
      if (resource[field] !== undefined) {
        result[field] = resource[field];
      }
    }
    return result;
  }
  
  // If hideFields is specified, include all fields except those (exclude mode)
  if (hideFields && hideFields.length > 0) {
    for (const [key, value] of Object.entries(resource)) {
      if (!hideFields.includes(key)) {
        result[key] = value;
      }
    }
  }
  
  return result;
}

/**
 * Get a nested property from an object using a path string
 * @param obj The object to get the property from
 * @param path The path to the property (e.g., "user.address.city")
 * @returns The property value or undefined if not found
 */
export function getNestedProperty(obj: Record<string, unknown>, path: string): unknown {
  if (!obj || !path) return undefined;
  
  const parts = path.split('.');
  let current: unknown = obj;
  
  for (const part of parts) {
    if (current === null || current === undefined) return undefined;
    if (typeof current !== 'object') return undefined;
    
    // Type assertion to access properties
    const currentObj = current as Record<string, unknown>;
    current = currentObj[part];
  }
  
  return current;
}

/**
 * Format a resource as a string for display
 * @param resource The resource object
 * @returns A string representation of the resource
 */
export function formatResourceForDisplay(resource: Record<string, unknown> | null): string {
  if (!resource) return 'Resource not found';
  
  // If the resource has a name or title, use that
  if (resource.name && typeof resource.name === 'string') return resource.name;
  if (resource.title && typeof resource.title === 'string') return resource.title;
  if (resource.summary && typeof resource.summary === 'string') return resource.summary;
  if (resource.content && typeof resource.content === 'string') return resource.content;
  if (resource.problem && typeof resource.problem === 'string') return resource.problem;
  if (resource.email && typeof resource.email === 'string') return resource.email;
  if (resource.phone && typeof resource.phone === 'string') return resource.phone;

  
  // Otherwise, return a generic string
  return 'Resource';
}
