// Utility functions for S3 uploads

/**
 * Format file size in a human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get a pre-signed URL for uploading a file to S3
 * This is a mock implementation - in a real app, this would call an API endpoint
 */
export const getPresignedUploadUrl = async (
  fileName: string
): Promise<{ url: string; fileId: string }> => {
  // In a real implementation, this would call an API endpoint to get a pre-signed URL
  // For this mock, we'll just return a fake URL and file ID
  
  // Generate a random file ID
  const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    url: `https://example-bucket.s3.amazonaws.com/${fileId}/${fileName}`,
    fileId
  };
};

/**
 * Upload a file to S3 using a pre-signed URL
 * This is a mock implementation - in a real app, this would actually upload the file
 */
export const uploadFileToS3 = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<string> => {
  // Get a pre-signed URL for the file
  const { fileId } = await getPresignedUploadUrl(file.name);
  
  // In a real implementation, this would upload the file to S3 using the pre-signed URL
  // For this mock, we'll just simulate progress and return the file ID
  
  // Simulate upload progress
  if (onProgress) {
    for (let progress = 0; progress <= 100; progress += 10) {
      onProgress(progress);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  } else {
    // Simulate network delay without progress updates
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return fileId;
};

/**
 * Upload multiple files to S3
 * This is a mock implementation - in a real app, this would actually upload the files
 */
export const uploadFilesToS3 = async (
  files: File[],
  onProgress?: (fileIndex: number, progress: number) => void
): Promise<string[]> => {
  const fileIds: string[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    // Upload the file
    const fileId = await uploadFileToS3(
      file,
      onProgress ? (progress) => onProgress(i, progress) : undefined
    );
    
    fileIds.push(fileId);
  }
  
  return fileIds;
};
