/**
 * Utility functions to convert a JSON schema to markdown format and generate sample data
 */
import { UISchema } from '@hospice-os/ui-components';
import {
  applyUIOrder,
  shouldDisplayProperty,
  getWidgetType,
  getWidgetOptions,
  getWidgetPropertyOrder
} from './uiSchemaUtils';
import {
  fetchResource,
  extractResourceFields,
  formatResourceForDisplay
} from './resourceUtils';
import { uuidv7 } from 'uuidv7';

/**
 * Format a property name for display in markdown
 * - Converts camelCase to space-separated words
 * - Capitalizes the first letter of each word
 * 
 * @param propertyName The property name to format
 * @returns The formatted property name
 */
export function formatPropertyName(propertyName: string): string {
  if (/^[A-Z]+$/.test(propertyName)) {
    // Entire field is an acronym (e.g., "DME"), return as-is
    return propertyName;
  }

  // Step 1: Add space before any uppercase letter that follows a lowercase letter
  let result = propertyName.replace(/([a-z])([A-Z])/g, '$1 $2');

  // Step 2: Add space between acronyms and normal words (e.g., CTIObtained → CTI Obtained)
  result = result.replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2');

  // Step 3: Capitalize each word, but preserve acronyms (2+ uppercase letters)
  return result
    .split(' ')
    .map(word => {
      return word.length > 1 && word === word.toUpperCase()
        ? word // keep acronyms as-is
        : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(' ');
}

/**
 * Convert a JSON schema to markdown format
 * @param schema The JSON schema object to convert
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @param uiSchema Optional UI schema for customizing the display
 * @returns A markdown string representation of the schema
 */
export async function schemaToMarkdown(
  schema: Record<string, unknown>,
  fetchExternalSchema?: (schemaId: string) => Promise<Record<string, unknown> | null>,
  uiSchema?: UISchema
): Promise<string> {
  if (!schema) return '';


  let markdown = '';

  // Add title and description if available
  if (schema.title && typeof schema.title === 'string') {
    markdown += `# ${schema.title}\n\n`;
  } else {
    markdown += `# Sample Data\n\n`;
  }

  if (schema.description && typeof schema.description === 'string') {
    markdown += `${schema.description}\n\n`;
  }

  try {
    // Generate sample data based on the schema
    const sampleData = await generateSampleData(schema, undefined, uiSchema, fetchExternalSchema);

    // Generate markdown representation of the sample data
    markdown += await dataToMarkdown(sampleData, uiSchema);
  } catch (error) {
    markdown += `Error generating sample data: ${error}\n\n`;
  }

  return markdown;
}

/**
 * Generate sample data based on a JSON schema
 * @param schema The JSON schema to generate sample data for
 * @param rootSchema The root schema for resolving references (defaults to schema if not provided)
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @returns Sample data that conforms to the schema
 */
async function generateSampleData(
  schema: Record<string, unknown>,
  rootSchema?: Record<string, unknown>,
  uiSchema?: UISchema,
  fetchExternalSchema?: (schemaId: string) => Promise<Record<string, unknown> | null>
): Promise<unknown> {
  if (!schema) return null;

  // Use the provided rootSchema or default to the schema itself
  const root = rootSchema || schema;

  // Handle $ref
  if (schema.$ref && typeof schema.$ref === 'string') {
    // Try to resolve the reference internally first
    let resolvedSchema = resolveReference(schema.$ref, root);

    // If not found internally and it looks like an external reference, try to fetch it
    if (!resolvedSchema && fetchExternalSchema && isExternalReference(schema.$ref)) {
      const schemaId = extractSchemaId(schema.$ref);
      if (schemaId) {
        const externalSchema = await fetchExternalSchema(schemaId);
        if (externalSchema) {
          // If the reference has a path within the external schema, resolve that too
          const pathWithinSchema = extractPathWithinSchema(schema.$ref);
          if (pathWithinSchema) {
            resolvedSchema = resolveReference(pathWithinSchema, externalSchema);
          } else {
            resolvedSchema = externalSchema;
          }
        }
      }
    }

    if (resolvedSchema) {
      return generateSampleData(resolvedSchema, root, uiSchema, fetchExternalSchema);
    } else {
      // If reference can't be resolved, return a placeholder
      return `[Reference to ${schema.$ref}]`;
    }
  }

  const filteredSchema = { ...schema }

  // Remove properties that are in hideFields from schema.properties
  if (schema.properties && uiSchema?.hideFields && Array.isArray(uiSchema.hideFields)) {
    const hideFields = uiSchema.hideFields;
    const properties = schema.properties as Record<string, unknown>;

    // Create a new object without the hidden fields instead of deleting
    const filteredProperties: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(properties)) {
      if (!hideFields.includes(key)) {
        filteredProperties[key] = value;
      }
    }
    filteredSchema.properties = filteredProperties
  }

  // Handle different schema types
  const schemaType = filteredSchema.type as string | undefined;

  switch (schemaType) {
    case 'object':
      return generateObjectData(filteredSchema, root, uiSchema, fetchExternalSchema);
    case 'array':
      return generateArrayData(filteredSchema, root, uiSchema, fetchExternalSchema);
    case 'string':
      return generateStringData(filteredSchema);
    case 'number':
    case 'integer':
      return generateNumberData(filteredSchema);
    case 'boolean':
      return generateBooleanData(filteredSchema);
    case 'null':
      return null;
    default:
      // If type is not specified, assume it's an object
      if (filteredSchema.properties && typeof filteredSchema.properties === 'object') {
        return generateObjectData(filteredSchema, root, uiSchema, fetchExternalSchema);
      }
      // If it has enum values, return the first one
      if (filteredSchema.enum && Array.isArray(filteredSchema.enum) && filteredSchema.enum.length > 0) {
        return filteredSchema.enum[0];
      }
      // Default fallback
      return {};
  }
}

/**
 * Check if a reference is to an external schema
 * @param ref The reference string
 * @returns True if the reference is to an external schema
 */
function isExternalReference(ref: string): boolean {
  // External references might be in formats like:
  // - "schemaId:path/within/schema"
  // - "schemaId"
  return !ref.startsWith('#/');
}

/**
 * Extract the schema ID from an external reference
 * @param ref The reference string
 * @returns The schema ID or null if not found
 */
function extractSchemaId(ref: string): string | null {
  // If the reference contains a colon, the part before the colon is the schema ID
  const colonIndex = ref.indexOf(':');
  if (colonIndex !== -1) {
    return ref.substring(0, colonIndex);
  }

  // If there's no colon, the entire reference might be the schema ID
  return ref;
}

/**
 * Extract the path within a schema from an external reference
 * @param ref The reference string
 * @returns The path within the schema or null if not found
 */
function extractPathWithinSchema(ref: string): string | null {
  const colonIndex = ref.indexOf(':');
  if (colonIndex !== -1 && colonIndex < ref.length - 1) {
    const path = ref.substring(colonIndex + 1);
    // Convert to JSON Pointer format if it's not already
    return path.startsWith('#/') ? path : `#/${path}`;
  }
  return null;
}

/**
 * Resolve a JSON Schema reference
 * @param ref The reference string (e.g., "#/definitions/Person")
 * @param rootSchema The root schema containing the definitions
 * @returns The resolved schema or null if not found
 */
function resolveReference(ref: string, rootSchema: Record<string, unknown>): Record<string, unknown> | null {
  // Only handle local references for now (starting with #/)
  if (!ref.startsWith('#/')) {
    return null;
  }

  // Remove the #/ prefix and split the path
  const path = ref.substring(2).split('/');

  // Navigate through the schema to find the referenced definition
  let current: unknown = rootSchema;
  for (const segment of path) {
    if (!current || typeof current !== 'object') {
      return null;
    }

    // Handle JSON pointer escaping
    const key = segment.replace(/~1/g, '/').replace(/~0/g, '~');

    const currentObj = current as Record<string, unknown>;

    if (currentObj[key] === undefined) {
      // Check alternative locations (for backward compatibility)
      if (key === 'definitions' && currentObj['$defs']) {
        current = currentObj['$defs'];
      } else if (key === '$defs' && currentObj['definitions']) {
        current = currentObj['definitions'];
      } else {
        return null; // Reference not found
      }
    } else {
      current = currentObj[key];
    }
  }

  if (!current || typeof current !== 'object') {
    return null;
  }

  return current as Record<string, unknown>;
}

/**
 * Generate sample data for an object schema
 * @param schema The object schema
 * @param rootSchema The root schema for resolving references
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @returns Sample object data
 */
async function generateObjectData(
  schema: Record<string, unknown>,
  rootSchema: Record<string, unknown>,
  uiSchema?: UISchema,
  fetchExternalSchema?: (schemaId: string) => Promise<Record<string, unknown> | null>
): Promise<Record<string, unknown>> {
  const result: Record<string, unknown> = {};

  if (!schema.properties || typeof schema.properties !== 'object') return result;

  const properties = schema.properties as Record<string, unknown>;

  // Process each property
  for (const [propName, propSchema] of Object.entries(properties)) {
    // Skip properties that have a const value
    if (typeof propSchema === 'object' && propSchema !== null && 'const' in propSchema) {
      continue;
    }

    if (typeof propSchema === 'object' && propSchema !== null) {
      // Format the property name for display
      const formattedName = formatPropertyName(propName);
      result[formattedName] = await generateSampleData(
        propSchema as Record<string, unknown>,
        rootSchema,
        uiSchema,
        fetchExternalSchema
      );
    }
  }

  return result;
}

/**
 * Generate sample data for an array schema
 * @param schema The array schema
 * @param rootSchema The root schema for resolving references
 * @param fetchExternalSchema Optional function to fetch external schemas by ID
 * @returns Sample array data
 */
async function generateArrayData(
  schema: Record<string, unknown>,
  rootSchema: Record<string, unknown>,
  uiSchema?: UISchema,
  fetchExternalSchema?: (schemaId: string) => Promise<Record<string, unknown> | null>
): Promise<unknown[]> {
  if (!schema.items) return [];

  // Generate 1-3 items for the array
  const minItems = typeof schema.minItems === 'number' ? schema.minItems : 1;
  const count = Math.min(minItems, 3);
  const result = [];

  for (let i = 0; i < count; i++) {
    if (typeof schema.items === 'object' && schema.items !== null) {
      result.push(await generateSampleData(
        schema.items as Record<string, unknown>,
        rootSchema,
        uiSchema,
        fetchExternalSchema
      ));
    }
  }

  return result;
}

/**
 * Generate sample data for a string schema
 * @param schema The string schema
 * @returns Sample string data
 */
function generateStringData(schema: Record<string, unknown>): string {
  // Handle specific formats
  // if (schema.format && typeof schema.format === 'string') {
  //   switch (schema.format) {
  //     case 'date':
  //       return '2025-04-18';
  //     case 'date-time':
  //       return '2025-04-18T13:45:30Z';
  //     case 'email':
  //       return '<EMAIL>';
  //     case 'uri':
  //     case 'url':
  //       return 'https://example.com';
  //     case 'uuid':
  //       return '123e4567-e89b-12d3-a456-************';
  //     case 'hostname':
  //       return 'example.com';
  //     case 'ipv4':
  //       return '***********';
  //     case 'ipv6':
  //       return '2001:0db8:85a3:0000:0000:8a2e:0370:7334';
  //     default:
  //       // Fall through to default handling
  //   }
  // }

  // // Use example if provided
  // if (schema.example !== undefined) return String(schema.example);

  // // Use default if provided
  // if (schema.default !== undefined) return String(schema.default);

  // // Use enum value if available
  // if (schema.enum && Array.isArray(schema.enum) && schema.enum.length > 0) {
  //   return String(schema.enum[0]);
  // }

  // // Use title or description if available
  // if (schema.title && typeof schema.title === 'string') return `Sample ${schema.title}`;
  // if (schema.description && typeof schema.description === 'string') return `Sample ${schema.description}`;

  // // Default value
  return ' ';
}

/**
 * Generate sample data for a number schema
 * @param schema The number schema
 * @returns Sample number data
 */
function generateNumberData(schema: Record<string, unknown>): number | null {
  // Use example if provided
  // if (schema.example !== undefined) return Number(schema.example);

  // // Use default if provided
  // if (schema.default !== undefined) return Number(schema.default);

  // // Use enum value if available
  // if (schema.enum && Array.isArray(schema.enum) && schema.enum.length > 0) {
  //   return Number(schema.enum[0]);
  // }

  // // Generate a value within the specified range
  // let min = typeof schema.minimum === 'number' ? schema.minimum : 0;
  // let max = typeof schema.maximum === 'number' ? schema.maximum : 100;

  // // Adjust for exclusive bounds
  // if (schema.exclusiveMinimum && min === schema.exclusiveMinimum) min += 1;
  // if (schema.exclusiveMaximum && max === schema.exclusiveMaximum) max -= 1;

  // // Ensure min <= max
  // if (min > max) [min, max] = [max, min];

  // // Generate a random number within the range
  // const value = min + Math.random() * (max - min);

  // // Return an integer if the schema type is integer
  // return schema.type === 'integer' ? Math.floor(value) : Number(value.toFixed(2));

  return null;
}

/**
 * Generate sample data for a boolean schema
 * @param schema The boolean schema
 * @returns Sample boolean data
 */
function generateBooleanData(schema: Record<string, unknown>): boolean | null {
  // // Use example if provided
  // if (schema.example !== undefined) return Boolean(schema.example);

  // Use default if provided
  // if (schema.default !== undefined) return Boolean(schema.default);

  return null;
}

/**
 * Generate a header markup based on the provided options
 * @param text The header text
 * @param nestingLevel Current nesting level
 * @param options UI schema options
 * @returns Markdown header with appropriate metadata
 */
function generateHeaderMarkup(
  text: string,
  nestingLevel: number,
  options?: Record<string, unknown>
): string {
  // Default header level based on nesting
  let headerLevel = nestingLevel === 0 ? 2 : 3;

  // Override with UI schema option if provided
  if (options?.headerLevel && typeof options.headerLevel === 'number') {
    headerLevel = Math.min(Math.max(options.headerLevel, 1), 6);
  }

  // Default collapsible state
  let collapsible = nestingLevel === 0;

  // Override with UI schema option if provided
  if (options?.collapsible !== undefined) {
    collapsible = Boolean(options.collapsible);
  }
  // let collapsible = true;

  // Add metadata as HTML comments
  let metadata = `<!-- collapsible:${collapsible} -->`;

  // Add line threshold metadata if provided
  if (options?.lineThreshold && typeof options.lineThreshold === 'number') {
    metadata += `\n<!-- lineThreshold:${options.lineThreshold} -->`;
  }

  // Generate the header with appropriate level
  const headerMarker = '#'.repeat(headerLevel);

  return `${metadata}\n${headerMarker} ${text}\n\n`;
}

/**
 * Convert data to markdown representation
 * @param data The data to convert to markdown
 * @param uiSchema Optional UI schema for customizing the display
 * @param nestingLevel Current nesting level (0 for top level)
 * @param schema Optional JSON schema describing the data structure
 * @returns Markdown representation of the data
 */
export async function dataToMarkdown(
  data: unknown,
  uiSchema?: UISchema,
  nestingLevel: number = 0,
  schema?: Record<string, unknown>
): Promise<string> {
  if (data === null || data === undefined) {
    return '';
  }

  let markdown = '';

  if (Array.isArray(data)) {
    // Handle array data
    for (let i = 0; i < data.length; i++) {

      const maxChars = 50;
      const rawTitle = formatResourceForDisplay(data[i]);
      const title = rawTitle.substring(0, maxChars) + (rawTitle.length > maxChars ? '...' : '');
      // markdown += generateHeaderMarkup(title, nestingLevel);

      const key = uuidv7();

      markdown += `### ${title}\n\n`;
      markdown += '<!-- collapsible:true -->\n\n';
      markdown += '<!-- start of object: ' + key + ' -->\n\n';
      markdown += await dataToMarkdown(data[i], undefined, nestingLevel + 1);
      markdown += '<!-- end of object: ' + key + ' -->\n\n';
    }
    return markdown;
  }

  if (typeof data === 'object' && data !== null) {
    // Handle object data
    const dataObj = data as Record<string, unknown>;
    let properties = Object.keys(dataObj);

    // Get the schema for this object if available
    const objectSchema = uiSchema && uiSchema['ui:schema'] ?
      uiSchema['ui:schema'] as Record<string, unknown> :
      undefined;

    // Apply UI schema ordering if available, passing the data object and schema to sort by type
    if (uiSchema && uiSchema['ui:order']) {
      properties = applyUIOrder(properties, uiSchema['ui:order'], dataObj, objectSchema);
    } else {
      // If no UI schema ordering, still sort by type (simple values first, then objects and arrays)
      properties = applyUIOrder(properties, undefined, dataObj, objectSchema);
    }

    // Process each property
    for (const key of properties) {
      const value = dataObj[key];

      // Skip hidden properties
      if (uiSchema && getWidgetType(key, uiSchema) === 'hidden') {
        continue;
      }

      // Skip properties that don't meet conditional display criteria
      if (uiSchema && !shouldDisplayProperty(key, uiSchema, dataObj)) {
        continue;
      }

      // Get widget type and options
      const widgetType = uiSchema ? getWidgetType(key, uiSchema) : undefined;
      const widgetOptions = uiSchema ? getWidgetOptions(key, uiSchema) : undefined;
      const widgetPropertyOrder = uiSchema ? getWidgetPropertyOrder(key, uiSchema) : undefined;

      // Handle different widget types
      if (widgetType === 'resourceDisplay' && widgetOptions) {
        // Resource display widgets always get their own section
        markdown += await renderResourceDisplay(key, value, widgetOptions, nestingLevel, uiSchema);
      } else if (widgetType === 'table' && widgetOptions) {
        if (Array.isArray(value)) {
          // Tables always get their own section
          markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);
          markdown += renderTable(key, value, widgetOptions);
        } else {
          const valueObj = value as Record<string, unknown>;
          let subProperties = Object.keys(valueObj)

          if (widgetPropertyOrder) {
            subProperties = applyUIOrder(subProperties, widgetPropertyOrder, undefined, objectSchema);
          }

          markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);

          // Check if this is a simple key-value object
          const isSimpleObject = subProperties.every(prop => {
            const val = valueObj[prop];
            return typeof val !== 'object' || val === null;
          });

          if (isSimpleObject) {
            // For simple objects, create a single row table
            markdown += renderTable(key, [valueObj], widgetOptions);
          } else {
            // For complex objects, create separate tables for each property
            for (const subKey of subProperties) {
              const subObj = valueObj[subKey]

              let arr = []
              if (Array.isArray(subObj)) {
                arr = [...subObj]
              } else {
                arr.push(subObj)
              }
              markdown += generateHeaderMarkup(formatPropertyName(subKey), nestingLevel + 1, widgetOptions);
              markdown += renderTable(key, arr, widgetOptions);
            }
          }
        }
      } else if (Array.isArray(value) && (!widgetType || (widgetType !== 'resourceDisplay' && widgetType !== 'table'))) {
        // Determine if this is a complex array (array of objects) based on schema if available
        let isComplexArray = false;

        // First check the schema passed directly to the function
        if (schema && schema.properties && typeof schema.properties === 'object') {
          const propSchema = (schema.properties as Record<string, unknown>)[key];

          if (propSchema && typeof propSchema === 'object') {
            const propSchemaObj = propSchema as Record<string, unknown>;

            if (propSchemaObj.type === 'array' && propSchemaObj.items) {
              const items = propSchemaObj.items as Record<string, unknown>;

              // Check if items is a reference or an object type
              if (items.$ref || items.type === 'object') {
                isComplexArray = true;
              }
            }
          }
        }

        // If not found in the main schema, check the UI schema
        if (!isComplexArray && objectSchema && objectSchema.properties && typeof objectSchema.properties === 'object') {
          const propSchema = (objectSchema.properties as Record<string, unknown>)[key];

          if (propSchema && typeof propSchema === 'object') {
            const propSchemaObj = propSchema as Record<string, unknown>;

            if (propSchemaObj.type === 'array' && propSchemaObj.items) {
              const items = propSchemaObj.items as Record<string, unknown>;

              // Check if items is a reference or an object type
              if (items.$ref || items.type === 'object') {
                isComplexArray = true;
              }
            }
          }
        }

        // If we couldn't determine from schema, check the actual data
        if (!isComplexArray && value.length > 0) {
          isComplexArray = typeof value[0] === 'object' && value[0] !== null;
        }

        if (isComplexArray) {
          // For arrays with object-based items, use a header
          markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);

          if (value.length > 0) {
            markdown += renderTable(key, value, {});
          }
        } else {
          // For arrays with primitive items, just bold the name (no header)
          markdown += `**${formatPropertyName(key)}:**\n\n`;

          if (value.length > 0) {
            // Simple array - create a list
            value.forEach((item: unknown) => {
              if (typeof item === 'object' && item !== null) {
                markdown += '- ' + JSON.stringify(item) + '\n';
              } else {
                markdown += '- ' + String(item) + '\n';
              }
            });
            markdown += '\n';
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        // Nested object
        const keyId = uuidv7();
        markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);
        markdown += '<!-- start of object: ' + keyId + ' -->\n\n';
        // Increase nesting level for recursive call
        markdown += await dataToMarkdown(
          value,
          uiSchema && uiSchema[key] as UISchema,
          nestingLevel + 1
        );
        markdown += '<!-- end of object: ' + keyId + ' -->\n\n';
      } else {
        let v = value
        if (value === 'true' || value === true) {
          v = 'Yes'
        } else if (value === 'false' || value === false) {
          v = 'No'
        }

        // Simple key-value pair - only show if value is not null
        if (value !== null) {
          markdown += `**${formatPropertyName(key)}:** ${v}\n\n`;
        } else {
          markdown += `**${formatPropertyName(key)}:**\n\n`;
        }
      }
    }

    return markdown;
  }

  // Handle primitive data
  return `${data}\n\n`;
}

/**
 * Render a resource display widget
 * @param key Property key
 * @param value Property value (resource ID or array of resource objects)
 * @param options Widget options
 * @param nestingLevel Current nesting level (0 for top level)
 * @returns Markdown representation of the resource(s)
 */
async function renderResourceDisplay(
  key: string,
  value: unknown,
  options: Record<string, unknown>,
  nestingLevel: number = 0,
  uiSchema?: UISchema
): Promise<string> {
  if (!value) return `**${formatPropertyName(key)}:** Not specified\n\n`;

  const subUISchema = uiSchema && uiSchema[key] as UISchema;
  // Generate header with appropriate metadata
  let markdown = generateHeaderMarkup(formatPropertyName(key), nestingLevel, options);

  try {
    // Handle array of resources
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const resourceItem = value[i];


        // Extract id and resourceType from the resource item
        if (typeof resourceItem === 'object' && resourceItem !== null) {
          const resourceObj = resourceItem as Record<string, unknown>;
          const resourceId = resourceObj.id as string;
          const resourceType = resourceObj.resourceType as string;

          if (!resourceId || !resourceType) {
            markdown += `**Error:** Resource missing id or resourceType\n\n`;
            continue;
          }

          // Fetch and display the resource
          const resource = await fetchResource(resourceType, resourceId);
          // Add resource number heading
          markdown += `### ${formatResourceForDisplay(resource)}\n\n`;

          if (!resource) {
            markdown += `**Resource not found:** ${resourceType} with ID ${resourceId}\n\n`;
            continue;
          }

          // Extract the fields to display or hide
          const displayFields = options.displayFields as string[] | undefined;
          const hideFields = options.hideFields as string[] | undefined;
          const displayData = extractResourceFields(resource, displayFields, hideFields);

          // Add resource title
          // markdown += `**${resourceType}:** ${formatResourceForDisplay(resource)}\n\n`;

          // Add resource fields
          if (displayData) {
            // Indent the content for better visual hierarchy
            const displayMarkdown = await dataToMarkdown(displayData, subUISchema, nestingLevel + 1);
            markdown += displayMarkdown;
          }
        }
      }
    } else {
      // Extract id and resourceType from the resource item
      if (typeof value === 'object' && value !== null) {
        const resourceObj = value as Record<string, unknown>;
        const resourceId = resourceObj.id as string;
        const resourceType = resourceObj.resourceType as string;

        if (!resourceId || !resourceType) {
          markdown += `**Error:** Resource missing id or resourceType\n\n`;
          return markdown;
        }

        // Fetch and display the resource
        const resource = await fetchResource(resourceType, resourceId);

        if (!resource) {
          markdown += `**Resource not found:** ${resourceType} with ID ${resourceId}\n\n`;
          return markdown;
        }

        if (options.titleField) {
          const title = resource[options.titleField as string] as string;
          markdown = generateHeaderMarkup(formatPropertyName(title), nestingLevel, options);

        }
        // Extract the fields to display or hide
        const displayFields = options.displayFields as string[] | undefined;
        const hideFields = options.hideFields as string[] | undefined;
        const displayData = extractResourceFields(resource, displayFields, hideFields);

        // Add resource title
        // markdown += `**${resourceType}:** ${formatResourceForDisplay(resource)}\n\n`;

        // Add resource fields
        if (displayData) {
          markdown += await dataToMarkdown(displayData, subUISchema, nestingLevel + 1);
        }
      }
    }
  } catch (error) {
    markdown += `**Error loading resource:** ${error}\n\n`;
  }

  return markdown;
}

/**
 * Render a table widget
 * @param key Property key
 * @param value Array of objects to display in a table
 * @param options Widget options
 * @returns Markdown table representation
 */
function renderTable(
  key: string,
  value: unknown[],
  options: Record<string, unknown>
): string {
  if (!Array.isArray(value) || value.length === 0) {
    return `**${formatPropertyName(key)}:** Empty list\n\n`;
  }

  // This header is already generated in the calling function, so we don't need to generate it again
  let markdown = '';

  // Get columns to display
  const columns = options.columns as string[] || Object.keys(value[0] as Record<string, unknown>);

  // Table header
  markdown += '| ' + columns.map((col: string) => formatPropertyName(col)).join(' | ') + ' |\n';
  markdown += '| ' + columns.map(() => '---').join(' | ') + ' |\n';

  // Table rows
  value.forEach((item: unknown) => {
    if (typeof item === 'object' && item !== null) {
      const itemObj = item as Record<string, unknown>;
      markdown += '| ' + columns.map((col: string) => {
        const cellValue = itemObj[col];
        if (cellValue === null || cellValue === undefined) return '';

        if (Array.isArray(cellValue)) {
          // Check if it's a simple array (all elements are primitives)
          const isSimpleArray = cellValue.every(element =>
            typeof element !== 'object' || element === null
          );

          if (isSimpleArray) {
            return cellValue.map(val => String(val || '')).join(', ');
          }
        }

        if (typeof cellValue === 'object') {
          const cellValueObj = cellValue as Record<string, unknown>;
          if (cellValueObj['id'] && cellValueObj['resourceType']) {
            // TODO:
            // Create a link to the resource (e.g display it in a modal or something)
            return 'Object'; //`[View ${cellValueObj['resourceType']}](/resources/${cellValueObj['id']})`;
          } else {
            return 'Object';
          }
        }

        return String(cellValue).replace(/\n/g, ' ');
      }).join(' | ') + ' |\n';
    }
  });

  markdown += '\n';

  return markdown;
}
