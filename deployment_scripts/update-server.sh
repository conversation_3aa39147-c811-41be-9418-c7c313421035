#!/usr/bin/env bash

ECS_SERVICE="arn:aws:ecs:us-east-1:400728567703:service/AllaymdProd-BackEndCluster6B6DC4A8-sKCzkFvk4LMv/AllaymdProd-BackEndFargateServiceD3B260C0-6SBiX488NU1B"
ECS_CLUSTER="arn:aws:ecs:us-east-1:400728567703:cluster/AllaymdProd-BackEndCluster6B6DC4A8-sKCzkFvk4LMv"

if [[ -z "${ECS_CLUSTER}" ]]; then
  echo "ECS_CLUSTER is missing"
  exit 1
fi

if [[ -z "${ECS_SERVICE}" ]]; then
  echo "ECS_SERVICE is missing"
  exit 1
fi

# Fail on error
set -e

# Echo commands
set -x

. ./scripts/build-server.sh
# Update the medplum fargate service
aws ecs update-service \
  --cluster "$ECS_CLUSTER" \
  --service "$ECS_SERVICE" \
  --force-new-deployment
