version: '3.8'

services:
  postgres:
    image: postgres:16.6
    container_name: hospice-os-postgres
    environment:
      POSTGRES_DB: hospice_os
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      # Reduce WAL (Write-Ahead Logging) size to save disk space
      POSTGRES_INITDB_ARGS: "--wal-segsize=1"
    command: >
      postgres
        -c shared_buffers=128MB
        -c max_connections=100
        -c effective_cache_size=256MB
        -c maintenance_work_mem=64MB
        -c max_wal_size=512MB
        -c min_wal_size=80MB
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # You can uncomment this service if you want to include a database admin tool
  # pgadmin:
  #   image: dpage/pgadmin4
  #   container_name: hospice-os-pgadmin
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin
  #   ports:
  #     - "5050:80"
  #   depends_on:
  #     - postgres

volumes:
  postgres-data:
    name: hospice-os-postgres-data
