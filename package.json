{"name": "hospice-os", "version": "1.0.0", "description": "", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build:ui-components": "cd packages/ui-components && npm run build", "dev:ui-components": "cd packages/ui-components && npm run dev", "sync-list": "exec syncpack -- list-mismatches", "sync-fix": "exec syncpack -- fix-mismatches", "test": "vitest run", "test:vitest": "vitest run", "dev:server": "cd apps/server && npm run app-dev", "dev:web": "cd apps/web && npm run dev", "dev:admin": "cd apps/admin && npm run start", "dev:worker": "cd apps/server && npm run worker-dev", "dev": "concurrently \"npm run dev:server\" \"npm run dev:web\" \"npm run dev:admin\" \"npm run dev:restate\"", "generate-types": "ts-node scripts/generate-types.ts", "format": "prettier --write ."}, "keywords": [], "author": "", "license": "ISC", "packageManager": "npm@10.2.4", "devDependencies": {"@eslint/js": "^9.18.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/pg": "^8.11.0", "@vitest/ui": "^3.1.2", "concurrently": "^9.1.2", "eslint": "^9.18.0", "globals": "^15.14.0", "jsdom": "^26.1.0", "prettier": "^3.2.5", "syncpack": "^13.0.0", "turborepo": "^0.0.1", "typescript": "^5.7.3", "typescript-eslint": "^8.21.0", "vitest": "^3.1.4"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.798.0", "@mantine/notifications": "^7.17.5", "dotenv": "^16.5.0", "json-pointer": "^0.6.2", "pg": "^8.11.3", "react-media-recorder": "^1.7.1", "react-pdf": "^9.2.1", "remark-gfm": "^4.0.1", "survey-core": "^2.0.2", "survey-react-ui": "^2.0.2", "ts-node": "^10.9.2"}, "overrides": {"aws-cdk": "2.177.0", "aws-cdk-lib": "2.177.0", "constructs": "10.0.0", "page-count": {"pdf-parse": "npm:pdf-parse-debugging-disabled@1.1.1"}}}