import { z } from 'zod';
export interface Action {
    uuid: string;
    title: string;
    content?: string;
    priority?: 'high' | 'medium' | 'low';
    completed?: boolean;
    actionType?: 'more_data' | 'approval' | 'reminder';
    dueDate?: string;
    requestRef?: {
        id: string;
        resourceType: 'Request';
    };
}
export declare const ActionSchema: z.ZodObject<{
    uuid: z.ZodString;
    title: z.ZodString;
    content: z.ZodOptional<z.ZodString>;
    priority: z.ZodOptional<z.ZodEnum<["high", "medium", "low"]>>;
    completed: z.ZodOptional<z.ZodBoolean>;
    actionType: z.ZodOptional<z.ZodEnum<["more_data", "approval", "reminder"]>>;
    dueDate: z.ZodOptional<z.ZodString>;
    requestRef: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        resourceType: z.<PERSON><PERSON><PERSON>iteral<"Request">;
    }, "strip", z.<PERSON>odTypeAny, {
        id: string;
        resourceType: "Request";
    }, {
        id: string;
        resourceType: "Request";
    }>>;
}, "strip", z.ZodTypeAny, {
    uuid: string;
    title: string;
    content?: string | undefined;
    priority?: "high" | "medium" | "low" | undefined;
    completed?: boolean | undefined;
    actionType?: "more_data" | "approval" | "reminder" | undefined;
    dueDate?: string | undefined;
    requestRef?: {
        id: string;
        resourceType: "Request";
    } | undefined;
}, {
    uuid: string;
    title: string;
    content?: string | undefined;
    priority?: "high" | "medium" | "low" | undefined;
    completed?: boolean | undefined;
    actionType?: "more_data" | "approval" | "reminder" | undefined;
    dueDate?: string | undefined;
    requestRef?: {
        id: string;
        resourceType: "Request";
    } | undefined;
}>;
//# sourceMappingURL=action.d.ts.map