import { z } from 'zod';
export const ActionSchema = z.object({
    uuid: z.string(),
    title: z.string(),
    content: z.string().optional(),
    priority: z.enum(['high', 'medium', 'low']).optional(),
    completed: z.boolean().optional(),
    actionType: z.enum(['more_data', 'approval', 'reminder']).optional(),
    dueDate: z.string().datetime().optional(),
    requestRef: z.object({
        id: z.string().uuid(),
        resourceType: z.literal('Request'),
    }).optional(),
});
//# sourceMappingURL=action.js.map