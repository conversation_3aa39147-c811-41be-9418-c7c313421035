import { z } from 'zod';
export declare const AgencySchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    stytchOrganizationId: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodOptional<z.ZodString>;
    updatedAt: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    updatedAt?: string | undefined;
    createdAt?: string | undefined;
    stytchOrganizationId?: string | undefined;
}, {
    id: string;
    name: string;
    updatedAt?: string | undefined;
    createdAt?: string | undefined;
    stytchOrganizationId?: string | undefined;
}>;
export interface Agency {
    id: string;
    name: string;
    stytchOrganizationId?: string;
    createdAt?: string;
    updatedAt?: string;
}
//# sourceMappingURL=agency.d.ts.map