import { z } from 'zod';
export interface Branch {
    id?: string;
    name?: string;
    idgInterval?: string;
    lastIdgDate?: string;
    nextIdgDate?: string;
    resourceType?: 'Branch';
}
export declare const BranchSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    name: z.ZodOptional<z.ZodString>;
    idgInterval: z.ZodOptional<z.ZodString>;
    lastIdgDate: z.ZodOptional<z.ZodString>;
    nextIdgDate: z.ZodOptional<z.ZodString>;
    resourceType: z.ZodOptional<z.ZodLiteral<"Branch">>;
}, "strip", z.ZodTypeAny, {
    id?: string | undefined;
    resourceType?: "Branch" | undefined;
    name?: string | undefined;
    idgInterval?: string | undefined;
    lastIdgDate?: string | undefined;
    nextIdgDate?: string | undefined;
}, {
    id?: string | undefined;
    resourceType?: "Branch" | undefined;
    name?: string | undefined;
    idgInterval?: string | undefined;
    lastIdgDate?: string | undefined;
    nextIdgDate?: string | undefined;
}>;
//# sourceMappingURL=branch.d.ts.map