import { z } from "zod";
import { BaseResource } from "./resource";
export interface IDGMeeting extends BaseResource {
    id: string;
    status: "In Progress" | "Completed";
    participants?: {
        id: string;
        resourceType: "User";
    }[];
    patients?: {
        id: string;
        resourceType: "Patient";
        isCompleted: boolean;
        idgRequest?: {
            id: string;
            resourceType: "Request";
        };
    }[];
    startTime?: string;
    endTime?: string;
}
export declare const IDGMeetingSchema: z.ZodObject<{
    resourceType: z.ZodString;
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
    updatedAt: z.ZodString;
    createdAt: z.ZodString;
} & {
    id: z.ZodString;
    status: z.ZodEnum<["In Progress", "Completed"]>;
    participants: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        resourceType: z.<PERSON>od<PERSON>iteral<"User">;
    }, "strip", z.<PERSON>TypeAny, {
        id: string;
        resourceType: "User";
    }, {
        id: string;
        resourceType: "User";
    }>, "many">>;
    patients: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        resourceType: z.ZodLiteral<"Patient">;
        isCompleted: z.ZodBoolean;
        idgRequest: z.ZodObject<{
            id: z.ZodString;
            resourceType: z.ZodLiteral<"Request">;
        }, "strip", z.ZodTypeAny, {
            id: string;
            resourceType: "Request";
        }, {
            id: string;
            resourceType: "Request";
        }>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        resourceType: "Patient";
        isCompleted: boolean;
        idgRequest: {
            id: string;
            resourceType: "Request";
        };
    }, {
        id: string;
        resourceType: "Patient";
        isCompleted: boolean;
        idgRequest: {
            id: string;
            resourceType: "Request";
        };
    }>, "many">>;
    startTime: z.ZodOptional<z.ZodString>;
    endTime: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: string;
    schemaId: string;
    updatedAt: string;
    createdAt: string;
    status: "In Progress" | "Completed";
    agencyId?: string | undefined;
    participants?: {
        id: string;
        resourceType: "User";
    }[] | undefined;
    patients?: {
        id: string;
        resourceType: "Patient";
        isCompleted: boolean;
        idgRequest: {
            id: string;
            resourceType: "Request";
        };
    }[] | undefined;
    startTime?: string | undefined;
    endTime?: string | undefined;
}, {
    id: string;
    resourceType: string;
    schemaId: string;
    updatedAt: string;
    createdAt: string;
    status: "In Progress" | "Completed";
    agencyId?: string | undefined;
    participants?: {
        id: string;
        resourceType: "User";
    }[] | undefined;
    patients?: {
        id: string;
        resourceType: "Patient";
        isCompleted: boolean;
        idgRequest: {
            id: string;
            resourceType: "Request";
        };
    }[] | undefined;
    startTime?: string | undefined;
    endTime?: string | undefined;
}>;
//# sourceMappingURL=idgmeeting.d.ts.map