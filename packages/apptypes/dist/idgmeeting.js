import { z } from "zod";
import { BaseResourceSchema } from "./resource";
export const IDGMeetingSchema = BaseResourceSchema.extend({
    id: z.string(),
    status: z.enum(["In Progress", "Completed"]),
    participants: z
        .array(z.object({
        id: z.string(),
        resourceType: z.literal("User"),
    }))
        .optional(),
    patients: z
        .array(z.object({
        id: z.string(),
        resourceType: z.literal("Patient"),
        isCompleted: z.boolean(),
        idgRequest: z.object({
            id: z.string(),
            resourceType: z.literal("Request"),
        }),
    }))
        .optional(),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
});
//# sourceMappingURL=idgmeeting.js.map