import { z } from 'zod';
export interface Medication {
    name: string;
    route?: string;
    dosage: string;
    frequency?: string;
    indication?: string;
    resourceType?: 'Medication';
}
export declare const MedicationSchema: z.ZodObject<{
    name: z.ZodString;
    route: z.ZodOptional<z.ZodString>;
    dosage: z.ZodString;
    frequency: z.ZodOptional<z.ZodString>;
    indication: z.ZodOptional<z.ZodString>;
    resourceType: z.ZodOptional<z.ZodLiteral<"Medication">>;
}, "strip", z.ZodTypeAny, {
    name: string;
    dosage: string;
    resourceType?: "Medication" | undefined;
    route?: string | undefined;
    frequency?: string | undefined;
    indication?: string | undefined;
}, {
    name: string;
    dosage: string;
    resourceType?: "Medication" | undefined;
    route?: string | undefined;
    frequency?: string | undefined;
    indication?: string | undefined;
}>;
//# sourceMappingURL=medication.d.ts.map