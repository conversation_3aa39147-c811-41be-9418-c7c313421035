import { z } from "zod";
import { BaseResource } from "./resource";
export interface Note extends BaseResource {
    id: string;
    title: string;
    summary?: string;
    authorId: string;
    noteType: string;
    createdAt: string;
    patientId: string;
    updatedAt: string;
    resourceType: "Note";
}
export declare const NoteSchema: z.ZodObject<{
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
} & {
    id: z.ZodString;
    title: z.ZodString;
    summary: z.ZodOptional<z.ZodString>;
    authorId: z.ZodString;
    noteType: z.ZodString;
    createdAt: z.ZodString;
    patientId: z.ZodString;
    updatedAt: z.ZodOptional<z.ZodString>;
    resourceType: z.Zod<PERSON>iteral<"Note">;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: "Note";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    noteType: string;
    patientId: string;
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
}, {
    id: string;
    resourceType: "Note";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    noteType: string;
    patientId: string;
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
}>;
//# sourceMappingURL=note.d.ts.map