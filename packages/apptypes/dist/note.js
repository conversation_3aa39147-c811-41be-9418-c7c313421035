import { z } from "zod";
import { BaseResourceSchema } from "./resource";
export const NoteSchema = BaseResourceSchema.extend({
    id: z.string().uuid(),
    title: z.string(),
    summary: z.string().optional(),
    authorId: z.string().uuid(),
    noteType: z.string(),
    createdAt: z.string().datetime(),
    patientId: z.string().uuid(),
    updatedAt: z.string().datetime().optional(),
    resourceType: z.literal("Note"),
});
//# sourceMappingURL=note.js.map