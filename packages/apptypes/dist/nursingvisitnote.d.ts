import { z } from 'zod';
import { Note } from './note';
export interface NursingVisitNote extends Note {
    woundCare?: string;
    vitalSigns: {
        heartRate: number;
        temperature: number;
        bloodPressure: string;
        respiratoryRate: number;
    };
    skinAssessment?: string;
    educationProvided?: string;
    medicationsAdministered?: {
        dose: string;
        medicationName: string;
        timeAdministered: string;
    }[];
}
export declare const NursingVisitNoteSchema: z.ZodObject<{
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
} & {
    id: z.ZodString;
    title: z.ZodString;
    summary: z.ZodOptional<z.ZodString>;
    authorId: z.ZodString;
    noteType: z.ZodString;
    createdAt: z.ZodString;
    patientId: z.ZodString;
    updatedAt: z.ZodOptional<z.ZodString>;
    resourceType: z.ZodLiteral<"Note">;
} & {
    woundCare: z.ZodOptional<z.ZodString>;
    vitalSigns: z.<PERSON>bject<{
        heartRate: z.ZodNumber;
        temperature: z.Zod<PERSON>;
        bloodPressure: z.ZodString;
        respiratoryRate: z.Zod<PERSON>;
    }, "strip", z.ZodTypeAny, {
        heartRate: number;
        temperature: number;
        bloodPressure: string;
        respiratoryRate: number;
    }, {
        heartRate: number;
        temperature: number;
        bloodPressure: string;
        respiratoryRate: number;
    }>;
    skinAssessment: z.ZodOptional<z.ZodString>;
    educationProvided: z.ZodOptional<z.ZodString>;
    medicationsAdministered: z.ZodOptional<z.ZodArray<z.ZodObject<{
        dose: z.ZodString;
        medicationName: z.ZodString;
        timeAdministered: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        dose: string;
        medicationName: string;
        timeAdministered: string;
    }, {
        dose: string;
        medicationName: string;
        timeAdministered: string;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: "Note";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    noteType: string;
    patientId: string;
    vitalSigns: {
        heartRate: number;
        temperature: number;
        bloodPressure: string;
        respiratoryRate: number;
    };
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
    woundCare?: string | undefined;
    skinAssessment?: string | undefined;
    educationProvided?: string | undefined;
    medicationsAdministered?: {
        dose: string;
        medicationName: string;
        timeAdministered: string;
    }[] | undefined;
}, {
    id: string;
    resourceType: "Note";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    noteType: string;
    patientId: string;
    vitalSigns: {
        heartRate: number;
        temperature: number;
        bloodPressure: string;
        respiratoryRate: number;
    };
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
    woundCare?: string | undefined;
    skinAssessment?: string | undefined;
    educationProvided?: string | undefined;
    medicationsAdministered?: {
        dose: string;
        medicationName: string;
        timeAdministered: string;
    }[] | undefined;
}>;
//# sourceMappingURL=nursingvisitnote.d.ts.map