import { z } from 'zod';
import { NoteSchema } from './note';
export const NursingVisitNoteSchema = NoteSchema.extend({
    woundCare: z.string().optional(),
    vitalSigns: z.object({
        heartRate: z.number().int(),
        temperature: z.number(),
        bloodPressure: z.string(),
        respiratoryRate: z.number().int(),
    }),
    skinAssessment: z.string().optional(),
    educationProvided: z.string().optional(),
    medicationsAdministered: z.array(z.object({
        dose: z.string(),
        medicationName: z.string(),
        timeAdministered: z.string().datetime(),
    })).optional(),
});
//# sourceMappingURL=nursingvisitnote.js.map