import { z } from "zod";
import { BaseResource } from "./resource";
export interface Order extends BaseResource {
    id: string;
    title: string;
    summary?: string;
    authorId: string;
    createdAt: string;
    orderType: string;
    patientId: string;
    updatedAt: string;
    resourceType: "Order";
}
export declare const OrderSchema: z.ZodObject<{
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
} & {
    id: z.ZodString;
    title: z.ZodString;
    summary: z.ZodOptional<z.ZodString>;
    authorId: z.ZodString;
    createdAt: z.ZodString;
    orderType: z.ZodString;
    patientId: z.ZodString;
    updatedAt: z.ZodOptional<z.ZodString>;
    resourceType: z.ZodLiteral<"Order">;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: "Order";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    patientId: string;
    orderType: string;
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
}, {
    id: string;
    resourceType: "Order";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    patientId: string;
    orderType: string;
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
}>;
//# sourceMappingURL=order.d.ts.map