import { z } from "zod";
import { BaseResourceSchema } from "./resource";
export const OrderSchema = BaseResourceSchema.extend({
    id: z.string().uuid(),
    title: z.string(),
    summary: z.string().optional(),
    authorId: z.string().uuid(),
    createdAt: z.string().datetime(),
    orderType: z.string(),
    patientId: z.string().uuid(),
    updatedAt: z.string().datetime().optional(),
    resourceType: z.literal("Order"),
});
//# sourceMappingURL=order.js.map