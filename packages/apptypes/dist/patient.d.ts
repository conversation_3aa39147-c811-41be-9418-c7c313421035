import { z } from 'zod';
import { BaseResource } from './resource';
import { Medication } from './medication';
import { Action } from './action';
import { TimelineItem } from './timelineitem';
export interface Patient extends BaseResource {
    id: string;
    email?: string;
    branch?: {
        id: string;
        resourceType: 'Branch';
    };
    gender?: string;
    orders?: any[];
    status?: 'Active' | 'Discharged' | 'Deceased' | 'Pending';
    address?: string;
    nextIDG?: string;
    lastName: string;
    allergies?: string[];
    firstName: string;
    insurance?: any[];
    planOfCare?: any;
    visitNotes?: any[];
    caseManager?: {
        id: string;
        resourceType: "User";
    };
    dateOfBirth: string;
    medications?: Medication[];
    phoneNumber?: string;
    resourceType: 'Patient';
    admissionDate?: string;
    agencyActions?: Action[];
    benefitPeriods?: {
        period: number;
        startDate: string;
        endDate: string;
        status: "Pending" | "Active" | "Completed";
        periodType: "First 90 days" | "Second 90 days" | "Subsequent 60 days";
    }[];
    timeLineItems?: TimelineItem[];
    terminalDiagnosis?: string;
    complianceTimeline?: {
        timeLineItems: ComplianceTimelineItem[];
    };
    comorbidities?: {
        code: string;
        description: string;
    }[];
    medicalRecordNumber?: string;
    emergencyContactName?: string;
    emergencyContactPhone?: string;
}
export interface ComplianceTimelineItem {
    id: string;
    status: "completed" | "in_progress" | "pending" | "error" | "cancelled" | "not_started" | "overdue";
    createdAt: string;
    startedAt?: string;
    completedAt?: string;
    completedByUser?: string;
    title: string;
    type: string;
    requests?: {
        resourceType: 'Request';
        id: string;
    }[];
}
export declare const PatientSchema: z.ZodObject<{
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
    updatedAt: z.ZodString;
    createdAt: z.ZodString;
} & {
    id: z.ZodString;
    email: z.ZodOptional<z.ZodString>;
    branch: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        resourceType: z.ZodLiteral<"Branch">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        resourceType: "Branch";
    }, {
        id: string;
        resourceType: "Branch";
    }>>;
    gender: z.ZodOptional<z.ZodString>;
    orders: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
    status: z.ZodOptional<z.ZodEnum<["Active", "Discharged", "Deceased"]>>;
    address: z.ZodOptional<z.ZodString>;
    nextIDG: z.ZodOptional<z.ZodString>;
    lastName: z.ZodString;
    allergies: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    firstName: z.ZodString;
    insurance: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
    planOfCare: z.ZodOptional<z.ZodAny>;
    visitNotes: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
    dateOfBirth: z.ZodString;
    medications: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        route: z.ZodOptional<z.ZodString>;
        dosage: z.ZodString;
        frequency: z.ZodOptional<z.ZodString>;
        indication: z.ZodOptional<z.ZodString>;
        resourceType: z.ZodOptional<z.ZodLiteral<"Medication">>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        dosage: string;
        resourceType?: "Medication" | undefined;
        route?: string | undefined;
        frequency?: string | undefined;
        indication?: string | undefined;
    }, {
        name: string;
        dosage: string;
        resourceType?: "Medication" | undefined;
        route?: string | undefined;
        frequency?: string | undefined;
        indication?: string | undefined;
    }>, "many">>;
    phoneNumber: z.ZodOptional<z.ZodString>;
    resourceType: z.ZodLiteral<"Patient">;
    admissionDate: z.ZodOptional<z.ZodString>;
    agencyActions: z.ZodOptional<z.ZodArray<z.ZodObject<{
        uuid: z.ZodString;
        title: z.ZodString;
        content: z.ZodOptional<z.ZodString>;
        priority: z.ZodOptional<z.ZodEnum<["high", "medium", "low"]>>;
        completed: z.ZodOptional<z.ZodBoolean>;
        actionType: z.ZodOptional<z.ZodEnum<["more_data", "approval", "reminder"]>>;
        dueDate: z.ZodOptional<z.ZodString>;
        requestRef: z.ZodOptional<z.ZodObject<{
            id: z.ZodString;
            resourceType: z.ZodLiteral<"Request">;
        }, "strip", z.ZodTypeAny, {
            id: string;
            resourceType: "Request";
        }, {
            id: string;
            resourceType: "Request";
        }>>;
    }, "strip", z.ZodTypeAny, {
        uuid: string;
        title: string;
        content?: string | undefined;
        priority?: "high" | "medium" | "low" | undefined;
        completed?: boolean | undefined;
        actionType?: "more_data" | "approval" | "reminder" | undefined;
        dueDate?: string | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
    }, {
        uuid: string;
        title: string;
        content?: string | undefined;
        priority?: "high" | "medium" | "low" | undefined;
        completed?: boolean | undefined;
        actionType?: "more_data" | "approval" | "reminder" | undefined;
        dueDate?: string | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
    }>, "many">>;
    benefitPeriod: z.ZodOptional<z.ZodString>;
    timeLineItems: z.ZodOptional<z.ZodArray<z.ZodObject<{
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        uuid: z.ZodString;
        title: z.ZodString;
        madeBy: z.ZodObject<{
            date: z.ZodString;
            name: z.ZodString;
            user: z.ZodOptional<z.ZodObject<{
                id: z.ZodString;
                resourceType: z.ZodLiteral<"User">;
            }, "strip", z.ZodTypeAny, {
                id: string;
                resourceType: "User";
            }, {
                id: string;
                resourceType: "User";
            }>>;
        }, "strip", z.ZodTypeAny, {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        }, {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        }>;
        status: z.ZodOptional<z.ZodEnum<["submitted", "waiting_on_more_data", "processing", "waiting_on_approvals"]>>;
        summary: z.ZodString;
        approvedBy: z.ZodOptional<z.ZodObject<{
            date: z.ZodString;
            name: z.ZodString;
            user: z.ZodOptional<z.ZodObject<{
                id: z.ZodString;
                resourceType: z.ZodLiteral<"User">;
            }, "strip", z.ZodTypeAny, {
                id: string;
                resourceType: "User";
            }, {
                id: string;
                resourceType: "User";
            }>>;
        }, "strip", z.ZodTypeAny, {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        }, {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        }>>;
        requestRef: z.ZodOptional<z.ZodObject<{
            id: z.ZodString;
            resourceType: z.ZodLiteral<"Request">;
        }, "strip", z.ZodTypeAny, {
            id: string;
            resourceType: "Request";
        }, {
            id: string;
            resourceType: "Request";
        }>>;
        versionHistorySections: z.ZodOptional<z.ZodArray<z.ZodObject<{
            name: z.ZodOptional<z.ZodString>;
            changes: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
        }, "strip", z.ZodTypeAny, {
            name?: string | undefined;
            changes?: any[] | undefined;
        }, {
            name?: string | undefined;
            changes?: any[] | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        uuid: string;
        title: string;
        summary: string;
        madeBy: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        };
        status?: "submitted" | "waiting_on_more_data" | "processing" | "waiting_on_approvals" | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
        tags?: string[] | undefined;
        approvedBy?: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        } | undefined;
        versionHistorySections?: {
            name?: string | undefined;
            changes?: any[] | undefined;
        }[] | undefined;
    }, {
        uuid: string;
        title: string;
        summary: string;
        madeBy: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        };
        status?: "submitted" | "waiting_on_more_data" | "processing" | "waiting_on_approvals" | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
        tags?: string[] | undefined;
        approvedBy?: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        } | undefined;
        versionHistorySections?: {
            name?: string | undefined;
            changes?: any[] | undefined;
        }[] | undefined;
    }>, "many">>;
    terminalDiagnosis: z.ZodOptional<z.ZodString>;
    complianceTimeline: z.ZodOptional<z.ZodObject<{
        timeLineItems: z.ZodOptional<z.ZodArray<z.ZodObject<{
            started: z.ZodOptional<z.ZodString>;
            blockers: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            completed: z.ZodOptional<z.ZodString>;
            description: z.ZodOptional<z.ZodString>;
            displayName: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            completed?: string | undefined;
            started?: string | undefined;
            blockers?: string[] | undefined;
            description?: string | undefined;
            displayName?: string | undefined;
        }, {
            completed?: string | undefined;
            started?: string | undefined;
            blockers?: string[] | undefined;
            description?: string | undefined;
            displayName?: string | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        timeLineItems?: {
            completed?: string | undefined;
            started?: string | undefined;
            blockers?: string[] | undefined;
            description?: string | undefined;
            displayName?: string | undefined;
        }[] | undefined;
    }, {
        timeLineItems?: {
            completed?: string | undefined;
            started?: string | undefined;
            blockers?: string[] | undefined;
            description?: string | undefined;
            displayName?: string | undefined;
        }[] | undefined;
    }>>;
    comorbidities: z.ZodOptional<z.ZodArray<z.ZodObject<{
        code: z.ZodString;
        description: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        code: string;
        description: string;
    }, {
        code: string;
        description: string;
    }>, "many">>;
    medicalRecordNumber: z.ZodOptional<z.ZodString>;
    emergencyContactName: z.ZodOptional<z.ZodString>;
    emergencyContactPhone: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: "Patient";
    schemaId: string;
    updatedAt: string;
    createdAt: string;
    lastName: string;
    firstName: string;
    dateOfBirth: string;
    agencyId?: string | undefined;
    status?: "Active" | "Discharged" | "Deceased" | undefined;
    email?: string | undefined;
    branch?: {
        id: string;
        resourceType: "Branch";
    } | undefined;
    gender?: string | undefined;
    orders?: any[] | undefined;
    address?: string | undefined;
    nextIDG?: string | undefined;
    allergies?: string[] | undefined;
    insurance?: any[] | undefined;
    planOfCare?: any;
    visitNotes?: any[] | undefined;
    medications?: {
        name: string;
        dosage: string;
        resourceType?: "Medication" | undefined;
        route?: string | undefined;
        frequency?: string | undefined;
        indication?: string | undefined;
    }[] | undefined;
    phoneNumber?: string | undefined;
    admissionDate?: string | undefined;
    agencyActions?: {
        uuid: string;
        title: string;
        content?: string | undefined;
        priority?: "high" | "medium" | "low" | undefined;
        completed?: boolean | undefined;
        actionType?: "more_data" | "approval" | "reminder" | undefined;
        dueDate?: string | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
    }[] | undefined;
    benefitPeriod?: string | undefined;
    timeLineItems?: {
        uuid: string;
        title: string;
        summary: string;
        madeBy: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        };
        status?: "submitted" | "waiting_on_more_data" | "processing" | "waiting_on_approvals" | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
        tags?: string[] | undefined;
        approvedBy?: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        } | undefined;
        versionHistorySections?: {
            name?: string | undefined;
            changes?: any[] | undefined;
        }[] | undefined;
    }[] | undefined;
    terminalDiagnosis?: string | undefined;
    complianceTimeline?: {
        timeLineItems?: {
            completed?: string | undefined;
            started?: string | undefined;
            blockers?: string[] | undefined;
            description?: string | undefined;
            displayName?: string | undefined;
        }[] | undefined;
    } | undefined;
    comorbidities?: {
        code: string;
        description: string;
    }[] | undefined;
    medicalRecordNumber?: string | undefined;
    emergencyContactName?: string | undefined;
    emergencyContactPhone?: string | undefined;
}, {
    id: string;
    resourceType: "Patient";
    schemaId: string;
    updatedAt: string;
    createdAt: string;
    lastName: string;
    firstName: string;
    dateOfBirth: string;
    agencyId?: string | undefined;
    status?: "Active" | "Discharged" | "Deceased" | undefined;
    email?: string | undefined;
    branch?: {
        id: string;
        resourceType: "Branch";
    } | undefined;
    gender?: string | undefined;
    orders?: any[] | undefined;
    address?: string | undefined;
    nextIDG?: string | undefined;
    allergies?: string[] | undefined;
    insurance?: any[] | undefined;
    planOfCare?: any;
    visitNotes?: any[] | undefined;
    medications?: {
        name: string;
        dosage: string;
        resourceType?: "Medication" | undefined;
        route?: string | undefined;
        frequency?: string | undefined;
        indication?: string | undefined;
    }[] | undefined;
    phoneNumber?: string | undefined;
    admissionDate?: string | undefined;
    agencyActions?: {
        uuid: string;
        title: string;
        content?: string | undefined;
        priority?: "high" | "medium" | "low" | undefined;
        completed?: boolean | undefined;
        actionType?: "more_data" | "approval" | "reminder" | undefined;
        dueDate?: string | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
    }[] | undefined;
    benefitPeriod?: string | undefined;
    timeLineItems?: {
        uuid: string;
        title: string;
        summary: string;
        madeBy: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        };
        status?: "submitted" | "waiting_on_more_data" | "processing" | "waiting_on_approvals" | undefined;
        requestRef?: {
            id: string;
            resourceType: "Request";
        } | undefined;
        tags?: string[] | undefined;
        approvedBy?: {
            date: string;
            name: string;
            user?: {
                id: string;
                resourceType: "User";
            } | undefined;
        } | undefined;
        versionHistorySections?: {
            name?: string | undefined;
            changes?: any[] | undefined;
        }[] | undefined;
    }[] | undefined;
    terminalDiagnosis?: string | undefined;
    complianceTimeline?: {
        timeLineItems?: {
            completed?: string | undefined;
            started?: string | undefined;
            blockers?: string[] | undefined;
            description?: string | undefined;
            displayName?: string | undefined;
        }[] | undefined;
    } | undefined;
    comorbidities?: {
        code: string;
        description: string;
    }[] | undefined;
    medicalRecordNumber?: string | undefined;
    emergencyContactName?: string | undefined;
    emergencyContactPhone?: string | undefined;
}>;
//# sourceMappingURL=patient.d.ts.map