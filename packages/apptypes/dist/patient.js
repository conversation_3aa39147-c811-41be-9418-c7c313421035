import { z } from 'zod';
import { BaseResourceSchema } from './resource';
import { MedicationSchema } from './medication';
import { ActionSchema } from './action';
import { TimelineItemSchema } from './timelineitem';
export const PatientSchema = BaseResourceSchema.extend({
    id: z.string(),
    email: z.string().email().optional(),
    branch: z.object({
        id: z.string().uuid(),
        resourceType: z.literal('Branch'),
    }).optional(),
    gender: z.string().optional(),
    orders: z.array(z.any()).optional(),
    status: z.enum(['Active', 'Discharged', 'Deceased']).optional(),
    address: z.string().optional(),
    nextIDG: z.string().datetime().optional(),
    lastName: z.string(),
    allergies: z.array(z.string()).optional(),
    firstName: z.string(),
    insurance: z.array(z.any()).optional(),
    planOfCare: z.any().optional(),
    visitNotes: z.array(z.any()).optional(),
    dateOfBirth: z.string(),
    medications: z.array(MedicationSchema).optional(),
    phoneNumber: z.string().optional(),
    resourceType: z.literal('Patient'),
    admissionDate: z.string().optional(),
    agencyActions: z.array(ActionSchema).optional(),
    benefitPeriod: z.string().optional(),
    timeLineItems: z.array(TimelineItemSchema).optional(),
    terminalDiagnosis: z.string().optional(),
    complianceTimeline: z.object({
        timeLineItems: z.array(z.object({
            started: z.string().datetime().optional(),
            blockers: z.array(z.string()).optional(),
            completed: z.string().datetime().optional(),
            description: z.string().optional(),
            displayName: z.string().optional(),
        })).optional(),
    }).optional(),
    comorbidities: z.array(z.object({ code: z.string().describe("ICD-10 code"), description: z.string() })).optional(),
    medicalRecordNumber: z.string().optional(),
    emergencyContactName: z.string().optional(),
    emergencyContactPhone: z.string().optional(),
});
//# sourceMappingURL=patient.js.map