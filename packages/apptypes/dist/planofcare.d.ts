import { z } from 'zod';
export interface PlanOfCare {
    orders: {
        currentDME?: string;
        pendingOrders?: string;
        suppliesOrdered?: string;
        additionalEquipmentNeeds?: string;
    };
    domains: {
        physical?: {
            dme?: string;
            goals?: string[];
            supplies?: string;
            medications?: string;
            identifiedProblems?: string[];
            plannedInterventions?: string;
            nonPharmacologicMeasures?: string;
        };
        spiritual?: {
            goals?: string[];
            ritualsDiscussed?: string;
            assessmentFindings?: string;
            plannedInterventions?: string;
            externalClergyCollaboration?: boolean;
        };
        functional?: {
            goals?: string[];
            plannedInterventions?: string;
            assistiveDevicesInUse?: string;
            caregiverEducationTopics?: string;
            identifiedFunctionalNeeds?: string[];
            rnSupervisoryVisitFrequency?: string;
        };
        bereavement?: {
            initialPlan?: string;
            riskFactors?: string[];
            followUpPlanned?: boolean;
            bereavementLevel?: 'Low' | 'Moderate' | 'High';
        };
        psychosocial?: {
            goals?: string[];
            supportPlans?: string;
            assessmentFindings?: string;
            plannedInterventions?: string;
        };
    };
    patientInfo: {
        mrn: string;
        patientName: string;
        hospicePhysician?: string;
        terminalDiagnosis: string;
        prognosisSummary: string;
        relatedDiagnoses?: string[];
        dateOfPOCCreation?: string;
        attendingPhysician?: string;
        unrelatedDiagnoses?: string[];
        certificationPeriod: string;
    };
    resourceType: 'PlanOfCare';
    reviewAndSignOff: {
        createdBy?: string;
        creationDate?: string;
        nextIDGReviewDate?: string;
        hospicePhysicianSignOffDate?: string;
    };
    visitFrequencies: {
        RN?: string;
        HHA?: string;
        MSW?: string;
        Other?: string;
        Chaplain?: string;
    };
    educationAndCoordination: {
        familyUnderstandsPlan?: boolean;
        scheduledIDGReviewDate?: string;
        educationTopicsProvided?: string[];
        additionalSupportFromIDG?: string;
    };
}
export declare const PlanOfCareSchema: z.ZodObject<{
    orders: z.ZodObject<{
        currentDME: z.ZodOptional<z.ZodString>;
        pendingOrders: z.ZodOptional<z.ZodString>;
        suppliesOrdered: z.ZodOptional<z.ZodString>;
        additionalEquipmentNeeds: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        currentDME?: string | undefined;
        pendingOrders?: string | undefined;
        suppliesOrdered?: string | undefined;
        additionalEquipmentNeeds?: string | undefined;
    }, {
        currentDME?: string | undefined;
        pendingOrders?: string | undefined;
        suppliesOrdered?: string | undefined;
        additionalEquipmentNeeds?: string | undefined;
    }>;
    domains: z.ZodObject<{
        physical: z.ZodOptional<z.ZodObject<{
            dme: z.ZodOptional<z.ZodString>;
            goals: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            supplies: z.ZodOptional<z.ZodString>;
            medications: z.ZodOptional<z.ZodString>;
            identifiedProblems: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            plannedInterventions: z.ZodOptional<z.ZodString>;
            nonPharmacologicMeasures: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            medications?: string | undefined;
            dme?: string | undefined;
            goals?: string[] | undefined;
            supplies?: string | undefined;
            identifiedProblems?: string[] | undefined;
            plannedInterventions?: string | undefined;
            nonPharmacologicMeasures?: string | undefined;
        }, {
            medications?: string | undefined;
            dme?: string | undefined;
            goals?: string[] | undefined;
            supplies?: string | undefined;
            identifiedProblems?: string[] | undefined;
            plannedInterventions?: string | undefined;
            nonPharmacologicMeasures?: string | undefined;
        }>>;
        spiritual: z.ZodOptional<z.ZodObject<{
            goals: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            ritualsDiscussed: z.ZodOptional<z.ZodString>;
            assessmentFindings: z.ZodOptional<z.ZodString>;
            plannedInterventions: z.ZodOptional<z.ZodString>;
            externalClergyCollaboration: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            ritualsDiscussed?: string | undefined;
            assessmentFindings?: string | undefined;
            externalClergyCollaboration?: boolean | undefined;
        }, {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            ritualsDiscussed?: string | undefined;
            assessmentFindings?: string | undefined;
            externalClergyCollaboration?: boolean | undefined;
        }>>;
        functional: z.ZodOptional<z.ZodObject<{
            goals: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            plannedInterventions: z.ZodOptional<z.ZodString>;
            assistiveDevicesInUse: z.ZodOptional<z.ZodString>;
            caregiverEducationTopics: z.ZodOptional<z.ZodString>;
            identifiedFunctionalNeeds: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            rnSupervisoryVisitFrequency: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assistiveDevicesInUse?: string | undefined;
            caregiverEducationTopics?: string | undefined;
            identifiedFunctionalNeeds?: string[] | undefined;
            rnSupervisoryVisitFrequency?: string | undefined;
        }, {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assistiveDevicesInUse?: string | undefined;
            caregiverEducationTopics?: string | undefined;
            identifiedFunctionalNeeds?: string[] | undefined;
            rnSupervisoryVisitFrequency?: string | undefined;
        }>>;
        bereavement: z.ZodOptional<z.ZodObject<{
            initialPlan: z.ZodOptional<z.ZodString>;
            riskFactors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            followUpPlanned: z.ZodOptional<z.ZodBoolean>;
            bereavementLevel: z.ZodOptional<z.ZodEnum<["Low", "Moderate", "High"]>>;
        }, "strip", z.ZodTypeAny, {
            initialPlan?: string | undefined;
            riskFactors?: string[] | undefined;
            followUpPlanned?: boolean | undefined;
            bereavementLevel?: "Low" | "Moderate" | "High" | undefined;
        }, {
            initialPlan?: string | undefined;
            riskFactors?: string[] | undefined;
            followUpPlanned?: boolean | undefined;
            bereavementLevel?: "Low" | "Moderate" | "High" | undefined;
        }>>;
        psychosocial: z.ZodOptional<z.ZodObject<{
            goals: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            supportPlans: z.ZodOptional<z.ZodString>;
            assessmentFindings: z.ZodOptional<z.ZodString>;
            plannedInterventions: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assessmentFindings?: string | undefined;
            supportPlans?: string | undefined;
        }, {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assessmentFindings?: string | undefined;
            supportPlans?: string | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        physical?: {
            medications?: string | undefined;
            dme?: string | undefined;
            goals?: string[] | undefined;
            supplies?: string | undefined;
            identifiedProblems?: string[] | undefined;
            plannedInterventions?: string | undefined;
            nonPharmacologicMeasures?: string | undefined;
        } | undefined;
        spiritual?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            ritualsDiscussed?: string | undefined;
            assessmentFindings?: string | undefined;
            externalClergyCollaboration?: boolean | undefined;
        } | undefined;
        functional?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assistiveDevicesInUse?: string | undefined;
            caregiverEducationTopics?: string | undefined;
            identifiedFunctionalNeeds?: string[] | undefined;
            rnSupervisoryVisitFrequency?: string | undefined;
        } | undefined;
        bereavement?: {
            initialPlan?: string | undefined;
            riskFactors?: string[] | undefined;
            followUpPlanned?: boolean | undefined;
            bereavementLevel?: "Low" | "Moderate" | "High" | undefined;
        } | undefined;
        psychosocial?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assessmentFindings?: string | undefined;
            supportPlans?: string | undefined;
        } | undefined;
    }, {
        physical?: {
            medications?: string | undefined;
            dme?: string | undefined;
            goals?: string[] | undefined;
            supplies?: string | undefined;
            identifiedProblems?: string[] | undefined;
            plannedInterventions?: string | undefined;
            nonPharmacologicMeasures?: string | undefined;
        } | undefined;
        spiritual?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            ritualsDiscussed?: string | undefined;
            assessmentFindings?: string | undefined;
            externalClergyCollaboration?: boolean | undefined;
        } | undefined;
        functional?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assistiveDevicesInUse?: string | undefined;
            caregiverEducationTopics?: string | undefined;
            identifiedFunctionalNeeds?: string[] | undefined;
            rnSupervisoryVisitFrequency?: string | undefined;
        } | undefined;
        bereavement?: {
            initialPlan?: string | undefined;
            riskFactors?: string[] | undefined;
            followUpPlanned?: boolean | undefined;
            bereavementLevel?: "Low" | "Moderate" | "High" | undefined;
        } | undefined;
        psychosocial?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assessmentFindings?: string | undefined;
            supportPlans?: string | undefined;
        } | undefined;
    }>;
    patientInfo: z.ZodObject<{
        mrn: z.ZodString;
        patientName: z.ZodString;
        hospicePhysician: z.ZodOptional<z.ZodString>;
        terminalDiagnosis: z.ZodString;
        prognosisSummary: z.ZodString;
        relatedDiagnoses: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        dateOfPOCCreation: z.ZodOptional<z.ZodString>;
        attendingPhysician: z.ZodOptional<z.ZodString>;
        unrelatedDiagnoses: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        certificationPeriod: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        terminalDiagnosis: string;
        mrn: string;
        patientName: string;
        prognosisSummary: string;
        certificationPeriod: string;
        hospicePhysician?: string | undefined;
        relatedDiagnoses?: string[] | undefined;
        dateOfPOCCreation?: string | undefined;
        attendingPhysician?: string | undefined;
        unrelatedDiagnoses?: string[] | undefined;
    }, {
        terminalDiagnosis: string;
        mrn: string;
        patientName: string;
        prognosisSummary: string;
        certificationPeriod: string;
        hospicePhysician?: string | undefined;
        relatedDiagnoses?: string[] | undefined;
        dateOfPOCCreation?: string | undefined;
        attendingPhysician?: string | undefined;
        unrelatedDiagnoses?: string[] | undefined;
    }>;
    resourceType: z.ZodLiteral<"PlanOfCare">;
    reviewAndSignOff: z.ZodObject<{
        createdBy: z.ZodOptional<z.ZodString>;
        creationDate: z.ZodOptional<z.ZodString>;
        nextIDGReviewDate: z.ZodOptional<z.ZodString>;
        hospicePhysicianSignOffDate: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        createdBy?: string | undefined;
        creationDate?: string | undefined;
        nextIDGReviewDate?: string | undefined;
        hospicePhysicianSignOffDate?: string | undefined;
    }, {
        createdBy?: string | undefined;
        creationDate?: string | undefined;
        nextIDGReviewDate?: string | undefined;
        hospicePhysicianSignOffDate?: string | undefined;
    }>;
    visitFrequencies: z.ZodObject<{
        RN: z.ZodOptional<z.ZodString>;
        HHA: z.ZodOptional<z.ZodString>;
        MSW: z.ZodOptional<z.ZodString>;
        Other: z.ZodOptional<z.ZodString>;
        Chaplain: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        RN?: string | undefined;
        HHA?: string | undefined;
        MSW?: string | undefined;
        Other?: string | undefined;
        Chaplain?: string | undefined;
    }, {
        RN?: string | undefined;
        HHA?: string | undefined;
        MSW?: string | undefined;
        Other?: string | undefined;
        Chaplain?: string | undefined;
    }>;
    educationAndCoordination: z.ZodObject<{
        familyUnderstandsPlan: z.ZodOptional<z.ZodBoolean>;
        scheduledIDGReviewDate: z.ZodOptional<z.ZodString>;
        educationTopicsProvided: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        additionalSupportFromIDG: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        familyUnderstandsPlan?: boolean | undefined;
        scheduledIDGReviewDate?: string | undefined;
        educationTopicsProvided?: string[] | undefined;
        additionalSupportFromIDG?: string | undefined;
    }, {
        familyUnderstandsPlan?: boolean | undefined;
        scheduledIDGReviewDate?: string | undefined;
        educationTopicsProvided?: string[] | undefined;
        additionalSupportFromIDG?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    resourceType: "PlanOfCare";
    orders: {
        currentDME?: string | undefined;
        pendingOrders?: string | undefined;
        suppliesOrdered?: string | undefined;
        additionalEquipmentNeeds?: string | undefined;
    };
    domains: {
        physical?: {
            medications?: string | undefined;
            dme?: string | undefined;
            goals?: string[] | undefined;
            supplies?: string | undefined;
            identifiedProblems?: string[] | undefined;
            plannedInterventions?: string | undefined;
            nonPharmacologicMeasures?: string | undefined;
        } | undefined;
        spiritual?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            ritualsDiscussed?: string | undefined;
            assessmentFindings?: string | undefined;
            externalClergyCollaboration?: boolean | undefined;
        } | undefined;
        functional?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assistiveDevicesInUse?: string | undefined;
            caregiverEducationTopics?: string | undefined;
            identifiedFunctionalNeeds?: string[] | undefined;
            rnSupervisoryVisitFrequency?: string | undefined;
        } | undefined;
        bereavement?: {
            initialPlan?: string | undefined;
            riskFactors?: string[] | undefined;
            followUpPlanned?: boolean | undefined;
            bereavementLevel?: "Low" | "Moderate" | "High" | undefined;
        } | undefined;
        psychosocial?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assessmentFindings?: string | undefined;
            supportPlans?: string | undefined;
        } | undefined;
    };
    patientInfo: {
        terminalDiagnosis: string;
        mrn: string;
        patientName: string;
        prognosisSummary: string;
        certificationPeriod: string;
        hospicePhysician?: string | undefined;
        relatedDiagnoses?: string[] | undefined;
        dateOfPOCCreation?: string | undefined;
        attendingPhysician?: string | undefined;
        unrelatedDiagnoses?: string[] | undefined;
    };
    reviewAndSignOff: {
        createdBy?: string | undefined;
        creationDate?: string | undefined;
        nextIDGReviewDate?: string | undefined;
        hospicePhysicianSignOffDate?: string | undefined;
    };
    visitFrequencies: {
        RN?: string | undefined;
        HHA?: string | undefined;
        MSW?: string | undefined;
        Other?: string | undefined;
        Chaplain?: string | undefined;
    };
    educationAndCoordination: {
        familyUnderstandsPlan?: boolean | undefined;
        scheduledIDGReviewDate?: string | undefined;
        educationTopicsProvided?: string[] | undefined;
        additionalSupportFromIDG?: string | undefined;
    };
}, {
    resourceType: "PlanOfCare";
    orders: {
        currentDME?: string | undefined;
        pendingOrders?: string | undefined;
        suppliesOrdered?: string | undefined;
        additionalEquipmentNeeds?: string | undefined;
    };
    domains: {
        physical?: {
            medications?: string | undefined;
            dme?: string | undefined;
            goals?: string[] | undefined;
            supplies?: string | undefined;
            identifiedProblems?: string[] | undefined;
            plannedInterventions?: string | undefined;
            nonPharmacologicMeasures?: string | undefined;
        } | undefined;
        spiritual?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            ritualsDiscussed?: string | undefined;
            assessmentFindings?: string | undefined;
            externalClergyCollaboration?: boolean | undefined;
        } | undefined;
        functional?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assistiveDevicesInUse?: string | undefined;
            caregiverEducationTopics?: string | undefined;
            identifiedFunctionalNeeds?: string[] | undefined;
            rnSupervisoryVisitFrequency?: string | undefined;
        } | undefined;
        bereavement?: {
            initialPlan?: string | undefined;
            riskFactors?: string[] | undefined;
            followUpPlanned?: boolean | undefined;
            bereavementLevel?: "Low" | "Moderate" | "High" | undefined;
        } | undefined;
        psychosocial?: {
            goals?: string[] | undefined;
            plannedInterventions?: string | undefined;
            assessmentFindings?: string | undefined;
            supportPlans?: string | undefined;
        } | undefined;
    };
    patientInfo: {
        terminalDiagnosis: string;
        mrn: string;
        patientName: string;
        prognosisSummary: string;
        certificationPeriod: string;
        hospicePhysician?: string | undefined;
        relatedDiagnoses?: string[] | undefined;
        dateOfPOCCreation?: string | undefined;
        attendingPhysician?: string | undefined;
        unrelatedDiagnoses?: string[] | undefined;
    };
    reviewAndSignOff: {
        createdBy?: string | undefined;
        creationDate?: string | undefined;
        nextIDGReviewDate?: string | undefined;
        hospicePhysicianSignOffDate?: string | undefined;
    };
    visitFrequencies: {
        RN?: string | undefined;
        HHA?: string | undefined;
        MSW?: string | undefined;
        Other?: string | undefined;
        Chaplain?: string | undefined;
    };
    educationAndCoordination: {
        familyUnderstandsPlan?: boolean | undefined;
        scheduledIDGReviewDate?: string | undefined;
        educationTopicsProvided?: string[] | undefined;
        additionalSupportFromIDG?: string | undefined;
    };
}>;
//# sourceMappingURL=planofcare.d.ts.map