import { z } from 'zod';
export const PlanOfCareSchema = z.object({
    orders: z.object({
        currentDME: z.string().optional(),
        pendingOrders: z.string().optional(),
        suppliesOrdered: z.string().optional(),
        additionalEquipmentNeeds: z.string().optional(),
    }),
    domains: z.object({
        physical: z.object({
            dme: z.string().optional(),
            goals: z.array(z.string()).optional(),
            supplies: z.string().optional(),
            medications: z.string().optional(),
            identifiedProblems: z.array(z.string()).optional(),
            plannedInterventions: z.string().optional(),
            nonPharmacologicMeasures: z.string().optional(),
        }).optional(),
        spiritual: z.object({
            goals: z.array(z.string()).optional(),
            ritualsDiscussed: z.string().optional(),
            assessmentFindings: z.string().optional(),
            plannedInterventions: z.string().optional(),
            externalClergyCollaboration: z.boolean().optional(),
        }).optional(),
        functional: z.object({
            goals: z.array(z.string()).optional(),
            plannedInterventions: z.string().optional(),
            assistiveDevicesInUse: z.string().optional(),
            caregiverEducationTopics: z.string().optional(),
            identifiedFunctionalNeeds: z.array(z.string()).optional(),
            rnSupervisoryVisitFrequency: z.string().optional(),
        }).optional(),
        bereavement: z.object({
            initialPlan: z.string().optional(),
            riskFactors: z.array(z.string()).optional(),
            followUpPlanned: z.boolean().optional(),
            bereavementLevel: z.enum(['Low', 'Moderate', 'High']).optional(),
        }).optional(),
        psychosocial: z.object({
            goals: z.array(z.string()).optional(),
            supportPlans: z.string().optional(),
            assessmentFindings: z.string().optional(),
            plannedInterventions: z.string().optional(),
        }).optional(),
    }),
    patientInfo: z.object({
        mrn: z.string(),
        patientName: z.string(),
        hospicePhysician: z.string().optional(),
        terminalDiagnosis: z.string(),
        prognosisSummary: z.string(),
        relatedDiagnoses: z.array(z.string()).optional(),
        dateOfPOCCreation: z.string().optional(),
        attendingPhysician: z.string().optional(),
        unrelatedDiagnoses: z.array(z.string()).optional(),
        certificationPeriod: z.string(),
    }),
    resourceType: z.literal('PlanOfCare'),
    reviewAndSignOff: z.object({
        createdBy: z.string().optional(),
        creationDate: z.string().optional(),
        nextIDGReviewDate: z.string().optional(),
        hospicePhysicianSignOffDate: z.string().optional(),
    }),
    visitFrequencies: z.object({
        RN: z.string().optional(),
        HHA: z.string().optional(),
        MSW: z.string().optional(),
        Other: z.string().optional(),
        Chaplain: z.string().optional(),
    }),
    educationAndCoordination: z.object({
        familyUnderstandsPlan: z.boolean().optional(),
        scheduledIDGReviewDate: z.string().optional(),
        educationTopicsProvided: z.array(z.string()).optional(),
        additionalSupportFromIDG: z.string().optional(),
    }),
});
//# sourceMappingURL=planofcare.js.map