import { z } from "zod";
export interface BaseResource {
    id: string;
    resourceType: string;
    schemaId: string;
    agencyId?: string;
    updatedAt: string;
    createdAt: string;
}
export declare const BaseResourceSchema: z.ZodObject<{
    id: z.ZodString;
    resourceType: z.ZodString;
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
    updatedAt: z.ZodString;
    createdAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: string;
    schemaId: string;
    updatedAt: string;
    createdAt: string;
    agencyId?: string | undefined;
}, {
    id: string;
    resourceType: string;
    schemaId: string;
    updatedAt: string;
    createdAt: string;
    agencyId?: string | undefined;
}>;
export declare function isBaseResource(obj: any): obj is BaseResource;
//# sourceMappingURL=resource.d.ts.map