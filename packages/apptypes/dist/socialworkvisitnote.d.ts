import { z } from 'zod';
import { Note } from './note';
export interface SocialWorkVisitNote extends Note {
    financialConcerns?: string;
    resourcesProvided?: string[];
    caregiverSupportPlan?: string;
    psychosocialAssessment: string;
}
export declare const SocialWorkVisitNoteSchema: z.ZodObject<{
    schemaId: z.ZodString;
    agencyId: z.ZodOptional<z.ZodString>;
} & {
    id: z.ZodString;
    title: z.ZodString;
    summary: z.ZodOptional<z.ZodString>;
    authorId: z.ZodString;
    noteType: z.ZodString;
    createdAt: z.ZodString;
    patientId: z.ZodString;
    updatedAt: z.ZodOptional<z.ZodString>;
    resourceType: z.<PERSON>od<PERSON>iteral<"Note">;
} & {
    financialConcerns: z.ZodOptional<z.ZodString>;
    resourcesProvided: z.ZodOptional<z.<PERSON><PERSON><PERSON><PERSON>y<z.ZodString, "many">>;
    caregiverSupportPlan: z.<PERSON>ptional<z.ZodString>;
    psychosocialAssessment: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: "Note";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    noteType: string;
    patientId: string;
    psychosocialAssessment: string;
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
    financialConcerns?: string | undefined;
    resourcesProvided?: string[] | undefined;
    caregiverSupportPlan?: string | undefined;
}, {
    id: string;
    resourceType: "Note";
    schemaId: string;
    createdAt: string;
    title: string;
    authorId: string;
    noteType: string;
    patientId: string;
    psychosocialAssessment: string;
    agencyId?: string | undefined;
    updatedAt?: string | undefined;
    summary?: string | undefined;
    financialConcerns?: string | undefined;
    resourcesProvided?: string[] | undefined;
    caregiverSupportPlan?: string | undefined;
}>;
//# sourceMappingURL=socialworkvisitnote.d.ts.map