import { z } from 'zod';
import { Action } from './action';
export interface test extends Action {
    test?: string;
}
export declare const testSchema: z.ZodObject<{
    uuid: z.ZodString;
    title: z.ZodString;
    content: z.ZodOptional<z.ZodString>;
    priority: z.<PERSON>al<z.ZodEnum<["high", "medium", "low"]>>;
    completed: z.<PERSON><z.ZodBoolean>;
    actionType: z.ZodOptional<z.ZodEnum<["more_data", "approval", "reminder"]>>;
    dueDate: z.ZodOptional<z.ZodString>;
    requestRef: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        resourceType: z.<PERSON><"Request">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        resourceType: "Request";
    }, {
        id: string;
        resourceType: "Request";
    }>>;
} & {
    test: z.ZodOptional<z.ZodString>;
}, "strip", z.<PERSON><PERSON><PERSON>ype<PERSON>ny, {
    uuid: string;
    title: string;
    content?: string | undefined;
    priority?: "high" | "medium" | "low" | undefined;
    completed?: boolean | undefined;
    actionType?: "more_data" | "approval" | "reminder" | undefined;
    dueDate?: string | undefined;
    requestRef?: {
        id: string;
        resourceType: "Request";
    } | undefined;
    test?: string | undefined;
}, {
    uuid: string;
    title: string;
    content?: string | undefined;
    priority?: "high" | "medium" | "low" | undefined;
    completed?: boolean | undefined;
    actionType?: "more_data" | "approval" | "reminder" | undefined;
    dueDate?: string | undefined;
    requestRef?: {
        id: string;
        resourceType: "Request";
    } | undefined;
    test?: string | undefined;
}>;
//# sourceMappingURL=test.d.ts.map