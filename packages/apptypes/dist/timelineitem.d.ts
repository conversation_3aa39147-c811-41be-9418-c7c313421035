import { z } from 'zod';
export interface TimelineItem {
    tags?: string[];
    uuid: string;
    title: string;
    madeBy: {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: 'User';
        };
    };
    status?: 'submitted' | 'waiting_on_more_data' | 'processing' | 'waiting_on_approvals';
    summary: string;
    approvedBy?: {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: 'User';
        };
    };
    requestRef?: {
        id: string;
        resourceType: 'Request';
    };
    versionHistorySections?: {
        name?: string;
        changes?: any[];
    }[];
}
export declare const TimelineItemSchema: z.ZodObject<{
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    uuid: z.ZodString;
    title: z.ZodString;
    madeBy: z.ZodObject<{
        date: z.ZodString;
        name: z.ZodString;
        user: z.ZodOptional<z.ZodObject<{
            id: z.ZodString;
            resourceType: z.ZodLiteral<"User">;
        }, "strip", z.ZodTypeAny, {
            id: string;
            resourceType: "User";
        }, {
            id: string;
            resourceType: "User";
        }>>;
    }, "strip", z.ZodTypeAny, {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    }, {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    }>;
    status: z.ZodOptional<z.ZodEnum<["submitted", "waiting_on_more_data", "processing", "waiting_on_approvals"]>>;
    summary: z.ZodString;
    approvedBy: z.ZodOptional<z.ZodObject<{
        date: z.ZodString;
        name: z.ZodString;
        user: z.ZodOptional<z.ZodObject<{
            id: z.ZodString;
            resourceType: z.ZodLiteral<"User">;
        }, "strip", z.ZodTypeAny, {
            id: string;
            resourceType: "User";
        }, {
            id: string;
            resourceType: "User";
        }>>;
    }, "strip", z.ZodTypeAny, {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    }, {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    }>>;
    requestRef: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        resourceType: z.ZodLiteral<"Request">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        resourceType: "Request";
    }, {
        id: string;
        resourceType: "Request";
    }>>;
    versionHistorySections: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodOptional<z.ZodString>;
        changes: z.ZodOptional<z.ZodArray<z.ZodAny, "many">>;
    }, "strip", z.ZodTypeAny, {
        name?: string | undefined;
        changes?: any[] | undefined;
    }, {
        name?: string | undefined;
        changes?: any[] | undefined;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    uuid: string;
    title: string;
    summary: string;
    madeBy: {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    };
    status?: "submitted" | "waiting_on_more_data" | "processing" | "waiting_on_approvals" | undefined;
    requestRef?: {
        id: string;
        resourceType: "Request";
    } | undefined;
    tags?: string[] | undefined;
    approvedBy?: {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    } | undefined;
    versionHistorySections?: {
        name?: string | undefined;
        changes?: any[] | undefined;
    }[] | undefined;
}, {
    uuid: string;
    title: string;
    summary: string;
    madeBy: {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    };
    status?: "submitted" | "waiting_on_more_data" | "processing" | "waiting_on_approvals" | undefined;
    requestRef?: {
        id: string;
        resourceType: "Request";
    } | undefined;
    tags?: string[] | undefined;
    approvedBy?: {
        date: string;
        name: string;
        user?: {
            id: string;
            resourceType: "User";
        } | undefined;
    } | undefined;
    versionHistorySections?: {
        name?: string | undefined;
        changes?: any[] | undefined;
    }[] | undefined;
}>;
//# sourceMappingURL=timelineitem.d.ts.map