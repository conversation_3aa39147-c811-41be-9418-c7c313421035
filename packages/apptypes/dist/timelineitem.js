import { z } from 'zod';
export const TimelineItemSchema = z.object({
    tags: z.array(z.string()).optional(),
    uuid: z.string(),
    title: z.string(),
    madeBy: z.object({
        date: z.string().datetime(),
        name: z.string(),
        user: z.object({
            id: z.string().uuid(),
            resourceType: z.literal('User'),
        }).optional(),
    }),
    status: z.enum(['submitted', 'waiting_on_more_data', 'processing', 'waiting_on_approvals']).optional(),
    summary: z.string(),
    approvedBy: z.object({
        date: z.string().datetime(),
        name: z.string(),
        user: z.object({
            id: z.string().uuid(),
            resourceType: z.literal('User'),
        }).optional(),
    }).optional(),
    requestRef: z.object({
        id: z.string().uuid(),
        resourceType: z.literal('Request'),
    }).optional(),
    versionHistorySections: z.array(z.object({
        name: z.string().optional(),
        changes: z.array(z.any()).optional(),
    })).optional(),
});
//# sourceMappingURL=timelineitem.js.map