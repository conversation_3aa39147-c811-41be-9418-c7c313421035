import { z } from "zod";
import { BaseResource } from "./resource";
export interface User extends BaseResource {
    id: string;
    role: "Admin" | "Nurse" | "Social Worker" | "Chaplain" | "Physician" | "Aide" | "Volunteer" | "Surveyor";
    email: string;
    agencyId: string;
    stytchId?: string;
    firstName: string;
    lastName: string;
    resourceType: "User";
}
export declare const UserSchema: z.ZodObject<{
    schemaId: z.ZodString;
    updatedAt: z.ZodString;
    createdAt: z.ZodString;
} & {
    id: z.ZodString;
    role: z.<PERSON>od<PERSON>num<["Admin", "Nurse", "Social Worker", "Chaplain", "Physician", "Aide", "Volunteer", "Surveyor"]>;
    email: z.ZodString;
    agencyId: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    stytchId: z.Zod<PERSON>ptional<z.ZodString>;
    resourceType: z.<PERSON><"User">;
}, "strip", z.ZodTypeAny, {
    id: string;
    resourceType: "User";
    schemaId: string;
    agencyId: string;
    updatedAt: string;
    createdAt: string;
    email: string;
    lastName: string;
    firstName: string;
    role: "Chaplain" | "Admin" | "Nurse" | "Social Worker" | "Physician" | "Aide" | "Volunteer" | "Surveyor";
    stytchId?: string | undefined;
}, {
    id: string;
    resourceType: "User";
    schemaId: string;
    agencyId: string;
    updatedAt: string;
    createdAt: string;
    email: string;
    lastName: string;
    firstName: string;
    role: "Chaplain" | "Admin" | "Nurse" | "Social Worker" | "Physician" | "Aide" | "Volunteer" | "Surveyor";
    stytchId?: string | undefined;
}>;
//# sourceMappingURL=user.d.ts.map