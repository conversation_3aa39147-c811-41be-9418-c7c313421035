import { z } from "zod";
import { BaseResourceSchema } from "./resource";
export const UserSchema = BaseResourceSchema.extend({
    id: z.string().uuid(),
    role: z.enum(["<PERSON><PERSON>", "<PERSON>", "Social Worker", "<PERSON><PERSON>", "Physician", "<PERSON><PERSON>", "Volunteer", "Surveyor"]),
    email: z.string().email(),
    agencyId: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    stytchId: z.string().optional(),
    resourceType: z.literal("User"),
});
//# sourceMappingURL=user.js.map