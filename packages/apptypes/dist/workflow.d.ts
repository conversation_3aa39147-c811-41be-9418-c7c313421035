import { z } from 'zod';
export interface Workflow {
    do: any;
    use?: {
        errors?: Record<string, any>;
        retries?: Record<string, any>;
        secrets?: string[];
        catalogs?: Record<string, any>;
        timeouts?: Record<string, any>;
        functions?: Record<string, any>;
        extensions?: Record<string, any>[];
        authentications?: Record<string, any>;
    };
    input?: any;
    output?: any;
    timeout?: any;
    document: {
        dsl: string;
        name: string;
        tags?: Record<string, any>;
        title?: string;
        summary?: string;
        version: string;
        metadata?: Record<string, any>;
        namespace: string;
    };
    schedule?: {
        on?: any;
        cron?: string;
        after?: any;
        every?: any;
    };
}
export declare const WorkflowSchema: z.ZodObject<{
    do: z.ZodAny;
    use: z.ZodOptional<z.ZodObject<{
        errors: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        retries: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        secrets: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        catalogs: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        timeouts: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        functions: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        extensions: z.ZodOptional<z.ZodArray<z.ZodRecord<z.ZodString, z.ZodAny>, "many">>;
        authentications: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        errors?: Record<string, any> | undefined;
        retries?: Record<string, any> | undefined;
        secrets?: string[] | undefined;
        catalogs?: Record<string, any> | undefined;
        timeouts?: Record<string, any> | undefined;
        functions?: Record<string, any> | undefined;
        extensions?: Record<string, any>[] | undefined;
        authentications?: Record<string, any> | undefined;
    }, {
        errors?: Record<string, any> | undefined;
        retries?: Record<string, any> | undefined;
        secrets?: string[] | undefined;
        catalogs?: Record<string, any> | undefined;
        timeouts?: Record<string, any> | undefined;
        functions?: Record<string, any> | undefined;
        extensions?: Record<string, any>[] | undefined;
        authentications?: Record<string, any> | undefined;
    }>>;
    input: z.ZodOptional<z.ZodAny>;
    output: z.ZodOptional<z.ZodAny>;
    timeout: z.ZodOptional<z.ZodAny>;
    document: z.ZodObject<{
        dsl: z.ZodString;
        name: z.ZodString;
        tags: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        title: z.ZodOptional<z.ZodString>;
        summary: z.ZodOptional<z.ZodString>;
        version: z.ZodString;
        metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        namespace: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
        dsl: string;
        version: string;
        namespace: string;
        title?: string | undefined;
        summary?: string | undefined;
        tags?: Record<string, any> | undefined;
        metadata?: Record<string, any> | undefined;
    }, {
        name: string;
        dsl: string;
        version: string;
        namespace: string;
        title?: string | undefined;
        summary?: string | undefined;
        tags?: Record<string, any> | undefined;
        metadata?: Record<string, any> | undefined;
    }>;
    schedule: z.ZodOptional<z.ZodObject<{
        on: z.ZodOptional<z.ZodAny>;
        cron: z.ZodOptional<z.ZodString>;
        after: z.ZodOptional<z.ZodAny>;
        every: z.ZodOptional<z.ZodAny>;
    }, "strip", z.ZodTypeAny, {
        every?: any;
        on?: any;
        cron?: string | undefined;
        after?: any;
    }, {
        every?: any;
        on?: any;
        cron?: string | undefined;
        after?: any;
    }>>;
}, "strip", z.ZodTypeAny, {
    document: {
        name: string;
        dsl: string;
        version: string;
        namespace: string;
        title?: string | undefined;
        summary?: string | undefined;
        tags?: Record<string, any> | undefined;
        metadata?: Record<string, any> | undefined;
    };
    do?: any;
    use?: {
        errors?: Record<string, any> | undefined;
        retries?: Record<string, any> | undefined;
        secrets?: string[] | undefined;
        catalogs?: Record<string, any> | undefined;
        timeouts?: Record<string, any> | undefined;
        functions?: Record<string, any> | undefined;
        extensions?: Record<string, any>[] | undefined;
        authentications?: Record<string, any> | undefined;
    } | undefined;
    input?: any;
    output?: any;
    timeout?: any;
    schedule?: {
        every?: any;
        on?: any;
        cron?: string | undefined;
        after?: any;
    } | undefined;
}, {
    document: {
        name: string;
        dsl: string;
        version: string;
        namespace: string;
        title?: string | undefined;
        summary?: string | undefined;
        tags?: Record<string, any> | undefined;
        metadata?: Record<string, any> | undefined;
    };
    do?: any;
    use?: {
        errors?: Record<string, any> | undefined;
        retries?: Record<string, any> | undefined;
        secrets?: string[] | undefined;
        catalogs?: Record<string, any> | undefined;
        timeouts?: Record<string, any> | undefined;
        functions?: Record<string, any> | undefined;
        extensions?: Record<string, any>[] | undefined;
        authentications?: Record<string, any> | undefined;
    } | undefined;
    input?: any;
    output?: any;
    timeout?: any;
    schedule?: {
        every?: any;
        on?: any;
        cron?: string | undefined;
        after?: any;
    } | undefined;
}>;
//# sourceMappingURL=workflow.d.ts.map