import { z } from 'zod';
export const WorkflowSchema = z.object({
    do: z.any(),
    use: z.object({
        errors: z.record(z.string(), z.any()).optional(),
        retries: z.record(z.string(), z.any()).optional(),
        secrets: z.array(z.string()).optional(),
        catalogs: z.record(z.string(), z.any()).optional(),
        timeouts: z.record(z.string(), z.any()).optional(),
        functions: z.record(z.string(), z.any()).optional(),
        extensions: z.array(z.record(z.string(), z.any())).optional(),
        authentications: z.record(z.string(), z.any()).optional(),
    }).optional(),
    input: z.any().optional(),
    output: z.any().optional(),
    timeout: z.any().optional(),
    document: z.object({
        dsl: z.string(),
        name: z.string(),
        tags: z.record(z.string(), z.any()).optional(),
        title: z.string().optional(),
        summary: z.string().optional(),
        version: z.string(),
        metadata: z.record(z.string(), z.any()).optional(),
        namespace: z.string(),
    }),
    schedule: z.object({
        on: z.any().optional(),
        cron: z.string().optional(),
        after: z.any().optional(),
        every: z.any().optional(),
    }).optional(),
});
//# sourceMappingURL=workflow.js.map