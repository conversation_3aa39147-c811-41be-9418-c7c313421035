import { z } from 'zod';

// Agency schema
export const AgencySchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  stytchOrganizationId: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Agency interface
export interface Agency {
  id: string;
  name: string;
  stytchOrganizationId?: string;
  createdAt?: string;
  updatedAt?: string;
}
