import { z } from 'zod';
import { BaseResource, BaseResourceSchema } from './resource';

export interface Branch {
  id?: string;
  name?: string;
  idgInterval?: string;
  lastIdgDate?: string;
  nextIdgDate?: string;
  resourceType?: 'Branch';
}

export const BranchSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().optional(),
  idgInterval: z.string().optional(),
  lastIdgDate: z.string().datetime().optional(),
  nextIdgDate: z.string().datetime().optional(),
  resourceType: z.literal('Branch').optional(),
});

