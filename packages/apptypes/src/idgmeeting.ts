import { z } from "zod";
import { BaseResource, BaseResourceSchema } from "./resource";

export interface IDGMeeting extends BaseResource {
  id: string;
  status: "In Progress" | "Completed";
  participants?: {
    id: string;
    resourceType: "User";
  }[];
  patients?: {
    id: string;
    resourceType: "Patient";
    isCompleted: boolean;
    idgRequest?: {
      id: string;
      resourceType: "Request";
    };
  }[];
  startTime?: string;
  endTime?: string;
}

export const IDGMeetingSchema = BaseResourceSchema.extend({
  id: z.string(),
  status: z.enum(["In Progress", "Completed"]),
  participants: z
    .array(
      z.object({
        id: z.string(),
        resourceType: z.literal("User"),
      }),
    )
    .optional(),
  patients: z
    .array(
      z.object({
        id: z.string(),
        resourceType: z.literal("Patient"),
        isCompleted: z.boolean(),
        idgRequest: z.object({
          id: z.string(),
          resourceType: z.literal("Request"),
        }),
      }),
    )
    .optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
});
