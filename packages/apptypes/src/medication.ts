import { z } from 'zod';
import { BaseResource, BaseResourceSchema } from './resource';

export interface Medication {
  name: string;
  route?: string;
  dosage: string;
  frequency?: string;
  indication?: string;
  resourceType?: 'Medication';
}

export const MedicationSchema = z.object({
  name: z.string(),
  route: z.string().optional(),
  dosage: z.string(),
  frequency: z.string().optional(),
  indication: z.string().optional(),
  resourceType: z.literal('Medication').optional(),
});

