import { z } from "zod";
import { BaseResource, BaseResourceSchema } from "./resource";

export interface Note extends BaseResource {
  id: string;
  title: string;
  summary?: string;
  authorId: string;
  noteType: string;
  createdAt: string;
  patientId: string;
  updatedAt: string;
  resourceType: "Note";
}

export const NoteSchema = BaseResourceSchema.extend({
  id: z.string().uuid(),
  title: z.string(),
  summary: z.string().optional(),
  authorId: z.string().uuid(),
  noteType: z.string(),
  createdAt: z.string().datetime(),
  patientId: z.string().uuid(),
  updatedAt: z.string().datetime().optional(),
  resourceType: z.literal("Note"),
});
