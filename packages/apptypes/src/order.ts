import { z } from "zod";
import { BaseResource, BaseResourceSchema } from "./resource";

export interface Order extends BaseResource {
  id: string;
  title: string;
  summary?: string;
  authorId: string;
  createdAt: string;
  orderType: string;
  patientId: string;
  updatedAt: string;
  resourceType: "Order";
}

export const OrderSchema = BaseResourceSchema.extend({
  id: z.string().uuid(),
  title: z.string(),
  summary: z.string().optional(),
  authorId: z.string().uuid(),
  createdAt: z.string().datetime(),
  orderType: z.string(),
  patientId: z.string().uuid(),
  updatedAt: z.string().datetime().optional(),
  resourceType: z.literal("Order"),
});
