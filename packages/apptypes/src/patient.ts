import { z } from 'zod';
import { BaseResource, BaseResourceSchema } from './resource';
import { Medication, MedicationSchema } from './medication';
import { Action, ActionSchema } from './action';
import { TimelineItem, TimelineItemSchema } from './timelineitem';

export interface Patient extends BaseResource {
  id: string;
  email?: string;
  branch?: {
    id: string;
    resourceType: 'Branch';
  };
  gender?: string;
  orders?: any[];
  status?: 'Active' | 'Discharged' | 'Deceased' | 'Pending';
  address?: string;
  nextIDG?: string;
  lastName: string;
  allergies?: string[];
  firstName: string;
  insurance?: any[];
  planOfCare?: any;
  visitNotes?: any[];
  caseManager?: { id: string; resourceType: "User" };
  dateOfBirth: string;
  medications?: Medication[];
  phoneNumber?: string;
  resourceType: 'Patient';
  admissionDate?: string;
  agencyActions?: Action[];
  benefitPeriods?: { period: number; startDate: string; endDate: string; status: "Pending" | "Active" | "Completed"; periodType: "First 90 days" | "Second 90 days" | "Subsequent 60 days" }[];
  timeLineItems?: TimelineItem[];
  terminalDiagnosis?: string;
  complianceTimeline?: {
    timeLineItems: ComplianceTimelineItem[];
  };
  comorbidities?: { code: string, description: string }[];
  medicalRecordNumber?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
}

export interface ComplianceTimelineItem {
  id: string;
  status: "completed" | "in_progress" | "pending" | "error" | "cancelled" | "not_started" | "overdue";
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  completedByUser?: string;
  title: string;
  type: string;
  requests?: { resourceType: 'Request', id: string }[]
}

export const PatientSchema = BaseResourceSchema.extend({
  id: z.string(),
  email: z.string().email().optional(),
  branch: z.object({
    id: z.string().uuid(),
    resourceType: z.literal('Branch'),
  }).optional(),
  gender: z.string().optional(),
  orders: z.array(z.any()).optional(),
  status: z.enum(['Active', 'Discharged', 'Deceased']).optional(),
  address: z.string().optional(),
  nextIDG: z.string().datetime().optional(),
  lastName: z.string(),
  allergies: z.array(z.string()).optional(),
  firstName: z.string(),
  insurance: z.array(z.any()).optional(),
  planOfCare: z.any().optional(),
  visitNotes: z.array(z.any()).optional(),
  dateOfBirth: z.string(),
  medications: z.array(MedicationSchema).optional(),
  phoneNumber: z.string().optional(),
  resourceType: z.literal('Patient'),
  admissionDate: z.string().optional(),
  agencyActions: z.array(ActionSchema).optional(),
  benefitPeriod: z.string().optional(),
  timeLineItems: z.array(TimelineItemSchema).optional(),
  terminalDiagnosis: z.string().optional(),
  complianceTimeline: z.object({
    timeLineItems: z.array(z.object({
    started: z.string().datetime().optional(),
    blockers: z.array(z.string()).optional(),
    completed: z.string().datetime().optional(),
    description: z.string().optional(),
    displayName: z.string().optional(),
  })).optional(),
  }).optional(),
  comorbidities: z.array(z.object({code: z.string().describe("ICD-10 code"), description: z.string()})).optional(),
  medicalRecordNumber: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
});

