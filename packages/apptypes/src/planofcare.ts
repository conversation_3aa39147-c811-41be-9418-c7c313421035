import { z } from 'zod';
import { BaseResource, BaseResourceSchema } from './resource';

export interface PlanOfCare {
  orders: {
    currentDME?: string;
    pendingOrders?: string;
    suppliesOrdered?: string;
    additionalEquipmentNeeds?: string;
  };
  domains: {
    physical?: {
    dme?: string;
    goals?: string[];
    supplies?: string;
    medications?: string;
    identifiedProblems?: string[];
    plannedInterventions?: string;
    nonPharmacologicMeasures?: string;
  };
    spiritual?: {
    goals?: string[];
    ritualsDiscussed?: string;
    assessmentFindings?: string;
    plannedInterventions?: string;
    externalClergyCollaboration?: boolean;
  };
    functional?: {
    goals?: string[];
    plannedInterventions?: string;
    assistiveDevicesInUse?: string;
    caregiverEducationTopics?: string;
    identifiedFunctionalNeeds?: string[];
    rnSupervisoryVisitFrequency?: string;
  };
    bereavement?: {
    initialPlan?: string;
    riskFactors?: string[];
    followUpPlanned?: boolean;
    bereavementLevel?: 'Low' | 'Moderate' | 'High';
  };
    psychosocial?: {
    goals?: string[];
    supportPlans?: string;
    assessmentFindings?: string;
    plannedInterventions?: string;
  };
  };
  patientInfo: {
    mrn: string;
    patientName: string;
    hospicePhysician?: string;
    terminalDiagnosis: string;
    prognosisSummary: string;
    relatedDiagnoses?: string[];
    dateOfPOCCreation?: string;
    attendingPhysician?: string;
    unrelatedDiagnoses?: string[];
    certificationPeriod: string;
  };
  resourceType: 'PlanOfCare';
  reviewAndSignOff: {
    createdBy?: string;
    creationDate?: string;
    nextIDGReviewDate?: string;
    hospicePhysicianSignOffDate?: string;
  };
  visitFrequencies: {
    RN?: string;
    HHA?: string;
    MSW?: string;
    Other?: string;
    Chaplain?: string;
  };
  educationAndCoordination: {
    familyUnderstandsPlan?: boolean;
    scheduledIDGReviewDate?: string;
    educationTopicsProvided?: string[];
    additionalSupportFromIDG?: string;
  };
}

export const PlanOfCareSchema = z.object({
  orders: z.object({
    currentDME: z.string().optional(),
    pendingOrders: z.string().optional(),
    suppliesOrdered: z.string().optional(),
    additionalEquipmentNeeds: z.string().optional(),
  }),
  domains: z.object({
    physical: z.object({
    dme: z.string().optional(),
    goals: z.array(z.string()).optional(),
    supplies: z.string().optional(),
    medications: z.string().optional(),
    identifiedProblems: z.array(z.string()).optional(),
    plannedInterventions: z.string().optional(),
    nonPharmacologicMeasures: z.string().optional(),
  }).optional(),
    spiritual: z.object({
    goals: z.array(z.string()).optional(),
    ritualsDiscussed: z.string().optional(),
    assessmentFindings: z.string().optional(),
    plannedInterventions: z.string().optional(),
    externalClergyCollaboration: z.boolean().optional(),
  }).optional(),
    functional: z.object({
    goals: z.array(z.string()).optional(),
    plannedInterventions: z.string().optional(),
    assistiveDevicesInUse: z.string().optional(),
    caregiverEducationTopics: z.string().optional(),
    identifiedFunctionalNeeds: z.array(z.string()).optional(),
    rnSupervisoryVisitFrequency: z.string().optional(),
  }).optional(),
    bereavement: z.object({
    initialPlan: z.string().optional(),
    riskFactors: z.array(z.string()).optional(),
    followUpPlanned: z.boolean().optional(),
    bereavementLevel: z.enum(['Low', 'Moderate', 'High']).optional(),
  }).optional(),
    psychosocial: z.object({
    goals: z.array(z.string()).optional(),
    supportPlans: z.string().optional(),
    assessmentFindings: z.string().optional(),
    plannedInterventions: z.string().optional(),
  }).optional(),
  }),
  patientInfo: z.object({
    mrn: z.string(),
    patientName: z.string(),
    hospicePhysician: z.string().optional(),
    terminalDiagnosis: z.string(),
    prognosisSummary: z.string(),
    relatedDiagnoses: z.array(z.string()).optional(),
    dateOfPOCCreation: z.string().optional(),
    attendingPhysician: z.string().optional(),
    unrelatedDiagnoses: z.array(z.string()).optional(),
    certificationPeriod: z.string(),
  }),
  resourceType: z.literal('PlanOfCare'),
  reviewAndSignOff: z.object({
    createdBy: z.string().optional(),
    creationDate: z.string().optional(),
    nextIDGReviewDate: z.string().optional(),
    hospicePhysicianSignOffDate: z.string().optional(),
  }),
  visitFrequencies: z.object({
    RN: z.string().optional(),
    HHA: z.string().optional(),
    MSW: z.string().optional(),
    Other: z.string().optional(),
    Chaplain: z.string().optional(),
  }),
  educationAndCoordination: z.object({
    familyUnderstandsPlan: z.boolean().optional(),
    scheduledIDGReviewDate: z.string().optional(),
    educationTopicsProvided: z.array(z.string()).optional(),
    additionalSupportFromIDG: z.string().optional(),
  }),
});

