import { z } from "zod";
import { BaseResource, BaseResourceSchema } from "./resource";

export type ChangesetItem = {
  op: "replace" | "add" | "remove";
  path: string;
  value: any; // Or a more specific type based on #/definitions/*
  oldValue: any; // Or a more specific type based on #/definitions/*
  reason: string;
  name: string;
  fieldName: string;
  readBackRequired?: boolean;
  readBackPerformed?: boolean;
};

export interface Request extends BaseResource {
  id: string;
  tags?:
    | "visit_note"
    | "order"
    | "poc_update"
    | "demographic_update"
    | "file_upload"[];
  status?:
    | "pending_review"
    | "completed"
    | "error"
    | "waiting_on_note_schema"
    | "processing";
  statusMessage?: string;
  content?: string;
  title?: string;
  patient?: {
    id?: string;
    resourceType?: "Patient";
  };
  hideOnTimeline?: boolean;
  summary?: string;
  responses?: {
    sentBy?: string;
    content?: string;
  }[];
  noteCreated?: {
    id: string;
    resourceType: "Note";
  };
  noteSchemaId?: string;
  orderCreated?: {
    id: string;
    resourceType: "Order";
  };
  resourceType: "Request";
  orderSchemaId?: string;
  parentRequest?: {
    id: string;
    resourceType: "Request";
  };
  transcription?: {
    urlPath: string;
    text: string;
  }[];
  changesetMessages?: any[];
  judgementMessages?: any[];
  fileAttachments?: string[];
  audioAttachments?: string[];
  subdocument?: {
    startPage: number;
    endPage: number;
    type: string;
    confidence: number;
  };
  idgMeeting?: {
    id: string;
    resourceType: "IDGMeeting";
  };
  qa?: string;
  approvedBy?: {
    id: string;
    resourceType: "User";
    name: string;
    date: string;
  };
  madeBy?: {
    id: string;
    resourceType: "User";
    name: string;
    date: string;
  };
  changes?: ChangesetItem[];
  validationErrors?: any[];
  type?: "new_patient";
}
