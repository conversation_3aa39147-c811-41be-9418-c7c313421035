import { z } from "zod";

// Base Resource interface that all resources will implement
export interface BaseResource {
  id: string;
  resourceType: string;
  schemaId: string;
  agencyId?: string;
  updatedAt: string;
  createdAt: string;
}

// Base Resource schema for validation
export const BaseResourceSchema = z.object({
  id: z.string().uuid(),
  resourceType: z.string(),
  schemaId: z.string().uuid(),
  agencyId: z.string().uuid().optional(),
  updatedAt: z.string(),
  createdAt: z.string(),
});

// Type guard to check if an object is a BaseResource
export function isBaseResource(obj: any): obj is BaseResource {
  return (
    typeof obj === "object" &&
    obj !== null &&
    typeof obj.id === "string" &&
    typeof obj.resourceType === "string"
  );
}
