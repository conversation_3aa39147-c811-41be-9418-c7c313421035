import { z } from 'zod';
import { Note, NoteSchema } from './note';

export interface SocialWorkVisitNote extends Note {
  financialConcerns?: string;
  resourcesProvided?: string[];
  caregiverSupportPlan?: string;
  psychosocialAssessment: string;
}

export const SocialWorkVisitNoteSchema = NoteSchema.extend({
  financialConcerns: z.string().optional(),
  resourcesProvided: z.array(z.string()).optional(),
  caregiverSupportPlan: z.string().optional(),
  psychosocialAssessment: z.string(),
});
