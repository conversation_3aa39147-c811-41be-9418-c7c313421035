import { z } from "zod";
import { BaseResource, BaseResourceSchema } from "./resource";

export interface User extends BaseResource {
  id: string;
  role: "<PERSON><PERSON>" | "<PERSON>" | "Social Worker" | "Chaplain" | "Physician" | "Aide" | "Volunteer" | "Surveyor";
  email: string;
  agencyId: string;
  stytchId?: string;
  firstName: string;
  lastName: string;
  resourceType: "User";
}

export const UserSchema = BaseResourceSchema.extend({
  id: z.string().uuid(),
  role: z.enum(["Ad<PERSON>", "Nurse", "Social Worker", "Cha<PERSON>", "Physician", "Aide", "Volunteer", "Surveyor"]),
  email: z.string().email(),
  agencyId: z.string().uuid(),
  firstName: z.string(),
  lastName: z.string(),
  stytchId: z.string().optional(),
  resourceType: z.literal("User"),
});
