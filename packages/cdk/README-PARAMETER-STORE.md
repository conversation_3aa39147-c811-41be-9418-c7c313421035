# Parameter Store Configuration

This document describes how to use AWS Parameter Store for configuration management in the Hospice OS application.

## Overview

The application now uses AWS Systems Manager Parameter Store for managing configuration values and secrets. This approach offers several advantages:

- **Stage-based configuration**: Different values for dev, staging, and production environments
- **Secure storage**: Sensitive values are stored as SecureString parameters
- **Centralized management**: All configuration in one place
- **Versioning**: Parameter Store maintains a history of parameter changes
- **Access control**: Fine-grained IAM permissions for parameter access

## Parameter Structure

Parameters are stored in a hierarchical structure:

```
/hospice-os/{stage}/{parameter-name}
```

For example:
- `/hospice-os/dev/DB_HOST`
- `/hospice-os/prod/STYTCH_PROJECT_ID`

## Configuration Types

There are two types of configuration parameters:

1. **CDK-managed parameters**: Created and managed by the CDK deployment process
   - Database connection information
   - Other infrastructure-related configuration

2. **External parameters**: Managed using the `manage-parameters.ts` script
   - Third-party API keys (e.g., Stytch)
   - Other secrets not managed by CDK

## Usage in Application

The application's `config.ts` module has been updated to:

1. Determine the current stage from the `APP_STAGE` environment variable (defaults to 'dev')
2. Attempt to load parameters from Parameter Store using the stage-based path
3. Fall back to environment variables if Parameter Store access fails

## Managing Parameters

### CDK-Managed Parameters

These are automatically created during CDK deployment. No manual action is required.

### External Parameters

Use the provided script to push external parameters to Parameter Store:

```bash
# Install dependencies
cd packages/cdk
npm install

# Push parameters for dev stage (reads from .env file)
npm run params:push:dev

# Push parameters for other stages
npm run params:push:stage
npm run params:push:prod

# Specify a custom .env file
npm run params:push -- --env-file /path/to/.env --stage dev
```

## Adding New Parameters

### CDK-Managed Parameters

To add a new CDK-managed parameter:

1. Add the parameter creation in `server-stack.ts` using the `createParameterForStage` function:

```typescript
createParameterForStage('PARAMETER_NAME', value, isSecure);
```

2. Update the `config.ts` file to load the parameter:

```typescript
config.section.parameter = await getConfigValue('PARAMETER_NAME', 'ENV_VAR_NAME', defaultValue);
```

### External Parameters

To add a new external parameter:

1. Add the parameter to the `externalSecrets` object in `manage-parameters.ts`:

```typescript
'PARAMETER_NAME': { value: process.env.ENV_VAR_NAME || '', secure: true },
```

2. Update the `config.ts` file to load the parameter:

```typescript
config.section.parameter = await getConfigValue('PARAMETER_NAME', 'ENV_VAR_NAME', defaultValue);
```

## Local Development

For local development, you can continue to use a `.env` file. The application will fall back to environment variables if Parameter Store access fails.

## Troubleshooting

If you encounter issues with Parameter Store:

1. Check AWS credentials and permissions
2. Verify the parameter path is correct
3. Check the application logs for error messages
4. Try accessing the parameters using the AWS CLI:

```bash
aws ssm get-parameter --name "/hospice-os/dev/PARAMETER_NAME" --with-decryption
```
