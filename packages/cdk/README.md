# Hospice OS Infrastructure

This package contains the AWS CDK infrastructure code for deploying the Hospice OS application. The infrastructure is divided into several stacks:

- `InfraStack`: Base infrastructure including VPC and VPC endpoints to avoid NAT gateways
- `StorageStack`: S3 buckets for web and admin UIs, ECR for container images
- `WebStack`: CloudFront distribution for the web app
- `AdminStack`: CloudFront distribution for the admin app
- `ServerStack`: ECS Fargate Spot setup for the server using the Docker image
- `RestateStack`: Lambdas that map to restate workflows and registration with restate cloud orchestrator
- `DeploymentStack`: (Under Construction) CodeBuild/CodeDeploy infrastructure for continuous deployment 

## Prerequisites

- AWS CLI installed and configured with appropriate credentials
- Node.js v18 or later
- Docker installed and running (for server deployments)
- ECR Repo named hospice-os (for the server) with the Docker image pushed to it

## Deploying the Infrastrure

push needed parameters

```bash
npm run params:push:staging
npm run params:push:prod
```

You can deploy all stacks at once or deploy individual stacks as needed:

```bash
# Deploy 
npm run deploy:staging
npm run deploy:prod

# Deploy specific stacks
npm run deploy:infra  # Deploy infrastructure and storage stacks
npm run deploy:web    # Deploy web app stack
npm run deploy:admin  # Deploy admin app stack
npm run deploy:server # Deploy server stack
```

## Deploying the Code

### Deploying the Server

This updates the ECS service with a new Docker image without updating the CloudFormation stack:

```bash
# run these from the project root (same as the DOCKERFILE.server file)

# Run the deployment script for staging
./scripts/deploy-server-staging.sh

# Run the deployment script for prod
./scripts/deploy-server-prod.sh
```

### Deploying the Web App

This builds the web app and deploys it to the S3 bucket, then invalidates the CloudFront cache:

```bash
# Run the deployment script
./scripts/deploy-web-staging.sh

# Run the deployment script for prod
./scripts/deploy-web-prod.sh
```

### Deploying the Admin App

This builds the admin app and deploys it to the S3 bucket, then invalidates the CloudFront cache:

```bash

# Run the deployment script staging
./scripts/deploy-admin-staging.sh

# Run the deployment script for prod
./scripts/deploy-admin-prod.sh
```

### Manual Variable Configuration

If you need to set the variables manually instead of using the initialization script:

```bash
# Server deployment variables
export ECR_REPOSITORY_URI="123456789012.dkr.ecr.us-east-1.amazonaws.com/hospice-os-server-dev"
export ECS_CLUSTER="hospice-os-server-dev"
export ECS_SERVICE="HospiceOS-Server-dev-ServerServiceXXXXXXXX-XXXXXXXXXXXX"
export TASK_DEFINITION_FAMILY="hospice-os-server-dev"

# Web deployment variables
export S3_BUCKET="hospice-os-web-dev-123456789012"
export CLOUDFRONT_DISTRIBUTION_ID="XXXXXXXXXXXXXX"

# Admin deployment variables
export ADMIN_S3_BUCKET="hospice-os-admin-dev-123456789012"
export ADMIN_CLOUDFRONT_DISTRIBUTION_ID="XXXXXXXXXXXXXX"
```

## Architecture

### Database Architecture

The database uses Aurora Serverless v2 for cost optimization and automatic scaling:

- Aurora PostgreSQL 16.1 Serverless v2 for auto-scaling
- Private isolated subnets for security
- Secrets Manager for credential management
- Different capacity settings for development/production environments
- VPC endpoint access for ECS tasks

### Server Architecture

The server runs on ECS Fargate with spot instances for cost optimization. It uses the following architecture:

- Application Load Balancer (ALB) for traffic distribution
- ECS Service with Fargate Spot capacity providers
- Auto-scaling based on CPU and memory usage
- CloudWatch Logs for log collection
- ECR for Docker image storage
- Integration with Aurora Serverless database

### Web/Admin Architecture

The web and admin apps are served via CloudFront with the following architecture:

- S3 bucket for static file storage (not publicly accessible)
- CloudFront distribution with Origin Access Identity for security
- Custom error responses for SPA routing
- Cache optimization for faster content delivery

### VPC Architecture

The VPC is designed to avoid NAT Gateways for cost optimization:

- Private isolated subnets for ECS tasks
- Public subnets for load balancers
- VPC Endpoints for AWS services (S3, ECR, CloudWatch, etc.)

## Configuration Management

The application uses AWS Systems Manager Parameter Store for configuration management. See [PARAMETER-STORE.md](./docs/PARAMETER-STORE.md) for detailed documentation.

### Managing External Parameters

External parameters (like API keys) can be managed using the provided script:

```bash
# Push parameter store params for a specific environment
npm run params:push:staging
npm run params:push:prod

# With custom options
npm run params:push -- --stage staging --region us-west-2 --env-file /path/to/.env
```

## Useful Commands

- `npm run build` - Compile TypeScript to JavaScript
- `npm run watch` - Watch for changes and compile
- `npm run test` - Perform the Jest unit tests
- `npm run cdk diff` - Compare deployed stack with current state
- `npm run cdk synth` - Emit the synthesized CloudFormation template
- `npm run params:push` - Push external parameters to Parameter Store
