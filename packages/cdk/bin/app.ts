#!/usr/bin/env node
import * as cdk from "aws-cdk-lib";
import { InfraStack } from "../lib/infra-stack";
import { StorageStack } from "../lib/storage-stack";
import { WebStack } from "../lib/web-stack";
import { AdminStack } from "../lib/admin-stack";
import { ServerStack } from "../lib/server-stack";
import { DeploymentStack } from "../lib/deployment-stack";
import { DbStack } from "../lib/db-stack";
import { TemporalWorkerStack } from "../lib/temporal-worker-stack";

export interface EnvConfig {
  stage: string;
  accountId: string;
  region: string;
  removalPolicy: cdk.RemovalPolicy;
  webCertificateArn: string;
  adminCertificateArn: string;
  serverCertificateArn: string;
  ecrRepoName: string;
}

const app = new cdk.App();
const stage = app.node.tryGetContext("stage") || "dev"; // Default to 'dev' if not specified
console.log("stage", stage);
const envConfig: EnvConfig = {
  ...app.node.tryGetContext(stage),
  stage,
  removalPolicy:
    stage === "prod" ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
};

const env = {
  account: process.env.CDK_DEFAULT_ACCOUNT || envConfig.accountId,
  region: process.env.CDK_DEFAULT_REGION || envConfig.region || "us-east-1",
};

// Create the infrastructure stack (VPC, Endpoints, etc.)
const infraStack = new InfraStack(app, `hospice-os-infra-${envConfig.stage}`, {
  env,
  crossRegionReferences: true,
  envConfig,
});

// Create the web stack (CloudFront) with the bucket from storage stack
const webStack = new WebStack(app, `hospice-os-web-${envConfig.stage}`, {
  env,
  crossRegionReferences: true,
  envConfig,
  certificateArn: envConfig.webCertificateArn,
});

// Create the admin stack (CloudFront)
const adminStack = new AdminStack(app, `hospice-os-admin-${envConfig.stage}`, {
  env,
  crossRegionReferences: true,
  envConfig,
  certificateArn: envConfig.adminCertificateArn,
});

// Create the storage stack (S3)
const storageStack = new StorageStack(
  app,
  `hospice-os-storage-${envConfig.stage}`,
  {
    env,
    crossRegionReferences: true,
    envConfig,
  },
);

// Create the database stack (Aurora Serverless)
const dbStack = new DbStack(app, `hospice-os-db-${envConfig.stage}`, {
  env,
  crossRegionReferences: true,
  envConfig,
  vpc: infraStack.vpc,
});

// Create the server stack (ECS Fargate)
const serverStack = new ServerStack(
  app,
  `hospice-os-server-${envConfig.stage}`,
  {
    env,
    crossRegionReferences: true,
    envConfig,
    vpc: infraStack.vpc,
    ecrRepoName: envConfig.ecrRepoName,
    uploadBucket: storageStack.uploadBucket,
    dbStack: dbStack,
    certificateArn: envConfig.serverCertificateArn,
  },
);

// Add explicit dependencies between stacks
serverStack.addDependency(dbStack);
serverStack.addDependency(infraStack);
serverStack.addDependency(storageStack);
dbStack.addDependency(infraStack);
webStack.addDependency(storageStack);
adminStack.addDependency(storageStack);

// Create the Temporal Worker stack
const temporalWorkerStack = new TemporalWorkerStack(
  app,
  `hospice-os-temporal-worker-${envConfig.stage}`,
  {
    env,
    crossRegionReferences: true,
    vpc: infraStack.vpc,
    stage: envConfig.stage,
  },
);

// Add dependencies for the Temporal Worker stack
temporalWorkerStack.addDependency(infraStack);
temporalWorkerStack.addDependency(dbStack); // May need database access for activities

// Create the deployment stack (CodeBuild, CodeDeploy)
// const deploymentStack = new DeploymentStack(app, `hospice-os-deployment-${envConfig.stage}`, {
//   env,
//   crossRegionReferences: true,
//   envConfig,
//   webBucket: webStack.webBucket,
//   adminBucket: adminStack.adminBucket,
//   webDistribution: webStack.distribution,
//   adminDistribution: adminStack.distribution,
//   serverService: serverStack.service,
// });

// // Add dependencies for the deployment stack
// deploymentStack.addDependency(webStack);
// deploymentStack.addDependency(adminStack);
// deploymentStack.addDependency(serverStack);
// deploymentStack.addDependency(storageStack);

app.synth();
