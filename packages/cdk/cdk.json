{"app": "npx ts-node --prefer-ts-exts bin/app.ts", "watch": {"include": ["**"], "exclude": ["README.md", "cdk*.json", "**/*.d.ts", "**/*.js", "node_modules", "test"]}, "context": {"@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": true, "@aws-cdk/core:stackRelativeExports": true, "@aws-cdk/aws-rds:lowercaseDbIdentifier": true, "@aws-cdk/aws-lambda:recognizeVersionProps": true, "@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": true, "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"], "dev": {"accountId": "************", "region": "us-east-1", "webCertificateArn": null, "adminCertificateArn": null, "serverCertificateArn": null, "ecrRepoName": "hospice-os", "restateCloudEnvironmentId": "env-test-********-0000-0000-0000-********0000", "restateAuthTokenSecret": "test/restateAuthToken", "externalParameters": {"STYTCH_PROJECT_ID": "project-test-452f27ba-d1b5-46ea-a4e4-f8c0623ec1e1", "STYTCH_SECRET": "secret-test-nA6akAJA1Oh70r-VJhQhVD8g00l0DHcYAdw=", "STYTCH_ENV": "test", "CORS_ALLOWED_ORIGINS": "https://admin-dev.tallio.com,https://app-dev.tallio.com,http://localhost:3001,http://localhost:5174,http://localhost:3000"}}, "staging": {"accountId": "************", "region": "us-east-1", "webCertificateArn": "arn:aws:acm:us-east-1:************:certificate/b3a0d7be-e57e-4b0f-87f4-94284fbdad82", "adminCertificateArn": "arn:aws:acm:us-east-1:************:certificate/b3a0d7be-e57e-4b0f-87f4-94284fbdad82", "serverCertificateArn": "arn:aws:acm:us-east-1:************:certificate/b3a0d7be-e57e-4b0f-87f4-94284fbdad82", "ecrRepoName": "hospice-os", "restateCloudEnvironmentId": "env_201js0bqq1sz6689d888rh7823f", "restateAuthTokenSecret": "staging/restateAuthToken", "externalParameters": {"STYTCH_PROJECT_ID": "project-test-452f27ba-d1b5-46ea-a4e4-f8c0623ec1e1", "STYTCH_SECRET": "secret-test-nA6akAJA1Oh70r-VJhQhVD8g00l0DHcYAdw=", "STYTCH_ENV": "test", "CORS_ALLOWED_ORIGINS": "https://admin-staging.tallio.com,https://app-staging.tallio.com,http://localhost:3001,http://localhost:5174,http://localhost:3000"}}, "prod": {"accountId": "************", "region": "us-east-1", "webCertificateArn": "arn:aws:acm:us-east-1:************:certificate/87c397c8-4ba3-449d-91b1-5cc1e917eab1", "adminCertificateArn": "arn:aws:acm:us-east-1:************:certificate/87c397c8-4ba3-449d-91b1-5cc1e917eab1", "serverCertificateArn": "arn:aws:acm:us-east-1:************:certificate/87c397c8-4ba3-449d-91b1-5cc1e917eab1", "ecrRepoName": "hospice-os", "restateCloudEnvironmentId": "env_201jtw21v35j9s86dt8h890m6n2", "restateAuthTokenSecret": "prod/restateAuthToken", "externalParameters": {"STYTCH_PROJECT_ID": "project-live-7ccd407e-60ad-4c77-a4ad-376de2a3a945", "STYTCH_SECRET": "************************************************", "STYTCH_ENV": "live", "CORS_ALLOWED_ORIGINS": "https://admin.tallio.com,https://app.tallio.com"}}}}