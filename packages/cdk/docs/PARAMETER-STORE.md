# Parameter Store Configuration

This document explains how the Hospice OS application uses AWS Systems Manager Parameter Store for configuration management.

## Overview

The application now uses AWS Parameter Store for storing configuration values and secrets with these key features:

1. **Stage-based Configuration**: Different values for dev, staging, and production environments
2. **Automatic Secret References**: Database credentials (username/password) are dynamically referenced from Secrets Manager
3. **External Parameter Management**: Third-party API credentials stored in Parameter Store via a dedicated script
4. **Consistent Access Pattern**: All configuration is retrieved using the same path pattern in the application

## Parameter Structure

All parameters follow this hierarchical structure:

```
/hospice-os/{stage}/{parameter-name}
```

Examples:
- `/hospice-os/dev/DB_HOST`
- `/hospice-os/prod/STYTCH_PROJECT_ID`

## Configuration Types

There are three types of configuration parameters:

1. **CDK-created Parameters**: Infrastructure values managed directly by CDK
   - Database connection info (DB_HOST, DB_PORT, DB_NAME)
   - Other infrastructure-related configuration

2. **Secret References**: Secure credential references managed by CDK
   - Database credentials (DB_USER, DB_PASSWORD) that reference Secrets Manager
   - Enables automatic credential rotation

3. **External Parameters**: Values managed using the `manage-parameters.ts` script
   - API keys (e.g., Stytch)
   - Other service credentials not created by CDK

## How It Works

### Parameter Creation

Parameters are created through two mechanisms:

#### 1. CDK Deployment
During CDK deployment, the server-stack.ts creates:
- Regular string parameters for infrastructure values
- Dynamic secret references for DB credentials using the syntax:
  ```
  {{resolve:secretsmanager:SECRET_ARN:SecretString:username}}
  ```

#### 2. Manage Parameters Script
The `manage-parameters.ts` script manages external values:
- Reads configuration from cdk.json based on stage
- Falls back to environment variables if needed
- Runs during deployment via the deploy-server.sh script

### Parameter Retrieval

The application's `config.ts` module retrieves parameters with this logic:
1. Determine the current stage from the `APP_STAGE` environment variable
2. Construct parameter paths with the structure `/hospice-os/{stage}/{param-name}`
3. Try to retrieve from Parameter Store
4. Fall back to environment variables if Parameter Store access fails

## Usage Guide

### Managing CDK-Created Parameters

These are automatically created as part of the CDK deployment. No manual action is required.

### Managing External Parameters

#### Configuration in cdk.json

Add stage-specific values in `cdk.json`:

```json
{
  "context": {
    "dev": {
      "externalParameters": {
        "STYTCH_PROJECT_ID": "project-dev-123",
        "STYTCH_SECRET": "secret-dev-456"
      }
    },
    "prod": {
      "externalParameters": {
        "STYTCH_PROJECT_ID": "project-prod-789",
        "STYTCH_SECRET": "secret-prod-012"
      }
    }
  }
}
```

#### Manual Parameter Management

Use the provided script:

```bash
# From packages/cdk directory
npm run params:push:dev   # For dev stage
npm run params:push:stage # For stage stage
npm run params:push:prod  # For prod stage

# With custom options
npm run params:push -- --stage dev --region us-west-2 --env-file /custom/path/.env
```

### Adding New Parameters

#### CDK-Managed Parameters

1. Add parameter creation in `server-stack.ts`:
   ```typescript
   createParameterForStage('NEW_PARAM_NAME', value, secure);
   ```

2. Update `config.ts` to load it:
   ```typescript
   config.section.param = await getConfigValue('NEW_PARAM_NAME', 'ENV_VAR_NAME', defaultValue);
   ```

#### External Parameters

1. Add to `cdk.json` under the appropriate stage:
   ```json
   "dev": {
     "externalParameters": {
       "NEW_PARAM_NAME": "value-for-dev"
     }
   }
   ```

2. Update `config.ts` to load it:
   ```typescript
   config.section.param = await getConfigValue('NEW_PARAM_NAME', 'ENV_VAR_NAME', defaultValue);
   ```

## Local Development

For local development:

1. Use a `.env` file with environment variables
2. Set `APP_STAGE=dev` to use the dev stage parameters
3. The app will fall back to environment variables if Parameter Store access fails

## Advanced: Secrets Manager References

Database credentials use dynamic references to Secrets Manager:

```typescript
new ssm.StringParameter(this, `Param-DB_USER-Ref`, {
  parameterName: `/hospice-os/${props.envConfig.stage}/DB_USER`,
  stringValue: `{{resolve:secretsmanager:${secretArn}:SecretString:username}}`,
});
```

This enables:
- Automatic credential rotation
- Consistent parameter access pattern
- Secure storage in Secrets Manager
