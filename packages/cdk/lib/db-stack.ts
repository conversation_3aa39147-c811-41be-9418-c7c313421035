import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as rds from "aws-cdk-lib/aws-rds";
import { Construct } from "constructs";
import { EnvConfig } from "../bin/app";

export interface DbStackProps extends cdk.StackProps {
  envConfig: EnvConfig;
  vpc: ec2.Vpc;
}

const POSTGRESQL_PORT = 5432;

export class DbStack extends cdk.Stack {
  public readonly clusterIdentifier: string;
  public readonly databaseName: string;
  public readonly cluster: rds.DatabaseCluster;
  public readonly securityGroup: ec2.SecurityGroup;
  public readonly proxy: rds.DatabaseProxy;
  public readonly proxySecurityGroup: ec2.SecurityGroup;

  constructor(scope: Construct, id: string, props: DbStackProps) {
    super(scope, id, props);

    // Define cluster configuration based on environment
    let clusterProps: any = {};
    const preferredMaintenanceWindow = "Sun:07:00-Sun:08:00"; // UTC - 3-4AM EST

    if (props.envConfig.stage === "prod") {
      clusterProps = {
        serverlessV2MinCapacity: 1,
        serverlessV2MaxCapacity: 16, // equivalent to 32gb
        deletionProtection: true,
        backup: {
          retention: cdk.Duration.days(35),
        },
        preferredMaintenanceWindow,
      };
    } else {
      clusterProps = {
        serverlessV2MinCapacity: 0,
        serverlessV2MaxCapacity: 2,
        backup: {
          retention: cdk.Duration.days(1),
        },
        preferredMaintenanceWindow,
      };
    }

    // Define database name and cluster identifier
    const defaultDatabaseName = `hospiceos`;
    const clusterIdentifier = `hospice-os-${props.envConfig.stage}`;

    // Create security group for RDS
    this.securityGroup = new ec2.SecurityGroup(
      this,
      `RdsSecurityGroup-${props.envConfig.stage}`,
      {
        vpc: props.vpc,
        securityGroupName: `rds-security-group-${props.envConfig.stage}`,
        description: `RDS Security Group - ${props.envConfig.stage}`,
        allowAllOutbound: true,
      },
    );

    // Allow PostgreSQL traffic from the VPC CIDR
    this.securityGroup.addIngressRule(
      ec2.Peer.ipv4(props.vpc.vpcCidrBlock),
      ec2.Port.tcp(POSTGRESQL_PORT),
      "Allow PostgreSQL traffic from within the VPC",
    );

    // Create Aurora Serverless v2 cluster
    this.cluster = new rds.DatabaseCluster(
      this,
      `AuroraServerlessCluster-${props.envConfig.stage}`,
      {
        engine: rds.DatabaseClusterEngine.auroraPostgres({
          version: rds.AuroraPostgresEngineVersion.VER_16_6,
        }),
        vpc: props.vpc,
        vpcSubnets: {
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        securityGroups: [this.securityGroup],
        credentials: {
          username: `hospiceos${props.envConfig.stage}`,
        },
        clusterIdentifier: clusterIdentifier,
        defaultDatabaseName: defaultDatabaseName,
        writer: rds.ClusterInstance.serverlessV2("writer"),
        enableDataApi: true,
        storageEncrypted: true,
        removalPolicy: props.envConfig.removalPolicy,
        ...clusterProps,
      },
    );

    // Create security group for RDS Proxy
    this.proxySecurityGroup = new ec2.SecurityGroup(
      this,
      `RdsProxySecurityGroup-${props.envConfig.stage}`,
      {
        vpc: props.vpc,
        securityGroupName: `rds-proxy-security-group-${props.envConfig.stage}`,
        description: `RDS Proxy Security Group - ${props.envConfig.stage}`,
        allowAllOutbound: true,
      },
    );

    // Allow PostgreSQL traffic from the VPC CIDR to the proxy
    this.proxySecurityGroup.addIngressRule(
      ec2.Peer.ipv4(props.vpc.vpcCidrBlock),
      ec2.Port.tcp(POSTGRESQL_PORT),
      "Allow PostgreSQL traffic from within the VPC to RDS Proxy",
    );

    // Allow the proxy to connect to the database
    this.securityGroup.addIngressRule(
      this.proxySecurityGroup,
      ec2.Port.tcp(POSTGRESQL_PORT),
      "Allow RDS Proxy to connect to Aurora cluster",
    );

    // Create RDS Proxy
    this.proxy = new rds.DatabaseProxy(
      this,
      `RdsProxy-${props.envConfig.stage}`,
      {
        proxyTarget: rds.ProxyTarget.fromCluster(this.cluster),
        secrets: [this.cluster.secret!],
        vpc: props.vpc,
        vpcSubnets: {
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        securityGroups: [this.proxySecurityGroup],
        dbProxyName: `hospice-os-proxy-${props.envConfig.stage}`,
        requireTLS: true,
        maxConnectionsPercent: 100,
        maxIdleConnectionsPercent: 50,
        idleClientTimeout: cdk.Duration.minutes(30),
      },
    );

    // Set class properties for external access
    this.clusterIdentifier = clusterIdentifier;
    this.databaseName = defaultDatabaseName;

    // Add secret rotation if needed
    // this.cluster.addRotationSingleUser();

    // Create outputs
    new cdk.CfnOutput(this, "DbClusterIdentifier", {
      value: this.clusterIdentifier,
      description: "The identifier of the Aurora cluster",
      exportName: `HospiceOS-${props.envConfig.stage}-DbClusterIdentifier`,
    });

    new cdk.CfnOutput(this, "DbName", {
      value: this.databaseName,
      description: "The name of the default database",
      exportName: `HospiceOS-${props.envConfig.stage}-DbName`,
    });

    new cdk.CfnOutput(this, "DbEndpoint", {
      value: this.cluster.clusterEndpoint.hostname,
      description: "The endpoint of the Aurora cluster",
      exportName: `HospiceOS-${props.envConfig.stage}-DbEndpoint`,
    });

    new cdk.CfnOutput(this, "DbSecretArn", {
      value: this.cluster.secret?.secretArn || "No secret created",
      description: "The ARN of the database credentials secret",
      exportName: `HospiceOS-${props.envConfig.stage}-DbSecretArn`,
    });

    new cdk.CfnOutput(this, "DbProxyEndpoint", {
      value: this.proxy.endpoint,
      description: "The endpoint of the RDS Proxy",
      exportName: `HospiceOS-${props.envConfig.stage}-DbProxyEndpoint`,
    });

    new cdk.CfnOutput(this, "DbProxyArn", {
      value: this.proxy.dbProxyArn,
      description: "The ARN of the RDS Proxy",
      exportName: `HospiceOS-${props.envConfig.stage}-DbProxyArn`,
    });
  }
}
