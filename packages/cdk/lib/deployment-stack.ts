import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { Construct } from 'constructs';
import { EnvConfig } from '../bin/app';

export interface DeploymentStackProps extends cdk.StackProps {
  envConfig: EnvConfig;
  webBucket: s3.Bucket;
  adminBucket: s3.Bucket;
  webDistribution: cloudfront.Distribution;
  adminDistribution: cloudfront.Distribution;
  serverService: ecs.FargateService;
}

export class DeploymentStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: DeploymentStackProps) {
    super(scope, id, props);

    // Create an IAM role for CodeBuild projects
    const codeBuildRole = new iam.Role(this, 'CodeBuildServiceRole', {
      assumedBy: new iam.ServicePrincipal('codebuild.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonECR-FullAccess'),
        iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonS3FullAccess'),
      ],
    });

    // Add specific permissions for CloudFront and ECS
    codeBuildRole.addToPrincipalPolicy(
      new iam.PolicyStatement({
        actions: [
          'cloudfront:CreateInvalidation',
          'ecs:UpdateService',
          'ecs:DescribeServices',
          'ecs:RegisterTaskDefinition',
          'ecs:DescribeTaskDefinition',
          'iam:PassRole',
        ],
        resources: ['*'], // Scope this down in production
      })
    );

    // Create a CodeBuild project for server deployment
    const serverDeployProject = new codebuild.Project(this, 'ServerDeployProject', {
      projectName: `hospice-os-server-deploy-${props.envConfig.stage}`,
      description: `Deploy server for Hospice OS ${props.envConfig.stage}`,
      environment: {
        buildImage: codebuild.LinuxBuildImage.AMAZON_LINUX_2_4,
        privileged: true, // Required to run Docker
      },
      environmentVariables: {
        ECR_REPOSITORY_NAME: {
          value: 'hospice-os',
        },
        ECS_CLUSTER: {
          value: props.serverService.cluster.clusterName,
        },
        ECS_SERVICE: {
          value: props.serverService.serviceName,
        },
        TASK_DEFINITION_FAMILY: {
          value: props.serverService.taskDefinition.family,
        },
        CONTAINER_NAME: {
          value: 'ServerContainer',
        },
        STAGE: {
          value: props.envConfig.stage,
        },
      },
      role: codeBuildRole,
      buildSpec: codebuild.BuildSpec.fromObject({
        version: '0.2',
        phases: {
          pre_build: {
            commands: [
              'echo Logging in to Amazon ECR...',
              'aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI',
              'COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)',
              'IMAGE_TAG=${COMMIT_HASH:=latest}',
            ],
          },
          build: {
            commands: [
              'echo Building the Docker image...',
              'docker build -f DOCKERFILE.server -t $ECR_REPOSITORY_URI:$IMAGE_TAG -t $ECR_REPOSITORY_URI:latest .',
              'docker push $ECR_REPOSITORY_URI:$IMAGE_TAG',
              'docker push $ECR_REPOSITORY_URI:latest',
            ],
          },
          post_build: {
            commands: [
              'echo Deploying to ECS without updating CloudFormation...',
              // Get current task definition
              'TASK_DEF=$(aws ecs describe-task-definition --task-definition $TASK_DEFINITION_FAMILY --query "taskDefinition" --output json)',
              // Create new task definition with new image
              'NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE "$ECR_REPOSITORY_URI:$IMAGE_TAG" \'.containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)\')',
              'echo "$NEW_TASK_DEF" > new-task-def.json',
              // Register new task definition
              'NEW_TASK_DEF_ARN=$(aws ecs register-task-definition --cli-input-json file://new-task-def.json --query "taskDefinition.taskDefinitionArn" --output text)',
              // Update service to use new task definition
              'aws ecs update-service --cluster $ECS_CLUSTER --service $ECS_SERVICE --task-definition $NEW_TASK_DEF_ARN --force-new-deployment',
              'echo Deployment completed successfully!',
            ],
          },
        },
        artifacts: {
          files: ['new-task-def.json'],
        },
      }),
    });

    // Create a CodeBuild project for web app deployment
    const webDeployProject = new codebuild.Project(this, 'WebDeployProject', {
      projectName: `hospice-os-web-deploy-${props.envConfig.stage}`,
      description: `Deploy web app for Hospice OS ${props.envConfig.stage}`,
      environment: {
        buildImage: codebuild.LinuxBuildImage.STANDARD_6_0,
      },
      environmentVariables: {
        S3_BUCKET: {
          value: props.webBucket.bucketName,
        },
        CLOUDFRONT_DISTRIBUTION_ID: {
          value: props.webDistribution.distributionId,
        },
        STAGE: {
          value: props.envConfig.stage,
        },
      },
      role: codeBuildRole,
      buildSpec: codebuild.BuildSpec.fromObject({
        version: '0.2',
        phases: {
          pre_build: {
            commands: [
              'echo Installing dependencies...',
              'npm ci',
            ],
          },
          build: {
            commands: [
              'echo Building web application...',
              'npm run build -w @hospice-os/web',
            ],
          },
          post_build: {
            commands: [
              'echo Deploying to S3...',
              'aws s3 sync apps/web/dist/ s3://$S3_BUCKET/ --delete',
              'echo Creating CloudFront invalidation...',
              'aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"',
              'echo Web deployment completed successfully!',
            ],
          },
        },
      }),
    });

    // Create a CodeBuild project for admin app deployment
    const adminDeployProject = new codebuild.Project(this, 'AdminDeployProject', {
      projectName: `hospice-os-admin-deploy-${props.envConfig.stage}`,
      description: `Deploy admin app for Hospice OS ${props.envConfig.stage}`,
      environment: {
        buildImage: codebuild.LinuxBuildImage.STANDARD_6_0,
      },
      environmentVariables: {
        S3_BUCKET: {
          value: props.adminBucket.bucketName,
        },
        CLOUDFRONT_DISTRIBUTION_ID: {
          value: props.adminDistribution.distributionId,
        },
        STAGE: {
          value: props.envConfig.stage,
        },
      },
      role: codeBuildRole,
      buildSpec: codebuild.BuildSpec.fromObject({
        version: '0.2',
        phases: {
          pre_build: {
            commands: [
              'echo Installing dependencies...',
              'npm ci',
            ],
          },
          build: {
            commands: [
              'echo Building admin application...',
              'npm run build -w @hospice-os/admin',
            ],
          },
          post_build: {
            commands: [
              'echo Deploying to S3...',
              'aws s3 sync apps/admin/dist/ s3://$S3_BUCKET/ --delete',
              'echo Creating CloudFront invalidation...',
              'aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"',
              'echo Admin deployment completed successfully!',
            ],
          },
        },
      }),
    });

    // Create deployment scripts
    this.createDeploymentScripts(props);

    // Outputs
    new cdk.CfnOutput(this, 'ServerDeployProjectName', {
      value: serverDeployProject.projectName,
      description: 'The name of the server deployment CodeBuild project',
      exportName: `HospiceOS-${props.envConfig.stage}-ServerDeployProjectName`,
    });

    new cdk.CfnOutput(this, 'WebDeployProjectName', {
      value: webDeployProject.projectName,
      description: 'The name of the web deployment CodeBuild project',
      exportName: `HospiceOS-${props.envConfig.stage}-WebDeployProjectName`,
    });

    new cdk.CfnOutput(this, 'AdminDeployProjectName', {
      value: adminDeployProject.projectName,
      description: 'The name of the admin deployment CodeBuild project',
      exportName: `HospiceOS-${props.envConfig.stage}-AdminDeployProjectName`,
    });
  }

  /**
   * Create deployment scripts for manual deployment
   */
  private createDeploymentScripts(props: DeploymentStackProps) {
    // This is a CDK asset that will be copied to the cdk.out directory
    // We're using this as a way to generate deployment scripts
    
    // Server deployment script
    new cdk.CfnOutput(this, 'ServerDeployScript', {
      value: `
#!/bin/bash
# Script to deploy server without updating CloudFormation
# Save this file as deploy-server.sh

set -e

STAGE="${props.envConfig.stage}"
ECR_REPOSITORY_NAME="${props.envConfig.ecrRepoName}"
ECS_CLUSTER="${props.serverService.cluster.clusterName}"
ECS_SERVICE="${props.serverService.serviceName}"
TASK_DEFINITION_FAMILY="${props.serverService.taskDefinition.family}"
CONTAINER_NAME="ServerContainer"

# Build and push Docker image
echo "getting repo uri"
ECR_REPOSITORY_URI=$(aws ecr describe-repositories --repository-names $ECR_REPOSITORY_NAME --query "repositories[0].repositoryUri" --output text)
echo "Building Docker image..."
docker build -f DOCKERFILE.server -t $ECR_REPOSITORY_URI:latest .
echo "Pushing to ECR..."
aws ecr get-login-password --region $(aws configure get region) | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI
docker push $ECR_REPOSITORY_URI:latest

# Get current task definition
echo "Getting current task definition..."
TASK_DEF=$(aws ecs describe-task-definition --task-definition $TASK_DEFINITION_FAMILY --query "taskDefinition" --output json)

# Create new task definition with new image
echo "Creating new task definition..."
NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE "$ECR_REPOSITORY_URI:latest" '.containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
echo "$NEW_TASK_DEF" > new-task-def.json

# Register new task definition
echo "Registering new task definition..."
NEW_TASK_DEF_ARN=$(aws ecs register-task-definition --cli-input-json file://new-task-def.json --query "taskDefinition.taskDefinitionArn" --output text)

# Update service to use new task definition
echo "Updating ECS service..."
aws ecs update-service --cluster $ECS_CLUSTER --service $ECS_SERVICE --task-definition $NEW_TASK_DEF_ARN --force-new-deployment

echo "Deployment completed. Service is being updated with the new task definition."
      `,
      description: 'Server deployment script',
    });

    // Web deployment script
    new cdk.CfnOutput(this, 'WebDeployScript', {
      value: `
#!/bin/bash
# Script to deploy web app without updating CloudFormation
# Save this file as deploy-web.sh

set -e

STAGE="${props.envConfig.stage}"
S3_BUCKET="${props.webBucket.bucketName}"
CLOUDFRONT_DISTRIBUTION_ID="${props.webDistribution.distributionId}"

# Build the web app
echo "Building web app..."
npm run build -w @hospice-os/web

# Deploy to S3
echo "Deploying to S3..."
aws s3 sync apps/web/dist/ s3://$S3_BUCKET/ --delete

# Create CloudFront invalidation
echo "Creating CloudFront invalidation..."
aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"

echo "Web deployment completed."
      `,
      description: 'Web deployment script',
    });

    // Admin deployment script
    new cdk.CfnOutput(this, 'AdminDeployScript', {
      value: `
#!/bin/bash
# Script to deploy admin app without updating CloudFormation
# Save this file as deploy-admin.sh

set -e

STAGE="${props.envConfig.stage}"
S3_BUCKET="${props.adminBucket.bucketName}"
CLOUDFRONT_DISTRIBUTION_ID="${props.adminDistribution.distributionId}"

# Build the admin app
echo "Building admin app..."
npm run build -w @hospice-os/admin

# Deploy to S3
echo "Deploying to S3..."
aws s3 sync apps/admin/dist/ s3://$S3_BUCKET/ --delete

# Create CloudFront invalidation
echo "Creating CloudFront invalidation..."
aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"

echo "Admin deployment completed."
      `,
      description: 'Admin deployment script',
    });
  }
}
