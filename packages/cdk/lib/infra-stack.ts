import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { EnvConfig } from '../bin/app';

export interface InfraStackProps extends cdk.StackProps {
  envConfig: EnvConfig;
}

export class InfraStack extends cdk.Stack {
  public readonly vpc: ec2.Vpc;

  constructor(scope: Construct, id: string, props: InfraStackProps) {
    super(scope, id, props);

    // Create VPC with isolated subnets (1 NAT Gateways)
    this.vpc = new ec2.Vpc(this, 'HospiceOSVpc', {
      maxAzs: 2,
      natGateways: 1,
      cidr: '10.0.0.0/16',
      subnetConfiguration: [
        {
          name: 'isolated',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
          cidrMask: 24,
        },
        {
          name: 'private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
          cidrMask: 22,
        },
        {
          name: 'public',
          subnetType: ec2.SubnetType.PUBLIC,
          cidrMask: 24,
        },
      ],
    });

    // Create security group for VPC endpoints
    // const endpointSecurityGroup = new ec2.SecurityGroup(this, 'EndpointSecurityGroup', {
    //   vpc: this.vpc,
    //   description: 'Security group for VPC Endpoints',
    //   allowAllOutbound: true,
    // });

    // endpointSecurityGroup.addIngressRule(
    //   ec2.Peer.ipv4(this.vpc.vpcCidrBlock),
    //   ec2.Port.allTcp(),
    //   'Allow all TCP traffic from within the VPC'
    // );

    // // Add VPC Endpoints to avoid NAT Gateways
    // // ECR Endpoints
    // this.vpc.addInterfaceEndpoint('EcrDockerEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.ECR_DOCKER,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // this.vpc.addInterfaceEndpoint('EcrEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.ECR,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // // S3 Gateway Endpoint (for ECR to pull images)
    // this.vpc.addGatewayEndpoint('S3Endpoint', {
    //   service: ec2.GatewayVpcEndpointAwsService.S3,
    // });

    // // CloudWatch Logs Endpoint
    // this.vpc.addInterfaceEndpoint('LogsEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // // Secrets Manager Endpoint
    // this.vpc.addInterfaceEndpoint('SecretsManagerEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.SECRETS_MANAGER,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // // SSM Endpoints
    // this.vpc.addInterfaceEndpoint('SsmEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.SSM,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // this.vpc.addInterfaceEndpoint('SsmMessagesEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.SSM_MESSAGES,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // // STS Endpoint
    // this.vpc.addInterfaceEndpoint('StsEndpoint', {
    //   service: ec2.InterfaceVpcEndpointAwsService.STS,
    //   securityGroups: [endpointSecurityGroup],
    //   privateDnsEnabled: true,
    // });

    // Output the VPC ID
    new cdk.CfnOutput(this, 'VpcId', {
      value: this.vpc.vpcId,
      description: 'The ID of the VPC',
      exportName: `HospiceOS-${props.envConfig.stage}-VpcId`,
    });
  }
}
