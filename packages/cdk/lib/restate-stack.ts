// import * as cdk from "aws-cdk-lib";
// import * as secrets_manager from "aws-cdk-lib/aws-secretsmanager";
// import * as iam from "aws-cdk-lib/aws-iam";
// import * as logs from "aws-cdk-lib/aws-logs";
// import { Construct } from "constructs";
// import * as restate from "@restatedev/restate-cdk";
// import * as lambda from "aws-cdk-lib/aws-lambda";
// import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
// import * as ec2 from "aws-cdk-lib/aws-ec2";
// import * as ssm from "aws-cdk-lib/aws-ssm";

// export interface RestateStackProps extends cdk.StackProps {
//   restateCloudEnvironmentId?: string;
//   deploySelfHostedRestateEnvironment?: boolean;
//   authTokenSecretName?: string;
//   vpc: ec2.Vpc;
//   stage: string;
//   region: string;
// }

// export class RestateStack extends cdk.Stack {
//   constructor(scope: Construct, id: string, props?: RestateStackProps) {
//     super(scope, id, props);
//     if (!props?.vpc) {
//       throw new Error("vpc is required");
//     }

//     // Utility function to create SSM parameters
//     const createParameterForStage = (
//       name: string,
//       value: string,
//       secure: boolean = false,
//     ) => {
//       return new ssm.StringParameter(this, `Param-${name}`, {
//         parameterName: `/hospice-os/${props.stage}/${name}`,
//         stringValue: value,
//         tier: secure ? ssm.ParameterTier.STANDARD : ssm.ParameterTier.STANDARD,
//       });
//     };

//     const restateServices = lambdaHandler(
//       this,
//       "RestateDBServices",
//       "../../apps/server/src/restate-services/index.ts",
//       {
//         NODE_ENV: "production",
//         APP_STAGE: props.stage, // Add stage for Parameter Store path construction
//       },
//       props?.vpc,
//     );

//     let restateEnvironment;
//     if (props?.restateCloudEnvironmentId) {
//       const authToken = authTokenSecret(this, props);
//       // This construct automatically creates an invoker role that Restate Cloud will be able to assume to invoke handlers
//       // on behalf of your environment. See https://docs.restate.dev/deploy/cloud for more information.
//       restateEnvironment = new restate.RestateCloudEnvironment(
//         this,
//         "RestateCloud",
//         {
//           environmentId:
//             props.restateCloudEnvironmentId as restate.EnvironmentId,
//           apiKey: authToken,
//         },
//       );

//       createParameterForStage(
//         "RESTATE_INGRESS_URL",
//         restateEnvironment.ingressUrl + ":8080",
//       );
//       createParameterForStage(
//         "RESTATE_CLOUD_ENVIRONMENT_ID",
//         props.restateCloudEnvironmentId,
//       );
//       createParameterForStage(
//         "RESTATE_CLOUD_API_KEY",
//         authToken.secretValue.unsafeUnwrap(),
//         true,
//       );
//     } else if (props?.deploySelfHostedRestateEnvironment) {
//       restateEnvironment = new restate.SingleNodeRestateDeployment(
//         this,
//         "HospiceOSWorkflowEnvironment",
//         {
//           ...props,
//           restateTag: "1.0.1",
//           tracing: restate.TracingMode.AWS_XRAY,
//           logGroup: new logs.LogGroup(this, "RestateLogGroup", {
//             logGroupName: "/restate-hospice-os/restate-server",
//             removalPolicy: cdk.RemovalPolicy.DESTROY, // Set to RETAIN if you'd prefer to keep the logs after stack deletion
//             retention: logs.RetentionDays.ONE_MONTH,
//           }),
//         },
//       );

//       createParameterForStage(
//         "RESTATE_INGRESS_URL",
//         restateEnvironment.ingressUrl,
//       );
//       new cdk.CfnOutput(this, "RestateIngressUrl", {
//         value: restateEnvironment.ingressUrl,
//       });
//     } else {
//       throw new Error(
//         "Either restateCloudEnvironmentId or deploySelfHostedRestateEnvironment must be set.",
//       );
//     }

//     const deployer = new restate.ServiceDeployer(
//       this,
//       "HospiceOSWorkflowDeployer",
//     );
//     // even though the first param is "transcription-workflow", we are deploying all of the services in this lambda
//     // restate only requires one of the names to match.
//     // https://docs.restate.dev/deploy/services#deploying-services

//     //TODO-MG: remove services/workflows that are no longer defined in the restate-services/index.ts file

//     deployer.deployService(
//       "patient-workflow",
//       restateServices.currentVersion,
//       restateEnvironment,
//       {
//         configurationVersion: new Date().toISOString(),
//       },
//     );
//     new cdk.CfnOutput(this, "restateIngressUrl", {
//       value: restateEnvironment.ingressUrl,
//     });
//   }
// }

// function lambdaHandler(
//   scope: Construct,
//   id: string,
//   handler: string,
//   environment: { [key: string]: string },
//   vpc: ec2.Vpc,
// ) {
//   return new NodejsFunction(scope, id, {
//     currentVersionOptions: {
//       removalPolicy: cdk.RemovalPolicy.RETAIN,
//     },
//     runtime: lambda.Runtime.NODEJS_20_X,
//     entry: handler,
//     architecture: lambda.Architecture.ARM_64,
//     memorySize: 512,
//     timeout: cdk.Duration.minutes(15),
//     bundling: {
//       minify: true,
//       sourceMap: true,
//       loader: {
//         ".node": "file",
//       },
//     },
//     // layers: [
//     //   lambda.LayerVersion.fromLayerVersionArn(scope, "ImageMagickLayer", "arn:aws:serverlessrepo:us-east-1:145266761615:applications/image-magick-lambda-layer"),
//     // ],
//     environment: {
//       NODE_OPTIONS: "--enable-source-maps",
//       ...environment,
//     },
//     vpc: vpc,
//     vpcSubnets: {
//       subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
//     },
//     initialPolicy: [
//       new iam.PolicyStatement({
//         effect: iam.Effect.ALLOW,
//         actions: [
//           "secretsmanager:GetSecretValue",
//           "secretsmanager:DescribeSecret",
//           "ssm:GetParameter",
//           "ssm:GetParameters",
//           "ssm:GetParametersByPath",
//         ],
//         resources: ["*"], // Scope this down in production
//       }),
//       new iam.PolicyStatement({
//         effect: iam.Effect.ALLOW,
//         actions: [
//           "s3:PutObject",
//           "s3:GetObject",
//           "s3:DeleteObject",
//           "s3:ListBucket",
//         ],
//         resources: ["*"],
//       }),
//     ],
//   });
// }

// function authTokenSecret(scope: Construct, props: RestateStackProps) {
//   if (!props.authTokenSecretName) {
//     throw new Error("authTokenSecretName is required");
//   }
//   return secrets_manager.Secret.fromSecretNameV2(
//     scope,
//     "AuthToken",
//     props.authTokenSecretName,
//   );
// }
