import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as ecr from "aws-cdk-lib/aws-ecr";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";
import * as ssm from "aws-cdk-lib/aws-ssm";
import * as elasticloadbalancingv2 from "aws-cdk-lib/aws-elasticloadbalancingv2";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as servicediscovery from "aws-cdk-lib/aws-servicediscovery";
import { Construct } from "constructs";
import { EnvConfig } from "../bin/app";

import { DbStack } from "./db-stack";

// Constants
const POSTGRESQL_PORT = 5432;

export interface ServerStackProps extends cdk.StackProps {
  envConfig: EnvConfig;
  vpc: ec2.Vpc;
  ecrRepoName: string;
  dbStack?: DbStack; // Make it optional to maintain backward compatibility
  uploadBucket: s3.Bucket;
  domainName?: string;
  certificateArn?: string;
}

export class ServerStack extends cdk.Stack {
  public readonly cluster: ecs.Cluster;
  public readonly service: ecs.FargateService;
  public readonly taskDefinition: ecs.FargateTaskDefinition;
  public readonly loadBalancer: elasticloadbalancingv2.ApplicationLoadBalancer;

  constructor(scope: Construct, id: string, props: ServerStackProps) {
    super(scope, id, props);

    // Create ECS Cluster
    this.cluster = new ecs.Cluster(this, "ServerCluster", {
      vpc: props.vpc,
      containerInsightsV2: ecs.ContainerInsights.ENHANCED,
      clusterName: `hospice-os-server-${props.envConfig.stage}`,
    });

    // Add capacity providers
    this.cluster.enableFargateCapacityProviders();

    // Create a security group for the ECS tasks
    const taskSecurityGroup = new ec2.SecurityGroup(this, "TaskSecurityGroup", {
      vpc: props.vpc,
      description: "Security group for ECS tasks",
      allowAllOutbound: true,
    });

    // Create CloudWatch log group
    const logGroup = new logs.LogGroup(this, "ServerLogGroup", {
      logGroupName: `/ecs/hospice-os-server-${props.envConfig.stage}`,
      removalPolicy: props.envConfig.removalPolicy,
      retention:
        props.envConfig.stage === "prod"
          ? logs.RetentionDays.THREE_MONTHS
          : logs.RetentionDays.ONE_WEEK,
    });

    // Create task execution role
    const executionRole = new iam.Role(this, "TaskExecutionRole", {
      assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName(
          "service-role/AmazonECSTaskExecutionRolePolicy",
        ),
      ],
    });

    // Create task role
    const taskRole = new iam.Role(this, "TaskRole", {
      assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
    });

    // Allow task role to access other AWS services as needed
    taskRole.addToPrincipalPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret",
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath",
        ],
        resources: ["*"], // Scope this down in production
      }),
    );
    taskRole.addToPrincipalPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "s3:PutObject",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:ListBucket",
        ],
        resources: ["*"],
      }),
    );

    // Utility function to create SSM parameters
    const createParameterForStage = (
      name: string,
      value: string,
      secure: boolean = false,
    ) => {
      return new ssm.StringParameter(this, `Param-${name}`, {
        parameterName: `/hospice-os/${props.envConfig.stage}/${name}`,
        stringValue: value,
        tier: secure ? ssm.ParameterTier.STANDARD : ssm.ParameterTier.STANDARD,
      });
    };

    // Create parameters for database connection if dbStack is provided
    if (props.dbStack) {
      createParameterForStage(
        "DB_HOST_DIRECT",
        props.dbStack.cluster.clusterEndpoint.hostname,
      );
      createParameterForStage("DB_HOST", props.dbStack.proxy.endpoint);
      createParameterForStage("DB_PORT", POSTGRESQL_PORT.toString());
      createParameterForStage("DB_NAME", props.dbStack.databaseName);

      // For DB credentials, create Parameter Store references to Secrets Manager
      // This allows us to take advantage of credential rotation
      if (props.dbStack.cluster.secret) {
        const secretArn = props.dbStack.cluster.secret.secretArn;

        // Store references to credentials in Parameter Store
        new ssm.StringParameter(this, `Param-DB_USER-Ref`, {
          parameterName: `/hospice-os/${props.envConfig.stage}/DB_USER`,
          stringValue: `{{resolve:secretsmanager:${secretArn}:SecretString:username}}`,
        });

        new ssm.StringParameter(this, `Param-DB_PASSWORD-Ref`, {
          parameterName: `/hospice-os/${props.envConfig.stage}/DB_PASSWORD`,
          stringValue: `{{resolve:secretsmanager:${secretArn}:SecretString:password}}`,
        });
      }
    }

    if (props.uploadBucket) {
      createParameterForStage(
        "AWS_UPLOAD_BUCKET_NAME",
        props.uploadBucket.bucketName,
      );
    }

    // Create task definition
    this.taskDefinition = new ecs.FargateTaskDefinition(this, "ServerTaskDef", {
      memoryLimitMiB: 2048,
      cpu: 1024,
      executionRole,
      taskRole,
      family: `hospice-os-server-${props.envConfig.stage}`,
    });

    // Create ECR repository from ARN
    const ecrRepo = ecr.Repository.fromRepositoryName(
      this,
      "ServerRepository",
      "hospice-os",
    );

    // Add container definition
    const container = this.taskDefinition.addContainer("ServerContainer", {
      image: ecs.ContainerImage.fromEcrRepository(ecrRepo, "latest"),
      essential: true,
      logging: new ecs.AwsLogDriver({
        streamPrefix: "hospice-os-server",
        logGroup,
      }),
      environment: {
        NODE_ENV: "production",
        APP_STAGE: props.envConfig.stage, // Add stage for Parameter Store path construction
      },
      // healthCheck: {
      //   command: ['CMD-SHELL', 'curl -f http://localhost:3000/health || exit 1'],
      //   interval: cdk.Duration.seconds(30),
      //   timeout: cdk.Duration.seconds(5),
      //   retries: 3,
      //   startPeriod: cdk.Duration.seconds(60),
      // },
    });

    // Add port mappings
    container.addPortMappings({
      containerPort: 3000, // Assuming the server listens on port 3000
      protocol: ecs.Protocol.TCP,
      name: "http", // Named port mapping for service connect
    });

    // Create security group for the load balancer
    const lbSecurityGroup = new ec2.SecurityGroup(this, "LBSecurityGroup", {
      vpc: props.vpc,
      description: "Security group for load balancer",
      allowAllOutbound: true,
    });

    lbSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(80),
      "Allow HTTP traffic",
    );

    lbSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      "Allow HTTPS traffic",
    );

    // Allow the load balancer to access the tasks
    taskSecurityGroup.addIngressRule(
      lbSecurityGroup,
      ec2.Port.tcp(3000),
      "Allow traffic from ALB to ECS tasks",
    );

    // Create load balancer
    this.loadBalancer = new elasticloadbalancingv2.ApplicationLoadBalancer(
      this,
      "ServerALB",
      {
        vpc: props.vpc,
        internetFacing: true,
        securityGroup: lbSecurityGroup,
        loadBalancerName: `hospice-os-server-${props.envConfig.stage}`,
      },
    );

    // Create target group
    const targetGroup = new elasticloadbalancingv2.ApplicationTargetGroup(
      this,
      "ServerTargetGroup",
      {
        vpc: props.vpc,
        port: 3000,
        protocol: elasticloadbalancingv2.ApplicationProtocol.HTTP,
        targetType: elasticloadbalancingv2.TargetType.IP,
        healthCheck: {
          path: "/health",
          interval: cdk.Duration.seconds(60),
          timeout: cdk.Duration.seconds(5),
          healthyThresholdCount: 2,
          unhealthyThresholdCount: 3,
        },
      },
    );

    // Create listener
    const httpListener = this.loadBalancer.addListener("HttpListener", {
      port: 80,
      defaultAction: elasticloadbalancingv2.ListenerAction.forward([
        targetGroup,
      ]),
    });

    // Create HTTPS listener if certificateArn is provided
    if (props.certificateArn) {
      const certificate = acm.Certificate.fromCertificateArn(
        this,
        "ServerCertificate",
        props.certificateArn,
      );

      const httpsListener = this.loadBalancer.addListener("HttpsListener", {
        port: 443,
        protocol: elasticloadbalancingv2.ApplicationProtocol.HTTPS,
        certificates: [certificate],
        defaultAction: elasticloadbalancingv2.ListenerAction.forward([
          targetGroup,
        ]),
      });
    }

    // Create Service Discovery Namespace
    const namespace = new servicediscovery.PrivateDnsNamespace(
      this,
      "ServiceDiscoveryNamespace",
      {
        name: `hospice-os-${props.envConfig.stage}`,
        vpc: props.vpc,
      },
    );

    // Create the Fargate service using spot capacity provider
    this.service = new ecs.FargateService(this, "ServerService", {
      cluster: this.cluster,
      taskDefinition: this.taskDefinition,
      desiredCount: 1,
      securityGroups: [taskSecurityGroup],
      assignPublicIp: false, // Using ALB so don't need public IPs ya big dummy
      vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
      capacityProviderStrategies: [
        {
          capacityProvider: "FARGATE_SPOT",
          weight: 2,
        },
        {
          capacityProvider: "FARGATE",
          weight: 1,
        },
      ],
      minHealthyPercent: 50,
      maxHealthyPercent: 200,
      healthCheckGracePeriod: cdk.Duration.minutes(5),
      serviceConnectConfiguration: {
        namespace: namespace.namespaceName,
        services: [
          {
            portMappingName: "http",
            dnsName: "server",
            port: 3000,
          },
        ],
      },
    });

    // Add autoscaling
    const scaling = this.service.autoScaleTaskCount({
      minCapacity: 1,
      maxCapacity: 10,
    });

    scaling.scaleOnCpuUtilization("CpuScaling", {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.seconds(300),
      scaleOutCooldown: cdk.Duration.seconds(60),
    });

    scaling.scaleOnMemoryUtilization("MemoryScaling", {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.seconds(300),
      scaleOutCooldown: cdk.Duration.seconds(60),
    });

    // Add the service as a target to the target group
    this.service.attachToApplicationTargetGroup(targetGroup);

    // Outputs - using lowercase naming convention
    new cdk.CfnOutput(this, "ServerLoadBalancerDNS", {
      value: this.loadBalancer.loadBalancerDnsName,
      description: "The DNS name of the server load balancer",
      exportName: `hospice-os-${props.envConfig.stage}-server-loadbalancer-dns`,
    });

    new cdk.CfnOutput(this, "ServerClusterName", {
      value: this.cluster.clusterName,
      description: "The name of the ECS cluster",
      exportName: `hospice-os-${props.envConfig.stage}-server-cluster-name`,
    });

    new cdk.CfnOutput(this, "ServerTaskDefinitionArn", {
      value: this.taskDefinition.taskDefinitionArn,
      description: "The ARN of the server task definition",
      exportName: `hospice-os-${props.envConfig.stage}-server-taskdef-arn`,
    });

    new cdk.CfnOutput(this, "ServerServiceName", {
      value: this.service.serviceName,
      description: "The name of the ECS service",
      exportName: `hospice-os-${props.envConfig.stage}-server-service-name`,
    });
  }
}
