import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { Construct } from 'constructs';
import { EnvConfig } from '../bin/app';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';

export interface StorageStackProps extends cdk.StackProps {
  envConfig: EnvConfig;
}

export class StorageStack extends cdk.Stack {
  public readonly uploadBucket: s3.Bucket;

  constructor(scope: Construct, id: string, props: StorageStackProps) {
    super(scope, id, props);

    // Create S3 bucket for Uploads
    this.uploadBucket = new s3.Bucket(this, 'UploadBucket', {
      bucketName: `hospice-os-uploads-${props.envConfig.stage}-${this.account}`,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: props.envConfig.removalPolicy,
      autoDeleteObjects: props.envConfig.removalPolicy === cdk.RemovalPolicy.DESTROY,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.GET,
            s3.HttpMethods.PUT,
            s3.HttpMethods.POST,
            s3.HttpMethods.DELETE,
            s3.HttpMethods.HEAD,
          ],
          allowedOrigins: ['https://*.tallio.com'], // In production, restrict this to your specific domains
          allowedHeaders: ['*'],
          exposedHeaders: [
            'ETag',
            'x-amz-meta-custom-header',
            'Authorization',
            'Content-Type',
            'Content-Length'
          ],
          maxAge: 3000
        }
      ]
    });

    new cdk.CfnOutput(this, 'UploadBucketName', {
      value: this.uploadBucket.bucketName,
      description: 'The name of the upload bucket',
      exportName: `HospiceOS-${props.envConfig.stage}-UploadBucketName`,
    });

  }
}
