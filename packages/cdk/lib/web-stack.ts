import * as cdk from "aws-cdk-lib";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as cloudfront from "aws-cdk-lib/aws-cloudfront";
import * as origins from "aws-cdk-lib/aws-cloudfront-origins";
import * as iam from "aws-cdk-lib/aws-iam";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import { Construct } from "constructs";
import { EnvConfig } from "../bin/app";

export interface WebStackProps extends cdk.StackProps {
  envConfig: EnvConfig;
  certificateArn?: string; // Optional: If you have a custom domain
}

export class WebStack extends cdk.Stack {
  public readonly distribution: cloudfront.Distribution;
  public readonly originAccessIdentity: cloudfront.OriginAccessIdentity;
  public readonly webBucket: s3.Bucket;
  constructor(scope: Construct, id: string, props: WebStackProps) {
    super(scope, id, props);

    // Create S3 bucket for Web UI

    // Create S3 bucket for Web UI
    this.webBucket = new s3.Bucket(this, "WebBucket", {
      bucketName: `hospice-os-web-${props.envConfig.stage}-${this.account}`,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: props.envConfig.removalPolicy,
      autoDeleteObjects:
        props.envConfig.removalPolicy === cdk.RemovalPolicy.DESTROY,
      encryption: s3.BucketEncryption.S3_MANAGED,
      versioned: props.envConfig.stage === "prod",
      lifecycleRules: [
        {
          id: "delete-old-versions",
          enabled: true,
          noncurrentVersionExpiration: cdk.Duration.days(30),
        },
      ],
      cors: [
        {
          allowedMethods: [s3.HttpMethods.GET],
          allowedOrigins: ["*"],
          allowedHeaders: ["*"],
        },
      ],
    });

    // CloudFront Origin Access Identity for S3
    this.originAccessIdentity = new cloudfront.OriginAccessIdentity(
      this,
      "WebOAI",
      {
        comment: `OAI for ${id}`,
      },
    );

    // Grant the OAI read access to the bucket
    this.webBucket.grantRead(this.originAccessIdentity);

    // Create a CloudFront function for SPA routing (for single-page applications)
    const rewriteFunction = new cloudfront.Function(this, "RewriteFunction", {
      code: cloudfront.FunctionCode.fromInline(`
        function handler(event) {
          var request = event.request;
          var uri = request.uri;

          // Check whether the URI is missing a file name.
          if (uri.endsWith('/')) {
              request.uri += 'index.html';
          }
          // Check whether the URI is missing a file extension.
          // Only rewrite if it doesn't have a file extension AND it's not an assets request
          else if (!uri.includes('.') && !uri.startsWith('/assets/')) {
              request.uri = '/index.html';
          }

          return request;
        }
      `),
    });

    // Define CloudFront distribution properties
    const distributionProps: cloudfront.DistributionProps = {
      defaultBehavior: {
        origin: origins.S3BucketOrigin.withOriginAccessIdentity(
          this.webBucket,
          {
            originAccessIdentity: this.originAccessIdentity,
          },
        ),
        compress: true,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
        functionAssociations: [
          {
            function: rewriteFunction,
            eventType: cloudfront.FunctionEventType.VIEWER_REQUEST,
          },
        ],
      },
      additionalBehaviors: {
        "/assets/*": {
          origin: origins.S3BucketOrigin.withOriginAccessIdentity(
            this.webBucket,
            {
              originAccessIdentity: this.originAccessIdentity,
            },
          ),
          compress: true,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
          viewerProtocolPolicy:
            cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
          // No function associations for assets - serve them directly
        },
      },
      errorResponses: [
        {
          httpStatus: 403,
          responseHttpStatus: 200,
          responsePagePath: "/index.html",
        },
        {
          httpStatus: 404,
          responseHttpStatus: 200,
          responsePagePath: "/index.html",
        },
      ],
      priceClass: cloudfront.PriceClass.PRICE_CLASS_100,
      enabled: true,
      comment: `Hospice OS Web - ${props.envConfig.stage}`,
      defaultRootObject: "index.html",
    };

    // If a certificate ARN is provided, add custom domain configuration
    if (props.certificateArn) {
      const certificate = acm.Certificate.fromCertificateArn(
        this,
        "WebCertificate",
        props.certificateArn,
      );

      this.distribution = new cloudfront.Distribution(this, "WebDistribution", {
        ...distributionProps,
        certificate,
        domainNames: [
          `${props.envConfig.stage === "prod" ? "app.tallio.com" : `app-${props.envConfig.stage}.tallio.com`}`,
        ],
      });
    } else {
      // Create distribution without custom domain
      this.distribution = new cloudfront.Distribution(
        this,
        "WebDistribution",
        distributionProps,
      );
    }

    // Output the CloudFront URL
    new cdk.CfnOutput(this, "WebDistributionDomainName", {
      value: this.distribution.distributionDomainName,
      description: "The domain name of the web CloudFront distribution",
      exportName: `HospiceOS-${props.envConfig.stage}-WebDistributionDomainName`,
    });

    new cdk.CfnOutput(this, "WebDistributionId", {
      value: this.distribution.distributionId,
      description: "The ID of the web CloudFront distribution",
      exportName: `HospiceOS-${props.envConfig.stage}-WebDistributionId`,
    });
  }
}
