#!/bin/bash
set -e

echo "Creating ECR repository for temporal worker in production..."

# Check if repository already exists
if aws ecr describe-repositories --repository-names hospice-os-temporal-worker --profile hospice-os-prod --region us-east-1 2>/dev/null; then
    echo "ECR repository 'hospice-os-temporal-worker' already exists in production"
else
    echo "Creating ECR repository 'hospice-os-temporal-worker' in production..."
    aws ecr create-repository \
        --repository-name hospice-os-temporal-worker \
        --image-scanning-configuration scanOnPush=true \
        --image-tag-mutability MUTABLE \
        --profile hospice-os-prod \
        --region us-east-1
    echo "ECR repository created successfully"
fi

echo "ECR repository setup complete for production"
