#!/bin/bash
# Script to deploy web application without updating CloudFormation

set -e

# You can override these variables with environment variables or by editing this file
AWS_DEFAULT_PROFILE=hospice-os-staging
STAGE=staging
AWS_REGION=us-east-1
STYTCH_PROJECT_ENV=test
STYTCH_PUBLIC_TOKEN=project-live-7ccd407e-60ad-4c77-a4ad-376de2a3a945
API_BASE_URL=https://api-staging.tallio.com
VITE_REDIRECT_URL=https://admin-staging.tallio.com
CLOUDFRONT_DISTRIBUTION_ID=E242Q1Y9YO9838


# Build the admin app
echo "Building admin app..."
API_BASE_URL=$API_BASE_URL \
STYTCH_PROJECT_ENV=$STYTCH_PROJECT_ENV \
STYTCH_PUBLIC_TOKEN=$STYTCH_PUBLIC_TOKEN \
VITE_REDIRECT_URL=$VITE_REDIRECT_URL \
npm run build -w @hospice-os/admin

# Check if build was successful
if [ ! -d "apps/admin/dist" ]; then
  echo "Error: Build failed or dist directory not found!"
fi

# Deploy to S3
echo "Deploying to S3..."
aws s3 sync apps/admin/dist/ s3://hospice-os-admin-staging-690071479855/ --delete --profile $AWS_DEFAULT_PROFILE

# Create CloudFront invalidation
echo "Creating CloudFront invalidation..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
  --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
  --paths "/*" \
  --query 'Invalidation.Id' \
  --output text \
  --profile $AWS_DEFAULT_PROFILE)


echo "Deployment completed successfully."
echo "CloudFront invalidation ($INVALIDATION_ID) in progress."
echo "You can check the invalidation status with:"
echo "aws cloudfront get-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --id $INVALIDATION_ID"
echo "Web app will be available at: https://${CLOUDFRONT_DISTRIBUTION_ID}.cloudfront.net/"

