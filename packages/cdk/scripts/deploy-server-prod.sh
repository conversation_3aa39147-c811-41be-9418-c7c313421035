aws ecr get-login-password --region us-east-1 --profile hospice-os-prod | docker login --username AWS --password-stdin 239026187112.dkr.ecr.us-east-1.amazonaws.com

docker build --platform linux/amd64 -f DOCKERFILE.server -t hospice-os .
docker tag hospice-os:latest 239026187112.dkr.ecr.us-east-1.amazonaws.com/hospice-os:latest 
docker push 239026187112.dkr.ecr.us-east-1.amazonaws.com/hospice-os:latest

# update the service to use the new image
aws ecs update-service --cluster hospice-os-server-prod --service hospice-os-server-prod-ServerService5A7CA185-ffviI4SWNCOH --force-new-deployment --profile hospice-os-prod