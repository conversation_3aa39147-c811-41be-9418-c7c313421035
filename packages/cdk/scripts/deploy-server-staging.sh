set -e

aws ecr get-login-password --region us-east-1 --profile hospice-os-staging | docker login --username AWS --password-stdin 690071479855.dkr.ecr.us-east-1.amazonaws.com

docker build --platform linux/amd64 -f 'DOCKERFILE.server' -t hospice-os .
docker tag hospice-os:latest 690071479855.dkr.ecr.us-east-1.amazonaws.com/hospice-os:latest 
docker push 690071479855.dkr.ecr.us-east-1.amazonaws.com/hospice-os:latest

# update the service to use the new image
aws ecs update-service --cluster hospice-os-server-staging --service hospice-os-server-staging-ServerService5A7CA185-hZqqJybetqQs --force-new-deployment --profile hospice-os-staging