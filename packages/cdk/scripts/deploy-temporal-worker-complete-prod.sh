#!/bin/bash
set -e

echo "=== Complete Temporal Worker Deployment for Production ==="

# Step 1: Create ECR repository if it doesn't exist
echo "Step 1: Ensuring ECR repository exists..."
./packages/cdk/scripts/create-temporal-worker-ecr-prod.sh

# Step 2: Build and push Docker image
echo "Step 2: Building and pushing Docker image..."
./packages/cdk/scripts/deploy-temporal-worker-prod.sh

# Step 3: Deploy CDK stack (this will create/update the ECS service)
echo "Step 3: Deploying CDK stack..."
cd packages/cdk
npm run deploy:temporal-worker:prod

echo "=== Temporal Worker Deployment Complete ==="
