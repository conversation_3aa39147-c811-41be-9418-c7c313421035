#!/bin/bash
set -e

echo "=== Complete Temporal Worker Deployment for Staging ==="

# Step 1: Create ECR repository if it doesn't exist
echo "Step 1: Ensuring ECR repository exists..."
./packages/cdk/scripts/create-temporal-worker-ecr-staging.sh

# Step 2: Build and push Docker image
echo "Step 2: Building and pushing Docker image..."
./packages/cdk/scripts/deploy-temporal-worker-staging.sh

# Step 3: Deploy CDK stack (this will create/update the ECS service)
echo "Step 3: Deploying CDK stack..."
cd packages/cdk
npm run deploy:temporal-worker:staging

echo "=== Temporal Worker Deployment Complete ==="
