#!/bin/bash
# Script to deploy web application without updating CloudFormation

set -e

# You can override these variables with environment variables or by editing this file
AWS_DEFAULT_PROFILE=hospice-os-prod
STAGE=prod
AWS_REGION=us-east-1
STYTCH_PROJECT_ENV=live
STYTCH_PUBLIC_TOKEN=public-token-live-5da64880-7ac6-4043-97bc-14a7878e65ef
API_BASE_URL=https://api.tallio.com
VITE_REDIRECT_URL=https://app.tallio.com
CLOUDFRONT_DISTRIBUTION_ID=EE0980FGYTIYG


# Build the web app
echo "Building prod web app..."
API_BASE_URL=$API_BASE_URL \
STYTCH_PROJECT_ENV=$STYTCH_PROJECT_ENV \
STYTCH_PUBLIC_TOKEN=$STYTCH_PUBLIC_TOKEN \
VITE_REDIRECT_URL=$VITE_REDIRECT_URL \
npm run build -w @hospice-os/web

# Check if build was successful
if [ ! -d "apps/web/dist" ]; then
  echo "Error: Build failed or dist directory not found!"
fi

# Deploy to S3
echo "Deploying to S3..."
aws s3 sync apps/web/dist/ s3://hospice-os-web-prod-239026187112/ --delete --profile $AWS_DEFAULT_PROFILE

# Create CloudFront invalidation
echo "Creating CloudFront invalidation..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
  --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
  --paths "/*" \
  --query 'Invalidation.Id' \
  --output text \
  --profile $AWS_DEFAULT_PROFILE)

echo "Deployment completed successfully."
echo "CloudFront invalidation ($INVALIDATION_ID) in progress."
echo "You can check the invalidation status with:"
echo "aws cloudfront get-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --id $INVALIDATION_ID"
echo "Web app will be available at: https://${CLOUDFRONT_DISTRIBUTION_ID}.cloudfront.net/"
