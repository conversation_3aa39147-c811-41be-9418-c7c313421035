#!/bin/bash
# Script to initialize deployment environment variables

set -e
AWS_PROFILE=$2

echo "Using AWS Profile: $AWS_PROFILE"

# Check if AWS profile exists and is accessible
if [ -n "$AWS_PROFILE" ]; then
  if ! aws configure list-profiles | grep -q "^$AWS_PROFILE$"; then
    echo "Error: AWS profile '$AWS_PROFILE' not found in credentials"
    exit 1
  fi
  
  # Test profile access
  if ! aws sts get-caller-identity --profile $AWS_PROFILE &> /dev/null; then
    echo "Error: Unable to access AWS using profile '$AWS_PROFILE'. Please check credentials."
    exit 1
  fi
fi


# Detect which stage to use
if [ -z "$1" ]; then
  echo "Usage: ./init-deploy-vars.sh <stage> (e.g., dev, staging, prod)"
  exit 1
fi

STAGE=$1
echo "Initializing deployment variables for $STAGE environment..."

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
  echo "Error: AWS CLI is not installed. Please install it first."
  exit 1
fi

# Get account ID from AWS
ACCOUNT_ID=$(aws sts get-caller-identity --query "Account" --output text)
if [ -z "$ACCOUNT_ID" ]; then
  echo "Error: Could not retrieve AWS account ID. Please check your AWS credentials."
  exit 1
fi

# Get AWS region
AWS_REGION=$(aws configure get region)
if [ -z "$AWS_REGION" ]; then
  AWS_REGION="us-east-1"
  echo "AWS region not set, defaulting to $AWS_REGION"
fi

# Create or update .env file for the specified stage
ENV_FILE="deploy-$STAGE.env"

echo "# Deployment environment variables for $STAGE environment" > $ENV_FILE
echo "# Generated on $(date)" >> $ENV_FILE
echo "# Run 'source $ENV_FILE' before running deployment scripts" >> $ENV_FILE
echo "" >> $ENV_FILE
echo "export STAGE=$STAGE" >> $ENV_FILE
echo "export AWS_REGION=$AWS_REGION" >> $ENV_FILE

# Get the CloudFormation outputs to populate the environment variables
echo "Retrieving outputs from CloudFormation stacks..."

# Database Stack Outputs
DB_STACK_NAME="hospice-os-db-$STAGE"
echo "# Database Stack Variables" >> $ENV_FILE
echo "export DB_CLUSTER_IDENTIFIER=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-DbClusterIdentifier'].OutputValue\" --output text || echo 'PLACEHOLDER_DB_CLUSTER')" >> $ENV_FILE
echo "export DB_NAME=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-DbName'].OutputValue\" --output text || echo 'PLACEHOLDER_DB_NAME')" >> $ENV_FILE
echo "export DB_ENDPOINT=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-DbEndpoint'].OutputValue\" --output text || echo 'PLACEHOLDER_DB_ENDPOINT')" >> $ENV_FILE
echo "export DB_SECRET_ARN=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-DbSecretArn'].OutputValue\" --output text || echo 'PLACEHOLDER_DB_SECRET_ARN')" >> $ENV_FILE

# Server Stack Outputs
SERVER_STACK_NAME="hospice-os-server-$STAGE"
echo "# Server Stack Variables" >> $ENV_FILE
# The ECR repo name is defined in cdk.json context
echo "export ECR_REPOSITORY_NAME=hospice-os" >> $ENV_FILE
echo "export ECS_CLUSTER=$(aws cloudformation describe-stacks --stack-name $SERVER_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='hospice-os-$STAGE-server-cluster-name'].OutputValue\" --output text || echo 'PLACEHOLDER_CLUSTER_NAME')" >> $ENV_FILE
echo "export ECS_SERVICE=$(aws cloudformation describe-stacks --stack-name $SERVER_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='hospice-os-$STAGE-server-service-name'].OutputValue\" --output text || echo 'PLACEHOLDER_SERVICE_NAME')" >> $ENV_FILE
echo "export TASK_DEFINITION_FAMILY=$(aws cloudformation describe-stacks --stack-name $SERVER_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='hospice-os-$STAGE-server-taskdef-arn'].OutputValue\" --output text | cut -d'/' -f2 | cut -d':' -f1 || echo 'PLACEHOLDER_TASK_FAMILY')" >> $ENV_FILE
echo "export SERVER_LB_DNS=$(aws cloudformation describe-stacks --stack-name $SERVER_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='hospice-os-$STAGE-server-loadbalancer-dns'].OutputValue\" --output text || echo 'PLACEHOLDER_LB_DNS')" >> $ENV_FILE

# Storage Stack Outputs
STORAGE_STACK_NAME="hospice-os-storage-$STAGE"
echo "# Storage Stack Variables" >> $ENV_FILE
echo "export UPLOAD_S3_BUCKET=hospice-os-uploads-${STAGE}-${ACCOUNT_ID}" >> $ENV_FILE

# Set RUN_MIGRATIONS to false by default
echo "# Set to true to run database migrations during deployment" >> $ENV_FILE
echo "export RUN_MIGRATIONS=false" >> $ENV_FILE

# Web Stack Outputs
WEB_STACK_NAME="hospice-os-web-$STAGE"
echo "# Web Stack Variables" >> $ENV_FILE
echo "export WEB_S3_BUCKET=hospice-os-web-${STAGE}-${ACCOUNT_ID}" >> $ENV_FILE
echo "export WEB_DISTRIBUTION_DOMAIN=$(aws cloudformation describe-stacks --stack-name $WEB_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-WebDistributionDomainName'].OutputValue\" --output text || echo 'PLACEHOLDER_WEB_DOMAIN')" >> $ENV_FILE
echo "export WEB_CLOUDFRONT_DISTRIBUTION_ID=$(aws cloudformation describe-stacks --stack-name $WEB_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-WebDistributionId'].OutputValue\" --output text || echo 'PLACEHOLDER_WEB_DISTRIBUTION_ID')" >> $ENV_FILE

# Admin Stack Outputs
ADMIN_STACK_NAME="hospice-os-admin-$STAGE"
echo "# Admin Stack Variables" >> $ENV_FILE
echo "export ADMIN_S3_BUCKET=hospice-os-admin-${STAGE}-${ACCOUNT_ID}" >> $ENV_FILE
echo "export ADMIN_DISTRIBUTION_DOMAIN=$(aws cloudformation describe-stacks --stack-name $ADMIN_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-AdminDistributionDomainName'].OutputValue\" --output text || echo 'PLACEHOLDER_ADMIN_DOMAIN')" >> $ENV_FILE
echo "export ADMIN_CLOUDFRONT_DISTRIBUTION_ID=$(aws cloudformation describe-stacks --stack-name $ADMIN_STACK_NAME --query \"Stacks[0].Outputs[?ExportName=='HospiceOS-$STAGE-AdminDistributionId'].OutputValue\" --output text || echo 'PLACEHOLDER_ADMIN_DISTRIBUTION_ID')" >> $ENV_FILE

echo "Environment variables saved to $ENV_FILE"
echo ""
echo "To use these variables, run:"
echo "source $ENV_FILE"
echo ""
echo "Then you can deploy with:"
echo "Server: ./scripts/deploy-server.sh"
echo "Web:    ./scripts/deploy-web.sh"
echo "Admin:  ./scripts/deploy-admin.sh"
