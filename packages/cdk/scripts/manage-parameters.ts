#!/usr/bin/env node
import { SSMClient, PutParameterCommand } from '@aws-sdk/client-ssm';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
import * as yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('stage', {
    alias: 's',
    description: 'Deployment stage (dev, staging, prod)',
    type: 'string',
    default: 'dev',
  })
  .option('env-file', {
    alias: 'e',
    description: 'Path to .env file',
    type: 'string',
    default: path.resolve(process.cwd(), '../../apps/server/.env'),
  })
  .option('region', {
    alias: 'r',
    description: 'AWS region',
    type: 'string',
  })
  .option('cdk-json', {
    alias: 'c',
    description: 'Path to cdk.json file',
    type: 'string',
    default: path.resolve(process.cwd(), './cdk.json'),
  })
  .help()
  .alias('help', 'h')
  .argv as any;

// Load environment variables
// const envFilePath = argv['env-file'];
// if (fs.existsSync(envFilePath)) {
//   console.log(`Loading environment variables from ${envFilePath}`);
//   dotenv.config({ path: envFilePath });
// } else {
//   console.log('No .env file found, using existing environment variables');
//   dotenv.config();
// }

const stage = argv.stage;
const region = argv.region || 'us-east-1';

console.log(`Using stage: ${stage}`);
console.log(`Using region: ${region}`);

// Load CDK configuration
let cdkConfig: any = {};
const cdkJsonPath = argv['cdk-json'];
console.log(`Using cdk.json path: ${cdkJsonPath}`);
if (fs.existsSync(cdkJsonPath)) {
  try {
    console.log(`Loading configuration from ${cdkJsonPath}`);
    const cdkJsonContent = fs.readFileSync(cdkJsonPath, 'utf-8');
    cdkConfig = JSON.parse(cdkJsonContent).context;
    // console.log(`CDK configuration: ${JSON.stringify(cdkConfig, null, 2)}`);
  } catch (error) {
    console.warn(`Warning: Failed to parse cdk.json: ${error}`);
  }
}

// Initialize SSM client
const ssmClient = new SSMClient({ region, profile: 'hospice-os-prod' });

/**
 * Set a parameter in AWS Parameter Store
 */
async function setParameter(name: string, value: string, secure: boolean = false): Promise<void> {
  if (!value) {
    console.warn(`Warning: Skipping parameter ${name} because value is empty`);
    return;
  }

  const parameterName = `/hospice-os/${stage}/${name}`;
  
  try {
    await ssmClient.send(
      new PutParameterCommand({
        Name: parameterName,
        Value: value,
        Type: secure ? 'SecureString' : 'String',
        Overwrite: true,
      })
    );
    console.log(`Successfully set parameter: ${parameterName}`);
  } catch (error) {
    console.error(`Error setting parameter ${parameterName}:`, error);
  }
}

/**
 * Get parameter value from different sources in order of precedence:
 * 1. Command line arguments
 * 2. cdk.json for the current stage
 * 3. Environment variables
 * 4. Default value
 */
function getParameterValue(name: string, defaultValue: string = ''): string {
  // Check command line arguments
  // if (argv[name]) {
  //   return argv[name];
  // }
  
  // console.log(JSON.stringify(cdkConfig[stage].externalParameters))
  // Check cdk.json for stage-specific configuration
  if (cdkConfig[stage] && cdkConfig[stage].externalParameters && cdkConfig[stage].externalParameters[name]) {
    return cdkConfig[stage].externalParameters[name];
  } else {
    console.log(`No value found for ${name} in cdk.json`);
  }
  
  // Check environment variables
  // if (process.env[name]) {
  //   return process.env[name]!;
  // }
  
  // Return default value
  return defaultValue;
}

/**
 * External secrets to manage (not created by CDK)
 * These are typically third-party API keys and credentials
 */
const externalSecrets = {
  // Stytch authentication
  'STYTCH_PROJECT_ID': { value: getParameterValue('STYTCH_PROJECT_ID'), secure: true },
  'STYTCH_SECRET': { value: getParameterValue('STYTCH_SECRET'), secure: true },
  'STYTCH_ENV': { value: getParameterValue('STYTCH_ENV'), secure: false },
  // AWS credentials (if not using IAM roles)
  // 'AWS_ACCESS_KEY_ID': { value: getParameterValue('AWS_ACCESS_KEY_ID'), secure: true },
  // 'AWS_SECRET_ACCESS_KEY': { value: getParameterValue('AWS_SECRET_ACCESS_KEY'), secure: true },
  
  // Other external services
  'CORS_ALLOWED_ORIGINS': { value: getParameterValue('CORS_ALLOWED_ORIGINS'), secure: false },
  
  // Add other external secrets here
};

/**
 * Main function to run the script
 */
async function run() {
  console.log(`Setting parameters for stage: ${stage}`);
  
  for (const [name, config] of Object.entries(externalSecrets)) {
    await setParameter(name, config.value, config.secure);
  }
  
  console.log('Parameter management complete');
}

// Export the run function so it can be called programmatically
export { run };

// Run the script if it's called directly
if (require.main === module) {
  run().catch(console.error);
}
