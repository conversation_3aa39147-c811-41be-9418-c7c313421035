export interface AudioRecorderHandle {
    start: () => void;
    stop: () => void;
    pause: () => void;
    resume: () => void;
    status: string;
    clear: () => void;
}
export interface AudioRecorderProps {
    onRecordingComplete?: (url: string) => void;
    onStatusChange?: (status: string) => void;
    showStopButton?: boolean;
}
export declare const AudioRecorder: import("react").ForwardRefExoticComponent<AudioRecorderProps & import("react").RefAttributes<AudioRecorderHandle>>;
