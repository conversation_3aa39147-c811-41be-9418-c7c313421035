import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Button, Select, Group, Box } from "@mantine/core";
import { IconMicrophone, IconMicrophoneOff, IconPlayerPause, IconPlayerPlay, } from "@tabler/icons-react";
import { useEffect, useState, useRef, forwardRef, useImperativeHandle, } from "react";
import { useReactMediaRecorder } from "react-media-recorder";
const AudioLevelIndicator = ({ level, isDetecting, }) => {
    const barCount = 5;
    const bars = Array.from({ length: barCount }, (_, i) => {
        const barHeight = Math.max(4, (level * 200 * (i + 1)) / barCount);
        const opacity = isDetecting ? 0.7 + level * 0.3 : 0.3;
        return (_jsx(Box, { style: {
                width: 3,
                height: barHeight,
                backgroundColor: isDetecting ? "#40c057" : "#868e96",
                borderRadius: "1px",
                transition: "height 0.1s ease-out, opacity 0.1s ease-out",
                opacity,
            } }, i));
    });
    return (_jsx(Group, { gap: 2, align: "flex-end", style: { height: 20 }, children: bars }));
};
export const AudioRecorder = forwardRef(({ onRecordingComplete, onStatusChange, showStopButton = true }, ref) => {
    const [devices, setDevices] = useState([]);
    const [selectedDevice, setSelectedDevice] = useState("");
    const [audioLevel, setAudioLevel] = useState(0);
    const { status, startRecording, stopRecording, pauseRecording, resumeRecording, mediaBlobUrl, clearBlobUrl, } = useReactMediaRecorder({
        audio: selectedDevice ? { deviceId: { exact: selectedDevice } } : true,
    });
    useImperativeHandle(ref, () => ({
        start: startRecording,
        stop: stopRecording,
        pause: pauseRecording,
        resume: resumeRecording,
        status,
        clear: clearBlobUrl,
    }), [
        status,
        startRecording,
        stopRecording,
        pauseRecording,
        resumeRecording,
        clearBlobUrl,
    ]);
    useEffect(() => {
        if (onStatusChange) {
            onStatusChange(status);
        }
    }, [status, onStatusChange]);
    useEffect(() => {
        if (mediaBlobUrl && onRecordingComplete) {
            onRecordingComplete(mediaBlobUrl);
        }
    }, [mediaBlobUrl]);
    useEffect(() => {
        navigator.mediaDevices
            .enumerateDevices()
            .then((ds) => {
            const inputs = ds.filter((d) => d.kind === "audioinput");
            setDevices(inputs);
            if (!selectedDevice && inputs[0]) {
                setSelectedDevice(inputs[0].deviceId);
            }
        })
            .catch((err) => {
            console.error("Error enumerating devices", err);
        });
    }, [selectedDevice]);
    const analyserRef = useRef(null);
    const audioContextRef = useRef(null);
    const rafRef = useRef(0);
    useEffect(() => {
        if (status === "recording") {
            navigator.mediaDevices
                .getUserMedia({
                audio: selectedDevice
                    ? { deviceId: { exact: selectedDevice } }
                    : true,
            })
                .then((stream) => {
                audioContextRef.current = new AudioContext();
                const source = audioContextRef.current.createMediaStreamSource(stream);
                const analyser = audioContextRef.current.createAnalyser();
                analyser.fftSize = 256;
                source.connect(analyser);
                analyserRef.current = analyser;
                const data = new Uint8Array(analyser.fftSize);
                const update = () => {
                    analyser.getByteTimeDomainData(data);
                    let sum = 0;
                    for (let i = 0; i < data.length; i++) {
                        const val = (data[i] - 128) / 128;
                        sum += val * val;
                    }
                    setAudioLevel(Math.sqrt(sum / data.length));
                    rafRef.current = requestAnimationFrame(update);
                };
                update();
            })
                .catch((err) => console.error(err));
        }
        return () => {
            if (rafRef.current)
                cancelAnimationFrame(rafRef.current);
            if (analyserRef.current)
                analyserRef.current.disconnect();
            if (audioContextRef.current) {
                audioContextRef.current.close();
                audioContextRef.current = null;
            }
        };
    }, [status, selectedDevice]);
    const isDetectingAudio = audioLevel > 0.02;
    const options = devices.map((d) => ({
        value: d.deviceId,
        label: d.label || `Microphone ${d.deviceId.slice(0, 5)}...`,
    }));
    return (_jsxs(Group, { gap: "xs", children: [_jsx(Select, { data: options, value: selectedDevice, onChange: (val) => setSelectedDevice(val ?? ""), placeholder: "Audio Input" }), (showStopButton ||
                (!showStopButton && status !== "recording" && status !== "paused")) && (_jsx(Button, { variant: "outline", color: status === "recording" || status === "paused" ? "red" : "blue", leftSection: status === "recording" || status === "paused" ? (_jsx(IconMicrophoneOff, { size: 16 })) : (_jsx(IconMicrophone, { size: 16 })), onClick: status === "recording" || status === "paused"
                    ? stopRecording
                    : startRecording, children: status === "recording" || status === "paused"
                    ? "End Recording"
                    : "Record Audio" })), (status === "recording" || status === "paused") && (_jsx(Button, { variant: "outline", color: status === "paused" ? "blue" : "orange", leftSection: status === "paused" ? (_jsx(IconPlayerPlay, { size: 16 })) : (_jsx(IconPlayerPause, { size: 16 })), onClick: status === "paused" ? resumeRecording : pauseRecording, children: status === "paused" ? "Resume Recording" : "Pause Recording" })), status === "recording" && (_jsx(AudioLevelIndicator, { level: audioLevel, isDetecting: isDetectingAudio }))] }));
});
AudioRecorder.displayName = "AudioRecorder";
