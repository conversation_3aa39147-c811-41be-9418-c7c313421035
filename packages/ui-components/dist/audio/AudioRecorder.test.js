import { jsx as _jsx } from "react/jsx-runtime";
import { render, screen, fireEvent } from "@testing-library/react";
import { MantineProvider } from "@mantine/core";
import { describe, it, expect, vi, beforeAll } from "vitest";
import { AudioRecorder } from "./AudioRecorder";
let mockReturn;
vi.mock("react-media-recorder", () => ({
    useReactMediaRecorder: () => mockReturn,
}));
beforeAll(() => {
    Object.defineProperty(window, "matchMedia", {
        writable: true,
        value: vi.fn().mockReturnValue({
            matches: false,
            addListener: vi.fn(),
            removeListener: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn(),
        }),
    });
    Object.defineProperty(navigator, "mediaDevices", {
        writable: true,
        value: {
            enumerateDevices: vi.fn().mockResolvedValue([]),
            getUserMedia: vi.fn().mockResolvedValue({}),
        },
    });
    window.ResizeObserver = class {
        observe() { }
        unobserve() { }
        disconnect() { }
    };
});
describe("AudioRecorder", () => {
    it("starts recording when button clicked", () => {
        const startRecording = vi.fn();
        mockReturn = {
            status: "idle",
            startRecording,
            stopRecording: vi.fn(),
            pauseRecording: vi.fn(),
            resumeRecording: vi.fn(),
            mediaBlobUrl: null,
            clearBlobUrl: vi.fn(),
        };
        render(_jsx(MantineProvider, { children: _jsx(AudioRecorder, {}) }));
        fireEvent.click(screen.getByRole("button", { name: /record audio/i }));
        expect(startRecording).toHaveBeenCalled();
    });
    it("calls callback when recording finishes", () => {
        const onComplete = vi.fn();
        mockReturn = {
            status: "stopped",
            startRecording: vi.fn(),
            stopRecording: vi.fn(),
            pauseRecording: vi.fn(),
            resumeRecording: vi.fn(),
            mediaBlobUrl: "blob:test",
            clearBlobUrl: vi.fn(),
        };
        render(_jsx(MantineProvider, { children: _jsx(AudioRecorder, { onRecordingComplete: onComplete }) }));
        expect(onComplete).toHaveBeenCalledWith("blob:test");
    });
    it("shows audio level indicator when recording", () => {
        mockReturn = {
            status: "recording",
            startRecording: vi.fn(),
            stopRecording: vi.fn(),
            pauseRecording: vi.fn(),
            resumeRecording: vi.fn(),
            mediaBlobUrl: null,
            clearBlobUrl: vi.fn(),
        };
        const { container } = render(_jsx(MantineProvider, { children: _jsx(AudioRecorder, {}) }));
        // Check if audio level indicator bars are present
        const audioBars = container.querySelectorAll('[style*="width: 3"]');
        expect(audioBars.length).toBe(5); // Should have 5 bars
    });
    it("hides audio level indicator when not recording", () => {
        mockReturn = {
            status: "idle",
            startRecording: vi.fn(),
            stopRecording: vi.fn(),
            pauseRecording: vi.fn(),
            resumeRecording: vi.fn(),
            mediaBlobUrl: null,
            clearBlobUrl: vi.fn(),
        };
        const { container } = render(_jsx(MantineProvider, { children: _jsx(AudioRecorder, {}) }));
        // Check if audio level indicator bars are not present
        const audioBars = container.querySelectorAll('[style*="width: 3"]');
        expect(audioBars.length).toBe(0); // Should have no bars when not recording
    });
});
