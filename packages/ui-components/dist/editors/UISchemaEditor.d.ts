import { UISchema } from '../types';
interface UISchemaEditorProps {
    /**
     * The JSON schema to create a UI schema for
     */
    schema: Record<string, unknown>;
    /**
     * The current UI schema (if any)
     */
    uiSchema?: UISchema;
    /**
     * Callback for when the UI schema changes
     */
    onChange: (newUiSchema: UISchema) => void;
}
export declare function UISchemaEditor({ schema, uiSchema, onChange }: UISchemaEditorProps): import("react/jsx-runtime").JSX.Element;
export {};
