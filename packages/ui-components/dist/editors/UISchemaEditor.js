import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Card, Tabs, Text, Group, Stack, Button, Select, TextInput, Checkbox, NumberInput, ActionIcon, Tooltip, Divider } from '@mantine/core';
import { IconPlus, IconTrash, IconArrowUp, IconArrowDown } from '@tabler/icons-react';
import { determineWidgetType, createDefaultUISchema } from '../utils';
export function UISchemaEditor({ schema, uiSchema, onChange }) {
    // Initialize UI schema if not provided
    const [currentUISchema, setCurrentUISchema] = useState(uiSchema || createDefaultUISchema(schema));
    // Update local state when props change
    useEffect(() => {
        if (uiSchema) {
            setCurrentUISchema(uiSchema);
        }
    }, [uiSchema]);
    // Get schema properties
    const properties = schema.properties ? Object.keys(schema.properties) : [];
    // Get current property order
    const propertyOrder = currentUISchema['ui:order'] || [];
    // Update UI schema and notify parent
    const updateUISchema = (newUISchema) => {
        setCurrentUISchema(newUISchema);
        onChange(newUISchema);
    };
    // Update property order
    const updatePropertyOrder = (newOrder) => {
        const newUISchema = { ...currentUISchema, 'ui:order': newOrder };
        updateUISchema(newUISchema);
    };
    // Move property up in order
    const movePropertyUp = (propName) => {
        const index = propertyOrder.indexOf(propName);
        if (index > 0) {
            const newOrder = [...propertyOrder];
            [newOrder[index - 1], newOrder[index]] = [newOrder[index], newOrder[index - 1]];
            updatePropertyOrder(newOrder);
        }
    };
    // Move property down in order
    const movePropertyDown = (propName) => {
        const index = propertyOrder.indexOf(propName);
        if (index < propertyOrder.length - 1) {
            const newOrder = [...propertyOrder];
            [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];
            updatePropertyOrder(newOrder);
        }
    };
    // Update property widget type
    const updatePropertyWidget = (propName, widgetType) => {
        const propUISchema = currentUISchema[propName] || {};
        const newPropUISchema = { ...propUISchema, 'ui:widget': widgetType };
        const newUISchema = { ...currentUISchema, [propName]: newPropUISchema };
        updateUISchema(newUISchema);
    };
    // Update property options
    const updatePropertyOptions = (propName, options) => {
        const propUISchema = currentUISchema[propName] || {};
        const newPropUISchema = {
            ...propUISchema,
            'ui:options': { ...(propUISchema['ui:options'] || {}), ...options }
        };
        const newUISchema = { ...currentUISchema, [propName]: newPropUISchema };
        updateUISchema(newUISchema);
    };
    // Get property schema
    const getPropertySchema = (propName) => {
        if (!schema.properties)
            return {};
        return schema.properties[propName] || {};
    };
    // Get property widget type
    const getPropertyWidgetType = (propName) => {
        const propUISchema = currentUISchema[propName];
        return propUISchema?.['ui:widget'] || determineWidgetType(getPropertySchema(propName));
    };
    // Get property options
    const getPropertyOptions = (propName) => {
        const propUISchema = currentUISchema[propName];
        return propUISchema?.['ui:options'] || {};
    };
    return (_jsx(Box, { children: _jsxs(Tabs, { defaultValue: "properties", children: [_jsxs(Tabs.List, { children: [_jsx(Tabs.Tab, { value: "properties", children: "Properties" }), _jsx(Tabs.Tab, { value: "ordering", children: "Ordering" }), _jsx(Tabs.Tab, { value: "global", children: "Global Options" }), _jsx(Tabs.Tab, { value: "json", children: "JSON" })] }), _jsx(Tabs.Panel, { value: "properties", pt: "md", children: _jsx(Stack, { children: properties.map(propName => {
                            const propSchema = getPropertySchema(propName);
                            const widgetType = getPropertyWidgetType(propName);
                            const options = getPropertyOptions(propName);
                            return (_jsxs(Card, { withBorder: true, p: "sm", children: [_jsxs(Group, { justify: "space-between", mb: "xs", children: [_jsx(Text, { fw: 500, children: propName }), _jsxs(Text, { size: "xs", c: "dimmed", children: ["Type: ", propSchema.type] })] }), _jsxs(Stack, { children: [_jsx(Select, { label: "Widget", value: widgetType, onChange: (value) => updatePropertyWidget(propName, value || 'string'), data: [
                                                    { value: 'string', label: 'String' },
                                                    { value: 'number', label: 'Number' },
                                                    { value: 'boolean', label: 'Boolean' },
                                                    { value: 'date', label: 'Date' },
                                                    { value: 'datetime', label: 'Date & Time' },
                                                    { value: 'select', label: 'Select' },
                                                    { value: 'table', label: 'Table' },
                                                    { value: 'array', label: 'Array' },
                                                    { value: 'object', label: 'Object' },
                                                    { value: 'resourceDisplay', label: 'Resource Display' },
                                                    { value: 'hidden', label: 'Hidden' }
                                                ] }), _jsx(Divider, { label: "Widget Options" }), _jsx(Checkbox, { label: "Collapsible", checked: !!options.collapsible, onChange: (e) => updatePropertyOptions(propName, { collapsible: e.currentTarget.checked }) }), _jsx(NumberInput, { label: "Header Level", min: 1, max: 6, value: options.headerLevel || 2, onChange: (value) => updatePropertyOptions(propName, { headerLevel: value }) }), _jsx(NumberInput, { label: "Line Threshold", min: 1, value: options.lineThreshold || 10, onChange: (value) => updatePropertyOptions(propName, { lineThreshold: value }) }), widgetType === 'resourceDisplay' && (_jsxs(_Fragment, { children: [_jsx(TextInput, { label: "Resource Type", value: options.resourceType || '', onChange: (e) => updatePropertyOptions(propName, { resourceType: e.currentTarget.value }) }), _jsx(TextInput, { label: "Title Field", value: options.titleField || '', onChange: (e) => updatePropertyOptions(propName, { titleField: e.currentTarget.value }) })] })), widgetType === 'table' && (_jsx(TextInput, { label: "Columns (comma-separated)", value: options.columns?.join(', ') || '', onChange: (e) => {
                                                    const columns = e.currentTarget.value.split(',').map(s => s.trim()).filter(Boolean);
                                                    updatePropertyOptions(propName, { columns });
                                                } }))] })] }, propName));
                        }) }) }), _jsx(Tabs.Panel, { value: "ordering", pt: "md", children: _jsxs(Stack, { children: [_jsx(Group, { children: _jsx(Button, { leftSection: _jsx(IconPlus, { size: 16 }), onClick: () => {
                                        // Add all properties that aren't already in the order
                                        const missingProps = properties.filter(p => !propertyOrder.includes(p));
                                        if (missingProps.length > 0) {
                                            updatePropertyOrder([...propertyOrder, ...missingProps]);
                                        }
                                    }, children: "Add All Missing" }) }), propertyOrder.map((propName, index) => (_jsx(Card, { withBorder: true, p: "sm", children: _jsxs(Group, { justify: "space-between", children: [_jsx(Text, { children: propName }), _jsxs(Group, { children: [_jsx(Tooltip, { label: "Move Up", children: _jsx(ActionIcon, { disabled: index === 0, onClick: () => movePropertyUp(propName), children: _jsx(IconArrowUp, { size: 16 }) }) }), _jsx(Tooltip, { label: "Move Down", children: _jsx(ActionIcon, { disabled: index === propertyOrder.length - 1, onClick: () => movePropertyDown(propName), children: _jsx(IconArrowDown, { size: 16 }) }) }), _jsx(Tooltip, { label: "Remove", children: _jsx(ActionIcon, { color: "red", onClick: () => {
                                                            const newOrder = propertyOrder.filter(p => p !== propName);
                                                            updatePropertyOrder(newOrder);
                                                        }, children: _jsx(IconTrash, { size: 16 }) }) })] })] }) }, propName))), properties.filter(p => !propertyOrder.includes(p)).length > 0 && (_jsxs(_Fragment, { children: [_jsx(Divider, { label: "Properties Not in Order" }), properties.filter(p => !propertyOrder.includes(p)).map(propName => (_jsx(Card, { withBorder: true, p: "sm", children: _jsxs(Group, { justify: "space-between", children: [_jsx(Text, { children: propName }), _jsx(Button, { leftSection: _jsx(IconPlus, { size: 16 }), onClick: () => {
                                                        updatePropertyOrder([...propertyOrder, propName]);
                                                    }, children: "Add" })] }) }, propName)))] }))] }) }), _jsx(Tabs.Panel, { value: "global", pt: "md", children: _jsxs(Stack, { children: [_jsx(TextInput, { label: "Display Fields (comma-separated)", value: currentUISchema.displayFields?.join(', ') || '', onChange: (e) => {
                                    const displayFields = e.currentTarget.value.split(',').map(s => s.trim()).filter(Boolean);
                                    updateUISchema({ ...currentUISchema, displayFields });
                                } }), _jsx(TextInput, { label: "Hide Fields (comma-separated)", value: currentUISchema.hideFields?.join(', ') || '', onChange: (e) => {
                                    const hideFields = e.currentTarget.value.split(',').map(s => s.trim()).filter(Boolean);
                                    updateUISchema({ ...currentUISchema, hideFields });
                                } })] }) }), _jsx(Tabs.Panel, { value: "json", pt: "md", children: _jsx("pre", { style: { whiteSpace: 'pre-wrap' }, children: JSON.stringify(currentUISchema, null, 2) }) })] }) }));
}
