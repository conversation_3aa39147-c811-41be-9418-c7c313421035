import { UISchema } from '../types';
interface UISchemaPreviewProps {
    /**
     * The JSON schema to preview
     */
    schema: Record<string, unknown>;
    /**
     * The UI schema to preview
     */
    uiSchema: UISchema;
    /**
     * Sample data to use for the preview (optional)
     */
    sampleData?: Record<string, unknown>;
}
export declare function UISchemaPreview({ schema, uiSchema, sampleData }: UISchemaPreviewProps): import("react/jsx-runtime").JSX.Element;
export {};
