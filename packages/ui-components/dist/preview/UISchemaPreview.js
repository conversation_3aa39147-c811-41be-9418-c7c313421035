import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Paper, Text, Loader, Stack, Tabs, Select } from '@mantine/core';
import { CollapsibleMarkdown } from '../widgets';
export function UISchemaPreview({ schema, uiSchema, sampleData }) {
    const [markdownContent, setMarkdownContent] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [viewMode, setViewMode] = useState('desktop');
    // Generate markdown when schema, uiSchema, or sampleData change
    useEffect(() => {
        const generateMarkdown = async () => {
            setIsLoading(true);
            setError(null);
            try {
                // Import from utils index to avoid path issues
                const { dataToMarkdown } = await import('../utils');
                // Use provided sample data or generate from schema
                const data = sampleData || await generateSampleData(schema);
                // Generate markdown
                const markdown = await dataToMarkdown(data, uiSchema, 0, schema);
                setMarkdownContent(markdown);
            }
            catch (err) {
                console.error('Error generating preview:', err);
                setError('Failed to generate preview. Please check the console for details.');
            }
            finally {
                setIsLoading(false);
            }
        };
        generateMarkdown();
    }, [schema, uiSchema, sampleData]);
    // Get preview container style based on view mode
    const getPreviewStyle = () => {
        switch (viewMode) {
            case 'mobile':
                return { maxWidth: '375px', margin: '0 auto' };
            case 'tablet':
                return { maxWidth: '768px', margin: '0 auto' };
            case 'desktop':
            default:
                return {};
        }
    };
    return (_jsx(Box, { children: _jsxs(Tabs, { defaultValue: "preview", children: [_jsxs(Tabs.List, { children: [_jsx(Tabs.Tab, { value: "preview", children: "Preview" }), _jsx(Tabs.Tab, { value: "markdown", children: "Markdown" })] }), _jsx(Box, { mt: "md", mb: "md", children: _jsx(Select, { label: "View Mode", value: viewMode, onChange: (value) => setViewMode(value || 'desktop'), data: [
                            { value: 'desktop', label: 'Desktop' },
                            { value: 'tablet', label: 'Tablet' },
                            { value: 'mobile', label: 'Mobile' }
                        ] }) }), _jsx(Tabs.Panel, { value: "preview", pt: "md", children: _jsx(Paper, { p: "md", withBorder: true, style: getPreviewStyle(), children: isLoading ? (_jsxs(Stack, { align: "center", gap: "md", children: [_jsx(Loader, { size: "md" }), _jsx(Text, { children: "Generating preview..." })] })) : error ? (_jsx(Text, { color: "red", children: error })) : (_jsx(CollapsibleMarkdown, { markdown: markdownContent })) }) }), _jsx(Tabs.Panel, { value: "markdown", pt: "md", children: _jsx(Paper, { p: "md", withBorder: true, children: isLoading ? (_jsxs(Stack, { align: "center", gap: "md", children: [_jsx(Loader, { size: "md" }), _jsx(Text, { children: "Generating markdown..." })] })) : error ? (_jsx(Text, { color: "red", children: error })) : (_jsx("pre", { style: { whiteSpace: 'pre-wrap', overflow: 'auto' }, children: markdownContent })) }) })] }) }));
}
/**
 * Generate sample data based on a JSON schema
 * @param schema The JSON schema to generate sample data for
 * @returns Sample data that conforms to the schema
 */
async function generateSampleData(schema) {
    // Simple implementation for now - can be expanded later
    const result = {};
    if (!schema.properties || typeof schema.properties !== 'object') {
        return result;
    }
    const properties = schema.properties;
    // Generate sample data for each property
    for (const [propName, propSchema] of Object.entries(properties)) {
        if (typeof propSchema !== 'object' || propSchema === null) {
            continue;
        }
        const type = propSchema.type;
        switch (type) {
            case 'string':
                result[propName] = `Sample ${propName}`;
                break;
            case 'number':
            case 'integer':
                result[propName] = 42;
                break;
            case 'boolean':
                result[propName] = true;
                break;
            case 'array':
                result[propName] = [
                    { id: '1', name: 'Sample Item 1' },
                    { id: '2', name: 'Sample Item 2' }
                ];
                break;
            case 'object':
                result[propName] = await generateSampleData(propSchema);
                break;
            default:
                result[propName] = null;
        }
    }
    return result;
}
