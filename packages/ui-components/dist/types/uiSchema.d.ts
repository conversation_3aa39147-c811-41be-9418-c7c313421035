/**
 * Type definitions for UI Schema to customize the display of JSON schemas
 */
/**
 * Options for UI schema widgets
 */
export interface UISchemaOptions {
    /**
     * Resource type for resourceDisplay widget
     */
    resourceType?: string;
    /**
     * Field to use for resource lookup
     */
    lookupBy?: string;
    /**
     * Fields to display from the resource
     */
    displayFields?: string[];
    /**
     * Fields to hide from the resource
     */
    hideFields?: string[];
    /**
     * Columns to display in table widget
     */
    columns?: string[];
    /**
     * Header level (1-6) to use for this section
     */
    headerLevel?: number;
    /**
     * Whether this section should be collapsible
     */
    collapsible?: boolean;
    /**
     * Line length threshold for collapsing content
     * If content exceeds this many lines, it will be collapsed by default
     */
    lineThreshold?: number;
    /**
     * Field to use as title in resource display
     */
    titleField?: string;
    /**
     * Any additional options
     */
    [key: string]: string | number | boolean | string[] | number[] | Record<string, unknown> | undefined;
}
/**
 * Properties for a specific field in the UI schema
 */
export interface UISchemaWidgetProps {
    /**
     * Widget type to use for rendering
     */
    "ui:widget"?: "hidden" | "resourceDisplay" | "table" | string;
    /**
     * Options for the widget
     */
    "ui:options"?: UISchemaOptions;
    /**
     * Order of child properties
     */
    "ui:order"?: string[];
    /**
     * Whether to show the label
     */
    "ui:label"?: boolean;
    /**
     * Whether the field is read-only
     */
    "ui:readonly"?: boolean;
    /**
     * Tooltip text
     */
    "ui:tooltip"?: string;
    /**
     * Description text
     */
    "ui:description"?: string;
    /**
     * Conditional display based on other field values
     */
    "ui:if"?: {
        [key: string]: string | number | boolean | null | undefined;
    };
    /**
     * Any additional properties
     */
    [key: string]: string | number | boolean | string[] | Record<string, unknown> | undefined;
}
/**
 * UI Schema for customizing the display of JSON schemas
 */
export interface UISchema {
    /**
     * Order of properties at the root level
     */
    "ui:order"?: string[];
    /**
     * Schema reference for validation
     */
    "ui:schema"?: Record<string, unknown>;
    /**
     * Fields to display
     */
    "displayFields"?: string[];
    /**
     * Fields to hide
     */
    "hideFields"?: string[];
    /**
     * Properties for specific fields or additional properties
     */
    [key: string]: UISchemaWidgetProps | string[] | Record<string, unknown> | undefined;
}
