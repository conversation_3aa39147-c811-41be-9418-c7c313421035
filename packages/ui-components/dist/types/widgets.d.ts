import { ReactNode } from 'react';
import { UISchema, UISchemaOptions } from './uiSchema';
/**
 * Props for all widget components
 */
export interface WidgetProps {
    /**
     * The property name
     */
    name: string;
    /**
     * The property path (array of property names)
     */
    path: string[];
    /**
     * The property value
     */
    value: unknown;
    /**
     * The JSON schema for the property
     */
    schema: Record<string, unknown>;
    /**
     * The UI schema for the property
     */
    uiSchema?: UISchema;
    /**
     * The parent data object
     */
    parentData?: Record<string, unknown>;
    /**
     * The nesting level (0 for top level)
     */
    nestingLevel: number;
    /**
     * Children to render inside the widget
     */
    children?: ReactNode;
}
/**
 * Props for widget editor components
 */
export interface WidgetEditorProps {
    /**
     * The property name
     */
    name: string;
    /**
     * The property path (array of property names)
     */
    path: string[];
    /**
     * The JSON schema for the property
     */
    schema: Record<string, unknown>;
    /**
     * The current UI schema for the property
     */
    uiSchema?: UISchema;
    /**
     * Callback for when the UI schema changes
     */
    onChange: (newUiSchema: UISchema) => void;
}
/**
 * Props for widget options editor components
 */
export interface WidgetOptionsEditorProps {
    /**
     * The current options for the widget
     */
    options?: UISchemaOptions;
    /**
     * The JSON schema for the property
     */
    schema: Record<string, unknown>;
    /**
     * Callback for when the options change
     */
    onChange: (newOptions: UISchemaOptions) => void;
}
/**
 * Definition of a widget for the registry
 */
export interface WidgetDefinition {
    /**
     * The widget type
     */
    type: string;
    /**
     * The widget component
     */
    component: React.ComponentType<WidgetProps>;
    /**
     * The schema types this widget is valid for
     */
    validForTypes: string[];
    /**
     * Default options for this widget
     */
    defaultOptions: Partial<UISchemaOptions>;
    /**
     * The options editor component for this widget
     */
    optionsEditor?: React.ComponentType<WidgetOptionsEditorProps>;
    /**
     * Icon for this widget
     */
    icon?: React.ReactNode;
    /**
     * Description of this widget
     */
    description?: string;
}
/**
 * Registry of available widgets
 */
export interface WidgetRegistry {
    /**
     * Get a widget by type
     */
    getWidget: (type: string) => WidgetDefinition | undefined;
    /**
     * Get all widgets
     */
    getAllWidgets: () => WidgetDefinition[];
    /**
     * Get widgets valid for a schema type
     */
    getWidgetsForType: (schemaType: string) => WidgetDefinition[];
    /**
     * Register a new widget
     */
    registerWidget: (widget: WidgetDefinition) => void;
}
