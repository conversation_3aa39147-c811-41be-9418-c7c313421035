/**
 * Utility functions to convert a JSON schema to markdown format and generate sample data
 */
import { UISchema } from '../types';
/**
 * Format a property name for display in markdown
 * - Converts camelCase to space-separated words
 * - Capitalizes the first letter of each word
 *
 * @param propertyName The property name to format
 * @returns The formatted property name
 */
export declare function formatPropertyName(propertyName: string): string;
/**
 * Convert data to markdown representation
 * @param data The data to convert to markdown
 * @param uiSchema Optional UI schema for customizing the display
 * @param nestingLevel Current nesting level (0 for top level)
 * @param schema Optional JSON schema describing the data structure
 * @returns Markdown representation of the data
 */
export declare function dataToMarkdown(data: unknown, uiSchema?: UISchema, nestingLevel?: number, schema?: Record<string, unknown>): Promise<string>;
