import { applyUIOrder, shouldDisplayProperty, getWidgetType, getWidgetOptions } from './uiSchemaUtils';
/**
 * Format a property name for display in markdown
 * - Converts camelCase to space-separated words
 * - Capitalizes the first letter of each word
 *
 * @param propertyName The property name to format
 * @returns The formatted property name
 */
export function formatPropertyName(propertyName) {
    if (/^[A-Z]+$/.test(propertyName)) {
        // Entire field is an acronym (e.g., "DME"), return as-is
        return propertyName;
    }
    // Step 1: Add space before any uppercase letter that follows a lowercase letter
    let result = propertyName.replace(/([a-z])([A-Z])/g, '$1 $2');
    // Step 2: Add space between acronyms and normal words (e.g., CTIObtained → CTI Obtained)
    result = result.replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2');
    // Step 3: Capitalize each word, but preserve acronyms (2+ uppercase letters)
    return result
        .split(' ')
        .map(word => {
        return word.length > 1 && word === word.toUpperCase()
            ? word // keep acronyms as-is
            : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
        .join(' ');
}
/**
 * Generate a header markup based on the provided options
 * @param text The header text
 * @param nestingLevel Current nesting level
 * @param options UI schema options
 * @returns Markdown header with appropriate metadata
 */
function generateHeaderMarkup(text, nestingLevel, options) {
    // Default header level based on nesting
    let headerLevel = nestingLevel === 0 ? 2 : 3;
    // Override with UI schema option if provided
    if (options?.headerLevel && typeof options.headerLevel === 'number') {
        headerLevel = Math.min(Math.max(options.headerLevel, 1), 6);
    }
    // Default collapsible state
    let collapsible = nestingLevel === 0;
    // Override with UI schema option if provided
    if (options?.collapsible !== undefined) {
        collapsible = Boolean(options.collapsible);
    }
    // Add metadata as HTML comments
    let metadata = `<!-- collapsible:${collapsible} -->`;
    // Add line threshold metadata if provided
    if (options?.lineThreshold && typeof options.lineThreshold === 'number') {
        metadata += `\n<!-- lineThreshold:${options.lineThreshold} -->`;
    }
    // Generate the header with appropriate level
    const headerMarker = '#'.repeat(headerLevel);
    return `${metadata}\n${headerMarker} ${text}\n\n`;
}
/**
 * Convert data to markdown representation
 * @param data The data to convert to markdown
 * @param uiSchema Optional UI schema for customizing the display
 * @param nestingLevel Current nesting level (0 for top level)
 * @param schema Optional JSON schema describing the data structure
 * @returns Markdown representation of the data
 */
export async function dataToMarkdown(data, uiSchema, nestingLevel = 0, schema) {
    if (data === null || data === undefined) {
        return 'null\n\n';
    }
    let markdown = '';
    if (Array.isArray(data)) {
        // Handle array data
        for (let i = 0; i < data.length; i++) {
            // Only use headings for top-level arrays (nestingLevel === 0)
            if (nestingLevel === 0) {
                markdown += `### Item ${i + 1}\n\n`;
                markdown += await dataToMarkdown(data[i], undefined, nestingLevel + 1);
            }
            else {
                // For nested arrays, use a different format that doesn't create collapsible sections
                markdown += `**Item ${i + 1}**\n\n`;
                markdown += await dataToMarkdown(data[i], undefined, nestingLevel + 1);
            }
        }
        return markdown;
    }
    if (typeof data === 'object' && data !== null) {
        // Handle object data
        const dataObj = data;
        let properties = Object.keys(dataObj);
        // Get the schema for this object if available
        const objectSchema = uiSchema && uiSchema['ui:schema'] ?
            uiSchema['ui:schema'] :
            undefined;
        // Apply UI schema ordering if available, passing the data object and schema to sort by type
        if (uiSchema && uiSchema['ui:order']) {
            properties = applyUIOrder(properties, uiSchema['ui:order'], dataObj, objectSchema);
        }
        else {
            // If no UI schema ordering, still sort by type (simple values first, then objects and arrays)
            properties = applyUIOrder(properties, undefined, dataObj, objectSchema);
        }
        // Process each property
        for (const key of properties) {
            const value = dataObj[key];
            // Skip hidden properties
            if (uiSchema && getWidgetType(key, uiSchema) === 'hidden') {
                continue;
            }
            // Skip properties that don't meet conditional display criteria
            if (uiSchema && !shouldDisplayProperty(key, uiSchema, dataObj)) {
                continue;
            }
            // Get widget type and options
            const widgetType = uiSchema ? getWidgetType(key, uiSchema) : undefined;
            const widgetOptions = uiSchema ? getWidgetOptions(key, uiSchema) : undefined;
            // Handle different widget types
            if (widgetType === 'resourceDisplay' && widgetOptions) {
                // Resource display widgets always get their own section
                markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);
                markdown += `Resource Display: ${value}\n\n`;
            }
            else if (widgetType === 'table' && Array.isArray(value) && widgetOptions) {
                // Tables always get their own section
                markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);
                markdown += renderTable(key, value, widgetOptions);
            }
            else if (Array.isArray(value)) {
                // Determine if this is a complex array (array of objects)
                let isComplexArray = false;
                // First check the schema passed directly to the function
                if (schema && schema.properties && typeof schema.properties === 'object') {
                    const propSchema = schema.properties[key];
                    if (propSchema && typeof propSchema === 'object') {
                        const propSchemaObj = propSchema;
                        if (propSchemaObj.type === 'array' && propSchemaObj.items) {
                            const items = propSchemaObj.items;
                            // Check if items is a reference or an object type
                            if (items.$ref || items.type === 'object') {
                                isComplexArray = true;
                            }
                        }
                    }
                }
                // If not found in the main schema, check the UI schema
                if (!isComplexArray && objectSchema && objectSchema.properties && typeof objectSchema.properties === 'object') {
                    const propSchema = objectSchema.properties[key];
                    if (propSchema && typeof propSchema === 'object') {
                        const propSchemaObj = propSchema;
                        if (propSchemaObj.type === 'array' && propSchemaObj.items) {
                            const items = propSchemaObj.items;
                            // Check if items is a reference or an object type
                            if (items.$ref || items.type === 'object') {
                                isComplexArray = true;
                            }
                        }
                    }
                }
                // If we couldn't determine from schema, check the actual data
                if (!isComplexArray && value.length > 0) {
                    isComplexArray = typeof value[0] === 'object' && value[0] !== null;
                }
                if (isComplexArray) {
                    // For arrays with object-based items, use a header
                    markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);
                    if (value.length === 0) {
                        markdown += 'Empty array\n\n';
                    }
                    else {
                        // Array of objects - create a table
                        const keys = Object.keys(value[0]);
                        // Table header
                        markdown += '| ' + keys.join(' | ') + ' |\n';
                        markdown += '| ' + keys.map(() => '---').join(' | ') + ' |\n';
                        // Table rows
                        value.forEach((item) => {
                            if (typeof item === 'object' && item !== null) {
                                const itemObj = item;
                                markdown += '| ' + keys.map(k => {
                                    const cellValue = itemObj[k];
                                    if (cellValue === null || cellValue === undefined)
                                        return '';
                                    if (typeof cellValue === 'object')
                                        return 'Object';
                                    return String(cellValue).replace(/\n/g, ' ');
                                }).join(' | ') + ' |\n';
                            }
                        });
                        markdown += '\n';
                    }
                }
                else {
                    // For arrays with primitive items, just bold the name (no header)
                    markdown += `**${formatPropertyName(key)}:**\n\n`;
                    if (value.length === 0) {
                        markdown += 'Empty array\n\n';
                    }
                    else {
                        // Simple array - create a list
                        value.forEach((item) => {
                            if (typeof item === 'object' && item !== null) {
                                markdown += '- ' + JSON.stringify(item) + '\n';
                            }
                            else {
                                markdown += '- ' + String(item) + '\n';
                            }
                        });
                        markdown += '\n';
                    }
                }
            }
            else if (typeof value === 'object' && value !== null) {
                // Nested object
                markdown += generateHeaderMarkup(formatPropertyName(key), nestingLevel, widgetOptions);
                // Increase nesting level for recursive call
                markdown += await dataToMarkdown(value, uiSchema && uiSchema[key], nestingLevel + 1);
            }
            else {
                // Simple key-value pair
                markdown += `**${formatPropertyName(key)}:** ${value}\n\n`;
            }
        }
        return markdown;
    }
    // Handle primitive data
    return `${data}\n\n`;
}
/**
 * Render a table widget
 * @param key Property key
 * @param value Array of objects to display in a table
 * @param options Widget options
 * @returns Markdown table representation
 */
function renderTable(key, value, options) {
    if (!Array.isArray(value) || value.length === 0) {
        return `**${formatPropertyName(key)}:** Empty list\n\n`;
    }
    let markdown = '';
    // Get columns to display
    const columns = options.columns || Object.keys(value[0]);
    // Table header
    markdown += '| ' + columns.map((col) => formatPropertyName(col)).join(' | ') + ' |\n';
    markdown += '| ' + columns.map(() => '---').join(' | ') + ' |\n';
    // Table rows
    value.forEach((item) => {
        if (typeof item === 'object' && item !== null) {
            const itemObj = item;
            markdown += '| ' + columns.map((col) => {
                const cellValue = itemObj[col];
                if (cellValue === null || cellValue === undefined)
                    return '';
                if (typeof cellValue === 'object')
                    return 'Object';
                return String(cellValue).replace(/\n/g, ' ');
            }).join(' | ') + ' |\n';
        }
    });
    markdown += '\n';
    return markdown;
}
