import { UISchema, UISchemaOptions } from '../types';
/**
 * Apply UI schema ordering to properties
 * @param properties Object properties to order
 * @param uiOrder Order specified in the UI schema
 * @param data The data object containing the properties
 * @param schema The JSON schema describing the data structure
 * @returns Ordered properties
 */
export declare function applyUIOrder(properties: string[], uiOrder?: string[], data?: Record<string, unknown>, schema?: Record<string, unknown>): string[];
/**
 * Check if a property should be displayed based on ui:if conditions
 * @param propName Property name
 * @param uiSchema UI schema for the property
 * @param data Current data object
 * @returns True if the property should be displayed
 */
export declare function shouldDisplayProperty(propName: string, uiSchema: UISchema, data: Record<string, unknown>): boolean;
/**
 * Get widget type for a property
 * @param propName Property name
 * @param uiSchema UI schema
 * @returns Widget type or undefined if not specified
 */
export declare function getWidgetType(propName: string, uiSchema: UISchema): string | undefined;
/**
 * Get widget options for a property
 * @param propName Property name
 * @param uiSchema UI schema
 * @returns Widget options or undefined if not specified
 */
export declare function getWidgetOptions(propName: string, uiSchema: UISchema): UISchemaOptions | undefined;
/**
 * Determine the appropriate widget type for a schema property
 * @param schema JSON schema for the property
 * @returns The appropriate widget type
 */
export declare function determineWidgetType(schema: Record<string, unknown>): string;
/**
 * Create a default UI schema for a JSON schema
 * @param schema JSON schema
 * @returns Default UI schema
 */
export declare function createDefaultUISchema(schema: Record<string, unknown>): UISchema;
