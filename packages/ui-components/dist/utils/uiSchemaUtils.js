/**
 * Apply UI schema ordering to properties
 * @param properties Object properties to order
 * @param uiOrder Order specified in the UI schema
 * @param data The data object containing the properties
 * @param schema The JSON schema describing the data structure
 * @returns Ordered properties
 */
export function applyUIOrder(properties, uiOrder, data, schema) {
    // If no data or schema is provided, just use the UI schema order
    if (!data && !schema) {
        if (!uiOrder || uiOrder.length === 0)
            return properties;
        const result = [];
        // Add properties in the specified order
        for (const prop of uiOrder) {
            if (properties.includes(prop)) {
                result.push(prop);
            }
        }
        // Add remaining properties that weren't in the order
        for (const prop of properties) {
            if (!result.includes(prop)) {
                result.push(prop);
            }
        }
        return result;
    }
    // Sort properties by type
    const simpleProps = [];
    const simpleArrayProps = []; // Arrays without object-based items
    const complexProps = []; // Objects and arrays with object-based items
    // Separate properties into different categories
    for (const prop of properties) {
        // Try to determine property type from schema first
        if (schema && schema.properties && typeof schema.properties === 'object') {
            const propSchema = schema.properties[prop];
            if (propSchema && typeof propSchema === 'object') {
                const propType = propSchema.type;
                if (propType === 'object') {
                    // Objects are always complex
                    complexProps.push(prop);
                    continue;
                }
                else if (propType === 'array') {
                    // Check if array items are objects or references to objects
                    const items = propSchema.items;
                    if (items && typeof items === 'object') {
                        const itemsObj = items;
                        // Check if it's a reference
                        if (itemsObj.$ref) {
                            // References to definitions are typically complex objects
                            complexProps.push(prop);
                            continue;
                        }
                        const itemType = itemsObj.type;
                        if (itemType === 'object') {
                            // Arrays of objects are complex
                            complexProps.push(prop);
                            continue;
                        }
                        else {
                            // Arrays of primitives are simple arrays
                            simpleArrayProps.push(prop);
                            continue;
                        }
                    }
                }
                else if (propType === 'string' || propType === 'number' || propType === 'integer' || propType === 'boolean') {
                    // Primitive types are simple
                    simpleProps.push(prop);
                    continue;
                }
            }
        }
        // If we couldn't determine from schema, fall back to checking the actual data
        if (data) {
            const value = data[prop];
            if (value === null || value === undefined || typeof value !== 'object') {
                // Simple values (strings, numbers, booleans)
                simpleProps.push(prop);
            }
            else if (Array.isArray(value)) {
                // Check if the array contains object-based items
                const hasObjectItems = value.length > 0 && typeof value[0] === 'object' && value[0] !== null;
                if (hasObjectItems) {
                    // Arrays with object-based items go with complex props
                    complexProps.push(prop);
                }
                else {
                    // Arrays with primitive items go with simple array props
                    simpleArrayProps.push(prop);
                }
            }
            else {
                // Objects go with complex props
                complexProps.push(prop);
            }
        }
        else {
            // If we have no data and couldn't determine from schema, default to simple
            simpleProps.push(prop);
        }
    }
    // If UI order is provided, respect it within each group
    if (uiOrder && uiOrder.length > 0) {
        const orderedSimple = [];
        const orderedSimpleArray = [];
        const orderedComplex = [];
        // Add properties in the specified order, maintaining the grouping
        for (const prop of uiOrder) {
            if (simpleProps.includes(prop) && !orderedSimple.includes(prop)) {
                orderedSimple.push(prop);
            }
            else if (simpleArrayProps.includes(prop) && !orderedSimpleArray.includes(prop)) {
                orderedSimpleArray.push(prop);
            }
            else if (complexProps.includes(prop) && !orderedComplex.includes(prop)) {
                orderedComplex.push(prop);
            }
        }
        // Add remaining properties that weren't in the order
        for (const prop of simpleProps) {
            if (!orderedSimple.includes(prop)) {
                orderedSimple.push(prop);
            }
        }
        for (const prop of simpleArrayProps) {
            if (!orderedSimpleArray.includes(prop)) {
                orderedSimpleArray.push(prop);
            }
        }
        for (const prop of complexProps) {
            if (!orderedComplex.includes(prop)) {
                orderedComplex.push(prop);
            }
        }
        // Return in the desired order: simple values, then simple arrays, then complex objects/arrays
        return [...orderedSimple, ...orderedSimpleArray, ...orderedComplex];
    }
    // If no UI order is provided, return in the desired order
    return [...simpleProps, ...simpleArrayProps, ...complexProps];
}
/**
 * Check if a property should be displayed based on ui:if conditions
 * @param propName Property name
 * @param uiSchema UI schema for the property
 * @param data Current data object
 * @returns True if the property should be displayed
 */
export function shouldDisplayProperty(propName, uiSchema, data) {
    const hideFields = uiSchema ? uiSchema['hideFields'] : undefined;
    const displayFields = uiSchema ? uiSchema['displayFields'] : undefined;
    if (hideFields && hideFields.includes(propName)) {
        return false;
    }
    if (displayFields && !displayFields.includes(propName)) {
        return false;
    }
    const propUISchema = uiSchema[propName];
    // If no UI schema or no ui:if, always display
    if (!propUISchema || !propUISchema['ui:if'])
        return true;
    // If ui:widget is hidden, don't display
    if (propUISchema['ui:widget'] === 'hidden')
        return false;
    // Check conditions
    const conditions = propUISchema['ui:if'];
    for (const [field, expectedValue] of Object.entries(conditions)) {
        // If the field doesn't exist in the data, condition fails
        if (!data || data[field] === undefined)
            return false;
        // If the value doesn't match, condition fails
        if (data[field] !== expectedValue)
            return false;
    }
    // All conditions passed
    return true;
}
/**
 * Get widget type for a property
 * @param propName Property name
 * @param uiSchema UI schema
 * @returns Widget type or undefined if not specified
 */
export function getWidgetType(propName, uiSchema) {
    const propUISchema = uiSchema[propName];
    return propUISchema?.['ui:widget'];
}
/**
 * Get widget options for a property
 * @param propName Property name
 * @param uiSchema UI schema
 * @returns Widget options or undefined if not specified
 */
export function getWidgetOptions(propName, uiSchema) {
    const propUISchema = uiSchema[propName];
    return propUISchema?.['ui:options'];
}
/**
 * Determine the appropriate widget type for a schema property
 * @param schema JSON schema for the property
 * @returns The appropriate widget type
 */
export function determineWidgetType(schema) {
    if (!schema)
        return 'string';
    const type = schema.type;
    if (type === 'object') {
        return 'object';
    }
    else if (type === 'array') {
        const items = schema.items;
        if (items && typeof items === 'object') {
            const itemType = items.type;
            if (itemType === 'object' || items.$ref) {
                return 'table';
            }
            else {
                return 'array';
            }
        }
        return 'array';
    }
    else if (type === 'string') {
        const format = schema.format;
        if (format === 'date') {
            return 'date';
        }
        else if (format === 'date-time') {
            return 'datetime';
        }
        else if (format === 'email') {
            return 'email';
        }
        else if (format === 'uri' || format === 'url') {
            return 'url';
        }
        else if (schema.enum && Array.isArray(schema.enum)) {
            return 'select';
        }
        return 'string';
    }
    else if (type === 'number' || type === 'integer') {
        return 'number';
    }
    else if (type === 'boolean') {
        return 'boolean';
    }
    return 'string';
}
/**
 * Create a default UI schema for a JSON schema
 * @param schema JSON schema
 * @returns Default UI schema
 */
export function createDefaultUISchema(schema) {
    const uiSchema = {};
    if (!schema || !schema.properties || typeof schema.properties !== 'object') {
        return uiSchema;
    }
    const properties = schema.properties;
    // Create UI schema for each property
    for (const [propName, propSchema] of Object.entries(properties)) {
        if (typeof propSchema === 'object' && propSchema !== null) {
            const widgetType = determineWidgetType(propSchema);
            // Only add widget type if it's not the default
            if (widgetType !== 'string') {
                uiSchema[propName] = {
                    'ui:widget': widgetType
                };
            }
            // For objects, recursively create UI schema
            if (widgetType === 'object' && propSchema.properties) {
                const nestedUISchema = createDefaultUISchema(propSchema);
                if (Object.keys(nestedUISchema).length > 0) {
                    if (!uiSchema[propName]) {
                        uiSchema[propName] = {};
                    }
                    Object.assign(uiSchema[propName], nestedUISchema);
                }
            }
        }
    }
    return uiSchema;
}
