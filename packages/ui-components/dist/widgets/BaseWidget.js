import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from '@mantine/core';
import { formatPropertyName } from '../utils';
/**
 * Base widget component that all other widgets extend
 */
export const BaseWidget = ({ name, value, children
// Other props are passed but not used in this component:
// path, schema, uiSchema, parentData, nestingLevel
 }) => {
    // Format property name for display
    const displayName = formatPropertyName(name);
    return (_jsxs(Box, { mb: "md", children: [_jsx(Text, { fw: 500, children: displayName }), _jsx(Box, { pl: 10, children: children || (_jsx(Text, { children: value === null || value === undefined ? 'null' : String(value) })) })] }));
};
