import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useCallback, createElement } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Box, Collapse, Group, ActionIcon } from '@mantine/core';
import { IconChevronRight, IconChevronDown } from '@tabler/icons-react';
function CollapsibleSection({ heading, level, children, collapsible, lineThreshold }) {
    // Determine initial open state based on:
    // - Top-level headers start expanded by default
    // - If lineThreshold is provided, check if content exceeds threshold
    const contentText = React.Children.toArray(children)
        .filter(child => typeof child === 'string')
        .join('');
    const contentLines = contentText.split('\n').length;
    const shouldCollapseByDefault = lineThreshold ? contentLines > lineThreshold : false;
    const [isOpen, setIsOpen] = useState(level === 1 && !shouldCollapseByDefault);
    const toggleOpen = useCallback(() => {
        setIsOpen(prev => !prev);
    }, []);
    // If not collapsible, render as a regular section
    if (!collapsible) {
        return (_jsxs("div", { className: `section level-${level}`, children: [createElement(`h${level}`, { style: { margin: 0 } }, heading), _jsx(Box, { pl: 10, pt: 10, className: "section-content", children: children })] }));
    }
    return (_jsxs("div", { className: `collapsible-section level-${level}`, children: [_jsxs(Group, { onClick: toggleOpen, className: "collapsible-header", style: { cursor: 'pointer' }, children: [_jsx(ActionIcon, { size: "sm", variant: "transparent", children: isOpen ? _jsx(IconChevronDown, { size: 16 }) : _jsx(IconChevronRight, { size: 16 }) }), createElement(`h${level}`, { style: { margin: 0 } }, heading)] }), _jsx(Collapse, { in: isOpen, children: _jsx(Box, { pl: 20, className: "collapsible-content", children: children }) })] }));
}
export function CollapsibleMarkdown({ markdown }) {
    // Parse the markdown to extract sections with their headings and content
    const sections = parseMarkdownSections(markdown);
    // Render the sections recursively
    return (_jsx("div", { className: "markdown-content", children: renderSections(sections) }));
}
function parseMarkdownSections(markdown) {
    const lines = markdown.split('\n');
    const rootSections = [];
    let currentSection = null;
    let currentContent = '';
    let preambleContent = '';
    let foundFirstHeading = false;
    let collapsible = true; // Default to collapsible
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        // Check if the line is a collapsible metadata comment
        const collapsibleMatch = line.match(/<!--\s*collapsible:(true|false)\s*-->/);
        if (collapsibleMatch) {
            collapsible = collapsibleMatch[1] === 'true';
            continue; // Skip this line
        }
        // Check if the line is a line threshold metadata comment
        const lineThresholdMatch = line.match(/<!--\s*lineThreshold:(\d+)\s*-->/);
        if (lineThresholdMatch && currentSection) {
            currentSection.lineThreshold = parseInt(lineThresholdMatch[1], 10);
            continue; // Skip this line
        }
        // Check if the line is a heading
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
        if (headingMatch) {
            foundFirstHeading = true;
            // If we were building a section, finalize it
            if (currentSection) {
                currentSection.content = currentContent.trim();
                currentContent = '';
            }
            const level = headingMatch[1].length;
            const heading = headingMatch[2];
            // Create a new section
            const newSection = {
                heading,
                level,
                content: '',
                children: [],
                collapsible
            };
            // Reset collapsible to default for next section
            collapsible = true;
            // Add the section to the appropriate parent
            if (level === 1 || rootSections.length === 0) {
                rootSections.push(newSection);
            }
            else {
                // Find the appropriate parent section
                const parent = findParentSection(rootSections, level);
                if (parent) {
                    parent.children.push(newSection);
                }
                else {
                    // If no parent found, add to root
                    rootSections.push(newSection);
                }
            }
            currentSection = newSection;
        }
        else if (currentSection) {
            // Add the line to the current section's content
            currentContent += line + '\n';
        }
        else if (!foundFirstHeading) {
            // Content before the first heading
            preambleContent += line + '\n';
        }
    }
    // Finalize the last section
    if (currentSection) {
        currentSection.content = currentContent.trim();
    }
    // If there's content before the first heading, create a special section for it
    if (preambleContent.trim()) {
        rootSections.unshift({
            heading: 'Introduction',
            level: 1,
            content: preambleContent.trim(),
            children: [],
            collapsible: true
        });
    }
    // If no sections were found, create a default one with all content
    if (rootSections.length === 0 && (preambleContent.trim() || currentContent.trim())) {
        rootSections.push({
            heading: 'Content',
            level: 1,
            content: (preambleContent + currentContent).trim(),
            children: [],
            collapsible: true
        });
    }
    return rootSections;
}
function findParentSection(sections, level) {
    // Start from the last section and work backwards
    for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i];
        // If this section's level is less than the target level, it's a potential parent
        if (section.level < level) {
            return section;
        }
        // Check this section's children
        const childParent = findParentSection(section.children, level);
        if (childParent) {
            return childParent;
        }
    }
    return null;
}
// Helper function to render sections with a flag to indicate if it's the root level
function renderSectionsWithLevel(sections, isRootLevel) {
    // Group sections by level to ensure consistent indentation for siblings
    const sectionsByLevel = {};
    // Group sections by their level
    sections.forEach(section => {
        if (!sectionsByLevel[section.level]) {
            sectionsByLevel[section.level] = [];
        }
        sectionsByLevel[section.level].push(section);
    });
    // Render sections level by level
    return Object.entries(sectionsByLevel).flatMap(([level, levelSections]) => {
        return levelSections.map((section, index) => {
            // Special case for the first section at the root level only
            if (isRootLevel && level === '1' && index === 0) {
                return (_jsxs(Box, { ml: 5, children: [_jsx(ReactMarkdown, { remarkPlugins: [remarkGfm], children: section.content }), renderSectionsWithLevel(section.children, false)] }, `root-${index}`));
            }
            // Normal case for all other sections
            return (_jsxs(CollapsibleSection, { heading: section.heading, level: section.level, collapsible: section.collapsible, lineThreshold: section.lineThreshold, children: [_jsx(ReactMarkdown, { remarkPlugins: [remarkGfm], children: section.content }), renderSectionsWithLevel(section.children, false)] }, `${section.level}-${index}`));
        });
    });
}
// Main render function that calls the helper with isRootLevel=true
function renderSections(sections) {
    return renderSectionsWithLevel(sections, true);
}
