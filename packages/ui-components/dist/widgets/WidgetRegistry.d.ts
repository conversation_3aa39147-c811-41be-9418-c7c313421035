import { WidgetDefinition, WidgetRegistry } from '../types';
/**
 * Implementation of the widget registry
 */
export declare class DefaultWidgetRegistry implements WidgetRegistry {
    private widgets;
    /**
     * Get a widget by type
     * @param type Widget type
     * @returns The widget definition or undefined if not found
     */
    getWidget(type: string): WidgetDefinition | undefined;
    /**
     * Get all registered widgets
     * @returns Array of widget definitions
     */
    getAllWidgets(): WidgetDefinition[];
    /**
     * Get widgets valid for a schema type
     * @param schemaType JSON schema type
     * @returns Array of widget definitions valid for the schema type
     */
    getWidgetsForType(schemaType: string): WidgetDefinition[];
    /**
     * Register a new widget
     * @param widget Widget definition
     */
    registerWidget(widget: WidgetDefinition): void;
}
/**
 * Create a new widget registry with default widgets
 * @returns A new widget registry
 */
export declare function createWidgetRegistry(): WidgetRegistry;
