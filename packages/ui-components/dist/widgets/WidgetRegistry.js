/**
 * Implementation of the widget registry
 */
export class DefaultWidgetRegistry {
    constructor() {
        Object.defineProperty(this, "widgets", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
    }
    /**
     * Get a widget by type
     * @param type Widget type
     * @returns The widget definition or undefined if not found
     */
    getWidget(type) {
        return this.widgets.get(type);
    }
    /**
     * Get all registered widgets
     * @returns Array of widget definitions
     */
    getAllWidgets() {
        return Array.from(this.widgets.values());
    }
    /**
     * Get widgets valid for a schema type
     * @param schemaType JSON schema type
     * @returns Array of widget definitions valid for the schema type
     */
    getWidgetsForType(schemaType) {
        return this.getAllWidgets().filter(widget => widget.validForTypes.includes(schemaType));
    }
    /**
     * Register a new widget
     * @param widget Widget definition
     */
    registerWidget(widget) {
        this.widgets.set(widget.type, widget);
    }
}
/**
 * Create a new widget registry with default widgets
 * @returns A new widget registry
 */
export function createWidgetRegistry() {
    const registry = new DefaultWidgetRegistry();
    // Register default widgets here when they are implemented
    // Example: registry.registerWidget(stringWidget);
    return registry;
}
