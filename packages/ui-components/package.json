{"name": "@hospice-os/ui-components", "version": "0.0.1", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "dependencies": {"@hospice-os/apptypes": "0.0.1", "@mantine/core": "^7.17.3", "@mantine/hooks": "^7.17.3", "@tabler/icons-react": "^3.31.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.0"}, "devDependencies": {"@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "typescript": "~5.7.2"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}