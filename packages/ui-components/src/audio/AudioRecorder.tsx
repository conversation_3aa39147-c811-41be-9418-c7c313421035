import { Button, Select, Group, Box } from "@mantine/core";
import {
  IconMicrophone,
  IconMicrophoneOff,
  IconPlayerPause,
  IconPlayerPlay,
} from "@tabler/icons-react";
import {
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useReactMediaRecorder } from "react-media-recorder";

export interface AudioRecorderHandle {
  start: () => void;
  stop: () => void;
  pause: () => void;
  resume: () => void;
  status: string;
  clear: () => void;
}

export interface AudioRecorderProps {
  onRecordingComplete?: (url: string) => void;
  onStatusChange?: (status: string) => void;
  showStopButton?: boolean;
}

interface AudioLevelIndicatorProps {
  level: number;
  isDetecting: boolean;
}

const AudioLevelIndicator: React.FC<AudioLevelIndicatorProps> = ({
  level,
  isDetecting,
}) => {
  const barCount = 5;
  const bars = Array.from({ length: barCount }, (_, i) => {
    const barHeight = Math.max(4, (level * 200 * (i + 1)) / barCount);
    const opacity = isDetecting ? 0.7 + level * 0.3 : 0.3;

    return (
      <Box
        key={i}
        style={{
          width: 3,
          height: barHeight,
          backgroundColor: isDetecting ? "#40c057" : "#868e96",
          borderRadius: "1px",
          transition: "height 0.1s ease-out, opacity 0.1s ease-out",
          opacity,
        }}
      />
    );
  });

  return (
    <Group gap={2} align="flex-end" style={{ height: 20 }}>
      {bars}
    </Group>
  );
};

export const AudioRecorder = forwardRef<
  AudioRecorderHandle,
  AudioRecorderProps
>(({ onRecordingComplete, onStatusChange, showStopButton = true }, ref) => {
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>("");
  const [audioLevel, setAudioLevel] = useState(0);

  const {
    status,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    mediaBlobUrl,
    clearBlobUrl,
  } = useReactMediaRecorder({
    audio: selectedDevice ? { deviceId: { exact: selectedDevice } } : true,
  });

  useImperativeHandle(
    ref,
    () => ({
      start: startRecording,
      stop: stopRecording,
      pause: pauseRecording,
      resume: resumeRecording,
      status,
      clear: clearBlobUrl,
    }),
    [
      status,
      startRecording,
      stopRecording,
      pauseRecording,
      resumeRecording,
      clearBlobUrl,
    ],
  );

  useEffect(() => {
    if (onStatusChange) {
      onStatusChange(status);
    }
  }, [status, onStatusChange]);

  useEffect(() => {
    if (mediaBlobUrl && onRecordingComplete) {
      onRecordingComplete(mediaBlobUrl);
    }
  }, [mediaBlobUrl]);

  useEffect(() => {
    navigator.mediaDevices
      .enumerateDevices()
      .then((ds) => {
        const inputs = ds.filter((d) => d.kind === "audioinput");
        setDevices(inputs);
        if (!selectedDevice && inputs[0]) {
          setSelectedDevice(inputs[0].deviceId);
        }
      })
      .catch((err) => {
        console.error("Error enumerating devices", err);
      });
  }, [selectedDevice]);

  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const rafRef = useRef<number>(0);

  useEffect(() => {
    if (status === "recording") {
      navigator.mediaDevices
        .getUserMedia({
          audio: selectedDevice
            ? { deviceId: { exact: selectedDevice } }
            : true,
        })
        .then((stream) => {
          audioContextRef.current = new AudioContext();
          const source =
            audioContextRef.current.createMediaStreamSource(stream);
          const analyser = audioContextRef.current.createAnalyser();
          analyser.fftSize = 256;
          source.connect(analyser);
          analyserRef.current = analyser;
          const data = new Uint8Array(analyser.fftSize);
          const update = () => {
            analyser.getByteTimeDomainData(data);
            let sum = 0;
            for (let i = 0; i < data.length; i++) {
              const val = (data[i] - 128) / 128;
              sum += val * val;
            }
            setAudioLevel(Math.sqrt(sum / data.length));
            rafRef.current = requestAnimationFrame(update);
          };
          update();
        })
        .catch((err) => console.error(err));
    }
    return () => {
      if (rafRef.current) cancelAnimationFrame(rafRef.current);
      if (analyserRef.current) analyserRef.current.disconnect();
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
    };
  }, [status, selectedDevice]);

  const isDetectingAudio = audioLevel > 0.02;
  const options = devices.map((d) => ({
    value: d.deviceId,
    label: d.label || `Microphone ${d.deviceId.slice(0, 5)}...`,
  }));

  return (
    <Group gap="xs">
      <Select
        data={options}
        value={selectedDevice}
        onChange={(val) => setSelectedDevice(val ?? "")}
        placeholder="Audio Input"
      />
      {(showStopButton ||
        (!showStopButton && status !== "recording" && status !== "paused")) && (
        <Button
          variant="outline"
          color={status === "recording" || status === "paused" ? "red" : "blue"}
          leftSection={
            status === "recording" || status === "paused" ? (
              <IconMicrophoneOff size={16} />
            ) : (
              <IconMicrophone size={16} />
            )
          }
          onClick={
            status === "recording" || status === "paused"
              ? stopRecording
              : startRecording
          }
        >
          {status === "recording" || status === "paused"
            ? "End Recording"
            : "Record Audio"}
        </Button>
      )}
      {(status === "recording" || status === "paused") && (
        <Button
          variant="outline"
          color={status === "paused" ? "blue" : "orange"}
          leftSection={
            status === "paused" ? (
              <IconPlayerPlay size={16} />
            ) : (
              <IconPlayerPause size={16} />
            )
          }
          onClick={status === "paused" ? resumeRecording : pauseRecording}
        >
          {status === "paused" ? "Resume Recording" : "Pause Recording"}
        </Button>
      )}
      {status === "recording" && (
        <AudioLevelIndicator
          level={audioLevel}
          isDetecting={isDetectingAudio}
        />
      )}
    </Group>
  );
});

AudioRecorder.displayName = "AudioRecorder";
