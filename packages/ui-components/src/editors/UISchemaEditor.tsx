import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  Tabs,
  Text,
  Group,
  Stack,
  Button,
  Select,
  TextInput,
  Checkbox,
  NumberInput,
  ActionIcon,
  Tooltip,
  Divider
} from '@mantine/core';
import { IconPlus, IconTrash, IconArrowUp, IconArrowDown } from '@tabler/icons-react';
import { UISchema, UISchemaWidgetProps } from '../types';
import { determineWidgetType, createDefaultUISchema } from '../utils';

interface UISchemaEditorProps {
  /**
   * The JSON schema to create a UI schema for
   */
  schema: Record<string, unknown>;
  
  /**
   * The current UI schema (if any)
   */
  uiSchema?: UISchema;
  
  /**
   * Callback for when the UI schema changes
   */
  onChange: (newUiSchema: UISchema) => void;
}

export function UISchemaEditor({ schema, uiSchema, onChange }: UISchemaEditorProps) {
  // Initialize UI schema if not provided
  const [currentUISchema, setCurrentUISchema] = useState<UISchema>(
    uiSchema || createDefaultUISchema(schema)
  );
  
  // Update local state when props change
  useEffect(() => {
    if (uiSchema) {
      setCurrentUISchema(uiSchema);
    }
  }, [uiSchema]);
  
  // Get schema properties
  const properties = schema.properties ? Object.keys(schema.properties as Record<string, unknown>) : [];
  
  // Get current property order
  const propertyOrder = currentUISchema['ui:order'] as string[] || [];
  
  // Update UI schema and notify parent
  const updateUISchema = (newUISchema: UISchema) => {
    setCurrentUISchema(newUISchema);
    onChange(newUISchema);
  };
  
  // Update property order
  const updatePropertyOrder = (newOrder: string[]) => {
    const newUISchema = { ...currentUISchema, 'ui:order': newOrder };
    updateUISchema(newUISchema);
  };
  
  // Move property up in order
  const movePropertyUp = (propName: string) => {
    const index = propertyOrder.indexOf(propName);
    if (index > 0) {
      const newOrder = [...propertyOrder];
      [newOrder[index - 1], newOrder[index]] = [newOrder[index], newOrder[index - 1]];
      updatePropertyOrder(newOrder);
    }
  };
  
  // Move property down in order
  const movePropertyDown = (propName: string) => {
    const index = propertyOrder.indexOf(propName);
    if (index < propertyOrder.length - 1) {
      const newOrder = [...propertyOrder];
      [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];
      updatePropertyOrder(newOrder);
    }
  };
  
  // Update property widget type
  const updatePropertyWidget = (propName: string, widgetType: string) => {
    const propUISchema = currentUISchema[propName] as UISchemaWidgetProps || {};
    const newPropUISchema = { ...propUISchema, 'ui:widget': widgetType };
    const newUISchema = { ...currentUISchema, [propName]: newPropUISchema };
    updateUISchema(newUISchema);
  };
  
  // Update property options
  const updatePropertyOptions = (propName: string, options: Record<string, unknown>) => {
    const propUISchema = currentUISchema[propName] as UISchemaWidgetProps || {};
    const newPropUISchema = { 
      ...propUISchema, 
      'ui:options': { ...(propUISchema['ui:options'] || {}), ...options } 
    };
    const newUISchema = { ...currentUISchema, [propName]: newPropUISchema };
    updateUISchema(newUISchema);
  };
  
  // Get property schema
  const getPropertySchema = (propName: string) => {
    if (!schema.properties) return {};
    return (schema.properties as Record<string, unknown>)[propName] as Record<string, unknown> || {};
  };
  
  // Get property widget type
  const getPropertyWidgetType = (propName: string) => {
    const propUISchema = currentUISchema[propName] as UISchemaWidgetProps;
    return propUISchema?.['ui:widget'] || determineWidgetType(getPropertySchema(propName));
  };
  
  // Get property options
  const getPropertyOptions = (propName: string) => {
    const propUISchema = currentUISchema[propName] as UISchemaWidgetProps;
    return propUISchema?.['ui:options'] || {};
  };
  
  return (
    <Box>
      <Tabs defaultValue="properties">
        <Tabs.List>
          <Tabs.Tab value="properties">Properties</Tabs.Tab>
          <Tabs.Tab value="ordering">Ordering</Tabs.Tab>
          <Tabs.Tab value="global">Global Options</Tabs.Tab>
          <Tabs.Tab value="json">JSON</Tabs.Tab>
        </Tabs.List>
        
        <Tabs.Panel value="properties" pt="md">
          <Stack>
            {properties.map(propName => {
              const propSchema = getPropertySchema(propName);
              const widgetType = getPropertyWidgetType(propName);
              const options = getPropertyOptions(propName);
              
              return (
                <Card key={propName} withBorder p="sm">
                  <Group justify="space-between" mb="xs">
                    <Text fw={500}>{propName}</Text>
                    <Text size="xs" c="dimmed">Type: {propSchema.type as string}</Text>
                  </Group>
                  
                  <Stack>
                    <Select
                      label="Widget"
                      value={widgetType}
                      onChange={(value) => updatePropertyWidget(propName, value || 'string')}
                      data={[
                        { value: 'string', label: 'String' },
                        { value: 'number', label: 'Number' },
                        { value: 'boolean', label: 'Boolean' },
                        { value: 'date', label: 'Date' },
                        { value: 'datetime', label: 'Date & Time' },
                        { value: 'select', label: 'Select' },
                        { value: 'table', label: 'Table' },
                        { value: 'array', label: 'Array' },
                        { value: 'object', label: 'Object' },
                        { value: 'resourceDisplay', label: 'Resource Display' },
                        { value: 'hidden', label: 'Hidden' }
                      ]}
                    />
                    
                    <Divider label="Widget Options" />
                    
                    <Checkbox
                      label="Collapsible"
                      checked={!!options.collapsible}
                      onChange={(e) => updatePropertyOptions(propName, { collapsible: e.currentTarget.checked })}
                    />
                    
                    <NumberInput
                      label="Header Level"
                      min={1}
                      max={6}
                      value={options.headerLevel as number || 2}
                      onChange={(value) => updatePropertyOptions(propName, { headerLevel: value })}
                    />
                    
                    <NumberInput
                      label="Line Threshold"
                      min={1}
                      value={options.lineThreshold as number || 10}
                      onChange={(value) => updatePropertyOptions(propName, { lineThreshold: value })}
                    />
                    
                    {widgetType === 'resourceDisplay' && (
                      <>
                        <TextInput
                          label="Resource Type"
                          value={options.resourceType as string || ''}
                          onChange={(e) => updatePropertyOptions(propName, { resourceType: e.currentTarget.value })}
                        />
                        
                        <TextInput
                          label="Title Field"
                          value={options.titleField as string || ''}
                          onChange={(e) => updatePropertyOptions(propName, { titleField: e.currentTarget.value })}
                        />
                      </>
                    )}
                    
                    {widgetType === 'table' && (
                      <TextInput
                        label="Columns (comma-separated)"
                        value={(options.columns as string[])?.join(', ') || ''}
                        onChange={(e) => {
                          const columns = e.currentTarget.value.split(',').map(s => s.trim()).filter(Boolean);
                          updatePropertyOptions(propName, { columns });
                        }}
                      />
                    )}
                  </Stack>
                </Card>
              );
            })}
          </Stack>
        </Tabs.Panel>
        
        <Tabs.Panel value="ordering" pt="md">
          <Stack>
            <Group>
              <Button 
                leftSection={<IconPlus size={16} />}
                onClick={() => {
                  // Add all properties that aren't already in the order
                  const missingProps = properties.filter(p => !propertyOrder.includes(p));
                  if (missingProps.length > 0) {
                    updatePropertyOrder([...propertyOrder, ...missingProps]);
                  }
                }}
              >
                Add All Missing
              </Button>
            </Group>
            
            {propertyOrder.map((propName, index) => (
              <Card key={propName} withBorder p="sm">
                <Group justify="space-between">
                  <Text>{propName}</Text>
                  <Group>
                    <Tooltip label="Move Up">
                      <ActionIcon 
                        disabled={index === 0} 
                        onClick={() => movePropertyUp(propName)}
                      >
                        <IconArrowUp size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Move Down">
                      <ActionIcon 
                        disabled={index === propertyOrder.length - 1} 
                        onClick={() => movePropertyDown(propName)}
                      >
                        <IconArrowDown size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Remove">
                      <ActionIcon 
                        color="red" 
                        onClick={() => {
                          const newOrder = propertyOrder.filter(p => p !== propName);
                          updatePropertyOrder(newOrder);
                        }}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Group>
              </Card>
            ))}
            
            {/* Properties not in order */}
            {properties.filter(p => !propertyOrder.includes(p)).length > 0 && (
              <>
                <Divider label="Properties Not in Order" />
                {properties.filter(p => !propertyOrder.includes(p)).map(propName => (
                  <Card key={propName} withBorder p="sm">
                    <Group justify="space-between">
                      <Text>{propName}</Text>
                      <Button 
                        leftSection={<IconPlus size={16} />}
                        onClick={() => {
                          updatePropertyOrder([...propertyOrder, propName]);
                        }}
                      >
                        Add
                      </Button>
                    </Group>
                  </Card>
                ))}
              </>
            )}
          </Stack>
        </Tabs.Panel>
        
        <Tabs.Panel value="global" pt="md">
          <Stack>
            <TextInput
              label="Display Fields (comma-separated)"
              value={(currentUISchema.displayFields as string[])?.join(', ') || ''}
              onChange={(e) => {
                const displayFields = e.currentTarget.value.split(',').map(s => s.trim()).filter(Boolean);
                updateUISchema({ ...currentUISchema, displayFields });
              }}
            />
            
            <TextInput
              label="Hide Fields (comma-separated)"
              value={(currentUISchema.hideFields as string[])?.join(', ') || ''}
              onChange={(e) => {
                const hideFields = e.currentTarget.value.split(',').map(s => s.trim()).filter(Boolean);
                updateUISchema({ ...currentUISchema, hideFields });
              }}
            />
          </Stack>
        </Tabs.Panel>
        
        <Tabs.Panel value="json" pt="md">
          <pre style={{ whiteSpace: 'pre-wrap' }}>
            {JSON.stringify(currentUISchema, null, 2)}
          </pre>
        </Tabs.Panel>
      </Tabs>
    </Box>
  );
}
