import { useState, useEffect } from 'react';
import { Box, Paper, Text, Loader, Stack, Tabs, Select } from '@mantine/core';
import { UISchema } from '../types';
import { CollapsibleMarkdown } from '../widgets';

interface UISchemaPreviewProps {
  /**
   * The JSON schema to preview
   */
  schema: Record<string, unknown>;
  
  /**
   * The UI schema to preview
   */
  uiSchema: UISchema;
  
  /**
   * Sample data to use for the preview (optional)
   */
  sampleData?: Record<string, unknown>;
}

export function UISchemaPreview({ schema, uiSchema, sampleData }: UISchemaPreviewProps) {
  const [markdownContent, setMarkdownContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<string>('desktop');
  
  // Generate markdown when schema, uiSchema, or sampleData change
  useEffect(() => {
    const generateMarkdown = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Import from utils index to avoid path issues
        const { dataToMarkdown } = await import('../utils');
        
        // Use provided sample data or generate from schema
        const data = sampleData || await generateSampleData(schema);
        
        // Generate markdown
        const markdown = await dataToMarkdown(data, uiSchema, 0, schema);
        
        setMarkdownContent(markdown);
      } catch (err) {
        console.error('Error generating preview:', err);
        setError('Failed to generate preview. Please check the console for details.');
      } finally {
        setIsLoading(false);
      }
    };
    
    generateMarkdown();
  }, [schema, uiSchema, sampleData]);
  
  // Get preview container style based on view mode
  const getPreviewStyle = () => {
    switch (viewMode) {
      case 'mobile':
        return { maxWidth: '375px', margin: '0 auto' };
      case 'tablet':
        return { maxWidth: '768px', margin: '0 auto' };
      case 'desktop':
      default:
        return {};
    }
  };
  
  return (
    <Box>
      <Tabs defaultValue="preview">
        <Tabs.List>
          <Tabs.Tab value="preview">Preview</Tabs.Tab>
          <Tabs.Tab value="markdown">Markdown</Tabs.Tab>
        </Tabs.List>
        
        <Box mt="md" mb="md">
          <Select
            label="View Mode"
            value={viewMode}
            onChange={(value) => setViewMode(value || 'desktop')}
            data={[
              { value: 'desktop', label: 'Desktop' },
              { value: 'tablet', label: 'Tablet' },
              { value: 'mobile', label: 'Mobile' }
            ]}
          />
        </Box>
        
        <Tabs.Panel value="preview" pt="md">
          <Paper p="md" withBorder style={getPreviewStyle()}>
            {isLoading ? (
              <Stack align="center" gap="md">
                <Loader size="md" />
                <Text>Generating preview...</Text>
              </Stack>
            ) : error ? (
              <Text color="red">{error}</Text>
            ) : (
              <CollapsibleMarkdown markdown={markdownContent} />
            )}
          </Paper>
        </Tabs.Panel>
        
        <Tabs.Panel value="markdown" pt="md">
          <Paper p="md" withBorder>
            {isLoading ? (
              <Stack align="center" gap="md">
                <Loader size="md" />
                <Text>Generating markdown...</Text>
              </Stack>
            ) : error ? (
              <Text color="red">{error}</Text>
            ) : (
              <pre style={{ whiteSpace: 'pre-wrap', overflow: 'auto' }}>
                {markdownContent}
              </pre>
            )}
          </Paper>
        </Tabs.Panel>
      </Tabs>
    </Box>
  );
}

/**
 * Generate sample data based on a JSON schema
 * @param schema The JSON schema to generate sample data for
 * @returns Sample data that conforms to the schema
 */
async function generateSampleData(schema: Record<string, unknown>): Promise<Record<string, unknown>> {
  // Simple implementation for now - can be expanded later
  const result: Record<string, unknown> = {};
  
  if (!schema.properties || typeof schema.properties !== 'object') {
    return result;
  }
  
  const properties = schema.properties as Record<string, unknown>;
  
  // Generate sample data for each property
  for (const [propName, propSchema] of Object.entries(properties)) {
    if (typeof propSchema !== 'object' || propSchema === null) {
      continue;
    }
    
    const type = (propSchema as Record<string, unknown>).type as string;
    
    switch (type) {
      case 'string':
        result[propName] = `Sample ${propName}`;
        break;
      case 'number':
      case 'integer':
        result[propName] = 42;
        break;
      case 'boolean':
        result[propName] = true;
        break;
      case 'array':
        result[propName] = [
          { id: '1', name: 'Sample Item 1' },
          { id: '2', name: 'Sample Item 2' }
        ];
        break;
      case 'object':
        result[propName] = await generateSampleData(propSchema as Record<string, unknown>);
        break;
      default:
        result[propName] = null;
    }
  }
  
  return result;
}
