import React from 'react';
import { WidgetProps } from '../types';
import { Box, Text } from '@mantine/core';
import { formatPropertyName } from '../utils';

/**
 * Base widget component that all other widgets extend
 */
export const BaseWidget: React.FC<WidgetProps> = ({
  name,
  value,
  children
  // Other props are passed but not used in this component:
  // path, schema, uiSchema, parentData, nestingLevel
}) => {
  // Format property name for display
  const displayName = formatPropertyName(name);
  
  return (
    <Box mb="md">
      <Text fw={500}>{displayName}</Text>
      <Box pl={10}>
        {children || (
          <Text>{value === null || value === undefined ? 'null' : String(value)}</Text>
        )}
      </Box>
    </Box>
  );
};
