# Comprehensive Temporal Implementation Plan for Hospice OS

## Executive Summary
This plan outlines a systematic approach to enhance the Temporal implementation in Hospice OS. The plan addresses critical gaps in the current implementation including error handling, testing, compliance workflows, and security for PHI data. Implementation is structured in 3 phases over 8-10 weeks.

## Current State Analysis

### Existing Implementation
- **3 workflows**: patientRequestWorkflow, pdfWorkflow, requestWorkflow
- **Duplicate client code** in temporal.ts and client.ts
- **Basic timeout configuration** (uniform 15-minute timeouts)
- **No retry policies** configured
- **No tests** for workflows or activities
- **No error categorization** or structured error handling
- **Single queue** for all workflow types

### Critical Gaps
1. No scheduled/cron workflows for compliance monitoring
2. Missing human-in-the-loop approval patterns
3. No PHI security layer or audit trail
4. Lack of production-ready error handling and retries
5. No workflow versioning strategy
6. Missing observability and monitoring

## Phase 1: Foundation (Weeks 1-3)

### Week 1: Core Infrastructure

#### 1.1 Consolidate Client Implementations
**Objective**: Merge duplicate code from temporal.ts and client.ts into a unified client

**Implementation**:
```typescript
// apps/server/src/temporal/temporal-client.ts
export class TemporalClient {
  private static instance: TemporalClient;
  private connection: Connection;
  private clients: Map<string, Client> = new Map();
  
  // Singleton pattern with lazy initialization
  static async getInstance(): Promise<TemporalClient> {
    if (!this.instance) {
      this.instance = new TemporalClient();
      await this.instance.connect();
    }
    return this.instance;
  }
  
  // Connection management with retry logic
  private async connect(): Promise<void> {
    const config = this.getStageConfig();
    this.connection = await Connection.connect({
      address: config.address,
      tls: config.tls,
      // Retry configuration
      connectionOptions: {
        initialReconnectInterval: 1000,
        maxReconnectInterval: 30000,
        reconnectBackoff: 2
      }
    });
  }
  
  // Health check
  async isHealthy(): Promise<boolean> {
    try {
      await this.connection.workflowService.getSystemInfo({});
      return true;
    } catch {
      return false;
    }
  }
}
```

#### 1.2 Implement Error Hierarchy
**Objective**: Create structured error types for better error handling

**Implementation**:
```typescript
// apps/server/src/temporal/errors.ts
export class TemporalError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly isRetryable: boolean,
    public readonly context?: Record<string, any>
  ) {
    super(message);
  }
}

export class RetryableError extends TemporalError {
  constructor(message: string, code: string, context?: Record<string, any>) {
    super(message, code, true, context);
  }
}

export class NonRetryableError extends TemporalError {
  constructor(message: string, code: string, context?: Record<string, any>) {
    super(message, code, false, context);
  }
}

// Specific error types
export class ValidationError extends NonRetryableError {}
export class RateLimitError extends RetryableError {}
export class NetworkError extends RetryableError {}
export class BusinessLogicError extends NonRetryableError {}
```

#### 1.3 Configure Retry Policies
**Objective**: Implement activity-specific retry policies

**Implementation**:
```typescript
// apps/server/src/temporal/retry-policies.ts
export const RetryPolicies = {
  // OpenAI API calls - expensive, rate-limited
  openAI: {
    initialInterval: '10s',
    backoffCoefficient: 3,
    maxInterval: '5m',
    maxAttempts: 3,
    nonRetryableErrorTypes: ['ValidationError', 'TokenLimitExceeded']
  },
  
  // Database operations - quick retry
  database: {
    initialInterval: '100ms',
    backoffCoefficient: 2,
    maxInterval: '5s',
    maxAttempts: 3,
    nonRetryableErrorTypes: ['ConstraintError', 'ValidationError']
  },
  
  // Transcription services
  transcription: {
    initialInterval: '5s',
    backoffCoefficient: 2,
    maxInterval: '2m',
    maxAttempts: 5,
    nonRetryableErrorTypes: ['InvalidAudioFormat', 'FileTooLarge']
  },
  
  // S3 operations
  s3: {
    initialInterval: '1s',
    backoffCoefficient: 2,
    maxInterval: '30s',
    maxAttempts: 4,
    nonRetryableErrorTypes: ['AccessDenied', 'NoSuchBucket']
  }
};

// Activity timeout profiles
export const TimeoutProfiles = {
  quick: { startToCloseTimeout: '30s', heartbeatTimeout: '10s' },
  standard: { startToCloseTimeout: '2m', heartbeatTimeout: '30s' },
  ai: { startToCloseTimeout: '5m', heartbeatTimeout: '1m' },
  longRunning: { startToCloseTimeout: '30m', heartbeatTimeout: '2m' }
};
```

### Week 2: Testing & Dead Letter Queue

#### 2.1 Implement Test Suite
**Objective**: Create comprehensive tests for workflows and activities

**Implementation**:
```typescript
// apps/server/src/temporal/__tests__/workflows.test.ts
import { TestWorkflowEnvironment } from '@temporalio/testing';

describe('PatientRequestWorkflow', () => {
  let testEnv: TestWorkflowEnvironment;
  
  beforeEach(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
  });
  
  afterEach(async () => {
    await testEnv.teardown();
  });
  
  test('should handle successful transcription and processing', async () => {
    const { client, nativeConnection } = testEnv;
    const worker = await Worker.create({
      connection: nativeConnection,
      workflowsPath: require.resolve('../workflows'),
      activities: mockActivities,
    });
    
    const result = await worker.runUntil(
      client.workflow.execute(patientRequestWorkflow, {
        workflowId: 'test-request-1',
        args: [mockRequest],
      })
    );
    
    expect(result.status).toBe('completed');
  });
  
  test('should retry on transient failures', async () => {
    // Test retry logic
  });
  
  test('should handle non-retryable errors', async () => {
    // Test permanent failure handling
  });
});
```

#### 2.2 Dead Letter Queue Implementation
**Objective**: Handle permanently failed workflows

**Implementation**:
```typescript
// apps/server/src/temporal/dead-letter-queue.ts
export class DeadLetterQueue {
  private readonly tableName = 'workflow_failures';
  
  async recordFailure(
    workflowId: string,
    workflowType: string,
    error: Error,
    attempts: number,
    input: any
  ): Promise<void> {
    await db.insert(this.tableName, {
      workflow_id: workflowId,
      workflow_type: workflowType,
      error_message: error.message,
      error_stack: error.stack,
      attempts,
      input: JSON.stringify(input),
      failed_at: new Date(),
      retry_eligible: this.isRetryEligible(error),
    });
    
    // Send alert for critical failures
    if (this.isCriticalFailure(workflowType)) {
      await this.alertOps(workflowId, error);
    }
  }
  
  async retryFailed(workflowId: string): Promise<void> {
    const failure = await db.findOne(this.tableName, { workflow_id: workflowId });
    if (!failure || !failure.retry_eligible) {
      throw new Error('Workflow not eligible for retry');
    }
    
    // Restart workflow with original input
    await temporalClient.startWorkflow(failure.workflow_type, {
      workflowId: `${workflowId}-retry-${Date.now()}`,
      args: [JSON.parse(failure.input)],
    });
  }
}
```

### Week 3: Configuration & Versioning

#### 3.1 Environment Configuration
**Objective**: Extract hardcoded values to environment variables

**Implementation**:
```typescript
// apps/server/src/config/temporal.config.ts
export const temporalConfig = {
  connection: {
    address: process.env.TEMPORAL_ADDRESS || 'localhost:7233',
    namespace: process.env.TEMPORAL_NAMESPACE || 'default',
    tls: process.env.TEMPORAL_TLS_ENABLED === 'true' ? {
      clientCertPair: {
        crt: readFileSync(process.env.TEMPORAL_CERT_PATH!),
        key: readFileSync(process.env.TEMPORAL_KEY_PATH!),
      },
    } : undefined,
  },
  
  worker: {
    taskQueue: process.env.TEMPORAL_TASK_QUEUE || 'main-v1.0',
    maxConcurrentActivityExecutions: parseInt(process.env.TEMPORAL_MAX_CONCURRENT_ACTIVITIES || '10'),
    maxConcurrentWorkflowExecutions: parseInt(process.env.TEMPORAL_MAX_CONCURRENT_WORKFLOWS || '10'),
  },
  
  queues: {
    main: process.env.TEMPORAL_QUEUE_MAIN || 'main-v1.0',
    compliance: process.env.TEMPORAL_QUEUE_COMPLIANCE || 'compliance-v1.0',
    batch: process.env.TEMPORAL_QUEUE_BATCH || 'batch-v1.0',
  },
};
```

#### 3.2 Workflow Versioning
**Objective**: Enable safe production deployments with versioning

**Implementation**:
```typescript
// apps/server/src/temporal/versioning.ts
import { patched, deprecatePatch } from '@temporalio/workflow';

export function workflowVersion(version: number) {
  return function (target: any) {
    target.version = version;
  };
}

// Example versioned workflow
@workflowVersion(2)
export async function patientRequestWorkflowV2(request: PatientRequest) {
  // Version 2 adds new validation step
  const isV2 = patched('add-validation-v2');
  
  if (isV2) {
    await validateRequest(request);
  }
  
  // Rest of workflow remains compatible
  return processRequest(request);
}

// Deployment strategy
export class WorkflowDeployment {
  async deployNewVersion(workflowName: string, version: number) {
    // 1. Deploy new worker with new version
    // 2. Wait for existing workflows to complete
    // 3. Switch traffic to new version
    // 4. Deprecate old version
  }
}
```

## Phase 2: Hospice-Specific Features (Weeks 4-6)

### Week 4: Scheduled Workflows & Compliance

#### 4.1 Scheduled/Cron Workflows
**Objective**: Implement automated compliance checks

**Implementation**:
```typescript
// apps/server/src/temporal/scheduled-workflows.ts
export async function registerScheduledWorkflows(client: Client) {
  // Daily benefit period check
  await client.schedule.create({
    scheduleId: 'benefit-period-check',
    spec: {
      cronExpressions: ['0 9 * * *'], // 9 AM daily
    },
    action: {
      type: 'startWorkflow',
      workflowType: benefitPeriodCheckWorkflow,
      args: [],
      taskQueue: 'compliance-v1.0',
    },
  });
  
  // Weekly IDG preparation
  await client.schedule.create({
    scheduleId: 'idg-preparation',
    spec: {
      cronExpressions: ['0 8 * * MON'], // Monday 8 AM
    },
    action: {
      type: 'startWorkflow',
      workflowType: idgPreparationWorkflow,
      args: [],
      taskQueue: 'compliance-v1.0',
    },
  });
}

// Benefit period check workflow
export async function benefitPeriodCheckWorkflow(): Promise<void> {
  const expiringPeriods = await checkExpiringBenefitPeriods();
  
  for (const period of expiringPeriods) {
    await startChild(recertificationWorkflow, {
      workflowId: `recert-${period.patientId}-${Date.now()}`,
      args: [period],
    });
  }
}
```

#### 4.2 Compliance Tracking
**Objective**: Track and ensure regulatory compliance

**Implementation**:
```typescript
// apps/server/src/temporal/compliance-workflows.ts
export interface ComplianceRequirement {
  type: 'visit' | 'documentation' | 'certification';
  frequency: string;
  deadline: Date;
  regulation: string;
}

export async function complianceMonitoringWorkflow(
  patientId: string
): Promise<ComplianceStatus> {
  const requirements = await getComplianceRequirements(patientId);
  const results: ComplianceCheckResult[] = [];
  
  for (const req of requirements) {
    const result = await checkRequirement(req, patientId);
    results.push(result);
    
    if (!result.compliant && result.daysUntilDeadline < 5) {
      await sendComplianceAlert({
        patientId,
        requirement: req,
        urgency: 'high',
      });
    }
  }
  
  // Generate compliance report
  const report = await generateComplianceReport(results);
  await storeComplianceAudit(patientId, report);
  
  return {
    compliant: results.every(r => r.compliant),
    report,
  };
}
```

### Week 5: Human-in-the-Loop & Security

#### 5.1 Human Approval Workflows
**Objective**: Implement approval patterns for critical decisions

**Implementation**:
```typescript
// apps/server/src/temporal/approval-workflows.ts
export interface ApprovalRequest {
  type: 'discharge' | 'medication_change' | 'plan_of_care';
  requesterId: string;
  patientId: string;
  changes: any;
  approvers: string[];
}

export async function approvalWorkflow(
  request: ApprovalRequest
): Promise<ApprovalResult> {
  // Set up signals for approval/rejection
  let approved = false;
  let rejected = false;
  let approver: string | null = null;
  let comments = '';
  
  setHandler(approveSignal, (data: ApprovalSignalData) => {
    approved = true;
    approver = data.approverId;
    comments = data.comments;
  });
  
  setHandler(rejectSignal, (data: RejectionSignalData) => {
    rejected = true;
    approver = data.approverId;
    comments = data.reason;
  });
  
  // Send approval request
  await sendApprovalRequest(request);
  
  // Wait for approval with timeout
  const timeout = '48h';
  const approvalDeadline = Date.now() + ms(timeout);
  
  // Send reminders
  let reminderCount = 0;
  while (!approved && !rejected && Date.now() < approvalDeadline) {
    await sleep('12h');
    
    if (!approved && !rejected) {
      reminderCount++;
      await sendApprovalReminder(request, reminderCount);
      
      // Escalate after 2 reminders
      if (reminderCount >= 2) {
        await escalateApproval(request);
      }
    }
  }
  
  if (!approved && !rejected) {
    // Auto-reject after timeout
    return {
      status: 'timeout',
      message: 'Approval request timed out',
    };
  }
  
  return {
    status: approved ? 'approved' : 'rejected',
    approver,
    comments,
    timestamp: new Date(),
  };
}
```

#### 5.2 PHI Security Layer
**Objective**: Protect patient health information in workflows

**Implementation**:
```typescript
// apps/server/src/temporal/security.ts
export class WorkflowSecurity {
  private static encryptionKey = process.env.TEMPORAL_ENCRYPTION_KEY!;
  
  // Encrypt sensitive data before passing to workflows
  static async encryptPHI<T>(data: T): Promise<EncryptedPayload> {
    const encrypted = await encrypt(
      JSON.stringify(data),
      this.encryptionKey
    );
    
    return {
      data: encrypted,
      timestamp: new Date(),
      version: 1,
    };
  }
  
  // Decrypt in activities (not in workflow code)
  static async decryptPHI<T>(payload: EncryptedPayload): Promise<T> {
    const decrypted = await decrypt(
      payload.data,
      this.encryptionKey
    );
    
    return JSON.parse(decrypted) as T;
  }
}

// Audit decorator for activities
export function auditPHIAccess(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalMethod = descriptor.value;
  
  descriptor.value = async function(...args: any[]) {
    const context = Context.current();
    const { workflowId, activityType } = context.info;
    
    // Log PHI access
    await logPHIAccess({
      workflowId,
      activityType,
      userId: context.userId,
      timestamp: new Date(),
      purpose: activityType.name,
    });
    
    // Execute original method
    const result = await originalMethod.apply(this, args);
    
    return result;
  };
}

// Example usage in activity
export class SecureActivities {
  @auditPHIAccess
  async readPatientData(encryptedId: EncryptedPayload): Promise<Patient> {
    const patientId = await WorkflowSecurity.decryptPHI<string>(encryptedId);
    return await db.findPatient(patientId);
  }
}
```

### Week 6: Event Integration & Monitoring

#### 6.1 Event-to-Workflow Bridge
**Objective**: Connect existing event system to Temporal workflows

**Implementation**:
```typescript
// apps/server/src/temporal/event-bridge.ts
export class EventToWorkflowBridge {
  constructor(
    private eventDispatcher: EventDispatcher,
    private temporalClient: TemporalClient
  ) {
    this.registerEventHandlers();
  }
  
  private registerEventHandlers() {
    // Patient status changes
    this.eventDispatcher.on('patient.status.changed', async (event) => {
      const { resourceId, data } = event;
      
      switch (data.status) {
        case 'Deceased':
          await this.startBereavementWorkflow(resourceId);
          break;
        case 'Admitted':
          await this.startAdmissionWorkflow(resourceId);
          break;
        case 'Discharged':
          await this.startDischargeWorkflow(resourceId);
          break;
      }
    });
    
    // Plan of care updates
    this.eventDispatcher.on('planOfCare.updated', async (event) => {
      await this.notifyTeamOfChanges(event.resourceId, event.data.changes);
    });
    
    // Action items
    this.eventDispatcher.on('action.overdue', async (event) => {
      await this.startEscalationWorkflow(event.resourceId);
    });
  }
  
  private async startBereavementWorkflow(patientId: string) {
    const client = await this.temporalClient.getClient();
    await client.workflow.start(bereavementWorkflow, {
      workflowId: `bereavement-${patientId}-${Date.now()}`,
      taskQueue: 'main-v1.0',
      args: [{ patientId }],
    });
  }
}
```

#### 6.2 Observability & Monitoring
**Objective**: Add comprehensive monitoring and alerting

**Implementation**:
```typescript
// apps/server/src/temporal/monitoring.ts
import { metrics } from '@opentelemetry/api';

export class TemporalMonitoring {
  private meter = metrics.getMeter('temporal-workflows');
  
  // Workflow metrics
  private workflowStarted = this.meter.createCounter('workflow_started');
  private workflowCompleted = this.meter.createCounter('workflow_completed');
  private workflowFailed = this.meter.createCounter('workflow_failed');
  private workflowDuration = this.meter.createHistogram('workflow_duration_ms');
  
  // Activity metrics
  private activityStarted = this.meter.createCounter('activity_started');
  private activityCompleted = this.meter.createCounter('activity_completed');
  private activityFailed = this.meter.createCounter('activity_failed');
  private activityRetried = this.meter.createCounter('activity_retried');
  
  // SLA monitoring
  async checkSLAs() {
    const slas = {
      patientAdmission: { maxDuration: '48h', alertThreshold: '36h' },
      recertification: { maxDuration: '5d', alertThreshold: '3d' },
      visitDocumentation: { maxDuration: '24h', alertThreshold: '20h' },
    };
    
    for (const [workflowType, sla] of Object.entries(slas)) {
      const violations = await this.findSLAViolations(workflowType, sla);
      
      if (violations.length > 0) {
        await this.alertOnSLAViolation(workflowType, violations);
      }
    }
  }
  
  // Custom business metrics
  recordComplianceCheck(patientId: string, compliant: boolean) {
    this.meter.createCounter('compliance_checks').add(1, {
      patient_id: patientId,
      compliant: compliant.toString(),
    });
  }
}

// Structured logging
export class TemporalLogger {
  static logWorkflowStart(workflowId: string, type: string, input: any) {
    logger.info('Workflow started', {
      workflowId,
      workflowType: type,
      inputSize: JSON.stringify(input).length,
      timestamp: new Date().toISOString(),
    });
  }
  
  static logActivityError(
    activityType: string,
    error: Error,
    attempt: number
  ) {
    logger.error('Activity failed', {
      activityType,
      error: error.message,
      stack: error.stack,
      attempt,
      isRetryable: error instanceof RetryableError,
    });
  }
}
```

## Phase 3: Advanced Features (Weeks 7-8)

### Week 7: Advanced Patterns

#### 7.1 Saga Pattern Implementation
**Objective**: Handle complex multi-step processes with compensation

**Implementation**:
```typescript
// apps/server/src/temporal/saga-pattern.ts
export interface SagaStep<T> {
  name: string;
  execute: (context: T) => Promise<void>;
  compensate: (context: T) => Promise<void>;
}

export async function sagaWorkflow<T>(
  steps: SagaStep<T>[],
  context: T
): Promise<void> {
  const completedSteps: SagaStep<T>[] = [];
  
  try {
    for (const step of steps) {
      await step.execute(context);
      completedSteps.push(step);
    }
  } catch (error) {
    // Compensate in reverse order
    for (const step of completedSteps.reverse()) {
      try {
        await step.compensate(context);
      } catch (compensationError) {
        // Log but continue compensation
        logger.error('Compensation failed', {
          step: step.name,
          error: compensationError,
        });
      }
    }
    throw error;
  }
}

// Example: Patient admission saga
const admissionSteps: SagaStep<AdmissionContext>[] = [
  {
    name: 'verifyInsurance',
    execute: async (ctx) => await verifyInsurance(ctx.patientId),
    compensate: async (ctx) => await cancelInsuranceVerification(ctx.verificationId),
  },
  {
    name: 'assignCaseManager',
    execute: async (ctx) => {
      ctx.caseManagerId = await assignCaseManager(ctx.patientId);
    },
    compensate: async (ctx) => await unassignCaseManager(ctx.caseManagerId),
  },
  {
    name: 'scheduleInitialVisits',
    execute: async (ctx) => {
      ctx.visitIds = await scheduleInitialVisits(ctx.patientId);
    },
    compensate: async (ctx) => await cancelVisits(ctx.visitIds),
  },
];
```

#### 7.2 Long-Running State Machines
**Objective**: Model patient lifecycle as state machine

**Implementation**:
```typescript
// apps/server/src/temporal/state-machines.ts
export enum PatientState {
  REFERRAL = 'REFERRAL',
  ADMITTED = 'ADMITTED',
  ACTIVE = 'ACTIVE',
  RECERTIFIED = 'RECERTIFIED',
  DISCHARGED = 'DISCHARGED',
  DECEASED = 'DECEASED',
  BEREAVEMENT = 'BEREAVEMENT',
}

export class PatientLifecycleWorkflow {
  private state: PatientState = PatientState.REFERRAL;
  private stateHistory: StateTransition[] = [];
  
  async execute(patientId: string): Promise<void> {
    // This workflow runs for the entire patient lifecycle
    let continueRunning = true;
    
    setHandler(transitionSignal, (transition: StateTransition) => {
      this.transitionTo(transition.newState, transition.reason);
    });
    
    setHandler(terminateSignal, () => {
      continueRunning = false;
    });
    
    while (continueRunning) {
      // Execute state-specific logic
      switch (this.state) {
        case PatientState.REFERRAL:
          await this.handleReferralState(patientId);
          break;
        case PatientState.ACTIVE:
          await this.handleActiveState(patientId);
          break;
        case PatientState.BEREAVEMENT:
          await this.handleBereavementState(patientId);
          break;
      }
      
      // Check for automatic transitions
      const autoTransition = await this.checkAutoTransitions(patientId);
      if (autoTransition) {
        this.transitionTo(autoTransition.state, autoTransition.reason);
      }
      
      // Sleep before next check
      await sleep('1h');
    }
  }
  
  private transitionTo(newState: PatientState, reason: string) {
    const transition = {
      from: this.state,
      to: newState,
      reason,
      timestamp: new Date(),
    };
    
    this.stateHistory.push(transition);
    this.state = newState;
    
    // Trigger state-specific workflows
    this.onStateTransition(transition);
  }
}
```

### Week 8: Batch Processing & Documentation

#### 8.1 Batch Processing Framework
**Objective**: Handle large-scale operations efficiently

**Implementation**:
```typescript
// apps/server/src/temporal/batch-processing.ts
export interface BatchJob<T> {
  items: T[];
  batchSize: number;
  processor: (batch: T[]) => Promise<void>;
  onError?: (error: Error, batch: T[]) => Promise<void>;
}

export async function batchProcessingWorkflow<T>(
  job: BatchJob<T>
): Promise<BatchResult> {
  const results: BatchResult = {
    total: job.items.length,
    processed: 0,
    failed: 0,
    errors: [],
  };
  
  // Process in parallel batches
  const batches = chunk(job.items, job.batchSize);
  const promises = batches.map(async (batch, index) => {
    try {
      await job.processor(batch);
      results.processed += batch.length;
    } catch (error) {
      results.failed += batch.length;
      results.errors.push({
        batchIndex: index,
        error: error.message,
      });
      
      if (job.onError) {
        await job.onError(error, batch);
      }
    }
  });
  
  await Promise.all(promises);
  
  return results;
}

// Example: Nightly census processing
export async function dailyCensusWorkflow(): Promise<void> {
  const patients = await getActivePatients();
  
  await batchProcessingWorkflow({
    items: patients,
    batchSize: 50,
    processor: async (batch) => {
      for (const patient of batch) {
        await calculatePatientDays(patient);
        await updateBillingRecords(patient);
        await checkComplianceStatus(patient);
      }
    },
  });
  
  // Generate reports
  await generateCensusReport();
  await sendCensusNotifications();
}
```

#### 8.2 Documentation
**Objective**: Create comprehensive documentation

**Implementation Structure**:
```
docs/temporal/
├── README.md                 # Overview and quick start
├── architecture.md          # Architecture decisions
├── workflows/
│   ├── patient-request.md   # Existing workflow docs
│   ├── compliance.md        # Compliance workflow docs
│   ├── approval.md          # Approval patterns
│   └── state-machines.md    # State machine patterns
├── operations/
│   ├── deployment.md        # Deployment procedures
│   ├── monitoring.md        # Monitoring and alerts
│   ├── troubleshooting.md   # Common issues
│   └── disaster-recovery.md # DR procedures
├── development/
│   ├── testing.md           # Testing strategies
│   ├── versioning.md        # Versioning guide
│   └── best-practices.md    # Coding standards
└── compliance/
    ├── hipaa.md             # HIPAA compliance
    ├── audit-trail.md       # Audit requirements
    └── data-retention.md    # Data retention policies
```

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-3)
- **Week 1**: Client consolidation, error hierarchy, retry policies
- **Week 2**: Test suite, dead letter queue
- **Week 3**: Configuration extraction, versioning strategy

### Phase 2: Hospice Features (Weeks 4-6)
- **Week 4**: Scheduled workflows, compliance monitoring
- **Week 5**: Human approval patterns, PHI security
- **Week 6**: Event integration, observability

### Phase 3: Advanced (Weeks 7-8)
- **Week 7**: Saga pattern, state machines
- **Week 8**: Batch processing, documentation

## Success Metrics

1. **Reliability**
   - Workflow success rate > 99.5%
   - Zero data loss incidents
   - Recovery time < 5 minutes

2. **Performance**
   - Activity p95 latency < 1 second (excluding AI calls)
   - Workflow throughput > 1000/minute
   - Queue depth < 100 items

3. **Compliance**
   - 100% audit trail coverage
   - Zero PHI exposure incidents
   - Compliance check automation > 90%

4. **Developer Experience**
   - Test coverage > 80%
   - Deployment time < 15 minutes
   - Mean time to implement new workflow < 2 days

## Risk Mitigation

1. **Data Security**
   - Encrypt all PHI in workflows
   - Implement audit logging
   - Regular security reviews

2. **Performance**
   - Load test all workflows
   - Implement circuit breakers
   - Monitor queue depths

3. **Reliability**
   - Multi-region deployment
   - Automated failover
   - Regular disaster recovery drills

## Next Steps

1. Review and approve plan
2. Set up development environment
3. Begin Phase 1 implementation
4. Weekly progress reviews
5. Adjust timeline based on findings