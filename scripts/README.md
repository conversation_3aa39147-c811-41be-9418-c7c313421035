# Hospice OS Scripts

This directory contains utility scripts for the Hospice OS project.

## Generate Types Script

The `generate-types.ts` script connects to the PostgreSQL database, fetches JSON schemas from the `schemas` table, and generates TypeScript interfaces and Zod schemas in the `packages/apptypes/src` directory.

### Usage

You can run the script using one of the following methods:

#### Using npm script

```bash
# From the project root
npm run generate-types

# From the apptypes package
cd packages/apptypes
npm run generate-types
```

#### Using the shell script

```bash
# From the project root
./scripts/generate-types.sh
```

### Environment Variables

The script uses the following environment variables for database connection:

- `DB_HOST`: PostgreSQL host (default: "localhost")
- `DB_PORT`: PostgreSQL port (default: "5432")
- `DB_NAME`: PostgreSQL database name (default: "hospice_os")
- `DB_USER`: PostgreSQL username (default: "postgres")
- `DB_PASSWORD`: PostgreSQL password (default: "postgres")

You can set these variables before running the script:

```bash
DB_HOST=myhost DB_USER=myuser DB_PASSWORD=mypassword npm run generate-types
```

### What the Script Does

1. Connects to the PostgreSQL database
2. Fetches all JSON schemas from the `schemas` table
3. Groups schemas by resource type
4. Converts JSON schemas to TypeScript interfaces and Zod schemas
5. Handles schema inheritance and references
6. Writes the generated code to files in `packages/apptypes/src`
7. Updates `packages/apptypes/src/index.ts` to export all types

### Generated Files

The script generates the following files:

- TypeScript interface files for each resource type (e.g., `patient.ts`, `user.ts`)
- Updated `index.ts` file that exports all types

Each generated file includes:
- TypeScript interfaces
- Zod schemas for validation
- Proper type inheritance
