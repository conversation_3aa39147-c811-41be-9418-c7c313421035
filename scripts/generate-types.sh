#!/bin/bash

# Set database connection environment variables
export DB_HOST=${DB_HOST:-"hospice-os-staging.cluster-cd2jnw5dlvuk.us-east-1.rds.amazonaws.com"}
export DB_PORT=${DB_PORT:-"5432"}
export DB_NAME=${DB_NAME:-"hospiceos"}
export DB_USER=${DB_USER:-"hospiceosstaging"}
export DB_PASSWORD=${DB_PASSWORD:-"Ch3I,YPwth3U.ER-dFHrPbikbX6cF7"}

# Run the TypeScript script
npx ts-node scripts/generate-types.ts
