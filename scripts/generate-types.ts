import { Pool } from 'pg';
import * as fs from 'fs';
import * as path from 'path';

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'hospice-os-staging.cluster-cd2jnw5dlvuk.us-east-1.rds.amazonaws.com',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'hospiceos',
  user: process.env.DB_USER || 'hospiceosstaging',
  password: process.env.DB_PASSWORD || 'Ch3I,YPwth3U.ER-dFHrPbikbX6cF7',
};

// Output directory for generated types
const outputDir = path.resolve(__dirname, '../packages/apptypes/src');

// Map of JSON Schema types to TypeScript types
const typeMapping: Record<string, string> = {
  string: 'string',
  number: 'number',
  integer: 'number',
  boolean: 'boolean',
  array: 'Array<any>',
  object: 'Record<string, any>',
  null: 'null',
};

// Map to store schemas by ID for reference resolution
const schemasById: Map<string, any> = new Map();
// Map to store generated interfaces by schema ID
const generatedInterfaces: Map<string, string> = new Map();
// Map to group schemas by resource type
const schemasByType: Map<string, any[]> = new Map();

/**
 * Main function to generate TypeScript interfaces from JSON schemas
 */
async function generateTypes() {
  console.log('Connecting to database...');
  const pool = new Pool(dbConfig);

  try {
    // Fetch only schemas from the super_admin agency
    const result = await pool.query(`
      SELECT id, name, version, agency_id, base_schema_id, schema
      FROM schemas
      WHERE agency_id = '857d2613-f37f-4c83-8e7e-cb7c906754f3'
      ORDER BY name, version
    `);

    console.log(`Found ${result.rows.length} schemas in the database`);

    // First pass: store all schemas by ID for reference resolution
    for (const row of result.rows) {
      // Remove "Base" from schema names that end with "Base"
      const name = row.name.endsWith('Base') ? row.name.slice(0, -4) : row.name;
      
      schemasById.set(row.id, {
        id: row.id,
        name: name,
        originalName: row.name, // Keep the original name for reference
        version: row.version,
        agencyId: row.agency_id,
        baseSchemaId: row.base_schema_id,
        schema: row.schema
      });
    }

    // Second pass: group schemas by type for file organization
    for (const row of result.rows) {
      const schema = row.schema;
      const schemaInfo = schemasById.get(row.id);
      
      if (!schemaInfo) continue;
      
      let resourceType = schemaInfo.name;
      
      // Try to determine the resource type from the schema
      if (schema.properties && schema.properties.resourceType && schema.properties.resourceType.const) {
        resourceType = schema.properties.resourceType.const;
      } else if (schema.title) {
        resourceType = schema.title.replace(/Base$/, '').toLowerCase();
      }
      
      // Convert to lowercase for file naming
      const fileType = resourceType.toLowerCase();
      
      if (!schemasByType.has(fileType)) {
        schemasByType.set(fileType, []);
      }
      
      schemasByType.get(fileType)?.push(schemaInfo);
    }

// Remove debug code

    // Generate interfaces for each type group
    for (const [fileType, schemas] of schemasByType.entries()) {
      await generateInterfacesForType(fileType, schemas);
    }

    // Update index.ts to export all types
    await updateIndexFile();

    console.log('TypeScript interfaces generated successfully!');
  } catch (error) {
    console.error('Error generating TypeScript interfaces:', error);
  } finally {
    await pool.end();
  }
}

/**
 * Generate interfaces for a specific resource type
 */
async function generateInterfacesForType(fileType: string, schemas: any[]) {
  console.log(`Generating interfaces for ${fileType}...`);
  
  let fileContent = `import { z } from 'zod';\n`;
  
  // Always import BaseResource for types that don't have a base schema
  if (fileType !== 'resource') {
    fileContent += `import { BaseResource, BaseResourceSchema } from './resource';\n`;
  }
  
  // Find all referenced types in the schemas
  const referencedTypes = new Set<string>();
  const importedTypes = new Set<string>();
  
  // First pass: collect all base schema IDs and referenced types
  const baseSchemaIds = schemas
    .filter(s => s.baseSchemaId)
    .map(s => s.baseSchemaId);
  
  // Add base schema imports
  for (const baseId of baseSchemaIds) {
    const baseSchema = schemasById.get(baseId);
    if (baseSchema) {
      const baseType = baseSchema.name.replace(/Base$/, '').toLowerCase();
      if (baseType !== fileType) {
        referencedTypes.add(baseSchema.name);
      }
    }
  }
  
  // Find all referenced types in properties
  for (const schema of schemas) {
    // Extract all properties including those in allOf
    const { properties } = extractProperties(schema.schema);
    
    // Check all properties for references
    for (const propSchema of Object.values<any>(properties)) {
      findReferencedTypes(propSchema, referencedTypes);
    }
  }
  
  // Add imports for referenced types
  for (const typeName of referencedTypes) {
    // Skip self-references
    if (schemas.some(s => s.name === typeName)) {
      continue;
    }
    
    // Find the schema for this type
    let schemaForType = null;
    for (const [id, schema] of schemasById.entries()) {
      if (schema.name === typeName) {
        schemaForType = schema;
        break;
      }
    }
    
    if (schemaForType) {
      const importType = schemaForType.name.replace(/Base$/, '').toLowerCase();
      if (importType !== fileType && !importedTypes.has(importType)) {
        fileContent += `import { ${schemaForType.name}, ${schemaForType.name}Schema } from './${importType}';\n`;
        importedTypes.add(importType);
      }
    }
  }
  
  fileContent += '\n';
  
  // Sort schemas to ensure base schemas are defined first
  const sortedSchemas = [...schemas].sort((a, b) => {
    // Base schemas (without baseSchemaId) come first
    if (!a.baseSchemaId && b.baseSchemaId) return -1;
    if (a.baseSchemaId && !b.baseSchemaId) return 1;
    return 0;
  });
  
  // Generate interfaces and Zod schemas
  for (const schema of sortedSchemas) {
    const interfaceContent = generateInterface(schema);
    const zodSchemaContent = generateZodSchema(schema);
    
    fileContent += interfaceContent + '\n\n' + zodSchemaContent + '\n\n';
  }
  
  // Write to file
  const filePath = path.join(outputDir, `${fileType}.ts`);
  await fs.promises.writeFile(filePath, fileContent);
  console.log(`Written to ${filePath}`);
}

/**
 * Extract properties from a JSON schema, including those in allOf
 */
function extractProperties(schema: any): { properties: Record<string, any>, required: string[] } {
  let properties: Record<string, any> = {};
  let required: string[] = [];
  
  // Handle direct properties
  if (schema.properties) {
    properties = { ...properties, ...schema.properties };
  }
  
  // Handle required fields
  if (schema.required && Array.isArray(schema.required)) {
    required = [...required, ...schema.required];
  }
  
  // Handle allOf
  if (schema.allOf && Array.isArray(schema.allOf)) {
    for (const subSchema of schema.allOf) {
      // Skip $ref entries as they're handled by inheritance
      if (subSchema.$ref) continue;
      
      // Extract properties from this part of allOf
      if (subSchema.properties) {
        properties = { ...properties, ...subSchema.properties };
      }
      
      // Extract required fields from this part of allOf
      if (subSchema.required && Array.isArray(subSchema.required)) {
        required = [...required, ...subSchema.required];
      }
    }
  }
  
  return { properties, required };
}

/**
 * Generate TypeScript interface from JSON schema
 */
function generateInterface(schemaInfo: any): string {
  const { id, name, baseSchemaId, schema } = schemaInfo;
  
  // Check if we've already generated this interface
  if (generatedInterfaces.has(id)) {
    return generatedInterfaces.get(id) || '';
  }
  
  let interfaceContent = `export interface ${name}`;
  
  // Handle inheritance
  if (baseSchemaId) {
    const baseSchema = schemasById.get(baseSchemaId);
    if (baseSchema) {
      interfaceContent += ` extends ${baseSchema.name}`;
    }
  } else if (name !== 'BaseResource' && 
             schema.properties && 
             schema.properties.resourceType && 
             schema.properties.id &&
             schema.required &&
             schema.required.includes('id') &&
             schema.required.includes('resourceType')) {
    // Make types without a base schema extend BaseResource if they have both resourceType and id properties
    // and both properties are required
    interfaceContent += ` extends BaseResource`;
  }
  
  interfaceContent += ' {\n';
  
  // Extract properties and required fields
  const { properties, required } = extractProperties(schema);
  
  // Add properties
  if (Object.keys(properties).length > 0) {
    for (const [propName, propSchema] of Object.entries<any>(properties)) {
      // Skip properties that are inherited from base schema
      if (baseSchemaId) {
        const baseSchema = schemasById.get(baseSchemaId);
        const baseProperties = baseSchema ? extractProperties(baseSchema.schema).properties : {};
        if (baseProperties[propName]) {
          continue;
        }
      }
      
      const propType = getTypeScriptType(propSchema);
      const isRequired = required.includes(propName);
      
      interfaceContent += `  ${propName}${isRequired ? '' : '?'}: ${propType};\n`;
    }
  }
  
  interfaceContent += '}';
  
  // Store the generated interface
  generatedInterfaces.set(id, interfaceContent);
  
  return interfaceContent;
}

/**
 * Generate Zod schema from JSON schema
 */
function generateZodSchema(schemaInfo: any): string {
  const { name, baseSchemaId, schema } = schemaInfo;
  
  let zodContent = `export const ${name}Schema = `;
  
  // Handle inheritance
  if (baseSchemaId) {
    const baseSchema = schemasById.get(baseSchemaId);
    if (baseSchema) {
      zodContent += `${baseSchema.name}Schema.extend({\n`;
    } else {
      zodContent += `z.object({\n`;
    }
  } else if (name !== 'BaseResource' && 
             schema.properties && 
             schema.properties.resourceType && 
             schema.properties.id &&
             schema.required &&
             schema.required.includes('id') &&
             schema.required.includes('resourceType')) {
    // Make types without a base schema extend BaseResourceSchema if they have both resourceType and id properties
    // and both properties are required
    zodContent += `BaseResourceSchema.extend({\n`;
  } else {
    zodContent += `z.object({\n`;
  }
  
  // Extract properties and required fields
  const { properties, required } = extractProperties(schema);
  
  // Add properties
  if (Object.keys(properties).length > 0) {
    for (const [propName, propSchema] of Object.entries<any>(properties)) {
      // Skip properties that are inherited from base schema
      if (baseSchemaId) {
        const baseSchema = schemasById.get(baseSchemaId);
        const baseProperties = baseSchema ? extractProperties(baseSchema.schema).properties : {};
        if (baseProperties[propName]) {
          continue;
        }
      }
      
      const zodType = getZodType(propSchema);
      const isRequired = required.includes(propName);
      
      zodContent += `  ${propName}: ${zodType}${isRequired ? '' : '.optional()'},\n`;
    }
  }
  
  zodContent += '});';
  
  return zodContent;
}

/**
 * Get TypeScript type from JSON schema
 */
function getTypeScriptType(propSchema: any): string {
  if (propSchema.$ref) {
    // Handle references to other schemas
    const refId = propSchema.$ref;
    const referencedSchema = schemasById.get(refId);
    if (referencedSchema) {
      return referencedSchema.name;
    }
    return 'any';
  }
  
  if (propSchema.const) {
    // Handle const values
    return typeof propSchema.const === 'string' 
      ? `'${propSchema.const}'` 
      : typeof propSchema.const;
  }
  
  if (propSchema.enum) {
    // Handle enums
    const enumValues = propSchema.enum
      .map((val: any) => typeof val === 'string' ? `'${val}'` : val)
      .join(' | ');
    return enumValues;
  }
  
  if (propSchema.type === 'array') {
    // Handle arrays
    if (propSchema.items) {
      const itemType = getTypeScriptType(propSchema.items);
      return `${itemType}[]`;
    }
    return 'any[]';
  }
  
  if (propSchema.type === 'object') {
    // Handle objects
    if (propSchema.properties) {
      let objectType = '{\n';
      for (const [subPropName, subPropSchema] of Object.entries<any>(propSchema.properties)) {
        const subPropType = getTypeScriptType(subPropSchema);
        const isRequired = propSchema.required && propSchema.required.includes(subPropName);
        objectType += `    ${subPropName}${isRequired ? '' : '?'}: ${subPropType};\n`;
      }
      objectType += '  }';
      return objectType;
    }
    return 'Record<string, any>';
  }
  
  // Handle basic types
  return typeMapping[propSchema.type] || 'any';
}

/**
 * Get Zod type from JSON schema
 */
function getZodType(propSchema: any): string {
  if (propSchema.$ref) {
    // Handle references to other schemas
    const refId = propSchema.$ref;
    const referencedSchema = schemasById.get(refId);
    if (referencedSchema) {
      return `${referencedSchema.name}Schema`;
    }
    return 'z.any()';
  }
  
  if (propSchema.const) {
    // Handle const values
    return typeof propSchema.const === 'string' 
      ? `z.literal('${propSchema.const}')` 
      : `z.literal(${propSchema.const})`;
  }
  
  if (propSchema.enum) {
    // Handle enums
    const enumValues = propSchema.enum
      .map((val: any) => typeof val === 'string' ? `'${val}'` : val)
      .join(', ');
    return `z.enum([${enumValues}])`;
  }
  
  if (propSchema.type === 'array') {
    // Handle arrays
    if (propSchema.items) {
      const itemType = getZodType(propSchema.items);
      return `z.array(${itemType})`;
    }
    return 'z.array(z.any())';
  }
  
  if (propSchema.type === 'object') {
    // Handle objects
    if (propSchema.properties) {
      let objectType = 'z.object({\n';
      for (const [subPropName, subPropSchema] of Object.entries<any>(propSchema.properties)) {
        const subPropType = getZodType(subPropSchema);
        const isRequired = propSchema.required && propSchema.required.includes(subPropName);
        objectType += `    ${subPropName}: ${subPropType}${isRequired ? '' : '.optional()'},\n`;
      }
      objectType += '  })';
      return objectType;
    }
    return 'z.record(z.string(), z.any())';
  }
  
  // Handle basic types with validation
  switch (propSchema.type) {
    case 'string':
      if (propSchema.format === 'email') {
        return 'z.string().email()';
      } else if (propSchema.format === 'uuid') {
        return 'z.string().uuid()';
      } else if (propSchema.format === 'date-time') {
        return 'z.string().datetime()';
      } else if (propSchema.minLength || propSchema.maxLength) {
        let result = 'z.string()';
        if (propSchema.minLength) result += `.min(${propSchema.minLength})`;
        if (propSchema.maxLength) result += `.max(${propSchema.maxLength})`;
        return result;
      }
      return 'z.string()';
    
    case 'number':
    case 'integer':
      let result = propSchema.type === 'integer' ? 'z.number().int()' : 'z.number()';
      if (propSchema.minimum !== undefined) result += `.min(${propSchema.minimum})`;
      if (propSchema.maximum !== undefined) result += `.max(${propSchema.maximum})`;
      return result;
    
    case 'boolean':
      return 'z.boolean()';
    
    default:
      return 'z.any()';
  }
}

/**
 * Update index.ts to export all generated types
 */
async function updateIndexFile() {
  const files = await fs.promises.readdir(outputDir);
  const typeFiles = files.filter(file => 
    file.endsWith('.ts') && 
    file !== 'index.ts' && 
    !file.startsWith('.') &&
    // Skip files that would cause naming conflicts
    file !== 'idgmeetingbase.ts'
  );
  
  let indexContent = '';
  
  for (const file of typeFiles) {
    const moduleName = file.replace('.ts', '');
    indexContent += `export * from './${moduleName}';\n`;
  }
  
  const indexPath = path.join(outputDir, 'index.ts');
  await fs.promises.writeFile(indexPath, indexContent);
  console.log(`Updated ${indexPath}`);
  
  // Update imports in files that reference schemas with "Base" in their names
  await updateImportsInFiles();
}

/**
 * Update imports in files that reference schemas with "Base" in their names
 */
async function updateImportsInFiles() {
  const files = await fs.promises.readdir(outputDir);
  const typeFiles = files.filter(file => 
    file.endsWith('.ts') && 
    file !== 'index.ts' && 
    !file.startsWith('.')
  );
  
  // Map of original schema names to new schema names
  const schemaNameMap = new Map<string, string>();
  
  // Build a map of original schema names to new schema names
  for (const [id, schema] of schemasById.entries()) {
    if (schema.originalName && schema.originalName !== schema.name) {
      schemaNameMap.set(schema.originalName, schema.name);
      schemaNameMap.set(`${schema.originalName}Schema`, `${schema.name}Schema`);
    }
  }
  
  // Update imports in all files
  for (const file of typeFiles) {
    const filePath = path.join(outputDir, file);
    let content = await fs.promises.readFile(filePath, 'utf-8');
    let modified = false;
    
    // Check for imports that need to be updated
    for (const [oldName, newName] of schemaNameMap.entries()) {
      const importRegex = new RegExp(`import\\s+\\{[^}]*\\b${oldName}\\b[^}]*\\}\\s+from\\s+`, 'g');
      if (importRegex.test(content)) {
        // Replace the import
        content = content.replace(
          new RegExp(`\\b${oldName}\\b`, 'g'),
          newName
        );
        modified = true;
      }
      
      // Also replace any usage of the old name in the file
      const usageRegex = new RegExp(`\\b${oldName}\\b`, 'g');
      if (usageRegex.test(content)) {
        content = content.replace(usageRegex, newName);
        modified = true;
      }
    }
    
    // Write the updated content back to the file
    if (modified) {
      await fs.promises.writeFile(filePath, content);
      console.log(`Updated imports in ${filePath}`);
    }
  }
}

/**
 * Recursively find referenced types in a JSON schema
 */
function findReferencedTypes(schema: any, referencedTypes: Set<string>) {
  if (!schema) return;
  
  // Check for direct references
  if (schema.$ref) {
    const refId = schema.$ref;
    const referencedSchema = schemasById.get(refId);
    if (referencedSchema) {
      referencedTypes.add(referencedSchema.name);
    }
  }
  
  // Check array items
  if (schema.type === 'array' && schema.items) {
    findReferencedTypes(schema.items, referencedTypes);
  }
  
  // Check object properties
  if (schema.type === 'object' && schema.properties) {
    for (const propSchema of Object.values<any>(schema.properties)) {
      findReferencedTypes(propSchema, referencedTypes);
    }
  }
  
  // Check for allOf, anyOf, oneOf
  for (const key of ['allOf', 'anyOf', 'oneOf']) {
    if (Array.isArray(schema[key])) {
      for (const subSchema of schema[key]) {
        findReferencedTypes(subSchema, referencedTypes);
      }
    }
  }
}

// Run the script
generateTypes().catch(console.error);
